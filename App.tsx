import React, { useEffect } from 'react';
import { Alert, Linking, Platform, SafeAreaView, StatusBar, Text, View, AppState } from 'react-native';
import { Provider, useDispatch } from 'react-redux';
import { setLocation } from './redux/locationSlice';
import Geolocation from '@react-native-community/geolocation';
import { requestMultiple, PERMISSIONS, RESULTS } from 'react-native-permissions';
import RNAndroidLocationEnabler from 'react-native-android-location-enabler';
import { COLORS } from './src/constants';
import { store } from './redux/store';
import AppNavigation from './src/navigations/AppNaviagtion';
import socketService from './src/services/socketService';
import locationService from './src/services/locationService';
import firebase from '@react-native-firebase/app';
import messaging from '@react-native-firebase/messaging';
import { CallProvider, useCall } from './src/services/CallContext';
import GlobalCallManager from './src/components/GlobalCallManager';
import callNotificationService from './src/services/CallNotificationService';
import PushNotification from 'react-native-push-notification';
import Toast from 'react-native-toast-message';
import { StreamVideoClient } from '@stream-io/video-react-native-sdk';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CommonActions, useNavigation, NavigationContainer } from '@react-navigation/native';
import { navigationRef } from './redux/shared';
import { STREAM_API_KEY } from './src/config/streamConfig';

// Define types for Stream.io token data
interface StreamTokenData {
  token: string;
  callId: string;
}

// Define types for call data
interface CallData {
  caller?: {
    name?: string;
  };
}

// Define types for call status data
interface CallStatusData {
  callId: string;
  status: string;
}

// Extend global type to include streamClient
declare global {
  var streamClient: StreamVideoClient | null;
}

const requestNotificationPermission = async () => {
  try {
    // Check if Firebase is initialized before using messaging
    try {
      if (!firebase.apps.length) {
        console.log('⚠️ [App] Firebase not initialized, skipping notification permission request');
        return;
      }
    } catch (error) {
      console.log('⚠️ [App] Firebase check failed, skipping notification permission request');
      return;
    }

    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      console.log('Authorization status:', authStatus);
      const token = await messaging().getToken();
      console.log('FCM Token:', token);
    } else {
      console.log('User has not granted permission');
      if (Platform.OS === 'ios') {
        Alert.alert(
          'Permission Required',
          'Please enable notifications in your device settings to receive important updates.',
          [
            {
              text: 'Open Settings',
              onPress: () => Linking.openSettings(),
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
      }
    }
  } catch (error) {
    console.error('Permission request error:', error);
  }
};

const NavigationContent = () => {
  const navigation = useNavigation();
  const { showGlobalCallScreen, currentCall, callStatus } = useCall();

  useEffect(() => {
    const initializeConnections = async () => {
      try {
        // Check Firebase initialization
        try {
          const apps = firebase.apps;
          if (apps.length > 0) {
            console.log('✅ [App] Firebase already initialized');
          } else {
            console.log('🔥 [App] Firebase not yet initialized, will be handled by native code');
          }
        } catch (firebaseError) {
          console.error('❌ [App] Firebase check failed:', firebaseError);
          // Continue without Firebase to prevent app crash
        }

        // Check if user is logged in by verifying token
        const token = await AsyncStorage.getItem('token');
        if (!token) {
          console.log('⚠️ [App] User not logged in, skipping connection initialization');
          return;
        }

        console.log('✅ [App] User is logged in, initializing connections');

        // Initialize location service for already logged-in users
        try {
          console.log('📍 [App] Initializing location service for logged-in user...');
          const locationInitialized = await locationService.initialize();
          if (locationInitialized) {
            console.log('✅ [App] Location service initialized successfully');
            // Start location tracking for already logged-in users
            await locationService.startTracking();
          } else {
            console.warn('⚠️ [App] Location service initialization failed');
          }
        } catch (error) {
          console.error('❌ [App] Error initializing location service:', error);
        }

        // Initialize unified socket connection
        socketService.connect();
        requestNotificationPermission();

        const initializeStreamClient = async (token: string) => {
          try {
            // Get the stored driverId from AsyncStorage
            const storedDriverId = await AsyncStorage.getItem('driverId');
            
            if (!storedDriverId) {
              console.error('No driverId found in AsyncStorage');
              return;
            }

            console.log('Initializing Stream client with driverId:', storedDriverId);
            
            // Initialize Stream client with the stored driverId
            global.streamClient = new StreamVideoClient({
              apiKey: STREAM_API_KEY, // Stream.io API key from config
              token,
              user: {
                id: storedDriverId,
              },
              // useHint: false,
              
              // options: {
              //   // Disable location hint to prevent the warning
              //   locationHint: false,
              //   // Add retry options
              //   retryOptions: {
              //     maxRetries: 3,
              //     retryInterval: 1000,
              //   }
              // }
            });

            console.log('Stream client initialized successfully');
          } catch (error) {
            console.error('Error initializing Stream client:', error);
          }
        };

        // Set up online status management
        const setupOnlineStatus = async () => {
          console.log('🔄 [App] Setting up online status management');

          // The unified socket service handles connection events internally
          // We just need to go online once connected
          if (socketService.isConnected) {
            console.log('🟢 [App] Socket already connected - Going online with location');
            await socketService.goOnline();
          }
        };

        // Listen for incoming calls at app level
        const setupIncomingCallListeners = () => {
          // Listen for video calls using unified socket service
          socketService.setIncomingVideoCallHandler((data: CallData) => {
            console.log('📹 [App] App-level incoming video call:', data);

            // Navigate to call screen
            navigation.dispatch(
              CommonActions.navigate({
                name: 'CallScreen',
                params: {
                  isIncoming: true,
                  callData: data,
                },
              })
            );

            // Show local notification if app is in background
            if (AppState.currentState !== 'active') {
              PushNotification.localNotification({
                title: 'Incoming Video Call',
                message: `${data.caller?.name || 'Unknown'} is video calling you`,
                playSound: true,
                soundName: 'default',
                importance: 'high',
                priority: 'high',
              });
            }
          });

          // Listen for Stream.io token events using unified socket service
          socketService.setReceiverTokenHandler(({ token, callId }: StreamTokenData) => {
            console.log('📞 [App] Receiver token received for call:', callId);
            if (!token) {
              console.error('❌ [App] Invalid receiver token received');
              Toast.show({
                type: 'error',
                text1: 'Call Error',
                text2: 'Invalid call token received. Please try again.',
              });
              return;
            }
            initializeStreamClient(token);
          });

          // Listen for call status changes
          socketService.setCallAcceptedHandler((data: CallStatusData) => {
            console.log('✅ App-level call accepted:', data);
          });

          socketService.setCallRejectedHandler((data: CallStatusData) => {
            console.log('❌ App-level call rejected:', data);
          });

          socketService.setCallEndedHandler((data: CallStatusData) => {
            console.log('🔚 App-level call ended:', data);
          });

          // Unified call status listeners
          socketService.setVideoCallAcceptedHandler((data: CallStatusData) => {
            console.log('✅ [App] App-level video call accepted:', data);
          });

          socketService.setVideoCallRejectedHandler((data: CallStatusData) => {
            console.log('❌ [App] App-level video call rejected:', data);
          });

          socketService.setVideoCallEndedHandler((data: CallStatusData) => {
            console.log('🔚 [App] App-level video call ended:', data);
          });
        };

        // Handle app state changes
        const subscription = AppState.addEventListener('change', async (nextAppState) => {
          if (nextAppState === 'active') {
            console.log('📱 [App] App became active');

            // Reconnect sockets when app comes to foreground
            if (!socketService.isConnected) {
              socketService.connect();
              await socketService.goOnline();
            }

            // Ensure location tracking is active for logged-in users
            const token = await AsyncStorage.getItem('token');
            if (token && !locationService.isTracking) {
              console.log('🔄 [App] Restarting location tracking on app resume');
              await locationService.startTracking();
            }
          }
        });

        // Set up listeners after connections are established
        await setupOnlineStatus();
        setupIncomingCallListeners();

        return () => {
          // Clean up listeners
          subscription.remove();
          socketService.setIncomingCallHandler(null);
          socketService.setIncomingVideoCallHandler(null);
          socketService.setCallAcceptedHandler(null);
          socketService.setVideoCallAcceptedHandler(null);
          socketService.setCallRejectedHandler(null);
          socketService.setVideoCallRejectedHandler(null);
          socketService.setCallEndedHandler(null);
          socketService.setVideoCallEndedHandler(null);
          socketService.setReceiverTokenHandler(null);
          
          // Clean up Stream client
          if (global.streamClient) {
            console.log('🔄 [App] Cleaning up Stream client');
            global.streamClient.disconnectUser();
            global.streamClient = null;
          }

          // Clean up location service
          locationService.cleanup();
        };
      } catch (error) {
        console.error('❌ [App] Error initializing connections:', error);
      }
    };

    initializeConnections();
  }, [navigation]);

  // Add debug logging for call state
  useEffect(() => {
    console.log('🔍 [App] Call State Debug:');
    console.log('- showGlobalCallScreen:', showGlobalCallScreen);
    console.log('- currentCall:', JSON.stringify(currentCall, null, 2));
    console.log('- callStatus:', callStatus);
  }, [showGlobalCallScreen, currentCall, callStatus]);

  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    try {
      // Only set up messaging listeners if Firebase is initialized
      const apps = firebase.apps;
      if (apps && apps.length > 0) {
        unsubscribe = messaging().onMessage(async remoteMessage => {
          console.log('A new FCM message arrived!', remoteMessage);
          // Display the notification
          PushNotification.localNotification({
            title: remoteMessage?.notification?.title || 'New Notification',
            message: remoteMessage?.notification?.body || 'You have a new message',
          });
        });
      } else {
        console.log('⚠️ [App] Firebase not initialized, skipping FCM message listener setup');
      }
    } catch (error) {
      console.error('❌ [App] Error setting up FCM message listener:', error);
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  // Configure Firebase background handler
  useEffect(() => {
    try {
      const apps = firebase.apps;
      if (apps && apps.length > 0) {
        messaging().setBackgroundMessageHandler(async (remoteMessage) => {
          console.log('Message handled in the background!', remoteMessage);
          PushNotification.localNotification({
            title: remoteMessage?.notification?.title || 'New Notification',
            message: remoteMessage?.notification?.body || 'You have a new message',
          });
        });
      } else {
        console.log('⚠️ [App] Firebase not initialized, skipping background message handler setup');
      }
    } catch (error) {
      console.error('❌ [App] Error setting up background message handler:', error);
    }
  }, []);

  // Configure PushNotification with call handling
  PushNotification.configure({
    onNotification: function (notification: any) {
      console.log('NOTIFICATION:', notification);

      // Handle notification taps using the call notification service
      const result = callNotificationService.handleNotificationTap(notification);
      if (result) {
        console.log('Notification tap handled:', result.action);
        // The CallContext and navigation will handle the UI
      }
    },

    onAction: function (notification: any) {
      console.log('ACTION:', notification.action);
      console.log('NOTIFICATION:', notification);

      // Handle notification actions using the call notification service
      const result = callNotificationService.handleNotificationAction(notification);
      if (result) {
        console.log('Call action handled:', result.action);
        // The CallContext will handle the actual call logic
      }
    },

    // IOS ONLY (optional): default: all - Permissions to register.
    permissions: {
      alert: true,
      badge: true,
      sound: true,
    },

    // Should the initial notification be popped automatically
    // default: true
    popInitialNotification: true,
    requestPermissions: true,
  });

  return (
    <>
      <StatusBar
        backgroundColor={Platform.OS === 'android' ? COLORS.primary : 'transparent'}
        barStyle={Platform.OS === 'ios' ? 'dark-content' : 'light-content'}
        translucent={Platform.OS === 'android'}
      />
      <AppNavigation />
      <GlobalCallManager />
      <Toast />
    </>
  );
};

const App = () => {
  return (
    <View style={{flex: 1}}>

    <NavigationContainer ref={navigationRef}>
      <Provider store={store}>
        <CallProvider>
          <NavigationContent />
        </CallProvider>
      </Provider>
    </NavigationContainer>
    </View>
  );
};

export default App;

