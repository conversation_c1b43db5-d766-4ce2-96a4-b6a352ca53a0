{"name": "RideFuseDriver", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@mapbox/polyline": "^1.2.1", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-community/slider": "^4.5.5", "@react-native-cookies/cookies": "^6.2.1", "@react-native-firebase/app": "^21.12.0", "@react-native-firebase/messaging": "^21.12.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "@react-navigation/stack": "^7.1.1", "@reduxjs/toolkit": "^1.9.7", "@stream-io/react-native-webrtc": "^125.0.7", "@stream-io/video-react-native-sdk": "^1.10.12", "expo": "^53.0.19", "expo-dev-client": "^5.2.4", "install": "^0.13.0", "npm": "^11.0.0", "react": "18.3.1", "react-native": "0.76.5", "react-native-android-location-enabler": "^2.0.1", "react-native-audio-recorder-player": "^3.6.12", "react-native-confirmation-code-field": "^7.4.0", "react-native-country-picker-modal": "^2.0.0", "react-native-encrypted-storage": "^4.0.3", "react-native-flash-message": "^0.4.2", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.21.2", "react-native-google-places-autocomplete": "^2.5.7", "react-native-image-picker": "^7.2.3", "react-native-image-resizer": "^1.4.5", "react-native-incall-manager": "^4.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-loading-spinner-overlay": "^3.0.1", "react-native-logs": "^5.3.0", "react-native-maps": "^1.20.1", "react-native-permissions": "^5.2.3", "react-native-push-notification": "^8.1.1", "react-native-raw-bottom-sheet": "^3.0.0", "react-native-safe-area-context": "^5.1.0", "react-native-screens": "^4.4.0", "react-native-svg": "^15.11.2", "react-native-toast-message": "^2.2.1", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.4", "react-redux": "^8.1.3", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.76.5", "@react-native/eslint-config": "0.76.5", "@react-native/metro-config": "0.76.5", "@react-native/typescript-config": "0.76.5", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}