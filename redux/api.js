import { api, api2, apiSocial, apiUrl, baseLink, handleSignInChange } from './shared';
import { signIn, updUser, signOut, overwriteStore } from './ActionCreator';
import { store } from './store';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AUTH_ENDPOINTS, RIDE_ENDPOINTS, APP_SETTINGS_ENDPOINTS } from '../src/config/apiConfig';




//Driver Authentication

export const signOutApi = async () => {
  try {
    const response = await api('POST', 'auth/signout');
    await AsyncStorage.removeItem('token');
    store.dispatch(signIn({ success: false }));

    return true;

  }

  catch (error) {
    console.log(error, "error in api.js");
  };
}





// export const SignIn = async data => {
//   try {
//     const response = await api('POST', 'user/signin', data);
//     // console.log(response, 'in the login api');
//     store.dispatch(
//       signIn({
//         success: true,
//         user: response,
//       }),
//     );

//     await AsyncStorage.setItem('token', JSON.stringify(response.token));

//     GetWalletBalance()

//     return response;
//   } catch (error) {
//     // console.log(error, "error in api.js");
//     throw error;
//   }
// };


export const RegisterWithPassengerAccount = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/registerWithPassengerAccount', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const SignIn = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/signin', data)
    return response;
  } catch (error) {
    throw error;
  }
};
export const CompleteDriverRegistration = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/completeDriverRegistration', data)
    return response;
  } catch (error) {
    throw error;
  }
};



export const RegisterNewDriver = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/registerNewDriver', data)
    return response;
  } catch (error) {
    throw error;
  }
};
export const VerifyPassengerToDriverAccountOtp = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/verifyPassengerToDriverAccountOtp', data)
    return response;
  } catch (error) {
    throw error;
  }
};



export const ResendOtp = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/resendOtp', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const CompleteNewDriverRegistration = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/completeDriverRegistration', data,{ file: true })
    log(response, "response in completeNewDriverRegistration")
    return response;
  } catch (error) {
    throw error;
  }
};

export const VerifyPersonalDetails = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/verifyPersonalDetails', data)

    return response;
  } catch (error) {
    throw error;
  }
};

export const VerifySSN = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/verifySSN', data)
    return response;
  } catch (error) {
    throw error;
  }
};


export const VerifyOtp = async (data) => {
  try {
    const response = await api('POST', 'auth/verifyOtp', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const VerifyLoginOtp = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/verifyLoginOtp', data)
    store.dispatch(
      signIn({
        success: true,
        user: response?.data,
      }),
    );

    // Save the token to AsyncStorage for auto-login
    if (response?.message) {
      await AsyncStorage.setItem('token', JSON.stringify(response.message));
      console.log('Token saved successfully for auto-login');
    }

    // Save the driverId to AsyncStorage for Stream client
    if (response?.data?.driverId) {
      await AsyncStorage.setItem('driverId', response.data.driverId);
      console.log('DriverId saved successfully for Stream client');
    }

    console.log(response?.message, "response in verifyLoginOtp");

    return response;
  } catch (error) {
    console.error('Error in VerifyLoginOtp:', error);
    throw error;
  }
};

export const VerifyToken = async (data) => {
  try {
    const response = await api('POST', 'driver/auth/verifyToken', data)
    return response;
  } catch (error) {
    throw error;
  }
};




export const NewCar = async (data) => {
  try {
    const response = await api('POST', 'driver/car/newCar', data)
    console.log(response, "response in newCar");

    return response;
  } catch (error) {
    throw error;
  }
};


export const UpdateCarDetails = async (data) => {
  try {
    const response = await api('POST', 'driver/car/updateCarDetails', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const ActivateCar = async (data) => {
  try {
    const response = await api('POST', 'driver/car/activateCar', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const GetCarDetail = async () => {
  try {
    const response = await api('GET', 'car/getCarDetail');
    store.dispatch(overwriteStore({ name: "GetCarDetail", value: response.data }));
    return response;
  }
  catch (error) {

  }
};

export const GetPayouts = async () => {
  try {
    const response = await api('GET', 'payout/getPayouts');
    store.dispatch(overwriteStore({ name: "GetPayouts", value: response.data }));
    return response;
  }
  catch (error) {

  }
};

export const GetCarDetails = async () => {
  try {
    const response = await api('GET', 'driver/car/getCarDetails');
   console.log(response, "response in getCarDetails");

    store.dispatch(overwriteStore({ name: "GetCarDetails", value: response.data }));
    return response;
  }
  catch (error) {

  }
};

export const DeleteCarDetails = async (data) => {
  try {
    const response = await api('POST', 'driver/car/deleteCarDetails', data)
    return response;
  } catch (error) {
    throw error;
  }
};


export const HomeBreak = async (data) => {
  try {
    const response = await api('POST', 'driver/homeBreak', data)
    return response;
  } catch (error) {
    throw error;
  }
};


export const NewBankDetails = async (data) => {
  try {
    const response = await api('POST', 'driver/bank/newBankDetails', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const UpdateBankDetails = async (data) => {
  try {
    const response = await api('POST', 'driver/bank/updateBankDetails', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const DeleteBankDetails = async (data) => {
  try {
    const response = await api('POST', 'driver/bank/deleteBankDetails', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const GetBankDetail = async () => {
  try {
    const response = await api('GET', 'driver/bank/getBankDetail');
    store.dispatch(overwriteStore({ name: "GetBankDetail", value: response.data }));
    return response;
  }
  catch (error) {

  }
}

export const GetBankDetails = async () => {
  try {
    const response = await api('GET', 'driver/bank/getBankDetails')
    store.dispatch(overwriteStore({ name: "GetBankDetails", value: response.data }));
    return response;
  } catch (error) {
    throw error;
  }
};

export const GetDriverRides = async () => {
  try {
    const response = await api('GET', 'rides/getDriverRides')
    store.dispatch(overwriteStore({ name: "GetDriverRides", value: response.data }));
    return response;
  } catch (error) {
    throw error;
  }
};

export const GetLastSevenDays = async () => {
  try {
    const response = await api('GET', 'rides/getLastSevenDays')
    store.dispatch(overwriteStore({ name: "GetDriverRides", value: response.data }));
    return response;
  } catch (error) {
    throw error;
  }
};

export const NewPayOut = async (data) => {
  try {
    const response = await api('POST', 'driver/payout/newPayout', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const GetPayout = async (data) => {
  try {
    const response = await api('POST', 'driver/payout/getPayouts', data)
    return response;
  } catch (error) {
    throw error;
  }
};

export const UpdateProfile = async (data) => {
  try {
    const response = await api('POST', 'driver/profile/updateProfile', data)
    return response;
  } catch (error) {
    throw error;
  }
};










