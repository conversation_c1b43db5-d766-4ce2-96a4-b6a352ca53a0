import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface StoreState {
  GetProfile: {
    id: string;
    firstName: string;
    lastName: string;
    profileImg: string;
  } | null;
}

const initialState: StoreState = {
  GetProfile: null,
};

const storeSlice = createSlice({
  name: 'store',
  initialState,
  reducers: {
    setProfile: (state, action: PayloadAction<StoreState['GetProfile']>) => {
      state.GetProfile = action.payload;
    },
  },
});

export const { setProfile } = storeSlice.actions;
export default storeSlice.reducer; 