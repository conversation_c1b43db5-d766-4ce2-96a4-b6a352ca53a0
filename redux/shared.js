// import AsyncStorage from '@react-native-async-storage/async-storage';
import React from 'react';
// import EncryptedStorage from 'react-native-encrypted-storage';
import EncryptedStorage from 'react-native-encrypted-storage';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { View, Alert, Image, Text, } from 'react-native';
import { store } from './store';
import { overwriteStore, storeError } from './ActionCreator';
import { logger } from 'react-native-logs';
import Toast from 'react-native-toast-message'
import ImageResizer from 'react-native-image-resizer';



var log = logger.createLogger();

import { API_BASE_URL, DRIVER_API_URL } from '../src/config/apiConfig';

// Use the centralized API URLs
export const baseLink = `${API_BASE_URL}`;
export const baseLink2 = DRIVER_API_URL;



// https://durabletel-api.successvtu.com/
// https://durabletel-purchase.successvtu.com/

 import Spinner from 'react-native-loading-spinner-overlay';
import { createNavigationContainerRef } from '@react-navigation/native';

import { FONTS } from '../src/constants';

export const navigationRef = createNavigationContainerRef();


export function navigate(name, params) {
  if (navigationRef.isReady()) {
    navigationRef.navigate(name, params);
  }
}

export function Capitalize(string) {
  if (!string) return null
  else
    return string.charAt(0).toUpperCase() + string.slice(1);
}


export const getScreenFromLinking = url => {
  try {
    const prefixes = linking.prefixes;
    const screens = linking.configs.screens;
    for (const prefix of prefixes) {
      if (url.startsWith(prefix)) {
        const path = url.replace(prefix, '');
        // console.log(path, 'path', screens);
        //check if path is in screens values
        for (const key in screens) {
          const screenspath = screens[key].path;
          if (Array.isArray(screenspath)) {
            for (const screenpath of screenspath) {
              let regx = new RegExp(screenpath);
              if (path.match(regx)) {
                return key;
              }
            }
          } else {
            if (path.match(new RegExp(screenspath))) {
              return key;
            }
          }
        }

        // for (const screen in screens) {
        //   // const config = configs[screen];
        //   // if (config.path === path) {
        //   //   return screen;
        //   // }
        // }
      }
    }
    return null;
  } catch (error) {
    return null;
  }
};
const defaultState = {
  msg: false,
  load: false,
  log: false,
  err: true,
  file: false,
  fetch: false,
};

export const displayMessage = options => {
  console.log(options, 'optionsss');
  Toast.show({
    ...options,
    // text2: options.text2.charAt(0).toUpperCase() + options.text2.slice(1),
    text2: Capitalize(options.text2),
    text1: Capitalize(options.type),
  });
};

export const formValidation = ({ onSubmit, data, rules }) => {
  //get all children of the form
  const beforeSubmit = () => {
    //iterate through children with validation rules
    for (const [name, validations] of Object.entries(rules)) {
      //get the value of the child
      const value = data[name];
      // console.log('value', value, 'name', rules, 'validations', validations);
      //get the validation rules of the child
      for (let j = 0; j < validations.length; j++) {
        const rule = validations[j];

        if (rule.required === true && !value) {
          //check if the value is empty
          if (!value) {
            //show error message
            displayMessage({
              text1: "error",
              text2: rule.message || `${name} is required`,
              type: 'error',
            });
            //return false
            return false;
          }
          //if value is array
          if (Array.isArray(value)) {
            //check if the array is empty
            if (value.length === 0) {
              //show error message
              displayMessage({
                text1: "error",
                text2: rule.message || `${name} is required`,
                type: 'error',
              });
              //return false
              return false;
            }
          }
        }
        if (rule.pattern && !rule.pattern?.test(value)) {
          displayMessage({
            text1: "error",
            text2: rule.message || `${name} is invalid`,
            type: 'error',
          });
          return false;
        }
        if (rule.custom && !rule.custom(value)) {
          displayMessage({
            text1: "error",
            text2: rule.message || `${name} is invalid`,
            type: 'error',
          });
          return false;
        }
      }
    }
    return true;
  };
  if (beforeSubmit()) {
    onSubmit();
  }
};

export const setAppLoad = opts => {
  store.dispatch(storeError(opts));
};

export const formattedAmountWithNaira = (amount, dp = 2) => {
  return amount
    ? "\u20A6" +
    parseFloat(amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,')
    : "₦0.00";
}

const setFetch = (val = false) => {
  store.dispatch(
    overwriteStore({
      name: 'fetch',
      value: val,
    }),
  );
};





export const api = async (method, url, body, state = defaultState, muteNotif) => {
  console.log('Request Body:', body);

  try {
    state = {
      ...defaultState,
      ...state,
    };

    // Handle loading state
    if (method?.toLowerCase() !== 'get') {
      state.msg = true;
      state.load = true;
    } else if (!state.load) {
      state.fetch = true;
    }

    if (state.fetch) {
      setFetch(true);
    }

    let token = await AsyncStorage.getItem('token');
    let ntoken = token ? JSON.parse(token) : null;

    console.log('Token:', ntoken);

    let requestOptions;

    if (state?.file) {
      // Prepare FormData for file uploads
      const formData = new FormData();

      for (const key in body) {
        if (Object.hasOwnProperty.call(body, key)) {
          if (key === 'carDetails') {
            // Handle nested carDetails object
            for (const carKey in body.carDetails) {
              if (Object.hasOwnProperty.call(body.carDetails, carKey)) {
                if (carKey === 'carImg') {
                  // Append car image
                  const file = body.carDetails[carKey];
                  formData.append(`carDetails[${carKey}]`, {
                    uri: file.uri,
                    type: file.type || 'image/jpeg',
                    name: file.name || `carImg_${Date.now()}.jpg`,
                  });
                } else {
                  // Append other carDetails fields
                  formData.append(`carDetails[${carKey}]`, body.carDetails[carKey]);
                }
              }
            }
          } else if (
            key === 'profileImg' ||
            key === 'driverLincenseImgFront' ||
            key === 'driverLincenseImgBack'
          ) {
            // Append top-level image file fields
            const file = body[key];
            formData.append('pricePerKm', 1000);
            formData.append(key, {
              uri: file.uri,
              type: file.type || 'image/jpeg',
              name: file.name || `${key}_${Date.now()}.jpg`,
            });
          } else if (key === 'coordinates') {
            // Handle nested coordinates object
            for (const coordKey in body.coordinates) {
              if (Object.hasOwnProperty.call(body.coordinates, coordKey)) {
                formData.append(`coordinates[${coordKey}]`, body.coordinates[coordKey]);
              }
            }
          } else {
            // Append other top-level fields
            formData.append(key, body[key]);
          }
        }
      }
    console.log(formData);

      requestOptions = {
        method,
        credentials: 'include',
        headers: {
          'Content-Type': 'multipart/form-data',
          // 'frontend-source': 'app',
          // Authorization: ntoken || '', // Token is optional
        },
        body: formData,
      };
    }
     else {
      // Prepare JSON body for non-file requests
      requestOptions = {
        method,
        credentials: 'include',
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          // 'frontend-source': 'app',
          // Authorization: ntoken || '',
        },
        body: JSON.stringify(body),
      };
    }

    // Ensure proper URL concatenation with a slash if needed
    const fullUrl = url.startsWith('/') ? `${baseLink}${url}` : `${baseLink}/${url}`;
    console.log('API Request:', fullUrl, requestOptions);

    // Perform the API call
    const res = await fetch(fullUrl, requestOptions);

    // Handle response content type
    const contentType = res.headers.get('content-type');
    const isJson = contentType && contentType.includes('application/json');
console.log(res, 'res');

    // Parse response
    const response = isJson ? await res.json() : await res.text();

    if (res.status >= 200 && res.status < 300) {
      if (state.msg) {
        displayMessage({
          text1: 'Success',
          text2: response?.msg || 'Operation successful',
          type: 'success',
        });
      }
      return response;
    } else {
      console.error('API Error Response:', response);
      throw response; // Handle API errors
    }
  } catch (error) {
    console.error('API Error:', error);

    // Handle network error
    if (error?.message === 'Network request failed') {
      displayMessage({
        type: 'error',
        text1: 'Network Error',
        text2: 'Please check your internet connection.',
      });
      return;
    }

    // Handle API response errors
    const userError =
      error?.error?.[0]?.msg || error?.message || error?.data || 'An error occurred';
    Toast.show({
      type: 'error',
      text1: 'Error',
      text2: userError,
    });
    return;
  }
};




export const api2 = async (method, url, body, state = defaultState, muteNotif) => {
  console.log(body, 'bodyt');
  try {
    state = {
      ...defaultState,
      ...state,
    };
    if (method?.toLowerCase() !== 'get') {
      state.msg = true;
      state.load = true;
    } else {
      if (!state.load) {
        state.fetch = true;
      }
    }

    if (state.fetch) {
      setFetch(true);

    }

    // if (state.load) {
    //   setAppLoad({
    //     error: false,
    //     loading: state.load,
    //   });
    // }

    // let token = await AsyncStorage.getItem('token');

    let token = await AsyncStorage.getItem('token');

    console.log('ues here', token);
    // if(token){
    let ntoken = JSON.parse(token || 0)

    // }
    // return
    //remove quote from token
    token = token?.replace(/"/g, '');
    console.log(token, 'token');
    // console.log('body', body, method);

    if (method?.toLowerCase() === 'get' && typeof body === 'object') {
      //check if ? is in the url
      // if (url?.indexOf("a") === -1) {
      //   url += '?';
      // }
      let arrQueries = '';
      //check if the body object has a value that is an array
      for (const [key, value] of Object.entries(body)) {
        if (Array.isArray(value)) {
          //if the value is an array
          for (let i = 0; i < value.length; i++) {
            arrQueries += `${key}[]=${value[i]}&`;
            //remove the key from the body object
            delete body[key];
          }
        } else {
        }
      }
      let queries = new URLSearchParams(body).toString();
      if (arrQueries) {
        queries += arrQueries;
      }
      //if there is a query string and url does not end with ?
      if (queries) {
        console.log('queries', queries);
        if (url[url.length - 1] !== '?') {
          url += '&';
          url += queries;
        } else {
          url += queries;
        }
        // console.log(url, 'url');
      }

      console.log(url, 'thenurl', 'body: ', body);
      //loop through the body object

      body = undefined;
    } else if (method?.toLowerCase() === 'get' && body) {
      console.info('Hey stop sending request with body on GET');
      body = undefined;
    }

    // console.log(body, 'body');
    let requestOptions;
    if (state?.file === true) {
      //send form data
      let formData = new FormData();
      for (const key in body) {
        if (Object.hasOwnProperty.call(body, key)) {
          const element = body[key];
          // console.log(element, 'element', key);
          const constructFileInterface = tf => {
            const file = {
              uri: tf.path,
              type: tf.mime,
              name: 'dataappictures',
            };
            if (!file.uri) {
              return tf;
            }
            return file;
          };
          console.log(element, 'eol');
          //if element is array
          if (Array.isArray(element)) {
            element.forEach((item, index) => {
              // console.log(item, 'item');
              formData.append(`${key}`, constructFileInterface(item));
            });
          } else if (typeof element === 'object') {
            console.log(key, 'keyy');
            formData.append(`file`, constructFileInterface(element));
          } else {
            formData.append(key, element);
          }
        }
      }
      //if formData doesn't have entries reject the request
      // if (!formData.entries()?.next()?.value) {
      //   throw new Error('Form data is empty');
      // }
      console.log(formData, '...theformData');
      console.log(JSON.stringify(formData), 'picbf');

      let theBody = JSON.stringify([formData])
      console.log('the body.........', theBody)
      requestOptions = {
        method: method,
        headers: {
          'Content-Type': 'multipart/form-data',
          "frontend-source":"app",
          Authorization: ntoken,
        },
        body: formData,
      };


    } else {
      requestOptions = {
        method: method,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          "frontend-source":"app",
          redirect: 'follow',
          // Authorization: 'Bearer ' + token,
          Authorization: ntoken


          // 'X-Auth-Token': user.session,
        },
        body: JSON.stringify(body),
      };
    }

    // Ensure proper URL concatenation with a slash if needed
    const fullUrl = url.startsWith('/') ? `${baseLink2}${url}` : `${baseLink2}/${url}`;
    console.log("API Request:", fullUrl, requestOptions);
    let res = await fetch(fullUrl, requestOptions);

    // Handle response content type
    const contentType = res.headers.get('content-type');
    const isJson = contentType && contentType.includes('application/json');
    console.log('Response Content-Type:', contentType);

    // Parse response based on content type
    const response = isJson ? await res.json() : await res.text();
    // console.log(response, 'response');

    if (state.load) {
      store.dispatch(
        storeError({
          error: false,
          loading: false,

        }),
      );
    }

    if (state.fetch) {
      setFetch(false);

    }

    // console.log(response, "the res");
    //check status code
    if (res.status === 200 || res.status === 201 || res.status === 204) {
      console.log("this is state", state);

      if (state.log) {
        try {
          log.info(
            `API ${url} response start`,
            response,
            `API ${url} response close`,
          );
          log.info(` API ${url} response?.data?.docs`, response?.data?.docs[0]);
        } catch (err) {
          console.info(err, 'error');
        }
      }
      if (state.msg) {
        displayMessage({
          text1: "success",
          text2: response?.msg || "successful",
          type: 'success',
        });
      }

      return response;
    } else {
      console.error('API Error Response:', response);
      console.error('Response Status:', res.status);

      // If response is HTML (not JSON), create a proper error object
      if (!isJson && typeof response === 'string') {
        const errorMessage = response.includes('<html>') || response.includes('<!DOCTYPE')
          ? 'Server returned an error page. Please check the API endpoint.'
          : response;

        throw {
          error: [{ msg: errorMessage }],
          message: errorMessage,
          status: res.status
        };
      }

      throw response;
    }

  } catch (error) {
    // console.log(`API ${url} error starts`, {err:error?.message, error}, `API ${url} error ends.......`);

    console.log('Network request failed............gqqqqqqqqqqqqqqqqgg.......................', {err:error?.message, error})
    if (error?.message == "Network request failed") {
      console.info('It is erroroooooooooooooooooooooooooo')
      displayMessage({
        type: 'error',
        text1: 'Network Error',
        text2: 'Network request failed. Please check your internet connection.',
      });
      return;
    }


    // Handle different error structures
    let usererror = error?.error?.[0]?.msg || error?.message || error?.data || 'An error occurred';

    console.log('user eroorrr.......................', usererror)





      displayMessage({
        type: 'error',
        text1: "error",
        text2:  usererror ,

      });
      return;

  }
};








export const Loader = propsl => {
  return (
    <Spinner
      {...propsl}
      textContent={'Data App'}
      textStyle={{
        color: 'white',
        ...FONTS.h3
      }}
      color={'#c79f1c'}
      overlayColor={'rgba(0,0,0,0.5)'}
    />
  );
};

export const formatDate = date => {
  const d = new Date(date);
  const month = '' + (d.getMonth() + 1);
  const day = '' + d.getDate();
  const year = d.getFullYear();
  return [year, month, day].join('-');
};

export const formatDateTime = date => {
  const d = new Date(date);
  const hour = '' + d.getHours();
  const minute = '' + d.getMinutes();
  //get date format as 09:30 pm
  //convert to am or pm
  const ampm = hour >= 12 ? 'pm' : 'am';
  //convert to 12 hour format
  const hour12 = hour % 12 === 0 ? 12 : hour % 12;
  return [hour12, minute].join(':') + ampm;
};

//This will convert 2022-09-27T18:42:48.347Z to 4 days ago or 2 hours ago or 2 minutes ago or 2 seconds ago or 4 months ago
export const timeAgo = date => {
  const d = new Date(date);
  const seconds = Math.floor((new Date() - d) / 1000);
  let interval = seconds / 31536000;
  //if it's NaN then it's not a date
  if (isNaN(interval)) {
    return null;
  }
  if (interval > 1) {
    return Math.floor(interval) + ' year ago';
  }
  interval = seconds / 2592000;
  if (interval > 1) {
    return Math.floor(interval) + ' month ago';
  }
  interval = seconds / 86400;
  if (interval > 1) {
    return Math.floor(interval) + ' day ago';
  }
  interval = seconds / 3600;
  if (interval > 1) {
    return Math.floor(interval) + ' hour ago';
  }
  interval = seconds / 60;
  if (interval > 1) {
    return Math.floor(interval) + ' minutes ago';
  }
  return Math.floor(seconds) + ' seconds ago';
};

//This converts 50000 to 50,000
export const formatNumber = number => {
  return number?.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
};
export const Affect = ({ load, cref, effect }) => {
  // console.log(cref)

  if (load) {
    return (
      <View
        style={{
          position: 'absolute',
          flex: 1,
          alignSelf: 'center',
          justifyContent: 'center',
          alignContent: 'center',
          elevation: 3,
        }}>
        <Loader />
      </View>
    );
  }
  if (cref) {
    if (effect) {
      effect.message = (effect.message ? effect.message : 'kksksk');
      if (effect.error === '') {
        effect.error = null;
      }

      if (!effect.error) {
        cref.setNativeProps({
          text: effect.message,
          style: {
            color: 'green',
            fontFamily: 'montaga1',
            alignSelf: 'center',
          },
        });
      } else {
        cref.setNativeProps({
          text: effect.message,
          style: { color: 'red' },
        });
      }
    } else {
      cref.setNativeProps({ text: null });
    }
  } else {
    // console.log("No ref")
  }
  return null;
};

export const formatItemtoValue = (key, val, items) => {
  if (items) {
    return items.map(v => {
      return {
        label: v[key],
        value: v[val],
      };
    });
  }

  return [];
};

export const checkValidUrl = (url) => {
  //define some image formats
  var types = ['jpg', 'jpeg', 'tiff', 'png', 'gif', 'bmp'];

  //split the url into parts that has dots before them
  var parts = url.split('.');

  //get the last part
  var extension = parts[parts.length - 1];

  //check if the extension matches list
  if (types.indexOf(extension) !== -1) {
    return true;
  }
  else {
    return false
  }
}
export const myListEmpty = (acct) => {
  return (
    <View style={{ alignItems: "center" }}>
      {/* <Text style={styles.item}>No data found</Text>
   */}

      {/* <Image source={EmptyImage}

        style={{
          height: width * 0.5,
          width: width * 0.5,
          borderRadius: 100,
        }}
      /> */}
      <Text style={{ paddingVertical: 10, ...FONTS.h3, color: "#000000" }}> {acct}</Text>
      {/* <Loader visible={true} /> */}
    </View>
  );
};




// Saving the token
export const saveToken = async (token) => {
  try {
    await AsyncStorage.setItem('token', token);
    console.log('Token saved successfully.');
  } catch (error) {
    console.log('Error saving token:', error);
  }
};

// Retrieving the token
export const getToken = async () => {
  try {
    const token = await AsyncStorage.getItem('token');
    console.log('====================================');
    console.log(token, "from shared");
    console.log('====================================');

    // Return null if token is null, undefined, or empty string
    if (!token || token === 'null' || token === 'undefined') {
      return null;
    }

    return token;
  } catch (error) {
    console.log('Error retrieving token:', error);
    return null;
  }
};

export const removeToken = async () => {
  try {
    await AsyncStorage.removeItem('token');
    console.log('Token removed successfully.');
  } catch (error) {
    console.log('Error removing token:', error);
  }
};
export const removeTokenn = async () => {
  try {
    await EncryptedStorage.removeItem('token');
    console.log('Token removed successfully.');
  } catch (error) {
    console.log('Error removing token:', error);
  }
};

// export { saveToken, getToken };
