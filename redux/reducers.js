import { createReducer } from '@reduxjs/toolkit';
import {
  overwriteStore,
  signIn,
  signOut,
  storeError,
  updStore,
  updUser,
} from './ActionCreator';
const iState = { loading: false, message: '' };

export const user = createReducer(
  { ...iState, loggedIn: false, user: [] },
  {
    [signIn]: (state, action) => {
      console.log(action.payload, "in redu")
      return {
        ...state,
        loggedIn: action.payload.login,
        user: action.payload.user || {},
      };
    },


    [signOut]: (state, action) => {
      console.log(action.payload, 'in redu');
      return {
        ...state,
        loggedIn: action.payload,
      };
    },
    [updUser]: (s, a) => {
      if (a.payload) {
        // console.log(s, "s in  action")

        //Immutability
        return {
          ...s,
          loggedIn: true,
          user: a.payload,
        };
      }

      return s;
    },
  },
);

// export const students = createReducer({...iState,students: []}, {
//   [updUser]:  (s, a) => {
//     if (a.payload) {
//       // console.log(s.user, "action")

//       return ({
//         ...s,
//         students: a.payload
//       })
//     }

//     return s

//   }
// })

export const theStore = createReducer(
  { ...iState, error: false },
  {
    [updStore]: (s, a) => {
      const newS = { ...s, error: false };
      if (a.payload) {
        const existStore = newS[a.payload.name];

        // console.log(
        //   'existing store ooo',
        //   existStore,
        //   'value oo',
        //   a.payload.value,
        // );

        if (existStore) {
          if (Array.isArray(existStore)) {
            console.log('it;s array');
            if (Array.isArray(a.payload.value)) {
              return {
                ...newS,
                [a.payload.name]: [...existStore, ...a.payload.value],
              };
            } else if (typeof a.payload.value === 'object') {
              let presArr = [];

              for (const key in a.payload.value) {
                if (Object.hasOwnProperty.call(a.payload.value, key)) {
                  const element = a.payload.value[key];
                  presArr.push({ [key]: element });
                }
              }

              return {
                ...newS,
                [a.payload.name]: [...existStore, ...presArr],
              };
            }

            console.warn(
              'You are trying to merge an array with something not array and object',
            );
            return {
              ...newS,
              [a.payload.name]: [...existStore, a.payload.value],
            };
          } else if (typeof existStore === 'object') {
            if (Array.isArray(a.payload.value)) {
              console.warn(
                'You are merging an object with array, it might destort your data to make keys as an integer',
              );
            }
            return {
              ...newS,
              [a.payload.name]: { ...existStore, ...a.payload.value },
            };
          }

          console.warn(
            'There is a prob, existing store is not object and not array',
          );
          return {
            ...newS,
            [a.payload.name]: a.payload.value,
          };
        }

        return {
          ...newS,
          [a.payload.name]: a.payload.value,
        };
      }

      console.warn('oops no payload was returned');

      return newS;
    },
    [overwriteStore]: (s, a) => {
      if (a.payload) {
        console.log('🔄 [Redux] overwriteStore - Key:', a.payload.name, 'Value:', JSON.stringify(a.payload.value, null, 2));

        return {
          ...s,
          [a.payload.name]: a.payload.value,
        };
      }

      return s;
    },
    [storeError]: (s, a) => {
      if (a.payload) {
        return {
          ...s,
          ...a.payload,
        };
      }
      console.warn('oops no payload was returned in store');
      return s;
    },
  },
);
