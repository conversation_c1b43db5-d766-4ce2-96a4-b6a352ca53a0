// Alternative approach - Using only Stream.io default controls
// Replace the video call section with this if you prefer Stream.io's default UI:

{route?.params?.isVideoCall !== false ? (
  <CallContent />  // Uses Stream.io's default controls
) : (
  <View style={styles.audioCallContainer}>
    <Text style={styles.audioCallText}>Audio Call in Progress</Text>
    <Text style={styles.audioCallSubtext}>
      {isOutgoingCall ? `Calling ${recipientName}` : `Call with ${caller}`}
    </Text>
    {/* For audio calls, you might still want custom controls */}
    <View style={styles.audioControlsContainer}>
      <TouchableOpacity
        style={[styles.controlButton, styles.endCallButton]}
        onPress={handleEndCall}>
        <Ionicons name="call" size={24} color="white" style={styles.endCallIcon} />
      </TouchableOpacity>
    </View>
  </View>
)}

// Remove the custom controls overlay completely in this approach
