{"cli": {"version": ">= 16.15.0", "appVersionSource": "local"}, "build": {"development": {"developmentClient": true, "distribution": "internal"}, "preview": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "ios": {"simulator": false, "distribution": "internal"}, "env": {"EXPO_NO_METRO_LAZY": "1", "NODE_ENV": "production"}}, "production": {"autoIncrement": true}, "testflight": {"distribution": "store", "ios": {"simulator": false, "buildConfiguration": "Release"}, "env": {"NODE_ENV": "production"}}}, "submit": {"production": {}}}