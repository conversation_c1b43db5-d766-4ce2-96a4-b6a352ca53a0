{
  "mobileNumber": "+18124079333",
  "email": "<EMAIL>",
  "dob": "1858-12-2",
  "state": "Canada",
  "zipcode": "21010",
  "driverLincenseNumber": "12345678",
  "driverLincenseState": "Canadian ",
  "userConsent": true,
  "signature": "Aremu Olujide search ",
  "middleName": "<PERSON> ",
  "lastName": "<PERSON><PERSON>",
  "firstName": "<PERSON>",
  "ssn": "123412325",
  "driverLincenseImgFront": {
    "uri": "file:///data/user/0/com.rideFuzeDriver/cache/rn_image_picker_lib_temp_cdfc9fc8-9366-4c20-a076-b3d04b527627.jpg",
    "type": "image/jpeg",
    "name": "1000524768.jpg"
  },
  "driverLincenseImgBack": {
    "uri": "file:///data/user/0/com.rideFuzeDriver/cache/rn_image_picker_lib_temp_01724757-b6ef-4d3a-b78b-ab6eb3220dac.jpg",
    "type": "image/jpeg",
    "name": "1000524768.jpg"
  },
  "profileImg": {
    "uri": "file:///data/user/0/com.rideFuzeDriver/cache/rn_image_picker_lib_temp_cde09d1a-908a-437b-8ddc-bd112da89719.jpg",
    "type": "image/jpeg",
    "name": "1000524768.jpg"
  },
  "carDetails": {
    "registrationNumber": "Ghajaha2151545",
    "year": "218",
    "model": "Benz",
    "color": "Yellow",
    "noOfSeats": "2",
    "carImg": {
      "name": "1000525374.jpg",
      "type": "image/jpeg",
      "uri": "file:///data/user/0/com.rideFuzeDriver/cache/rn_image_picker_lib_temp_dc8b40ae-e3b3-4b49-a6c5-7d82d5eafc79.jpg"
    }
  },
  "opreatingCity": "Ogbomoso Palace, Ogbomoso, Nigeria",
  "coordinates": {
    "lat": 8.1290979,
    "lng": 4.2475563
  }
}

{"data": "Driver linsence number is required", "message": "", "success": false}