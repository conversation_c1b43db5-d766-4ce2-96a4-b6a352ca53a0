import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  Alert,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import {COLORS, images} from '../../../constants';
import Grad<PERSON>Background from '../../../components/shared/GradientBackground';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import {AUTH_ENDPOINTS} from '../../../config/apiConfig';
import {useNavigation} from '@react-navigation/native';

const {height: screenHeight} = Dimensions.get('window');

// Define a simple navigation type to avoid TypeScript errors
type NavigationProp = {
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
};

const WelcomeBack = () => {
  const navigation = useNavigation<NavigationProp>();
  const [mobileNumber, setMobileNumber] = useState('8124070000');
  const [loader, setLoader] = useState(false);
  // Fixed US calling code (for USA)
  const callingCode = '1';

  const handleContinue = async () => {
    if (!mobileNumber || mobileNumber.length < 10) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Mobile Number',
        text2: 'Please enter a valid 10-digit mobile number.',
      });
      return;
    }

    try {
      setLoader(true);

      const fullMobileNumber = `+${callingCode}${mobileNumber}`;
      const response = await fetch(AUTH_ENDPOINTS.SIGN_IN, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({mobileNumber: fullMobileNumber}),
      });
      console.log(JSON.stringify({mobileNumber: fullMobileNumber}), 'body');

      const data = await response.json();
      console.log('Sign in Response:', data);

      if (data?.data == 'Unverified account') {
        Alert.alert(
          'Unverified account',
          'You have not verified your account. Do you want to verify it now?',
          [
            {
              text: 'Cancel',
              onPress: () => console.log('Cancel Pressed'),
              style: 'cancel',
            },
            {
              text: 'Verify',
              onPress: () =>
                navigation.navigate('MobileNumber', {
                  mobileNumber: fullMobileNumber,
                }),
            },
          ],
        );
        return;
      }

      if (data.data == 'Mobile number does not exist') {
        Alert.alert(
          'Mobile number does not exist',
          'You have not registered with this mobile number. Do you want to register now?',
          [
            {
              text: 'Cancel',
              onPress: () => console.log('Cancel Pressed'),
              style: 'cancel',
            },
            {
              text: 'Register',
              onPress: () =>
                navigation.navigate('MobileNumber', {
                  mobileNumber: fullMobileNumber,
                }),
            },
          ],
        );
        return;
      }

      if (!response.ok || !data.success) {
        throw new Error(data?.message || 'Something went wrong');
      }

      const otp = data.message;
      navigation.navigate('SecretCode', {
        mobileNumber: fullMobileNumber,
        otp: otp,
      });
    } catch (error) {
      console.error('Error in registration:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred.',
      });
    } finally {
      setLoader(false);
    }
  };

  return (
    <GradientBackground style={styles.container}>
      <View style={{paddingTop: 60}} />
      <Spinner visible={loader} />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled">
        {/* Header */}
        <View style={styles.headerContainer}>
          <TouchableOpacity style={styles.backButton}>
            <Image source={images.logo} style={styles.icon} />
          </TouchableOpacity>
        </View>

        {/* Title */}
        <Text style={styles.title}>Welcome back to RIDEFUZE</Text>

        {/* Mobile Number Input */}
        <View style={styles.inputContainer}>
          {/* US Flag (Static) */}
          <View style={styles.countryPicker}>
            <Text style={styles.flagEmoji}>🇺🇸</Text>
          </View>
          <Text style={styles.callingCode}>+{callingCode}</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your mobile number"
            value={mobileNumber}
            placeholderTextColor={COLORS.grey}
            onChangeText={setMobileNumber}
            keyboardType="phone-pad"
            maxLength={10}
          />
        </View>
        <Text style={styles.subtitle}>
          We'll text a code to verify your mobile number
        </Text>

        {/* Continue Button */}
        <TouchableOpacity
          style={styles.continueButton}
          onPress={handleContinue}>
          <Text style={styles.continueButtonText}>Continue</Text>
        </TouchableOpacity>

        {/* Footer */}
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>
            Create an account?{' '}
            <Text
              style={styles.linkText}
              onPress={() => navigation.navigate('GetStarted')}>
              Sign up
            </Text>
          </Text>
          <Text style={styles.termsText}>
            By Proceeding, you agree to get calls, SMS messages, including
            automated calls from <Text style={styles.brandText}>RideFuze</Text>{' '}
            and its affiliated numbers. Text "STOP" to 20013 to opt-out and you
            consent to its{' '}
            <Text
              style={styles.linkText}
              onPress={() => navigation.navigate('PrivacyPolicyScreen')}>
              Privacy Policy
            </Text>{' '}
            and{' '}
            <Text
              style={styles.linkText}
              onPress={() => navigation.navigate('TAC')}>
              Terms of Service
            </Text>
            .
          </Text>
        </View>
      </ScrollView>
    </GradientBackground>
    // <SafeAreaView style={styles.safeArea}>
    // </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8FBFF',
  },
  container: {
    flex: 1,
    backgroundColor: '#F8FBFF',
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingBottom: 20,
  },
  headerContainer: {
    alignItems: 'center',
    paddingTop: 20,
    paddingBottom: 10,
  },
  backButton: {
    alignItems: 'center',
  },
  icon: {
    width: 120,
    height: 120,
    resizeMode: 'contain',
  },
  title: {
    textAlign: 'center',
    marginBottom: 20,
    color: COLORS.black,
    fontWeight: 'bold',
    fontSize: 18,
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: 20,
    color: '#555',
    fontSize: 14,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    marginBottom: 10,
  },
  countryPicker: {
    marginRight: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  flagEmoji: {
    fontSize: 20,
    marginRight: 5,
  },
  callingCode: {
    marginRight: 10,
    color: COLORS.black,
    fontSize: 16,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: COLORS.black,
    backgroundColor: '#fff',
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 30,
  },
  continueButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  footerContainer: {

    paddingTop: 20,
  },
  footerText: {
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 14,
    color: '#555',
  },
  linkText: {
    color: COLORS.primary,
    fontWeight: 'bold',
    fontSize: 14,
  },
  termsText: {
    textAlign: 'justify',
    fontSize: 12,
    color: '#555',
    lineHeight: 18,
  },
  brandText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});

export default WelcomeBack;
