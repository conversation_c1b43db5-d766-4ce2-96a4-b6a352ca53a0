import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
  Alert,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, Cursor, useBlurOnFulfill, useClearByFocusCell } from 'react-native-confirmation-code-field';
import { COLORS, SIZES, icons } from '../../../constants'; // Replace with your constants
import GradientBackground from '../../../components/shared/GradientBackground';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { ResendOtp, VerifyLoginOtp } from '../../../../redux/api';
import { overwriteStore } from '../../../../redux/ActionCreator';
import { useDispatch } from 'react-redux';
import socketService from '../../../services/socketService';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

// Define interfaces for type safety
interface RouteParams {
  mobileNumber: string;
  otp: string;
}

// Define navigation types
type RootStackParamList = {
  SNotification: undefined;
  WelcomeBack: undefined;
};

type SecretCodeNavigationProp = StackNavigationProp<RootStackParamList>;
type SecretCodeRouteProp = RouteProp<{SecretCode: RouteParams}, 'SecretCode'>;

interface SecretCodeProps {
  navigation: SecretCodeNavigationProp;
  route: SecretCodeRouteProp;
}

const CELL_COUNT = 4;

const SecretCode: React.FC<SecretCodeProps> = ({ navigation, route }) => {
  const dispatch = useDispatch();
  const { mobileNumber } = route.params; // Get the mobile number from route params
  const { otp } = route.params; // Get the mobile number from route params
  const [value, setValue] = useState<string>('');
  const [loader, setLoader] = useState<boolean>(false);
  const ref = useBlurOnFulfill({ value, cellCount: CELL_COUNT });
  const [props, getCellOnLayoutHandler] = useClearByFocusCell({
    value,
    setValue,
  });

  console.log('mobileNumber:', mobileNumber,otp);

  const isOtpComplete = value.length === CELL_COUNT;

  const handleNext = async () => {
    if (!isOtpComplete) return;

    try {
      setLoader(true);
      const response = await VerifyLoginOtp({ mobileNumber, otp: value });
      dispatch(overwriteStore({ name: 'user', value: response?.data || [] }));
      navigation.navigate('SNotification');

      console.log('OTP Verification Response:', response);

      if(response){
        navigation.navigate('SNotification');

      }
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'OTP Verified',
          text2: 'You have successfully verified your OTP.',
        });

        // Initialize socket connection after successful login
        console.log('🔄 [SecretCode] Initializing socket connection after successful login');
        try {
          socketService.connect();
          console.log('✅ [SecretCode] Socket connection initiated successfully');
        } catch (error) {
          console.error('❌ [SecretCode] Error initializing socket connection:', error);
        }

      } else {
        Toast.show({
          type: 'error',
          text1: 'Verification Failed',
          text2:   'Invalid OTP. Please try again.',
        });
        navigation.navigate('WelcomeBack');

      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'An unexpected error occurred.',
      });
    } finally {
      setLoader(false);
    }
  };

  const handleResendOtp = async () => {
    try {
      setLoader(true);
      const response = await ResendOtp({ mobileNumber: mobileNumber} );
      console.log('Resend OTP Response:', response);

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'OTP Sent',
          text2: 'A new OTP has been sent to your mobile number.',
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Resend Failed',
          text2: response.message || 'Unable to resend OTP. Please try again.',
        });
      }
    } catch (error) {
      console.error('Error resending OTP:', error);
      // Toast.show({
      //   type: 'error',
      //   text1: 'Error',
      //   text2: error?.data || 'An unexpected error occurred.',
      // });
    } finally {
      setLoader(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Loader */}
            <View style={{marginTop:20}}/>

      <Spinner visible={loader} />

      {/* Header */}
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>

      {/* Title */}
      <Text style={styles.title}>Text Alert! Find Your Secret Code inside</Text>
      <Text style={styles.subtitle}>
        Enter the code sent to you at <Text style={styles.phoneNumber}>{mobileNumber}</Text>
      </Text>
      <Text style={styles.phoneNumber}>{otp}</Text>
      {/* OTP Input */}
      <CodeField
        secureTextEntry
        ref={ref}
        {...props}
        value={value}
        onChangeText={setValue}
        cellCount={CELL_COUNT}
        rootStyle={styles.codeFieldRoot}
        keyboardType="number-pad"
        // @ts-ignore - textContentType and autoComplete have compatibility issues with TypeScript
        textContentType="oneTimeCode"
        // @ts-ignore
        autoComplete={Platform.OS === 'android' ? 'sms-otp' : 'one-time-code'}
        renderCell={({ index, symbol, isFocused }) => (
          <Text
            key={index}
            style={[styles.cell, isFocused && styles.focusCell]}
            onLayout={getCellOnLayoutHandler(index)}
          >
            {symbol || (isFocused ? <Cursor /> : null)}
          </Text>
        )}
      />

      {/* Resend Code */}
      <Text style={styles.resendText}>
        Didn’t receive code?{' '}
        <Text style={styles.resendLink} onPress={handleResendOtp}>
          Resend
        </Text>
      </Text>



      {/* Next Button */}
      <TouchableOpacity
        style={[styles.nextButton, isOtpComplete ? styles.activeButton : styles.inactiveButton]}
        disabled={!isOtpComplete}
        onPress={handleNext}
      >
        <Image source={icons.ArrowIcon} style={styles.nextIcon} />
        {/* Replace with your icon */}
      </TouchableOpacity>
    </View>
  );
};

export default SecretCode;

const styles = StyleSheet.create({
  root: {flex: 1, padding: 40},
  codeFieldRoot: {
    marginTop: 20,
    borderRadius: 10,
    width: '70%',
    alignSelf: 'center',
  },
  cell: {
    width: 55,
    height: 55,
    lineHeight: 60,
    fontSize: 24,
    borderWidth: 0.3,
    // borderColor: '#00000030',
    // elevation: 1,
    borderRadius: 5,
    textAlign: 'center',
  },
  focusCell: {
    borderColor: '#000',
  },
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#E5F3FF', // Light blue background
  },
  backButton: {
    marginBottom: 20,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.h2,
    lineHeight: 23.44,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: 20,
    color: COLORS.black,
  },
  subtitle: {
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.body3,
    lineHeight: 20,
    textAlign: 'center',
    marginBottom: 30,
    color: '#555',
  },
  phoneNumber: {
    fontWeight: '600',
    color: COLORS.black,
  },
  resendText: {
    textAlign: 'center',
    fontFamily: 'Poppins-Medium',
    fontSize: SIZES.body3,
    lineHeight: 20,
    color: '#555',
    marginTop: 20,
  },
  resendLink: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  nextButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  activeButton: {
    backgroundColor: COLORS.primary,
  },
  inactiveButton: {
    backgroundColor: '#ddd',
  },
  nextIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: '#fff',
  },
});
