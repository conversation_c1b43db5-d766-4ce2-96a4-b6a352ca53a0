import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ActivityIndicator,
  Platform,
  Alert,
  PermissionsAndroid,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useNavigation } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import socketService from '../../../services/socketService';
import messaging from '@react-native-firebase/messaging';

const SNotification = () => {
  const navigation = useNavigation<any>();
  const [modalVisible, setModalVisible] = useState(false);
  const userMain = useSelector((state: any) => state?.user) || {};

  // console.log('User:', userMain);

  // Request notification permission
  const requestNotificationPermission = async () => {
    try {
      console.log('📱 [SNotification] Requesting notification permission...');

      if (Platform.OS === 'android') {
        // For Android 13+ (API level 33+), request POST_NOTIFICATIONS permission
        if (Platform.Version >= 33) {
          const granted = await PermissionsAndroid.request(
            PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
            {
              title: 'RideFuze Notification Permission',
              message: 'RideFuze needs notification permission to send you ride updates and important alerts.',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            }
          );

          if (granted === PermissionsAndroid.RESULTS.GRANTED) {
            console.log('✅ [SNotification] Android notification permission granted');
            return true;
          } else {
            console.log('❌ [SNotification] Android notification permission denied');
            return false;
          }
        } else {
          // For older Android versions, notifications are enabled by default
          console.log('✅ [SNotification] Android notification permission not required for this version');
          return true;
        }
      } else {
        // For iOS, use Firebase messaging
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          console.log('✅ [SNotification] iOS notification permission granted:', authStatus);
          return true;
        } else {
          console.log('❌ [SNotification] iOS notification permission denied:', authStatus);
          return false;
        }
      }
    } catch (error) {
      console.error('❌ [SNotification] Error requesting notification permission:', error);
      return false;
    }
  };

  // Handle "Yes, notify me" button press
  const handleNotifyMe = async () => {
    console.log('🔔 [SNotification] User clicked "Yes, notify me"');

    const permissionGranted = await requestNotificationPermission();

    if (permissionGranted) {
      console.log('✅ [SNotification] Notification permission granted, proceeding to app');
      proceedToApp();
    } else {
      // Show alert and still proceed to app
      Alert.alert(
        'Notification Permission',
        'You can enable notifications later in your device settings to receive ride updates.',
        [
          {
            text: 'OK',
            onPress: () => {
              console.log('ℹ️ [SNotification] User acknowledged notification permission denial');
              proceedToApp();
            }
          }
        ]
      );
    }
  };

  // Handle "Skip" button press
  const handleSkip = () => {
    console.log('⏭️ [SNotification] User clicked "Skip"');
    proceedToApp();
  };

  // Common function to proceed to main app
  const proceedToApp = () => {
    setModalVisible(true);

    // Initialize socket connection before entering main app
    console.log('🔄 [SNotification] Ensuring socket connection before entering main app');
    try {
      if (!socketService.isConnected) {
        // Add a small delay to ensure authentication is complete
        setTimeout(() => {
          socketService.connect();
          console.log('✅ [SNotification] Socket connection initiated after auth completion');
        }, 100);
      } else {
        console.log('✅ [SNotification] Socket already connected');
      }
    } catch (error) {
      console.error('❌ [SNotification] Error ensuring socket connection:', error);
    }

    setTimeout(() => {
      setModalVisible(false);
      navigation.navigate('TabStack');
      console.log('User:', userMain);
    }, 200);
  };

  return (
    <GradientBackground style={{ flex: 1 }}>
      {/* Header Section */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} >
        </TouchableOpacity>
        <Image source={images.notify} style={styles.illustration} resizeMode="contain" />
      </View>

      {/* Text Section */}
      <View style={styles.textSection}>
        <Text style={styles.mainText}>
          Don’t forget to check details and account activity, it’s important stuff!
        </Text>
        <Text style={styles.subText}>
          Get car booking updates, personalized recommendations, and more with a user-friendly{' '}
          <Text style={styles.brandName}>RideFuze</Text> platform
        </Text>
      </View>

      {/* Footer Buttons */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.notifyButton} onPress={handleNotifyMe}>
          <Text style={styles.notifyButtonText}>Yes, notify me</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Modal for Loading */}
      <Modal visible={modalVisible} transparent animationType="fade">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalText}>Welcome back to</Text>
            <Text style={styles.modalTitle}>RIDEFUZE</Text>
            <ActivityIndicator size="large" color={COLORS.primary} />
          </View>
        </View>
      </Modal>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 20,
  },
  backButton: {
    position: 'absolute',
    top: 10,
    left: 10,
  },
  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  illustration: {
    width: '100%',
    height: 300,
    marginTop: 70,
  },
  textSection: {
    marginHorizontal: 20,
    flex: 1,
    alignItems: 'center',
    padding: 20,
    marginTop: 50,
  },
  mainText: {
    ...FONTS.h3,
    textAlign: 'center',
    color: '#343A40',
    marginBottom: 10,
  },
  subText: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
  },
  brandName: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal: 20,
  },
  notifyButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    marginRight: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  notifyButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  skipButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  skipButtonText: {
    ...FONTS.h3,
    color: COLORS.primary,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    width: '80%',
  },
  modalText: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.primary,
    marginBottom: 10,
  },
  modalTitle: {
    ...FONTS.h1,
    color: COLORS.primary,
    marginBottom: 20,
  },
});

export default SNotification;
