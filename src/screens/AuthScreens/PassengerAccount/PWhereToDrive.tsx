import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
} from 'react-native';
import { COLORS, icons } from '../../../constants';
import GradientBackground from '../../../components/shared/GradientBackground';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// Define interfaces for type safety
interface RouteParams {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
}

// Define navigation types
type RootStackParamList = {
  PTermOfService: RouteParams & { opreatingCity: string };
}

type NavigationProp = StackNavigationProp<RootStackParamList>;

const mockCities = [
  'New York',
  'Los Angeles',
  'Chicago',
  'Houston',
  'Phoenix',
  'Philadelphia',
  'San Antonio',
  'San Diego',
  'Dallas',
  'San Jose',
  'Austin',
  'Jacksonville',
  'Fort Worth',
  'Columbus',
  'San Francisco',
  'Charlotte',
  'Indianapolis',
  'Seattle',
  'Denver',
  'Washington, D.C.',
  'Boston',
  'El Paso',
  'Nashville',
  'Detroit',
  'Oklahoma City',
  'Portland',
  'Las Vegas',
  'Memphis',
  'Louisville',
];

const PWhereToDrive = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();

  // Retrieve data from previous screen with default values
  const {
    firstName = '',
    lastName = '',
    email = '',
    mobileNumber = ''
  } = route.params || {};

  const [query, setQuery] = useState<string>('');
  const [filteredCities, setFilteredCities] = useState<string[]>([]);
  const [opreatingCity, setopreatingCity] = useState<string>('');

  const handleInputChange = (text: string): void => {
    setQuery(text);
    if (text) {
      const results = mockCities.filter((city) =>
        city.toLowerCase().includes(text.toLowerCase())
      );
      setFilteredCities(results);
    } else {
      setFilteredCities([]);
    }
  };

  const handleCitySelect = (city: string): void => {
    setQuery(city);
    setopreatingCity(city);
    setFilteredCities([]);
  };

  const handleNext = (): void => {
    // Pass all data to the next screen
    console.log('firstName:', firstName,'lastName:', lastName, 'email:', email, 'opreatingCity:', opreatingCity, 'mobileNumber:', mobileNumber);

    navigation.navigate('PTermOfService', {
      firstName,
      lastName,
      email,
      opreatingCity,
      mobileNumber
    });
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <TouchableOpacity>
          <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Where do you plan to drive?</Text>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Image source={icons.Search} style={styles.icon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Your city"
          value={query}
          onChangeText={handleInputChange}
        />
      </View>

      {/* Suggestions List */}
      {filteredCities.length > 0 && (
        <FlatList
          data={filteredCities}
          keyExtractor={(_item, index) => index.toString()}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.suggestionItem}
              onPress={() => handleCitySelect(item)}
            >
              <Text style={styles.suggestionText}>{item}</Text>
            </TouchableOpacity>
          )}
          style={[
            styles.suggestionsList,
            {
              height:
                filteredCities.length === 1
                  ? 50 // Adjust height for one result
                  : 150, // Max height for multiple results
            },
          ]}
        />
      )}

      {/* Subtitle */}
      <Text style={styles.subtitle}>
        This will be based on the requirement of where you plan to drive
      </Text>

      {/* Next Button */}
      <TouchableOpacity
        onPress={handleNext}
        style={[
          styles.nextButton,
          { opacity: opreatingCity ? 1 : 0.5 },
        ]}
        disabled={!opreatingCity} // Disable the button until a city is selected
      >
        <Image source={icons.ArrowIcon} style={styles.icon} />
      </TouchableOpacity>
    </GradientBackground>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    // alignItems: 'center',
  },
  header: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: 20,
    color: '#000',
    marginBottom: 10,
  },
  searchContainer: {
    marginHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    fontWeight: '500',
    paddingLeft: 10
  },
  suggestionsList: {
    marginHorizontal: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    minHeight: 50,
    maxHeight: 150, // Limit the height of the suggestion
  },
  suggestionItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#000',
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
    color: '#555',
    margin: 25,
  },
  nextButton: {
    marginTop: 50,
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  helpText: {
    fontSize: 14,
    fontWeight: '400',
    color: COLORS.black,
  },
});

export default PWhereToDrive;
