import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { FONTS, images } from '../../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';

const PTermsOfService = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve data from the previous screen
  const { firstName, lastName, email, opreatingCity ,mobileNumber} = route.params || {};
console.log('firstName:', firstName,'lastName:', lastName, 'email:', email, 'opreatingCity:', opreatingCity, 'mobileNumber:', mobileNumber);

  const handleNext = () => {
    // Pass all data to the next screen
    navigation.navigate('PAddSocialSecurity', {
      firstName,
      lastName,
      email,
      opreatingCity,mobileNumber
    });
  };

  return (
    <GradientBackground>
      {/* Header Image */}
      <Image
        source={images.Termofservice} // Replace with the path to your image
        style={styles.headerImage}
        resizeMode="cover"
      />

      {/* Content Section */}
      <ScrollView contentContainerStyle={styles.content}>
        {/* Title */}
        <Text style={styles.title}>
          <Text style={styles.brandName}>RIDEFUZE </Text>
          Terms of Services
        </Text>
        <Text style={styles.lastUpdated}>Last Updated : July 13, 2900</Text>

        {/* Body Content */}
        <Text style={styles.bodyText}>
          Sagittis et eu at elementum, quis in. Proin praesent volutpat egestas
          sociis sit lorem nunc nunc sit. Eget diam curabitur mi ac. Auctor
          rutrum lacus malesuada massa ornare et.
        </Text>
        <Text style={styles.bodyText}>
          Lectus id duis vitae porttitor enim gravida morbi.
        </Text>
        {/* Bullet Points */}
        <View style={styles.bulletsContainer}>
          <Text style={styles.bulletPoint}>
            • Eu turpis posuere semper feugiat volutpat elit, ultrices
            suspendisse.
          </Text>
          <Text style={styles.bulletPoint}>
            • Suspendisse maecenas ac donec scelerisque diam sed est duis purus.
          </Text>
        </View>
        <Text style={styles.bodyText}>
          Sagittis et eu at elementum, quis in. Proin praesent volutpat egestas
          sociis sit lorem nunc nunc sit. Eget diam curabitur mi ac. Auctor
          rutrum lacus malesuada massa ornare et.
        </Text>
        <Text style={styles.bodyText}>
          Sagittis et eu at elementum, quis in. Proin praesent volutpat egestas
          sociis sit lorem nunc nunc sit. Eget diam curabitur mi ac. Auctor
          rutrum lacus malesuada massa ornare et.
        </Text>
      </ScrollView>

      {/* Footer Button */}
      <TouchableOpacity onPress={handleNext} style={styles.footerButton}>
        <Text style={styles.footerButtonText}>I Agree →</Text>
      </TouchableOpacity>
    </GradientBackground>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerImage: {
    width: '100%',
    height: 300,
    marginBottom: 10,
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 80, // Space for the footer button
  },
  title: {
...FONTS.h1,
    color: '#000',
    marginBottom: 5,
  },
  brandName: {
    color: '#007AFF', // Blue color for the brand name
  },
  lastUpdated: {
    fontSize: 14,
    color: '#6C757D',
    marginBottom: 20,
  },
  bodyText: {
 ...FONTS.body3,
    color: '#343A40',
    marginBottom: 10,
  },
  bulletsContainer: {
    marginBottom: 10,
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    color: '#343A40',
    marginBottom: 5,
  },
  footerButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: '#007AFF',
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
  },
  footerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFF',
  },
});

export default PTermsOfService;
