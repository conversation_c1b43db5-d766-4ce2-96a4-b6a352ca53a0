import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import * as ImagePicker from 'react-native-image-picker'; // Ensure this library is installed
import Gradient<PERSON>ackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const PAddProfilePicture = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from the previous screen
  const {
    firstName,
    lastName,
    email,
    opreatingCity,
    ssn,
    driverLincenseImgFront,
    driverLincenseImgBack,
    mobileNumber,
  } = route.params || {};

  const [profileImg, setProfileImg] = useState(null);

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibrary({
        mediaType: 'photo',
        includeBase64: false,
        selectionLimit: 1,
      });

      if (result.didCancel) {
        console.log('User cancelled image picker');
        return;
      }

      if (result.errorCode) {
        console.log('Error Code:', result.errorCode);
        Alert.alert('Error', result.errorMessage || 'Unable to pick an image.');
        return;
      }

      if (result.assets && result.assets[0]) {
        const asset = result.assets[0];

        // Format the image in the expected object structure
        const formattedImage = {
          uri: asset.uri,
          type: asset.type || 'image/jpeg', // Default MIME type if not provided
          name: asset.fileName || `profile_${Date.now()}.jpg`, // Generate name if not provided
        };

        setProfileImg(formattedImage);
      } else {
        Alert.alert('Error', 'No image selected.');
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Error', 'Unable to pick an image. Please try again.');
    }
  };

  const handleNext = () => {
    if (!profileImg) {
      Alert.alert('Incomplete', 'Please upload a profile picture.');
      return;
    }

    // Pass all data, including formatted profileImg, to the next screen
    navigation.navigate('PCheckConsent', {
      firstName,
      lastName,
      email,
      mobileNumber,
      opreatingCity,
      ssn,
      driverLincenseImgFront,
      driverLincenseImgBack,
      profileImg, // Pass the formatted image object
    });
  };

  return (
    <GradientBackground>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
        <Text style={styles.helpText}>Help</Text>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Upload Profile Picture</Text>

      {/* Profile Image Section */}
      <View style={styles.imageSection}>
        <Text style={styles.imageLabel}>Profile Picture</Text>
        {profileImg ? (
          <>
            <Image source={{ uri: profileImg.uri }} style={styles.uploadedImage} />
            <TouchableOpacity onPress={handleImagePick}>
              <Text style={styles.repickText}>Repick Image</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity style={styles.imagePlaceholder} onPress={handleImagePick}>
            <Image source={icons.UploadIcon} style={styles.uploadIcon} />
            <Text style={styles.uploadText}>Upload Image</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Next Button */}
      <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
        <Text style={styles.nextButtonText}>Next</Text>
      </TouchableOpacity>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 18,
    height: 18,
    tintColor: COLORS.black,
    resizeMode:'contain'
  },
  helpText: {
    fontSize: 14,
    fontFamily: FONTS.body3,
    color: COLORS.black,
  },
  title: {
    ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 16,
  },
  imageSection: {
    marginVertical: 16,
    alignItems: 'center',
  },
  imageLabel: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    marginBottom: 8,
  },
  imagePlaceholder: {
    width: 150,
    height: 150,
    borderRadius: 75,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.white,
  },
  uploadIcon: {
    width: 40,
    height: 40,
    tintColor: COLORS.primary,
  },
  uploadText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
    marginTop: 8,
  },
  uploadedImage: {
    width: 150,
    height: 150,
    borderRadius: 75,
  },
  repickText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.secondary,
    marginTop: 8,
    textDecorationLine: 'underline',
  },
  nextButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginTop: 24,
    marginHorizontal:20
  },
  nextButtonText: {
    fontSize: 16,
    fontFamily: FONTS.bold,
    color: COLORS.white,
  },
});

export default PAddProfilePicture;
