import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Alert,
  StyleSheet,
} from 'react-native';
import * as ImagePicker from 'react-native-image-picker'; // Ensure this library is installed
import Gradient<PERSON>ackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const PDriverLicenseImage = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from the previous screen
  const { firstName, lastName, email, opreatingCity, ssn, mobileNumber } = route.params || {};

  const [driverLincenseImgFront, setDriverLicenseImgFront] = useState(null);
  const [driverLincenseImgBack, setDriverLicenseImgBack] = useState(null);

  const handleImagePick = async (setImage) => {
    try {
      const result = await ImagePicker.launchImageLibrary({
        mediaType: 'photo',
        includeBase64: false,
        selectionLimit: 1,
      });

      if (result.didCancel) {
        console.log('User cancelled image picker');
        return;
      }

      if (result.errorCode) {
        console.log('Error Code:', result.errorCode);
        Alert.alert('Error', result.errorMessage || 'Unable to pick an image.');
        return;
      }

      if (result.assets && result.assets[0]) {
        const asset = result.assets[0];

        // Format the image into the expected object structure
        const formattedImage = {
          uri: asset.uri,
          type: asset.type || 'image/jpeg', // Default MIME type
          name: asset.fileName || `license_${Date.now()}.jpg`, // Generate name if not provided
        };

        setImage(formattedImage);
      } else {
        Alert.alert('Error', 'No image selected.');
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Error', 'Unable to pick an image. Please try again.');
    }
  };

  const handleNext = () => {
    if (!driverLincenseImgFront || !driverLincenseImgBack) {
      Alert.alert('Incomplete', 'Please upload both front and back images of your driver’s license.');
      return;
    }

    // Pass all data, including formatted driver’s license images, to the next screen
    navigation.navigate('PIdentificationSubmitted', {
      firstName,
      lastName,
      email,
      opreatingCity,
      ssn,
      mobileNumber,
      driverLincenseImgFront,
      driverLincenseImgBack,
    });
  };

  return (
    <GradientBackground>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.helpText}>Help</Text>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Upload Driver’s License</Text>

      {/* Front Image Section */}
      <View style={styles.imageSection}>
        <Text style={styles.imageLabel}>Front of Driver’s License</Text>
        {driverLincenseImgFront ? (
          <>
            <Image source={{ uri: driverLincenseImgFront.uri }} style={styles.uploadedImage} />
            <TouchableOpacity onPress={() => handleImagePick(setDriverLicenseImgFront)}>
              <Text style={styles.repickText}>Repick Front Image</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            style={styles.imagePlaceholder}
            onPress={() => handleImagePick(setDriverLicenseImgFront)}
          >
            <Image source={icons?.UploadIcon} style={styles.uploadIcon} />
            <Text style={styles.uploadText}>Upload Front Image</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Back Image Section */}
      <View style={styles.imageSection}>
        <Text style={styles.imageLabel}>Back of Driver’s License</Text>
        {driverLincenseImgBack ? (
          <>
            <Image source={{ uri: driverLincenseImgBack.uri }} style={styles.uploadedImage} />
            <TouchableOpacity onPress={() => handleImagePick(setDriverLicenseImgBack)}>
              <Text style={styles.repickText}>Repick Back Image</Text>
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            style={styles.imagePlaceholder}
            onPress={() => handleImagePick(setDriverLicenseImgBack)}
          >
            <Image source={icons?.UploadIcon} style={styles.uploadIcon} />
            <Text style={styles.uploadText}>Upload Back Image</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Next Button */}
      <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
        <Text style={styles.nextButtonText}>Next</Text>
      </TouchableOpacity>
    </GradientBackground>
  );
};


const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 20,
    height: 20,
    tintColor: COLORS.black,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.black,    
  },
  title: {
    ...FONTS.h2,
    fontSize:15,
    textAlign: 'center',
    marginVertical: 16,
  },
  imageSection: {
    marginVertical: 16,
    alignItems: 'center',
  },
  imageLabel: {
    fontSize: 16,
    fontFamily: FONTS.medium,
    marginBottom: 8,
  },
  imagePlaceholder: {
    width: 150,
    height: 150,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.white,
  },
  uploadIcon: {
    width: 40,
    height: 40,
    tintColor: COLORS.primary,
  },
  uploadText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
    marginTop: 8,
  },
  uploadedImage: {
    resizeMode: 'cover',
    width: '90%',
    height: 150,
    borderRadius: 8,
  },
  repickText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.secondary,
    marginTop: 8,
    textDecorationLine: 'underline',
  },
  nextButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignItems: 'center',
    marginTop: 24,
    marginHorizontal: 20,
  },
  nextButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default PDriverLicenseImage;
