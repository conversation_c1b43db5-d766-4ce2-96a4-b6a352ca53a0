import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Alert,
} from 'react-native';
import { FONTS } from '../../../constants';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { VerifyOtp, VerifyPersonalDetails } from '../../../../redux/api';
const PAddEmail = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { firstName, lastName,mobileNumber } = route.params || {}; // Get firstName and lastName from route params
  const [email, setEmail] = useState('');
  const buttonOpacity = useRef(new Animated.Value(0.3)).current;
  const [loader, setLoader] = useState(false);

  // Validate email format
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  useEffect(() => {
    // Check if email is valid
    const isButtonVisible = email.trim().length > 0 && isValidEmail(email);

    // Animate button opacity based on input
    Animated.timing(buttonOpacity, {
      toValue: isButtonVisible ? 1 : 0.3, // Start faded (0.3) and go full (1)
      duration: 300, // Animation duration
      useNativeDriver: true,
    }).start();
  }, [email]);

  // const handleNext = async() => {
  //   if (!isValidEmail(email)) {
  //     Alert.alert('Invalid Email', 'Please enter a valid email address.');
  //     return;
  //   }
 
   

  //   // Pass all data to the next screen
  //   // navigation.navigate('WhereToDrive', { mobileNumber,firstName, lastName, email });
  // };

    const handleNext = async () => {
      if (!isValidEmail(email)) {
        Alert.alert('Invalid Email', 'Please enter a valid email address.');
        return;
      }
  
      try {
        setLoader(true);
        const response = await VerifyPersonalDetails({ email: email, mobileNumber: mobileNumber });
        console.log('OTP Verification Response:', response);
  
        if (response.success) {
          Toast.show({
            type: 'success',
            text1: 'Email Confirmed',
            text2: response?.data
          });
          navigation.navigate('PWhereToDrive', { mobileNumber,firstName, lastName, email });
        } else {
          Toast.show({
            type: 'error',
            text1: 'Verification Failed',
            text2: response.data
          });
        }
      } catch (error) {
        console.error('Error verifying OTP:', error);
        // Toast.show({
        //   type: 'error',
        //   text1: 'Error',
        //   text2: error?.data,
        // });
      } finally {
        setLoader(false);
      }
    };

  return (
    <LinearGradient colors={['#D0E6FF', '#F2FCFC']} style={styles.container}>
      <Spinner visible={loader} />
    
      <View>
        {/* Back Arrow */}
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Text style={styles.backArrow}>←</Text>
        </TouchableOpacity>

        {/* Header Text */}
        <Text style={styles.headerText}>Nice to meet you, {firstName}</Text>
        <Text style={styles.subText}>
          Ride receipts and account updates will be sent to your mail
        </Text>

        {/* Input Fields */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email*</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your email"
            placeholderTextColor="#BDBDBD"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>
      </View>

      {/* Next Button with Adjustable Opacity */}
          <Animated.View style={[styles.nextButton, { opacity: buttonOpacity }]}>
            <TouchableOpacity onPress={handleNext} >
              <Text style={styles.nextArrow}>→</Text>
            </TouchableOpacity>
          </Animated.View>
    </LinearGradient>
  );
};


const styles = StyleSheet.create({
  container: {
    // margin: 20,
    flex: 1,
    // justifyContent: 'space-between',
  },
  backButton: {
    position: 'relative',
    top: 10,
    left: 5,
  },
  backArrow: {
    fontSize: 20,
    color: '#000',
  },
  headerText: {
    ...FONTS.h2,
    color: '#000',
    textAlign: 'center',
    marginHorizontal:20,
    marginTop: 30,
  },
  subText: {
    ...FONTS.body3,
    color: '#7E7E7E',
    textAlign: 'center',
    marginBottom: 30,
    marginHorizontal:20,

    marginTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
    marginHorizontal:20
  },
  label: {
    ...FONTS.body3,
    color: '#000',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DADADA',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    height: 50,
    backgroundColor: '#FFF',
  },
  nextButton: {
    position: 'absolute',
    right:20,
    bottom: 20, // Adjusted to sit at the bottom center
    alignSelf: 'center', // Centered horizontally
    width: 50,
    height: 50,
    borderRadius: 30,
    backgroundColor: '#007BFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    // marginTop:100,

  },
  nextArrow: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFF',
  },
});

export default PAddEmail;
