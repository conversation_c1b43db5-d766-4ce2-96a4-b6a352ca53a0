 
import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';

const PAddYourCar = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from previous screens
  const {
    firstName,
    lastName,
    email,
    opreatingCity,mobileNumber,
    ssn,
    driverLincenseImgFront,
    driverLincenseImgBack,
    profileImg,
  } = route.params || {};

  const handleNextForCarDetails = () => {
    navigation.navigate('PAddACarDetails', {
      firstName,
      lastName,
      email,
      opreatingCity,
      ssn,mobileNumber,
      driverLincenseImgFront,
      driverLincenseImgBack,
      profileImg,
    
    });
  };

  const handleNextForRentCar = () => {
    navigation.navigate('PAddACarDetails', {
      firstName,
      lastName,
      email,
      opreatingCity,
      ssn,
      driverLincenseImgFront,
      driverLincenseImgBack,
      profileImg,
       
    });
  };

  return (
    <GradientBackground>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.helpText}>Help</Text>
      </View>

      {/* Title */}
      <Text style={styles.title}>Add car details</Text>

      {/* I Have a Car Section */}
      <View style={styles.card}>
        <Image source={images.CarSingle} style={styles.carImage} />
        <Text style={styles.cardTitle}>I have a car</Text>
        <Text style={styles.cardDescription}>
          Add your car feature to make your passenger know the car that they are riding with
        </Text>
        <TouchableOpacity style={styles.cardButton} onPress={handleNextForCarDetails}>
          <Text style={styles.cardButtonText}>Add your car</Text>
        </TouchableOpacity>
      </View>

      {/* Rent a Car Section */}
      <View style={styles.card}>
        <Image source={images.CarMultiple} style={styles.carImage} />
        <Text style={styles.cardTitle}>Rent a car</Text>
        <Text style={styles.cardDescription}>
          Get an affordable rental you can use to drive on inride starts from{' '}
          <Text style={styles.price}>$20.5 - $40.5</Text>
        </Text>
        <Text style={styles.termsLink}>Terms and condition applied</Text>
        <TouchableOpacity style={styles.cardButton} onPress={handleNextForRentCar}>
          <Text style={styles.cardButtonText}>Rent a car</Text>
        </TouchableOpacity>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  title: {
   ...FONTS.body2,
    textAlign: 'center',
    marginVertical: 16,
    color: COLORS.black,
  },
  card: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    marginHorizontal: 16,
    marginVertical: 8,
    padding: 10,
    alignItems: 'center',
    shadowColor: COLORS.black,
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 3,
  },
  carImage: {
    width: 150,
    height: 90,
    resizeMode: 'contain',
    // marginBottom: 12,
  },
  cardTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 8,
  },
  cardDescription: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
    marginHorizontal: 16,
    marginBottom: 10,
  },
  price: {
    color: COLORS.primary,
    ...FONTS.h4
  },
  termsLink: {
    ...FONTS.body4,
    color: COLORS.primary,
    textDecorationLine: 'underline',
    marginBottom: 12,
  },
  cardButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  cardButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default PAddYourCar;
