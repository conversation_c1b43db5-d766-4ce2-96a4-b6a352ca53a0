import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { VerifySSN } from '../../../../redux/api';

const PAddSocialSecurityNumber = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [loader, setLoader] = useState(false);

  // Retrieve data from the previous screen
  const { firstName, lastName, email, opreatingCity,mobileNumber } = route.params || {};
  const [ssn, setSsn] = useState('');

   


  const handleNext = async() => {
    // Validate the SSN input
    if (ssn.trim().length !== 10) {
      Alert.alert(
        'Invalid SSN',
        'Social Security Number must be exactly 10 digits.',
      );
      return;
    }

    try {
      setLoader(true);
      const response = await VerifySSN({ ssn: ssn });
      console.log('SSN Response:', response);

      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'SSN Confirmed',
          text2: response?.data
        });
        navigation.navigate('PDriverLicense', {
          firstName,
          lastName,
          email,
          opreatingCity,
          ssn,mobileNumber
        });
      } else {
        Toast.show({
          type: 'error',
          text1: 'Verification Failed',
          text2: response.data
        });
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      // Toast.show({
      //   type: 'error',
      //   text1: 'Error',
      //   text2: error?.data,
      // });
    } finally {
      setLoader(false);
    }
      
    // Pass all data, including ssn, to the next screen
  
  };

  return (
    <View style={styles.container}>
      <Spinner visible={loader} />

      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
        <Text style={styles.helpText}>Help</Text>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Add Social Security Number</Text>

      {/* Input Label */}
      <Text style={styles.label}>Social Security Number</Text>

      {/* Input Field */}
      <TextInput
        style={styles.input}
        placeholder="Enter Social Security Number"
        value={ssn}
        onChangeText={(text) => setSsn(text)}
        keyboardType="numeric"
        maxLength={10} // Ensure input does not exceed 10 digits
      />

      {/* Description */}
      <Text style={styles.description}>
        Your SSN is important for payment purposes and for background checks - it won’t be used for something else.
      </Text>

      {/* Button */}
      <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
        <Text style={styles.primaryButtonText}>Add Driver’s License</Text>
      </TouchableOpacity>
    </View>
  );
};




const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: '#D0E6FF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal:20
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
 ...FONTS.h3,
    textAlign: 'center',
    marginVertical: 20,
    color: '#000',
  },
  label: {
    marginTop:20,
    ...FONTS.body3,
    marginBottom: 10,
    color: '#343A40',
    marginHorizontal:20

  },
  input: {
    height: 50,
    color:COLORS.black,
    marginHorizontal:20,

    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 10,
    paddingHorizontal: 15,
    backgroundColor: '#FFFFFF',
    fontSize: 16,
    marginBottom: 15,
  },
  description: {
    ...FONTS.body4,
    textAlign: 'center',
    color: '#6C757D',
    marginBottom: 30,
    marginHorizontal:20

  },
  primaryButton: {
    marginHorizontal:20,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
  },
  primaryButtonText: {
    color: '#FFF',
  ...FONTS.h3
  },
});

export default PAddSocialSecurityNumber;
