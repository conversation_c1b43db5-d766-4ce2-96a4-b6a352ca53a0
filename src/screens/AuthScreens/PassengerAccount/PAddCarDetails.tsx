import React from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../../constants';
import GradientBackground from '../../../components/shared/GradientBackground';
import { useNavigation } from '@react-navigation/native';

// Registration Steps Data
const steps = [
  {
    id: '1',
    title: 'Social Security Number',
    description: 'Pls fill your SSN for security reasons',
    completed: true,
    screen: 'AddSocialSecurity',
  },
  {
    id: '2',
    title: "Driver's License",
    description: 'Pls help us with your driver’s license to verify your identity',
    completed: false,
    screen: 'DriverLicense',
  },
  {
    id: '3',
    title: 'Profile picture',
    description: 'Make it easy for people to recognize you',
    completed: false,
    screen: 'AddProfilePicture',
  },
  {
    id: '4',
    title: 'Background check consent',
    description: 'Enable us to protect everyone',
    completed: false,
    screen: 'CheckConsent',
  },
  {
    id: '5',
    title: 'Add car',
    description: 'Use your own car or rent a car',
    completed: false,
    screen: 'AddYourCar',
  },
];

const PAddCarDetails = () => {
  const navigation = useNavigation();

  const renderStep = ({ item, index }) => {
    const isCompleted = item.completed;
    const isActive = index === 0 || steps[index - 1].completed;

    return (
      <TouchableOpacity
        style={styles.stepContainer}
        onPress={() => navigation.navigate(item.screen)}
      >
        {/* Timeline Left Section */}
        <View style={styles.timelineContainer}>
          <View
            style={[
              styles.circle,
              isCompleted && styles.completedCircle,
              isActive && !isCompleted && styles.activeCircle,
            ]}
          >
            {isCompleted && (
              <Image
                source={icons.Tick} // Dummy check icon
                style={styles.checkIcon}
              />
            )}
          </View>
          {index !== steps.length - 1 && (
            <View
              style={[
                styles.line,
                isCompleted && styles.completedLine,
              ]}
            />
          )}
        </View>

        {/* Step Content */}
        <View style={styles.contentContainer}>
          <Text
            style={[
              styles.stepTitle,
              isCompleted || isActive ? styles.activeText : styles.inactiveText,
            ]}
          >
            {item.title}
          </Text>
          <Text
            style={[
              styles.stepDescription,
              isCompleted || isActive ? styles.activeText : styles.inactiveText,
            ]}
          >
            {item.description}
          </Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <GradientBackground>
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image
              source={icons.BackIcon} // Dummy back icon
              style={styles.icon}
            />
          </TouchableOpacity>
           <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
        </View>

        {/* Title */}
        <Text style={styles.title}>Add Car details</Text>

        {/* Steps */}
        <FlatList
          data={steps}
          keyExtractor={(item) => item.id}
          renderItem={renderStep}
          contentContainerStyle={styles.stepsList}
        />
      </View>
    </GradientBackground>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    backgroundColor: 'transparent',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 20,
    marginBottom:20
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: COLORS.black || '#343A40',
  },
  title: {
 ...FONTS.h3,
    textAlign: 'center',
    marginVertical: 20,
    marginBottom: 40,
    color: '#000',
  },
  stepsList: {
    paddingBottom: 20,
  },
  stepContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 0,
  },
  timelineContainer: {
    alignItems: 'center',
    marginRight: 15,
  },
  circle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#F2F4F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  completedCircle: {
    backgroundColor: COLORS.primary,
  },
  activeCircle: {
    borderWidth: 8,
    borderColor: COLORS.primary,
    backgroundColor: '#FFF',
  },
  checkIcon: {
    width: 14,
    height: 14,
    tintColor: '#FFF',
  },
  line: {
    width: 2,
    flex: 1,
    height:60,
    backgroundColor: '#E0E0E0',
  },
  completedLine: {
    backgroundColor: COLORS.primary,
    height:50
  },
  contentContainer: {
    flex: 1,
  },
  stepTitle: {
...FONTS.body3,
    marginBottom: 5,
  },
  stepDescription: {
    fontSize: 14,
    color: COLORS.grey,
  },
  activeText: {
    ...FONTS.body3,
    
    color: COLORS.primary,
  },
  inactiveText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default PAddCarDetails;
