import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
} from 'react-native';
import { COLORS, icons } from '../../../constants'; // Replace with your constants
import GradientBackground from '../../../components/shared/GradientBackground';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { RegisterWithPassengerAccount } from '../../../../redux/api';
import { useNavigation } from '@react-navigation/native';

// Define a simple navigation type to avoid TypeScript errors
type NavigationProp = {
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
};

const PMobileNumber = () => {
  const navigation = useNavigation<NavigationProp>();
  const [mobileNumber, setMobileNumber] = useState('');
  const [loader, setLoader] = useState(false);
  // Fixed US calling code (for USA)
  const callingCode = '1';
  // Create the full mobile number with US country code
  const fullMobileNumber = `+${callingCode}${mobileNumber}`;


  const handleContinue = async () => {
    if (!mobileNumber || mobileNumber.length < 10) {
      Toast.show({
        type: 'error',
        text1: 'Invalid Mobile Number',
        text2: 'Please enter a valid 10-digit mobile number.',
      });
      return;
    }

    try {
      setLoader(true);

      const fullMobileNumber = `+${callingCode}${mobileNumber}`;
      const response = await RegisterWithPassengerAccount({ mobileNumber: fullMobileNumber });

      // Validate response structure
      if (!response || !response.success) {
        throw new Error(response?.data || 'Unable to register driver number');
      }


      Toast.show({
        type: 'success',
        text1: 'Success',
        text2: response?.data
      });
      console.log('Registration Response:', response);

      // Navigate to next screen if successful
      navigation.navigate('PSecretCode', { mobileNumber: fullMobileNumber });
    } catch (error) {
      console.error('Error in registration:', error);


      // Toast.show({
      //   type: 'error',
      //   text1: 'Error',
      //   text2: error.data || 'An unexpected error occurred.',
      // });
    } finally {
      setLoader(false);
    }
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Loader */}
      <Spinner visible={loader} />

      {/* Header */}
      <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
        <Image source={icons.BackIcon} style={styles.icon as any} />
      </TouchableOpacity>

      {/* Title */}
      <Text style={styles.title}>Enter your mobile number</Text>
      <Text style={styles.subtitle}>We’ll text a code to verify your mobile number</Text>

      {/* Mobile Number Input */}
      <View style={styles.inputContainer}>
        {/* US Flag (Static) */}
        <View style={styles.countryPicker}>
          <Text style={styles.flagEmoji}>🇺🇸</Text>
        </View>
        <Text style={styles.callingCode}>+{callingCode}</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your mobile number"
          value={mobileNumber}
          onChangeText={setMobileNumber}
          keyboardType="phone-pad"
          maxLength={10}
        />
      </View>

      {/* Continue Button */}
      <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
        <Text style={styles.continueButtonText}>Continue →</Text>
      </TouchableOpacity>

      {/* OR Divider */}
      {/* <Text style={styles.orText}>OR</Text> */}

      {/* Social Signup Buttons */}
      {/* <TouchableOpacity style={styles.socialButton} onPress={() => navigation.navigate('RSecretCode',{ mobileNumber: fullMobileNumber })}>
        <Image source={icons.GoogleIcon} style={styles.socialIcon as any} />
        <Text style={styles.socialButtonText}>Sign up with Google</Text>
      </TouchableOpacity> */}
      {/* <TouchableOpacity style={styles.socialButton}>
        <Image source={icons.FacebookIcon} style={styles.socialIcon as any} />
        <Text style={styles.socialButtonText}>Sign up with Facebook</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.socialButton}>
        <Image source={icons.AppleIcon} style={styles.socialIcon as any} />
        <Text style={styles.socialButtonText}>Sign up with Apple</Text>
      </TouchableOpacity> */}

      {/* Footer */}
      <Text style={styles.footerText}>
        Have an account?{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('WelcomeBack')}>
          Sign in
        </Text>
      </Text>
      <Text style={styles.termsText}>
        By Proceeding, you agree to get calls, SMS messages, including automated calls from{' '}
        <Text style={styles.brandText}>RideFuze</Text> and its affiliated numbers. Text “STOP” to 20013 to opt-out and you consent to its{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('PrivacyPolicyScreen')}>
          Privacy Policy
        </Text>{' '}
        and{' '}
        <Text style={styles.linkText} onPress={() => navigation.navigate('TermsOfServiceScreen')}>
          Terms of Service
        </Text>.
      </Text>
    </GradientBackground>
  );
};

export default PMobileNumber;


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F8FBFF',
  },
  backButton: {
    marginBottom: 20,
  },
  icon: {
    margin: 20,
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    fontWeight: 'bold',
    fontSize: 18,
    textAlign: 'center',
    marginBottom: 10,
    color: COLORS.black,
  },
  subtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 30,
    color: '#555',
  },
  inputContainer: {
    marginHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
    marginBottom: 30,
  },
  countryPicker: {
    marginRight: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  flagEmoji: {
    fontSize: 20,
    marginRight: 5,
  },
  callingCode: {
    fontSize: 16,
    marginRight: 10,
    color: COLORS.black,
  },
  input: {
    flex: 1,
    height: 50,
    fontSize: 16,
    color: COLORS.black,
    backgroundColor: '#fff',
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal: 20,
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  orText: {
    textAlign: 'center',
    fontSize: 14,
    marginVertical: 10,
    color: '#555',
  },
  socialButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingVertical: 13,
    paddingHorizontal: 20,
    marginBottom: 10,
    backgroundColor: '#fff',
  },
  socialIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  socialButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  footerText: {
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal: 20,
    fontSize: 14,
    color: '#555',
  },
  linkText: {
    color: COLORS.primary,
    fontWeight: 'bold',
    fontSize: 12
  },
  termsText: {
    textAlign: 'justify',
    fontSize: 12,
    marginHorizontal: 22,
    color: '#555',
  },
  brandText: {
    color: COLORS.primary,
    fontWeight: '600',
  },
});
