import React from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Grad<PERSON>Background from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useNavigation } from '@react-navigation/native';

const PNotification = () => {
      const navigation = useNavigation();
  
  return (
   <GradientBackground>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton}>
          <Image
            source={icons.BackIcon} // Replace with the back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
        <Image
          source={images.notify} // Replace with the illustration path
          style={styles.illustration}
          resizeMode="contain"
        />
      </View>

      {/* Text Section */}
      <View style={styles.textSection}>
        <Text style={styles.mainText}>
          Don’t forget to check details and account activity, it’s important
          stuff!
        </Text>
        <Text style={styles.subText}>
          Get car booking updates, personalized recommendations, and more with a
          user-friendly <Text style={styles.brandName}>RideFuze</Text> platform
        </Text>
      </View>

      {/* Footer Buttons */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.notifyButton}  onPress={()=>navigation.navigate('AddCarDetails')}>
          <Text style={styles.notifyButtonText}>Yes, notify me</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.skipButton}>
          <Text style={styles.skipButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>
      </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
  },
  header: {
    flex: 1,
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal:20

  },
  backButton: {
    position: 'absolute',
    top: 10,
    left: 10,
  },
  icon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  illustration: {
    width: '100%',
    height: 300,
    marginTop:70
  },
  textSection: {
    marginHorizontal:20,
    flex: 1,
    // justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    marginTop:50
  },
  mainText: {
   ...FONTS.h3,
    textAlign: 'center',
    color: '#343A40',
    marginBottom: 10,
  },
  subText: {
   ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
  },
  brandName: {
    color: COLORS.primary,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    marginHorizontal:20
  },
  notifyButton: {
    flex: 1,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    marginRight: 10,
    borderRadius: 10,
    alignItems: 'center',
  },
  notifyButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  skipButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },

skipButtonText: {...FONTS.h3,
    color: COLORS.primary,
  },
});

export default PNotification;
