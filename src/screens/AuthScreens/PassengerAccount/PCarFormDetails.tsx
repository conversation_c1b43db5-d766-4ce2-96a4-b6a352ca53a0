import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  PermissionsAndroid,
  Platform,
  ScrollView,
} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { CompleteNewDriverRegistration } from '../../../../redux/api';
import {launchImageLibrary} from 'react-native-image-picker'; // Import the image picker library

const PCarFormDetails = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [loader, setLoader] = useState(false);

  // Retrieve data passed from the previous screen
  const allData = route.params || {};

  const [form, setForm] = useState({
    registrationNumber: '',
    year: '',
    model: '',
    color: '',
    noOfSeats: '',
    pricePerKm:'',
    carImg: null, 
  });

  const [isFormComplete, setIsFormComplete] = useState(false);
  const [locationGranted, setLocationGranted] = useState(false);
  const [coordinates, setCoordinates] = useState({ latitude: null, longitude: null });

  useEffect(() => {
    const allFieldsFilled = Object.values(form).every((field) => {
      if (typeof field === 'string') {
        return field.trim() !== ''; // For strings, ensure they are not empty
      }
      return field !== null && field !== undefined; // Ensure non-string values are not null or undefined
    });
    setIsFormComplete(allFieldsFilled);
  }, [form]);
  

  useEffect(() => {
    // Request location permission when the component mounts
    getLocation(); 
    requestLocationPermission();  

  }, []);

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'We need your location to complete the registration.',
            buttonPositive: 'OK',
          }
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setLocationGranted(true);
          getLocation();
        } else {
          setLocationGranted(false);
          Alert.alert('Permission Denied', 'Location permission is required to proceed.');
        }
      } else {
        Geolocation.requestAuthorization('whenInUse').then((status) => {
          if (status === 'granted') {
            setLocationGranted(true);
            getLocation();
            
          } else {
            setLocationGranted(false);
            Alert.alert('Permission Denied', 'Location permission is required to proceed.');
          }
        });
      }
    } catch (error) {
      console.error('Permission Error:', error);
    }
  };

  const getLocation = () => {
    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const coords = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
          setCoordinates(coords);
          console.log('Location:', coords);
          resolve(coords);
        },
        (error) => {
          console.error('Location Error:', error);
          Alert.alert('Error', 'Unable to fetch location. Please try again.');
          reject(error);
        },
        { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
      );
    });
  };
  

  const handleCarImageUpload = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        quality: 1, // Image quality (1 = best)
      });
  
      if (result.didCancel) {
        console.log('Image picker canceled');
        return;
      }
  
      if (result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
  
        setForm((prevForm) => ({
          ...prevForm,
          carImg: {
            name: selectedImage.fileName || selectedImage.uri.split('/').pop(),
            type: selectedImage.type,
            uri: selectedImage.uri,
          },
        }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'An error occurred while selecting the image. Please try again.');
    }
  };


  const handleChange = (key, value) => {
    setForm((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = async () => {
    if (!isFormComplete) {
      Alert.alert('Error', 'Please fill all fields before proceeding.');
      return;
    }
  
    try {
      // Ensure location permission is granted
      if (!locationGranted) {
        await requestLocationPermission();
        if (!locationGranted) {
          return; // Stop if permission is still not granted
        }
      }
  
      // Fetch location
      const location = await getLocation();
      if (!location || !location.latitude || !location.longitude) {
        Alert.alert(
          'Error',
          'Unable to fetch location. Please try again or ensure location is enabled.'
        );
        return;
      }
  
      // Consolidate car details into an object
      const carDetails = { ...form };
  
      // Prepare the full payload
      const payload = {
        ...allData,
        pricePerKm: 10000,
        carDetails,
        coordinates: location, // Use fetched location
      };
  
      // Show the loader
      setLoader(true);
  
      // Call the API
      const response = await CompleteNewDriverRegistration(payload);
      console.log('API Response:', response);
  
      if (response.success) {
        Toast.show({
          type: 'success',
          text1: 'Registration Complete',
          text2: 'Your details have been successfully submitted.',
        });
  
        // Navigate to the next screen
        navigation.navigate('NextScreen', payload);
      } else {
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: response.message || 'Unable to complete registration. Please try again.',
        });
      }
    } catch (error) {
      console.error('API Error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Something went wrong. Please try again.',
      });
    } finally {
      // Hide the loader
      setLoader(false);
    }
  };
  

  return (
    <GradientBackground>
    {/* Header Section */}
    <Spinner visible={loader} />
    <ScrollView>
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>
      <Text style={styles.helpText}>Help</Text>
    </View>
  
    {/* Title */}
    <Text style={styles.title}>Add car details</Text>
  
    {/* Form Fields */}
    <View style={styles.formContainer}>
      <Text style={styles.label}>Registration Number</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Registration Number"
        value={form.registrationNumber}
        onChangeText={(text) => handleChange('registrationNumber', text)}
      />
  
      <Text style={styles.label}>Year</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter year"
        value={form.year}
        onChangeText={(text) => handleChange('year', text)}
        keyboardType="numeric"
      />
  
      <Text style={styles.label}>Model</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter model"
        value={form.model}
        onChangeText={(text) => handleChange('model', text)}
      />
  
      <Text style={styles.label}>Color</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter color"
        value={form.color}
        onChangeText={(text) => handleChange('color', text)}
      />

<Text style={styles.label}>Price Per Km</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter price per km"
        value={form.color}
        onChangeText={(text) => handleChange('pricePerKm', text)}
      />


  
      <Text style={styles.label}>Number of Seats</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Number of Seats"
        value={form.noOfSeats}
        onChangeText={(text) => handleChange('noOfSeats', text)}
        keyboardType="numeric"
      />
  
{/* Car Image Upload */}
<Text style={styles.label}>Car Image</Text>
<TouchableOpacity style={styles.uploadButton} onPress={handleCarImageUpload}>
  {form.carImg && form.carImg.uri ? (
    <Image
      source={{ uri: form.carImg.uri }}
      style={styles.carImagePreview} // Add styling for the image preview
      resizeMode="contain"
    />
  ) : (
    <Text style={styles.uploadButtonText}>Upload Car Image</Text>
  )}
</TouchableOpacity>


    </View>
  
    {/* Submit Button */}
    <TouchableOpacity
      style={[
        styles.submitButton,
        isFormComplete ? styles.submitButtonEnabled : styles.submitButtonDisabled,
      ]}
      onPress={handleSubmit}
      disabled={!isFormComplete} // Disable the button when the form is incomplete
    >
      <Text style={styles.submitButtonText}>Submit</Text>
    </TouchableOpacity>
    </ScrollView>
  </GradientBackground>
  
  );
};
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  title: {
  ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 16,
    color: COLORS.black,
  },
  formContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  label: {
     ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
  },
  input: {
    height: 48,
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.black,
    backgroundColor: COLORS.white,
  },
  submitButton: {
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonEnabled: {
    backgroundColor: COLORS.primary,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.lightGray,
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: FONTS.bold,
    color: COLORS.white,
  },
  carImagePreview: {
    width: 150, // Adjust width as needed
    height: 150, // Adjust height as needed
    borderRadius: 10,
    marginVertical: 10,
  },
  uploadButton: {
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 10,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  uploadButtonText: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  
});

export default PCarFormDetails;
