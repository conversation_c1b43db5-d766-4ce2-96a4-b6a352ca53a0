

import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const CheckConsent = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from the previous screen
  const updatedPayload = route.params || {};

  const handleNext = () => {
    // Pass all data to the next screen
    navigation.navigate('AddYourCar', {updatedPayload});
  };

  console.log(JSON.stringify(updatedPayload,null,2));
  
  return (
    <GradientBackground>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
         <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Backgroud check consent</Text>

      {/* Illustration */}
      <Image
        source={images.id} // Replace with your placeholder image
        style={styles.illustration}
        resizeMode="contain"
      />

      {/* Description */}
      <Text style={styles.description}>
        Enable us to protect everyone
      </Text>

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
          <Text style={styles.primaryButtonText}>Check</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handleNext}  
        >
          <Text style={styles.secondaryButtonText}>Skip</Text>
        </TouchableOpacity>
      </View>
    </GradientBackground>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 20
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
    ...FONTS.body2,
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal: 20,
    marginTop: 100,
    color: '#000',
  },
  illustration: {
    marginTop: 10,
    width: '80%',
    height: 100,
    alignSelf: 'center',
  },
  description: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
    marginVertical: 10,
  },
  buttonContainer: {
    marginVertical: 30,
    marginHorizontal: 20

  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButtonText: {
    color: '#FFF',
    ...FONTS.h3

  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor: COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: COLORS.primary,
    ...FONTS.h3
  },
});

export default CheckConsent;
