

import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Platform,
  ScrollView,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const TAC = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from the previous screen
  const updatedPayload = route.params || {};

  const handleNext = () => {
    // Pass all data to the next screen
    navigation.goBack();
    // navigation.navigate('AddYourCar', {updatedPayload});
  };

  console.log(JSON.stringify(updatedPayload,null,2));
  
  return (
    <GradientBackground>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
         <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}></Text>

      

      <ScrollView contentContainerStyle={styles.content}>
    <Text style={styles.title}>
       Ridefuze Terms and Conditions 
    </Text>

    <Text style={styles.bodyText}>
       1. Introduction 
      {'\n'}
      Welcome to Ridefuze! These Terms and Conditions ("Terms") govern your access to and use of the Ridefuze application and services ("Services"). By accessing or using our Services, you agree to comply with and be bound by these Terms. If you do not agree with these Terms, please do not use our Services.
    </Text>

    <Text style={styles.bodyText}>
       2. Definitions 
      {'\n'}
      -  "Ridefuze," "we," "us,"  or  "our"  refers to Ridefuze, a company registered in the State of Minnesota.
      {'\n'}
      -  "User," "you,"  or  "your"  refers to any individual accessing or using our Services.
      {'\n'}
      -  "Driver"  refers to individuals providing transportation services through our platform.
      {'\n'}
      -  "Rider"  refers to individuals requesting and receiving transportation services through our platform.
    </Text>

    <Text style={styles.bodyText}>
       3. Eligibility 
      {'\n'}
      To use Ridefuze, you must:
      {'\n'}
      - Be at least 16 years of age.
      {'\n'}
      - Have the legal capacity to enter into a binding contract.
      {'\n'}
      - Comply with all applicable laws and regulations, including those of the State of Minnesota.
    </Text>

    <Text style={styles.bodyText}>
       4. User Accounts 
      {'\n'}
      To access our Services, you must create an account by providing accurate and complete information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. Notify us immediately of any unauthorized use of your account.
    </Text>

    <Text style={styles.bodyText}>
       5. Services 
      {'\n'}
      Ridefuze connects Riders with Drivers for transportation services. We act as an intermediary platform and do not provide transportation services ourselves. Drivers are independent contractors, not employees of Ridefuze.
    </Text>

    <Text style={styles.bodyText}>
       6. User Conduct 
      {'\n'}
      When using our Services, you agree to:
      {'\n'}
      - Use the Services only for lawful purposes.
      {'\n'}
      - Not engage in any behavior that is harmful, threatening, or harassing.
      {'\n'}
      - Not interfere with the proper functioning of the Services.
    </Text>

    <Text style={styles.bodyText}>
       7. Payments 
      {'\n'}
      Riders agree to pay the fare displayed at the end of each ride, which includes applicable taxes and fees. Payments are processed through our third-party payment processor. By providing your payment information, you authorize Ridefuze to charge the applicable fees to your payment method.
    </Text>

    <Text style={styles.bodyText}>
       8. Cancellation Policy 
      {'\n'}
      -  Riders:  You may cancel a ride request at any time before a Driver accepts it without charge. If you cancel after a Driver has accepted, a cancellation fee may apply.
      {'\n'}
      -  Drivers:  Repeated cancellations after accepting ride requests may result in account suspension or termination.
    </Text>

    <Text style={styles.bodyText}>
       9. Disclaimers 
      {'\n'}
      -  Service Availability:  We strive to keep our Services operational but do not guarantee uninterrupted access.
      {'\n'}
      -  Third-Party Content:  Our Services may include links to third-party websites or services. We are not responsible for their content or practices.
    </Text>

    <Text style={styles.bodyText}>
       10. Limitation of Liability 
      {'\n'}
      To the fullest extent permitted by law, Ridefuze shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses, resulting from:
      {'\n'}
      - Your use of or inability to use the Services.
      {'\n'}
      - Any unauthorized access to or use of our servers and/or any personal information stored therein.
      {'\n'}
      - Any interruption or cessation of transmission to or from the Services.
      {'\n'}
      - Any bugs, viruses, trojan horses, or the like that may be transmitted to or through our Services by any third party.
      {'\n'}
      - Any errors or omissions in any content or for any loss or damage incurred as a result of the use of any content posted, emailed, transmitted, or otherwise made available through the Services.
    </Text>

    <Text style={styles.bodyText}>
       12. Arbitration Agreement 
      {'\n'}
       a. Agreement to Arbitrate:  You and Ridefuze agree that any dispute, claim, or controversy arising out of or relating to these Terms or the use of the Services shall be resolved through binding arbitration, rather than in court, except for matters that may be taken to small claims court.
      {'\n'}
       b. Arbitration Procedures:  The Federal Arbitration Act and federal arbitration law apply to this agreement. Arbitration shall be conducted by the American Arbitration Association (AAA) under its rules, including the AAA's Consumer Arbitration Rules. The arbitration shall take place in the State of Minnesota.
      {'\n'}
       c. Class Action Waiver:  You and Ridefuze agree that any arbitration shall be conducted in an individual capacity only and not as a class, representative, or consolidated action. The arbitrator may not consolidate more than one person's claims or preside over any form of a representative or class proceeding.
      {'\n'}
       d. Severability:  If any provision of this Arbitration Agreement is found unenforceable, the unenforceable provision shall be severed, and the remaining arbitration terms shall be enforced.
    </Text>

    <Text style={styles.bodyText}>
       13. Governing Law 
      {'\n'}
      These Terms are governed by and construed in accordance with the laws of the State of Minnesota, without regard to its conflict of law principles.
    </Text>

    <Text style={styles.bodyText}>
       14. Changes to Terms 
      {'\n'}
      Ridefuze reserves the right to modify these Terms at any time. We will notify you of any changes by posting the new Terms on our platform. Your continued use of the Services after such changes constitutes your acceptance of the new Terms.
    </Text>

     <Text style={styles.bodyText}>
             15. Contact Us 
            {'\n'}
            If you have any questions about these Terms, please contact us at:
            {'\n'}
            Ridefuze
            {'\n'}
            2323 16th Ave S, unit 306, Moorhead, MN 56560
            {'\n'}
            [<EMAIL>]
            {'\n'}
            +16124967591
          </Text>

    <Text style={styles.bodyText}>
       16. Entire Agreement 
      {'\n'}
      These Terms constitute the entire agreement between you and Ridefuze regarding the use of our Services and supersede any prior agreements between you and Ridefuze.
    </Text>

    <Text style={styles.bodyText}>
      By using the Ridefuze platform, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.
    </Text>
</ScrollView>

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
          <Text style={styles.primaryButtonText}>I agree</Text>
        </TouchableOpacity>

        {/* <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handleNext}  
        >
          <Text style={styles.secondaryButtonText}>Skip</Text>
        </TouchableOpacity> */}
      </View>
    </GradientBackground>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal: 20
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
    ...FONTS.body2,
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal: 20,
    marginTop: 10,
    color: '#000',
  },
  illustration: {
    marginTop: 10,
    width: '80%',
    height: 100,
    alignSelf: 'center',
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 80, // Space for the footer button
  },
   bodyText: {
 ...FONTS.body3,
 textAlign: 'justify',
    color: '#343A40',
    marginBottom: 15,
  },
  brandName: {
    color: '#007AFF', // Blue color for the brand name
  },
  lastUpdated: {
    fontSize: 14,
    color: '#6C757D',
    marginBottom: 20,
  },

  description: {
    ...FONTS.body3,
    textAlign: 'justify',
    color: '#6C757D',
    marginVertical: 10,
    marginHorizontal: 20
  },
  buttonContainer: {
    marginVertical: 30,
    marginHorizontal: 20

  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButtonText: {
    color: '#FFF',
    ...FONTS.h3

  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor: COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: COLORS.primary,
    ...FONTS.h3
  },
});

export default TAC;
