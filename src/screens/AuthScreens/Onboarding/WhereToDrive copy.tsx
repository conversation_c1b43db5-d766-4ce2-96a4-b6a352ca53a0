import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Image,
  ActivityIndicator,
} from 'react-native';
import { COLORS, icons } from '../../../constants';
import GradientBackground from '../../../components/shared/GradientBackground';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';

// Define interfaces for type safety
interface RouteParams {
  firstName: string;
  lastName: string;
  email: string;
  mobileNumber: string;
}

interface CityPrediction {
  id: string;
  description: string;
}

// Define navigation types
type RootStackParamList = {
  TermOfService: RouteParams & {
    opreatingCity: string;
    coordinates: [number, number];
  };
}

type NavigationProp = StackNavigationProp<RootStackParamList>;

const GOOGLE_API_KEY = 'AIzaSyC0VJu9ttMNPOWP-vxTuXtzAaR932hdKUc';

const WhereToDrive = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();

  // Retrieve data from previous screen with default values
  const {
    firstName = '',
    lastName = '',
    email = '',
    mobileNumber = ''
  } = route.params || {};

  const [query, setQuery] = useState<string>('');
  const [suggestions, setSuggestions] = useState<CityPrediction[]>([]);
  const [opreatingCity, setopreatingCity] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);

  const fetchPlaceDetails = async (placeId: string): Promise<[number, number] | null> => {
    const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${GOOGLE_API_KEY}`;
    try {
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === "OK") {
        const location = data.result.geometry.location; // Extract lat/lng
        return [location.lng, location.lat]; // Longitude first
      } else {
        console.error("Google Place Details API Error:", data.status);
        return null;
      }
    } catch (error) {
      console.error("Error fetching place details:", error);
      return null;
    }
  };

  const handleInputChange = async (text: string): Promise<void> => {
    setQuery(text);
    if (text.length > 2) {
      setLoading(true);
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&types=(cities)&key=${GOOGLE_API_KEY}`
        );
        const data = await response.json();
        console.log('Google Places API Response:', data);

        if (data.status === 'OK') {
          const predictions = data.predictions.map((prediction: any) => ({
            id: prediction.place_id,
            description: prediction.description,
          }));
          setSuggestions(predictions);
        } else {
          console.error('Google Places API Error:', data.status);
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Google Places API Error:', error);
      } finally {
        setLoading(false);
      }
    } else {
      setSuggestions([]);
    }
  };

  const handleCitySelect = async (city: CityPrediction): Promise<void> => {
    setQuery(city.description);
    setopreatingCity(city.description);
    setSuggestions([]);

    const coordinates = await fetchPlaceDetails(city.id); // Fetch lat/lng

    if (coordinates) {
      console.log("📍 Selected City Coordinates:", coordinates, firstName,
        lastName,
        email,
        city.description,
        mobileNumber,
        coordinates);

      // Pass data to the next screen
      navigation.navigate("TermOfService", {
        firstName,
        lastName,
        email,
        opreatingCity: city.description,
        mobileNumber,
        coordinates, // Pass the [longitude, latitude] array
      });
    }
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <TouchableOpacity>
          <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Where do you plan to drive?</Text>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Image source={icons.Search} style={styles.icon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Your city"
          value={query}
          onChangeText={handleInputChange}
        />
        {loading && <ActivityIndicator size="small" color={COLORS.primary} />}
      </View>

      {/* Suggestions List */}
      {suggestions.length > 0 && (
        <FlatList
          data={suggestions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={styles.suggestionItem}
              onPress={() => handleCitySelect(item)}
            >
              <Text style={styles.suggestionText}>{item.description}</Text>
            </TouchableOpacity>
          )}
          style={[
            styles.suggestionsList,
            {
              height: suggestions.length === 1 ? 50 : 150,
            },
          ]}
        />
      )}

      {/* Subtitle */}
      <Text style={styles.subtitle}>
        This will be based on the requirement of where you plan to drive
      </Text>

      {/* Next Button */}
      <TouchableOpacity
        onPress={() => {
          // Only proceed if a city has been selected
          if (opreatingCity && suggestions.length > 0) {
            // Find the selected city in suggestions
            const selectedCity = suggestions.find(city => city.description === opreatingCity);
            if (selectedCity) {
              handleCitySelect(selectedCity);
            }
          }
        }}
        style={[styles.nextButton, { opacity: opreatingCity ? 1 : 0.5 }]}
        disabled={!opreatingCity}
      >
        <Image source={icons.ArrowIcon} style={styles.icon} />
      </TouchableOpacity>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // alignItems: 'center',
  },
  header: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: 20,
    color: '#000',
    marginBottom: 10,
  },
  searchContainer: {
    marginHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    fontWeight: '500',
    paddingLeft: 10
  },
  suggestionsList: {
    marginHorizontal: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    minHeight: 50,
    maxHeight: 300, // Limit the height of the suggestion
  },
  suggestionItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#000',
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
    color: '#555',
    margin: 25,
  },
  nextButton: {
    marginTop: 50,
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  helpText: {
    fontSize: 14,
    fontWeight: '400',
    color: COLORS.black,
  },
});

export default WhereToDrive;
