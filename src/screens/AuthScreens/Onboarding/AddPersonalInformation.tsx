import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Platform,
  ScrollView,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {COLORS, icons} from '../../../constants'; // Ensure your constants are imported
import KeyboardAvoidingWrapper from '../../../components/shared/KeyboardAvoidingWrapper';

// Define interface for form data
interface FormData {
  firstName: string;
  lastName: string;
  middleName: string;
  dob: string;
  state: string;
  zipcode: string;
  driverLincenseNumber: string;
  driverLincenseState: string;
  signature: string;
  userConsent: boolean;
  [key: string]: string | boolean; // Allow dynamic properties
}

const AddPersonalInformation = () => {
  const navigation = useNavigation<any>();
  const route = useRoute<any>();
  const payload = route.params || {}; // Default payload is route params

  const statesCodeAndName = [
    {code: 'AL', name: 'Alabama'},
    {code: 'AK', name: 'Alaska'},
    {code: 'AZ', name: 'Arizona'},
    {code: 'AR', name: 'Arkansas'},
    {code: 'CA', name: 'California'},
    {code: 'CO', name: 'Colorado'},
    {code: 'CT', name: 'Connecticut'},
    {code: 'DE', name: 'Delaware'},
    {code: 'FL', name: 'Florida'},
    {code: 'GA', name: 'Georgia'},
    {code: 'HI', name: 'Hawaii'},
    {code: 'ID', name: 'Idaho'},
    {code: 'IL', name: 'Illinois'},
    {code: 'IN', name: 'Indiana'},
    {code: 'IA', name: 'Iowa'},
    {code: 'KS', name: 'Kansas'},
    {code: 'KY', name: 'Kentucky'},
    {code: 'LA', name: 'Louisiana'},
    {code: 'ME', name: 'Maine'},
    {code: 'MD', name: 'Maryland'},
    {code: 'MA', name: 'Massachusetts'},
    {code: 'MI', name: 'Michigan'},
    {code: 'MN', name: 'Minnesota'},
    {code: 'MS', name: 'Mississippi'},
    {code: 'MO', name: 'Missouri'},
    {code: 'MT', name: 'Montana'},
    {code: 'NE', name: 'Nebraska'},
    {code: 'NV', name: 'Nevada'},
    {code: 'NH', name: 'New Hampshire'},
    {code: 'NJ', name: 'New Jersey'},
    {code: 'NM', name: 'New Mexico'},
    {code: 'NY', name: 'New York'},
    {code: 'NC', name: 'North Carolina'},
    {code: 'ND', name: 'North Dakota'},
    {code: 'OH', name: 'Ohio'},
    {code: 'OK', name: 'Oklahoma'},
    {code: 'OR', name: 'Oregon'},
    {code: 'PA', name: 'Pennsylvania'},
    {code: 'RI', name: 'Rhode Island'},
    {code: 'SC', name: 'South Carolina'},
    {code: 'SD', name: 'South Dakota'},
    {code: 'TN', name: 'Tennessee'},
    {code: 'TX', name: 'Texas'},
    {code: 'UT', name: 'Utah'},
    {code: 'VT', name: 'Vermont'},
    {code: 'VA', name: 'Virginia'},
    {code: 'WA', name: 'Washington'},
    {code: 'WV', name: 'West Virginia'},
    {code: 'WI', name: 'Wisconsin'},
    {code: 'WY', name: 'Wyoming'},
    {code: 'DC', name: 'Washington, D.C.'},
  ];

  const [formData, setFormData] = useState<FormData & {userConsent: boolean}>({
    dob: '',
    state: '',
    zipcode: '',
    driverLincenseNumber: '',
    driverLincenseState: '',
    userConsent: false,
    signature: '',
    middleName: '',
    lastName: '',
    firstName: '',
  });

  const [stateDropdownOpen, setStateDropdownOpen] = useState(false);
  const [dlStateDropdownOpen, setDlStateDropdownOpen] = useState(false);

  // Handle input changes for text fields
  const handleInputChange = (name: string, value: string): void => {
    setFormData(prevData => ({
      ...prevData,
      [name]: value,
    }));
  };

  // Function to validate Date of Birth format (YYYY-MM-DD)
  const validateDOB = (dob: string): boolean => {
    const regex = /^(?:19|20)\d{2}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$/; // Regular Expression for YYYY-MM-DD
    return regex.test(dob);
  };

  // Handle the date input change
  const handleDobChange = (text: string): void => {
    // Only allow the date format YYYY-MM-DD (no more than 10 characters)
    if (text.length <= 10) {
      setFormData(prevData => ({
        ...prevData,
        dob: text,
      }));
    }
  };

  // Handle state selection
  const handleStateSelect = (stateCode: string): void => {
    setFormData(prevData => ({
      ...prevData,
      state: stateCode, // Only store the state code in formData
    }));
    setStateDropdownOpen(false);
  };

  // Handle driver license state selection
  const handleDriverLicenseStateSelect = (stateCode: string): void => {
    setFormData(prevData => ({
      ...prevData,
      driverLincenseState: stateCode, // Only store the state code in formData
    }));
    setDlStateDropdownOpen(false);
  };

  // Toggle dropdown visibility and handle scrolling
  const toggleStateDropdown = () => {
    if (!stateDropdownOpen) {
      // Close other dropdown if open
      setDlStateDropdownOpen(false);
    }
    setStateDropdownOpen(!stateDropdownOpen);
  };

  const toggleDlStateDropdown = () => {
    if (!dlStateDropdownOpen) {
      // Close other dropdown if open
      setStateDropdownOpen(false);
    }
    setDlStateDropdownOpen(!dlStateDropdownOpen);
  };

  // Handle user consent checkbox
  const handleUserConsent = () => {
    setFormData(prevData => ({
      ...prevData,
      userConsent: !prevData.userConsent,
    }));
  };

  // Validate input fields before submission
  const validateForm = () => {
    const {
      dob,
      state,
      zipcode,
      driverLincenseNumber,
      driverLincenseState,
      userConsent,
      firstName,
      lastName,
    } = formData;

    if (!dob || !validateDOB(dob)) {
      Alert.alert(
        'Invalid Date',
        'Please enter a valid Date of Birth (YYYY-MM-DD).',
      );
      return false;
    }

    if (
      !state ||
      !zipcode ||
      !driverLincenseNumber ||
      !driverLincenseState ||
      !userConsent ||
      !firstName ||
      !lastName
    ) {
      Alert.alert('Error', 'Please fill all the required fields.');
      return false;
    }
    return true;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      const updatedPayload = {
        ...payload,
        ...formData, // This includes state and driverLincenseState as codes only
      };
      console.log(
        'Payload to be sent to next screen:',
        JSON.stringify(updatedPayload, null, 2),
      );
      navigation.navigate('AddSocialSecurity', updatedPayload);
    }
  };

  // Get state name from code
  const getStateName = (stateCode: string): string => {
    const state = statesCodeAndName.find(s => s.code === stateCode);
    return state ? state.name : '';
  };

  return (
    <KeyboardAvoidingWrapper style={styles.container}>
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>Add Personal Information</Text>
        <Text></Text>
      </View>

      {/* First Name */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>First Name</Text>
        <TextInput
          style={styles.input}
          value={formData.firstName}
          placeholder="Enter First Name"
          placeholderTextColor="#A9A9A9"
          onChangeText={value => handleInputChange('firstName', value)}
        />
      </View>
      {/* Middle Name */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Middle Name</Text>
        <TextInput
          style={styles.input}
          value={formData.middleName}
          placeholder="Enter Middle Name"
          placeholderTextColor="#A9A9A9"
          onChangeText={value => handleInputChange('middleName', value)}
        />
      </View>
      {/* Last Name */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Last Name</Text>
        <TextInput
          style={styles.input}
          value={formData.lastName}
          placeholder="Enter Last Name"
          placeholderTextColor="#A9A9A9"
          onChangeText={value => handleInputChange('lastName', value)}
        />
      </View>

      {/* Date of Birth */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Date of Birth</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter Date of Birth (YYYY-MM-DD)"
          placeholderTextColor="#A9A9A9"
          value={formData.dob}
          onChangeText={handleDobChange}
          keyboardType="numeric"
          maxLength={10}
        />
      </View>

      {/* State - Dropdown */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>State</Text>
        <TouchableOpacity
          style={styles.dropdownButton}
          onPress={toggleStateDropdown}>
          <Text
            style={
              formData.state
                ? styles.dropdownSelectedText
                : styles.dropdownPlaceholderText
            }>
            {formData.state
              ? `${getStateName(formData.state)} (${formData.state})`
              : 'Select State'}
          </Text>
          <Image
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/512/32/32195.png',
            }}
            style={[
              styles.dropdownIcon,
              stateDropdownOpen && styles.dropdownIconRotated,
            ]}
          />
        </TouchableOpacity>

        {stateDropdownOpen && (
          <View style={styles.dropdownListContainer}>
            <ScrollView
              nestedScrollEnabled={true}
              style={styles.dropdownList}
              keyboardShouldPersistTaps="handled">
              {statesCodeAndName.map((state, index) => (
                <TouchableOpacity
                  key={state.code}
                  style={[
                    styles.dropdownItem,
                    index === statesCodeAndName.length - 1
                      ? null
                      : styles.dropdownItemBorder,
                  ]}
                  onPress={() => handleStateSelect(state.code)}>
                  <Text style={styles.dropdownItemText}>
                    {state.name} ({state.code})
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>

      {/* Zip Code */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Zip Code</Text>
        <TextInput
          style={styles.input}
          keyboardType="numeric"
          value={formData.zipcode}
          placeholder="Enter Zip Code"
          placeholderTextColor="#A9A9A9"
          maxLength={5}
          onChangeText={value => handleInputChange('zipcode', value)}
        />
      </View>

      {/* Driver's License Number */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Driver's License Number</Text>
        <TextInput
          style={styles.input}
          value={formData.driverLincenseNumber}
          placeholder="Enter Driver's License Number"
          placeholderTextColor="#A9A9A9"
          onChangeText={value =>
            handleInputChange('driverLincenseNumber', value)
          }
        />
      </View>

      {/* Driver's License State - Dropdown */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Driver's License State</Text>
        <TouchableOpacity
          style={styles.dropdownButton}
          onPress={toggleDlStateDropdown}>
          <Text
            style={
              formData.driverLincenseState
                ? styles.dropdownSelectedText
                : styles.dropdownPlaceholderText
            }>
            {formData.driverLincenseState
              ? formData.driverLincenseState
              : "Select Driver's License State"}
          </Text>
          <Image
            source={{
              uri: 'https://cdn-icons-png.flaticon.com/512/32/32195.png',
            }}
            style={[
              styles.dropdownIcon,
              dlStateDropdownOpen && styles.dropdownIconRotated,
            ]}
          />
        </TouchableOpacity>

        {dlStateDropdownOpen && (
          <View style={styles.dropdownListContainer}>
            <ScrollView
              nestedScrollEnabled={true}
              style={styles.dropdownList}
              keyboardShouldPersistTaps="handled">
              {statesCodeAndName.map((state, index) => (
                <TouchableOpacity
                  key={state.code}
                  style={[
                    styles.dropdownItem,
                    index === statesCodeAndName.length - 1
                      ? null
                      : styles.dropdownItemBorder,
                  ]}
                  onPress={() => handleDriverLicenseStateSelect(state.code)}>
                  <Text style={styles.dropdownItemText}>
                    {state.name} ({state.code})
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>

      {/* User Consent */}
      <View style={styles.checkboxContainer}>
        <TouchableOpacity style={styles.checkbox} onPress={handleUserConsent}>
          <View
            style={[
              styles.checkboxBox,
              formData.userConsent ? styles.checked : styles.unchecked,
            ]}>
            {formData.userConsent && (
              <Text style={styles.checkmarkText}>✔</Text>
            )}
          </View>
          <View>
            <Text style={styles.checkboxText}>
              I agree to the terms and conditions
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      <View>
        <Text
          style={styles.linkText}
          onPress={() => navigation.navigate('TAC')}>
          Click here to read the Terms and Conditions
        </Text>
      </View>

      {/* Submit Button */}
      <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
        <Text style={styles.submitButtonText}>Submit</Text>
      </TouchableOpacity>
    </KeyboardAvoidingWrapper>
  );
};

const styles = StyleSheet.create({
  linkText: {
    fontSize: 13,
    marginLeft: 25,
    color: '#0645AD', // A common link color
    textDecorationLine: 'underline', // Underline to indicate it's clickable
  },
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F2FCFC',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  icon: {
    width: 15,
    height: 15,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 15,
    fontWeight: '500',
  },
  inputContainer: {
    marginVertical: 10,
    zIndex: 1, // Ensure dropdown shows over other elements
  },
  label: {
    fontSize: 13,
    fontWeight: '500',
    marginBottom: 5,
  },
  errorText: {
    color: 'red',
    fontSize: 12,
    marginTop: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.grey,
    padding: 10,
    borderRadius: 8,
    fontSize: 15,
  },
  // Dropdown styles
  dropdownButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.grey,
    padding: 10,
    borderRadius: 8,
    height: 45,
  },
  dropdownPlaceholderText: {
    color: '#A9A9A9',
    fontSize: 15,
  },
  dropdownSelectedText: {
    color: '#000',
    fontSize: 15,
  },
  dropdownIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
  },
  dropdownIconRotated: {
    transform: [{rotate: '180deg'}],
  },
  dropdownListContainer: {
    position: 'relative',
    width: '100%',
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 8,
    marginTop: 5,
    zIndex: 2,
    elevation: 3, // For Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  dropdownList: {
    maxHeight: 200,
  },
  dropdownItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemBorder: {
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dropdownItemText: {
    fontSize: 14,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 1,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  checkboxBox: {
    width: 15,
    height: 15,
    borderWidth: 1,
    borderRadius: 4,
    marginRight: 10,
    borderColor: '#000', // Black border for both checked and unchecked
    alignItems: 'center',
    justifyContent: 'center',
  },
  checked: {
    borderColor: COLORS.primary, // Optional: change border color if checked
  },
  unchecked: {
    // No specific style needed here
  },
  checkmarkText: {
    fontSize: 10,
    color: COLORS.primary, // Color of the check mark
  },
  checkboxText: {
    fontSize: 14,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 50,
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
});

export default AddPersonalInformation;
