// 
import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
  Platform,
} from "react-native";
import { useNavigation, useRoute } from "@react-navigation/native";
import { COLORS, icons } from "../../../constants";

const AddDigitalSignature = () => {
  const navigation = useNavigation();
  const route = useRoute();

  const payload = route.params || {}; // Get first name and last name

  const [signature, setSignature] = useState("");
  const [userConsent, setUserConsent] = useState(false);

  // Handle input change for digital signature
  const handleSignatureChange = (text) => {
    setSignature(text);
  };

  // Handle custom checkbox for user consent
  const handleUserConsent = () => {
    setUserConsent(!userConsent); // Toggle the user consent checkbox
  };

  // Validate input before submission
  const validateForm = () => {
    if (!signature) {
      Alert.alert("Validation Error", "Signature cannot be empty.");
      return false;
    }

    if (!userConsent) {
      Alert.alert("User Consent", "Please agree to the terms.");
      return false;
    }

    return true;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      // const payload = {
      //   ...updatedPayload, // Spread the existing payload
      //   signature, // Add the signature
      //   userConsent, // Add user consent
      // };

      console.log("Updated Payload to be sent:", payload);

      // Navigate to the next screen with the updated payload
      navigation.navigate("AddYourCar", { ...payload, signature, userConsent });
    }
  };

  return (
    <View style={styles.container}>
      {/* Header Section */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text></Text>
        <TouchableOpacity>
          <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      <Text style={styles.title}>Add Digital Signature</Text>

      {/* Digital Signature */}
      <View style={styles.inputContainer}>
        <Text style={styles.label}>Digital Signature</Text>
        <TextInput
          // multiline
          // numberOfLines={5}

          style={styles.input}
          placeholder="Enter digital signature"
          value={signature}
          placeholderTextColor={COLORS.grey}
          onChangeText={handleSignatureChange}
        />
      </View>
      {/* Disclaimer */}
      <Text style={styles.disclaimer}>
        The signature is the exact name that was entered in the personal information
      </Text>

<View>
  <Text style={[styles.checkboxText, {color: 'green',fontSize:14}]}>
  A copy of the verification progress would be sent to email address provided
  </Text>
</View>
      {/* User Consent Checkbox (Custom) */}
      <View style={styles.checkboxContainer}>
        <TouchableOpacity
          style={styles.checkbox}
          onPress={handleUserConsent}
        >
          <View style={[styles.checkboxBox, userConsent ? styles.checked : styles.unchecked]}>
            {userConsent && <Text style={styles.checkmarkText}>✔</Text>}
          </View>
          <View>
            <Text style={styles.checkboxText}>
              I agree to the terms and conditions
            </Text>
          </View>


        </TouchableOpacity>

      </View>
      <View>
        <Text style={styles.linkText} onPress={() => navigation.navigate("TAC")}>
          Click here to read the Terms and Conditions
        </Text>
      </View>


      {/* Submit Button */}
      <TouchableOpacity style={styles.submitButton} onPress={handleSubmit}>
        <Text style={styles.submitButtonText}>Submit</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F2FCFC',
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 50,
    marginTop: 20,
  },
  linkText: {
    fontSize: 13,
    color: '#0645AD',
    textDecorationLine: 'underline',
    marginLeft: 30,
  },
  backText: {
    fontSize: 24,
    fontWeight: "bold",
  },
  icon: {
    width: 15,
    height: 15,
    resizeMode: "contain",
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 20,
  },
  helpText: {
    fontSize: 16,
    color: COLORS.primary,
  },
  inputContainer: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    // fontWeight: "500",
    marginBottom: 5,
  },
  input: {
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    padding: 10,
    borderRadius: 8,
    fontSize: 16,
    // height: 100,
    backgroundColor: COLORS.white,
  },
  checkboxContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 1,
  },
  checkbox: {
    flexDirection: "row",
    alignItems: "center",
  },
  checkboxBox: {
    width: 15,
    height: 15,
    borderWidth: 2,
    borderRadius: 4,
    marginRight: 10,
    borderColor: '#000',  // Black border for both checked and unchecked
    alignItems: 'center',
    justifyContent: 'center',
  },
  checked: {
    borderColor: COLORS.primary,  // Optional: change border color if checked
  },
  unchecked: {
    // No specific style needed here
  },
  checkmarkText: {
    fontSize: 10,
    color: COLORS.primary  // Color of the check mark
  },
  checkboxText: {
    fontSize: 14,
  },
  disclaimer: {
    fontSize: 13,
    color: "red",
    marginBottom: 20,
  },
  submitButton: {
    marginTop: 20,
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: "bold",
  },
});

export default AddDigitalSignature;
