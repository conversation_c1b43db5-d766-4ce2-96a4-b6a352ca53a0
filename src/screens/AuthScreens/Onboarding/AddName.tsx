import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Image,
  Platform,
} from 'react-native';
import { FONTS, icons } from '../../../constants';
import LinearGradient from 'react-native-linear-gradient';
import { useNavigation, useRoute } from '@react-navigation/native';

const AddName = () => {
  const navigation = useNavigation();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const buttonOpacity = useRef(new Animated.Value(0.3)).current;
 const route = useRoute();
  const { mobileNumber } = route.params || {}; // Get firstName and lastName from route params
console.log(mobileNumber, 'mobileNumber');

  useEffect(() => {
    // Check if both fields are filled
    const isButtonVisible = firstName.trim().length > 0 && lastName.trim().length > 0;

    // Animate button opacity based on input
    Animated.timing(buttonOpacity, {
      toValue: isButtonVisible ? 1 : 0.3, // Start faded (0.3) and go full (1)
      duration: 300, // Animation duration
      useNativeDriver: true,
    }).start();
  }, [firstName, lastName]);

  const handleNext = () => {
    if (firstName.trim() && lastName.trim()) {
      // Pass data to AddEmail screen
      navigation.navigate('AddEmail', { firstName, lastName ,mobileNumber});
      console.log(firstName, lastName, 'firstName, lastName',mobileNumber);
      
    }
  };

  return (
    <LinearGradient colors={['#D0E6FF', '#F2FCFC']} style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View>
        {/* Back Arrow */}
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>

        {/* Header Text */}
        <Text style={styles.headerText}>By the way, What’s your name</Text>
        <Text style={styles.subText}>
          Driver will confirm it's you upon arrival and for proper addressing
        </Text>

        {/* Input Fields */}
        <View style={styles.inputContainer}>
          <Text style={styles.label}>First name*</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your First name"
            placeholderTextColor="#BDBDBD"
            value={firstName}
            onChangeText={setFirstName}
          />
        </View>
        <View style={styles.inputContainer}>
          <Text style={styles.label}>Last name*</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your Last name"
            placeholderTextColor="#BDBDBD"
            value={lastName}
            onChangeText={setLastName}
          />
        </View>
      </View>

      {/* Next Button with Adjustable Opacity */}
      <Animated.View style={[styles.nextButton, { opacity: buttonOpacity }]}>
        <TouchableOpacity onPress={handleNext} >
          <Text style={styles.nextArrow}>→</Text>
        </TouchableOpacity>
      </Animated.View>
    </LinearGradient>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: 'space-between',
  },
  backButton: {
    margin: 20,

    position: 'relative',
    top: 10,
    left: 5,
  },
  backArrow: {
    fontSize: 24,
    color: '#000',
  },
  headerText: {
    ...FONTS.h2,
    color: '#000',
    textAlign: 'center',
    marginHorizontal:20,
    marginTop: 30,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  subText: {
    ...FONTS.body3,
    color: '#7E7E7E',
    textAlign: 'center',
    marginBottom: 30,
    marginHorizontal:20,

    marginTop: 20,
  },
  inputContainer: {
    marginBottom: 20,
    marginHorizontal:20
  },
  label: {
    ...FONTS.body3,
    color: '#000',
    marginBottom: 5,
  },
  input: {
    borderWidth: 1,
    borderColor: '#DADADA',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    height: 50,
    backgroundColor: '#FFF',
  },
  nextButton: {
    position: 'absolute',
    right:20,
    bottom: 20, // Adjusted to sit at the bottom center
    alignSelf: 'center', // Centered horizontally
    width: 50,
    height: 50,
    borderRadius: 30,
    backgroundColor: '#007BFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    // marginTop:100,

  },
  nextArrow: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFF',
  },
});

export default AddName;
