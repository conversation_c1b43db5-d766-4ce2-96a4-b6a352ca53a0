import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Platform,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { FONTS, images } from '../../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';

const TermsOfService = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve data from the previous screen
  const payload = route.params || {};
console.log(JSON.stringify(payload,null,2));

  const handleNext = () => {
    // Pass all data to the next screen
    navigation.navigate('AddDigitalSignature', payload);
  };

  return (
    <GradientBackground>
      {/* Header Image */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <Image
        source={images.Termofservice} // Replace with the path to your image
        style={styles.headerImage}
        resizeMode="cover"
      />

      <ScrollView contentContainerStyle={styles.content}>
       <Text style={styles.title}>
          Ridefuze Terms and Conditions 
       </Text>
   
       <Text style={styles.bodyText}>
          1. Introduction 
         {'\n'}
         Welcome to Ridefuze! These Terms and Conditions ("Terms") govern your access to and use of the Ridefuze application and services ("Services"). By accessing or using our Services, you agree to comply with and be bound by these Terms. If you do not agree with these Terms, please do not use our Services.
       </Text>
   
       <Text style={styles.bodyText}>
          2. Definitions 
         {'\n'}
         -  "Ridefuze," "we," "us,"  or  "our"  refers to Ridefuze, a company registered in the State of Minnesota.
         {'\n'}
         -  "User," "you,"  or  "your"  refers to any individual accessing or using our Services.
         {'\n'}
         -  "Driver"  refers to individuals providing transportation services through our platform.
         {'\n'}
         -  "Rider"  refers to individuals requesting and receiving transportation services through our platform.
       </Text>
   
       <Text style={styles.bodyText}>
          3. Eligibility 
         {'\n'}
         To use Ridefuze, you must:
         {'\n'}
         - Be at least 16 years of age.
         {'\n'}
         - Have the legal capacity to enter into a binding contract.
         {'\n'}
         - Comply with all applicable laws and regulations, including those of the State of Minnesota.
       </Text>
   
       <Text style={styles.bodyText}>
          4. User Accounts 
         {'\n'}
         To access our Services, you must create an account by providing accurate and complete information. You are responsible for maintaining the confidentiality of your account credentials and for all activities that occur under your account. Notify us immediately of any unauthorized use of your account.
       </Text>
   
       <Text style={styles.bodyText}>
          5. Services 
         {'\n'}
         Ridefuze connects Riders with Drivers for transportation services. We act as an intermediary platform and do not provide transportation services ourselves. Drivers are independent contractors, not employees of Ridefuze.
       </Text>
   
       <Text style={styles.bodyText}>
          6. User Conduct 
         {'\n'}
         When using our Services, you agree to:
         {'\n'}
         - Use the Services only for lawful purposes.
         {'\n'}
         - Not engage in any behavior that is harmful, threatening, or harassing.
         {'\n'}
         - Not interfere with the proper functioning of the Services.
       </Text>
   
       <Text style={styles.bodyText}>
          7. Payments 
         {'\n'}
         Riders agree to pay the fare displayed at the end of each ride, which includes applicable taxes and fees. Payments are processed through our third-party payment processor. By providing your payment information, you authorize Ridefuze to charge the applicable fees to your payment method.
       </Text>
   
       <Text style={styles.bodyText}>
          8. Cancellation Policy 
         {'\n'}
         -  Riders:  You may cancel a ride request at any time before a Driver accepts it without charge. If you cancel after a Driver has accepted, a cancellation fee may apply.
         {'\n'}
         -  Drivers:  Repeated cancellations after accepting ride requests may result in account suspension or termination.
       </Text>
   
       <Text style={styles.bodyText}>
          9. Disclaimers 
         {'\n'}
         -  Service Availability:  We strive to keep our Services operational but do not guarantee uninterrupted access.
         {'\n'}
         -  Third-Party Content:  Our Services may include links to third-party websites or services. We are not responsible for their content or practices.
       </Text>
   
       <Text style={styles.bodyText}>
          10. Limitation of Liability 
         {'\n'}
         To the fullest extent permitted by law, Ridefuze shall not be liable for any indirect, incidental, special, consequential, or punitive damages, or any loss of profits or revenues, whether incurred directly or indirectly, or any loss of data, use, goodwill, or other intangible losses, resulting from:
         {'\n'}
         - Your use of or inability to use the Services.
         {'\n'}
         - Any unauthorized access to or use of our servers and/or any personal information stored therein.
         {'\n'}
         - Any interruption or cessation of transmission to or from the Services.
         {'\n'}
         - Any bugs, viruses, trojan horses, or the like that may be transmitted to or through our Services by any third party.
         {'\n'}
         - Any errors or omissions in any content or for any loss or damage incurred as a result of the use of any content posted, emailed, transmitted, or otherwise made available through the Services.
       </Text>
   
       <Text style={styles.bodyText}>
          12. Arbitration Agreement 
         {'\n'}
          a. Agreement to Arbitrate:  You and Ridefuze agree that any dispute, claim, or controversy arising out of or relating to these Terms or the use of the Services shall be resolved through binding arbitration, rather than in court, except for matters that may be taken to small claims court.
         {'\n'}
          b. Arbitration Procedures:  The Federal Arbitration Act and federal arbitration law apply to this agreement. Arbitration shall be conducted by the American Arbitration Association (AAA) under its rules, including the AAA's Consumer Arbitration Rules. The arbitration shall take place in the State of Minnesota.
         {'\n'}
          c. Class Action Waiver:  You and Ridefuze agree that any arbitration shall be conducted in an individual capacity only and not as a class, representative, or consolidated action. The arbitrator may not consolidate more than one person's claims or preside over any form of a representative or class proceeding.
         {'\n'}
          d. Severability:  If any provision of this Arbitration Agreement is found unenforceable, the unenforceable provision shall be severed, and the remaining arbitration terms shall be enforced.
       </Text>
   
       <Text style={styles.bodyText}>
          13. Governing Law 
         {'\n'}
         These Terms are governed by and construed in accordance with the laws of the State of Minnesota, without regard to its conflict of law principles.
       </Text>
   
       <Text style={styles.bodyText}>
          14. Changes to Terms 
         {'\n'}
         Ridefuze reserves the right to modify these Terms at any time. We will notify you of any changes by posting the new Terms on our platform. Your continued use of the Services after such changes constitutes your acceptance of the new Terms.
       </Text>
   
       <Text style={styles.bodyText}>
          15. Contact Us 
         {'\n'}
         If you have any questions about these Terms, please contact us at:
         {'\n'}
         Ridefuze
         {'\n'}
         2323 16th Ave S, unit 306, Moorhead, MN 56560
         {'\n'}
         [<EMAIL>]
         {'\n'}
         +16124967591
       </Text>
   
       <Text style={styles.bodyText}>
          16. Entire Agreement 
         {'\n'}
         These Terms constitute the entire agreement between you and Ridefuze regarding the use of our Services and supersede any prior agreements between you and Ridefuze.
       </Text>
   
       <Text style={styles.bodyText}>
         By using the Ridefuze platform, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.
       </Text>
   </ScrollView>

      {/* Footer Button */}
      <TouchableOpacity onPress={handleNext} style={styles.footerButton}>
        <Text style={styles.footerButtonText}>I Agree →</Text>
      </TouchableOpacity>
    </GradientBackground>
  );
};



const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerImage: {
    width: '100%',
    height: 300,
    marginBottom: 10,
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 80,  
  },
  title: {
...FONTS.h1,
    color: '#000',
    marginBottom: 5,
  },
  brandName: {
    color: '#007AFF',  
  },
  lastUpdated: {
    fontSize: 14,
    color: '#6C757D',
    marginBottom: 20,
  },
  
   bodyText: {
 ...FONTS.body3,
 textAlign: 'justify',
    color: '#343A40',
    marginBottom: 15,
  },
  bulletsContainer: {
    marginBottom: 10,
  },
  bulletPoint: {
    fontSize: 16,
    lineHeight: 24,
    color: '#343A40',
    marginBottom: 5,
  },
  footerButton: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: '#007AFF',
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: 'center',
  },
  footerButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFF',
  },
});

export default TermsOfService;
