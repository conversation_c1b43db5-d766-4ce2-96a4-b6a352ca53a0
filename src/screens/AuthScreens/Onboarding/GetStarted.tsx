import React, { useRef } from 'react';
import { View, Text, Image, StyleSheet, TouchableOpacity, Platform } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import { COLORS, FONTS, images } from '../../../constants';
import GradientBackground from '../../../components/shared/GradientBackground';
import { useNavigation } from '@react-navigation/native';

const GetStarted = () => {
  const bottomSheetRef = useRef(null);
  const navigation = useNavigation();
  const handleOpenBottomSheet = () => {
    bottomSheetRef.current?.open(); // Open the BottomSheet
  };

  const handleCloseBottomSheet = () => {
    bottomSheetRef.current?.close(); // Close the BottomSheet
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Logo Section */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <Image
        source={images.favicon} // Replace with your logo URL
        style={styles.logo}
        resizeMode="contain"
      />
      {/* Title Section */}
      <Text style={styles.title}>Earn good money, meet great people</Text>
      {/* Button Section */}
      <TouchableOpacity style={styles.getStartedButton} onPress={handleOpenBottomSheet}>
        <Text style={styles.getStartedText}>Get started →</Text>
      </TouchableOpacity>
      {/* Footer Section */}
      <Text style={styles.footerText}>
        Need a ride?{' '}
        <Text style={styles.linkText}>Open a passenger app</Text>
      </Text>

      {/* BottomSheet Section */}
      <RBSheet
        ref={bottomSheetRef}
        closeOnDragDown={true}
        closeOnPressMask={true}
        height={300}
        customStyles={{
          wrapper: {
            backgroundColor: 'rgba(0,0,0,0.5)',
          },
          draggableIcon: {
            backgroundColor: '#000',
          },
          container: {
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            backgroundColor: COLORS.white,
          },
        }}
      >
        <View style={styles.bottomSheetContent}>
          <Text style={styles.bottomSheetTitle}>
           Continue registration with your Ridefuze passenger account
          </Text>
          <TouchableOpacity onPress={
            () => {
              navigation.navigate('PMobileNumber')
              bottomSheetRef.current?.close()
            }} style={styles.actionButton}>
            <Text style={styles.actionButtonText}> Yes, Continue</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={
            () => {
              navigation.navigate('MobileNumber')
              bottomSheetRef.current?.close()
            }}
            style={[styles.actionButton, styles.secondaryButton]}>
            <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>
              Create a new Driver account
            </Text>
          </TouchableOpacity>
        </View>
      </RBSheet>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: 200,
    height: 80,
    marginBottom: 30,
  },
  title: {
    ...FONTS.h2,
    marginHorizontal: 50,
    textAlign: 'center',
    color: '#222222',
    marginBottom: 40,
  },
  getStartedButton: {
    width: '90%',
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    marginTop: 60,
    paddingVertical: 15,
    paddingHorizontal: 30,
    marginBottom: 20,
  },
  getStartedText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.white,
    textAlign: 'center',
  },
  footerText: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
  },
  linkText: {
    color: COLORS.primary,
    ...FONTS.body3,
  },
  bottomSheetContent: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  bottomSheetTitle: {
    ...FONTS.body3, marginHorizontal: 40,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  actionButton: {
    width: '90%',
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    marginVertical: 10,
  },
  actionButtonText: {
    color: COLORS.white,
    textAlign: 'center',
    ...FONTS.h4

  },
  secondaryButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.light_grey,
  },
  secondaryButtonText: {
    color: COLORS.black,
    ...FONTS.h4
  },
});

export default GetStarted;
