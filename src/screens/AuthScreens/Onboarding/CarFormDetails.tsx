 
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  PermissionsAndroid,
  Platform,
  ScrollView,
} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import Grad<PERSON>Background from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { launchImageLibrary } from 'react-native-image-picker';

const CarFormDetails = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [loader, setLoader] = useState(false);

  const payload = route.params || {};
  console.log(payload, 'payloas');
  

  const [form, setForm] = useState({
    registrationNumber: '',
    year: '',
    model: '',
    color: '',
    noOfSeats: '',
    carImg: null,
    // carDetails:{}
  });

  const [isFormComplete, setIsFormComplete] = useState(false);

  const handleCarImageUpload = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        quality: 1,
      });

      if (result.didCancel) {
        console.log('Image picker canceled');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        setForm((prevForm) => ({
          ...prevForm,
          carImg: {
            name: selectedImage.fileName || selectedImage.uri.split('/').pop(),
            type: selectedImage.type,
            uri: selectedImage.uri,
          },
        }));
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'An error occurred while selecting the image. Please try again.');
    }
  };

  useEffect(() => {
    const allFieldsFilled = Object.values(form).every((field) => {
      if (typeof field === 'string') {
        return field.trim() !== '';
      }
      return field !== null && field !== undefined;
    });
    setIsFormComplete(allFieldsFilled);
  }, [form]);

 
  const handleChange = (key, value) => {
    setForm((prev) => ({ ...prev, [key]: value }));
  };
 
  const handleSubmit = async () => {
    if (!isFormComplete) {
      Alert.alert('Error', 'Please fill all fields before proceeding.');
      return;
    }
   
   navigation.navigate('WhereToDrive', { ...payload, carDetails: form });
  };
  
  
return (
    <GradientBackground>
    {/* Header Section */}
     {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                    {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
    <Spinner visible={loader} />
    <ScrollView>
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>
       <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
    </View>
  
    {/* Title */}
    <Text style={styles.title}>Add car details</Text>
  
    {/* Form Fields */}
    <View style={styles.formContainer}>
      <Text style={styles.label}>Registration Number</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Registration Number"
        placeholderTextColor={COLORS.grey}
        value={form.registrationNumber}
        onChangeText={(text) => handleChange('registrationNumber', text)}
      />
  
      <Text style={styles.label}>Year</Text>
      <TextInput
        style={styles.input}
        placeholderTextColor={COLORS.grey}
        placeholder="Enter year"
        value={form.year}
        onChangeText={(text) => handleChange('year', text)}
        keyboardType="numeric"
      />
  
      <Text style={styles.label}>Model</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter model"
        value={form.model}
        onChangeText={(text) => handleChange('model', text)}
      />
  
      <Text style={styles.label}>Color</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter color"
        value={form.color}
        placeholderTextColor={COLORS.grey}
        onChangeText={(text) => handleChange('color', text)}
      />

{/* <Text style={styles.label}>Price Per Km</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter price per km"
        value={pricePerKm}
        onChangeText={(text) => setPricePerKm('pricePerKm', text)}
      /> */}


  
      <Text style={styles.label}>Number of Seats</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Number of Seats"
        placeholderTextColor={COLORS.grey}
        value={form.noOfSeats}
        onChangeText={(text) => handleChange('noOfSeats', text)}
        keyboardType="numeric"
      />
  
{/* Car Image Upload */}
<Text style={styles.label}>Car Image</Text>
<TouchableOpacity style={styles.uploadButton} onPress={handleCarImageUpload}>
  {form.carImg && form.carImg.uri ? (
    <Image
      source={{ uri: form.carImg.uri }}
      style={styles.carImagePreview} // Add styling for the image preview
      resizeMode="contain"
    />
  ) : (
    <Text style={styles.uploadButtonText}>Upload Car Image</Text>
  )}
</TouchableOpacity>


    </View>
  
    {/* Submit Button */}
    <TouchableOpacity
      style={[
        styles.submitButton,
        isFormComplete ? styles.submitButtonEnabled : styles.submitButtonDisabled,
      ]}
      onPress={handleSubmit}
      disabled={!isFormComplete} // Disable the button when the form is incomplete
    >
      <Text style={styles.submitButtonText}>Submit</Text>
    </TouchableOpacity>
    </ScrollView>
  </GradientBackground>
  
  );
};
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    fontFamily: FONTS.medium,
    color: COLORS.primary,
  },
  title: {
  ...FONTS.h2,
    textAlign: 'center',
    marginVertical: 16,
    color: COLORS.black,
  },
  formContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  label: {
     ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
  },
  input: {
    height: 48,
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
    fontSize: 14,
    fontFamily: FONTS.regular,
    color: COLORS.black,
    backgroundColor: COLORS.white,
  },
  submitButton: {
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonEnabled: {
    backgroundColor: COLORS.primary,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.lightGray,
  },
  submitButtonText: {
    fontSize: 16,
    fontFamily: FONTS.bold,
    color: COLORS.white,
  },
  carImagePreview: {
    width: 150, // Adjust width as needed
    height: 150, // Adjust height as needed
    borderRadius: 10,
    marginVertical: 10,
  },
  uploadButton: {
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 10,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  uploadButtonText: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  
});

export default CarFormDetails;
