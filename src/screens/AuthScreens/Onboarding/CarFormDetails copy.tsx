
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  PermissionsAndroid,
  Platform,
  ScrollView,
} from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import Grad<PERSON><PERSON>ackground from '../../../components/shared/GradientBackground';
import { COLORS, icons } from '../../../constants';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';
import { launchImageLibrary } from 'react-native-image-picker';
import { API_BASE_URL } from '../../../config/apiConfig';

// Define interfaces for type safety
interface CarImage {
  uri: string;
  type: string | undefined;
  name: string;
}

// Modified to handle undefined values
interface CarFormData {
  registrationNumber: string;
  year: string;
  model: string;
  color: string;
  noOfSeats: string;
  carImg: CarImage | null;
  carDetails: Record<string, any>;
}

// For FormData._parts workaround
interface ExtendedFormData extends FormData {
  _parts: Array<[string, any]>;
}

interface RouteParams {
  mobileNumber: string;
  firstName: string;
  lastName: string;
  email: string;
  opreatingCity: string;
  ssn: string;
  driverLincenseImgFront: CarImage;
  driverLincenseImgBack: CarImage;
  profileImg: CarImage;
  [key: string]: any;
}

type RootStackParamList = {
  WelcomeBack: undefined;
  FAQ: undefined;
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

const CarFormDetails = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const [loader, setLoader] = useState(false);

  const allData: RouteParams = route.params || {} as RouteParams;
  console.log(allData, 'alldata');

  const [form, setForm] = useState<CarFormData>({
    registrationNumber: '',
    year: '',
    model: '',
    color: '',
    noOfSeats: '',
    carImg: null,
    carDetails: {}
  });

  const [isFormComplete, setIsFormComplete] = useState(false);
  const [locationGranted, setLocationGranted] = useState(false);
  const [coordinates, setCoordinates] = useState<number[]>([]);

  useEffect(() => {
    const allFieldsFilled = Object.values(form).every((field) => {
      if (typeof field === 'string') {
        return field.trim() !== '';
      }
      return field !== null && field !== undefined;
    });
    setIsFormComplete(allFieldsFilled);
  }, [form]);

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Location Permission',
            message: 'We need your location to complete the registration.',
            buttonPositive: 'OK',
          }
        );
        if (granted === PermissionsAndroid.RESULTS.GRANTED) {
          setLocationGranted(true);
          getLocation();
        } else {
          Alert.alert('Permission Denied', 'Location permission is required to proceed.');
        }
      } else {
        const status = await Geolocation.requestAuthorization('whenInUse');
        if (status === 'granted') {
          setLocationGranted(true);
          getLocation();
        } else {
          Alert.alert('Permission Denied', 'Location permission is required to proceed.');
        }
      }
    } catch (error) {
      console.error('Permission Error:', error);
    }
  };

  const getLocation = async () => {
    Geolocation.getCurrentPosition(
      (position) => {
        const coords = [position.coords.longitude, position.coords.latitude];
        setCoordinates(coords);
        console.log('Location:', coords);
      },
      (error) => {
        console.error('Location Error:', error);
        Alert.alert('Error', 'Unable to fetch location. Please try again.');
      },
      { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
    );
  };

  const handleCarImageUpload = async () => {
    try {
      const result = await launchImageLibrary({
        mediaType: 'photo',
        quality: 1,
      });

      if (result.didCancel) {
        console.log('Image picker canceled');
        return;
      }

      if (result.assets && result.assets.length > 0) {
        const selectedImage = result.assets[0];
        if (selectedImage.uri) {
          setForm((prevForm) => ({
            ...prevForm,
            carImg: {
              name: selectedImage.fileName || selectedImage.uri?.split('/').pop() || 'image.jpg',
              type: selectedImage.type || 'image/jpeg',
              uri: selectedImage.uri || '',
            },
          }));
        }
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'An error occurred while selecting the image. Please try again.');
    }
  };

  const handleChange = (key: string, value: string) => {
    setForm((prev) => ({ ...prev, [key]: value }));
  };

  const handleSubmit = async () => {
    if (!isFormComplete) {
      Alert.alert('Error', 'Please fill all fields before proceeding.');
      return;
    }

    try {
      if (!locationGranted) {
        await requestLocationPermission();
        if (!locationGranted) {
          return;
        }
      }

      // Call getLocation but don't wait for it since we already have coordinates
      getLocation();
      // console.log(allData, 'alldata payload');
      setLoader(true);

      const formData = new FormData();
      // formData.append('coordinates', coordinates);
      formData.append('coordinates', JSON.stringify(coordinates));
      // formData.append('coordinates', JSON.stringify(allData.coordinates));
      formData.append('mobileNumber', allData.mobileNumber);
      formData.append('firstName', allData.firstName);
      formData.append('lastName', allData.lastName);
      formData.append('email', allData.email);
      formData.append('opreatingCity', allData.opreatingCity);
      formData.append('ssn', allData.ssn);
      formData.append('pricePerKm', '2');
      formData.append('idCardType', 'driverLicense');
      formData.append('driverLincenseImgFront', {
        uri: allData.driverLincenseImgFront.uri,
        type: allData.driverLincenseImgFront.type,
        name: allData.driverLincenseImgFront.uri.split('/').pop(),
        // name: allData.driverLincenseImgFront.uri.split('/').pop(),
      });
      formData.append('driverLincenseImgBack', {
        uri: allData.driverLincenseImgBack.uri,
        type: allData.driverLincenseImgBack.type || 'image/jpeg',
        name: allData.driverLincenseImgBack.uri.split('/').pop(),
      });
      formData.append('profileImg', {
        uri: allData.profileImg.uri,
        type: allData.profileImg.type || 'image/jpeg',
        name: allData.profileImg.uri.split('/').pop(),
      });

      formData.append('carDetails[registrationNumber]', form.registrationNumber);
      formData.append('carDetails[year]', form.year);
      formData.append('carDetails[model]', form.model);
      formData.append('carDetails[color]', form.color);
      formData.append('carDetails[noOfSeats]', form.noOfSeats);

      // Check if carImg exists before accessing its properties
      if (form.carImg) {
        formData.append('carDetails[carImg]', {
          uri: form.carImg.uri,
          type: form.carImg.type || 'image/jpeg',
          name: form.carImg.name.split('/').pop() || 'image.jpg',
        });
      }

      // formData.append('carDetails[registrationNumber]', form.carDetails.registrationNumber);
      // formData.append('carDetails[year]', form.carDetails.year);
      // formData.append('carDetails[model]', form.model);
      // formData.append('carDetails[color]', form.color);
      // formData.append('carDetails[noOfSeats]', form.noOfSeats);
      // formData.append("carImg", {
      //   uri: form.carDetails.carImg.uri,
      //   type: form.carDetails.carImg.type || "image/jpeg",
      //   name: form.carDetails.carImg.name.split("/").pop(),
      // });

      console.log('FormData Content:');

      // Type assertion to access _parts
      (formData as ExtendedFormData)._parts.forEach(([key, value]) => {
        console.log(`${key}:`, value);
      });

      const response = await fetch(
        `${API_BASE_URL}/driver/auth/completeDriverRegistration`,
        {
          credentials: 'include',
          method: 'POST',
          headers: { 'Content-Type': 'multipart/form-data' },
          body: formData,
        }
      );

      console.log('Response Status:', response.status);
      console.log('Response Headers:', response.headers);

      const responseBody = await response.text();
      console.log('Raw Response Body:', responseBody);

      let result;
      try {
        result = JSON.parse(responseBody);
        console.log('Parsed JSON Response:', result);
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        result = { message: 'Invalid JSON response from server' };
      }

      if (response.ok) {
        Toast.show({
          type: 'success',
          text1: 'Registration Complete',
          text2: 'Your details have been successfully submitted.',
        });
        navigation.navigate('WelcomeBack');

      } else {
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2: result.data || 'Unable to complete registration. Please try again.',
        });
      }
    } catch (error) {
      console.error('API Error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Something went wrong. Please try again.',
      });
    } finally {
      setLoader(false);
    }
  };


return (
    <GradientBackground style={{ flex: 1 }}>
    {/* Header Section */}
    <Spinner visible={loader} />
    <ScrollView>
    <View style={styles.header}>
      <TouchableOpacity onPress={() => navigation.goBack()}>
        <Image source={icons.BackIcon} style={styles.icon} />
      </TouchableOpacity>
       <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
    </View>

    {/* Title */}
    <Text style={styles.title}>Add car details</Text>

    {/* Form Fields */}
    <View style={styles.formContainer}>
      <Text style={styles.label}>Registration Number</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Registration Number"
        placeholderTextColor={COLORS.grey}
        value={form.registrationNumber}
        onChangeText={(text) => handleChange('registrationNumber', text)}
      />

      <Text style={styles.label}>Year</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter year"
        placeholderTextColor={COLORS.grey}
        value={form.year}
        onChangeText={(text) => handleChange('year', text)}
        keyboardType="numeric"
      />

      <Text style={styles.label}>Model</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter model"
        value={form.model}
        placeholderTextColor={COLORS.grey}
        onChangeText={(text) => handleChange('model', text)}
      />

      <Text style={styles.label}>Color</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter color"
        placeholderTextColor={COLORS.grey}
        value={form.color}
        onChangeText={(text) => handleChange('color', text)}
      />

{/* <Text style={styles.label}>Price Per Km</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter price per km"
        value={pricePerKm}
        onChangeText={(text) => setPricePerKm('pricePerKm', text)}
      /> */}



      <Text style={styles.label}>Number of Seats</Text>
      <TextInput
        style={styles.input}
        placeholder="Enter Number of Seats"
        placeholderTextColor={COLORS.grey}
        value={form.noOfSeats}
        onChangeText={(text) => handleChange('noOfSeats', text)}
        keyboardType="numeric"
      />

{/* Car Image Upload */}
<Text style={styles.label}>Car Image</Text>
<TouchableOpacity style={styles.uploadButton} onPress={handleCarImageUpload}>
  {form.carImg && form.carImg.uri ? (
    <Image
      source={{ uri: form.carImg.uri }}
      style={styles.carImagePreview} // Add styling for the image preview
      resizeMode="contain"
    />
  ) : (
    <Text style={styles.uploadButtonText}>Upload Car Image</Text>
  )}
</TouchableOpacity>


    </View>

    {/* Submit Button */}
    <TouchableOpacity
      style={[
        styles.submitButton,
        isFormComplete ? styles.submitButtonEnabled : styles.submitButtonDisabled,
      ]}
      onPress={handleSubmit}
      disabled={!isFormComplete} // Disable the button when the form is incomplete
    >
      <Text style={styles.submitButtonText}>Submit</Text>
    </TouchableOpacity>
    </ScrollView>
  </GradientBackground>

  );
};
const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    fontFamily: 'Roboto-Medium', // Replace FONTS.medium
    color: COLORS.primary,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: 16,
    color: COLORS.black,
  },
  formContainer: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.black,
    marginBottom: 10,
  },
  input: {
    height: 48,
    borderWidth: 0.5,
    borderColor: COLORS.grey,
    borderRadius: 8,
    paddingHorizontal: 12,
    marginBottom: 20,
    fontSize: 14,
    fontFamily: 'Roboto-Regular', // Replace FONTS.regular
    color: COLORS.black,
    backgroundColor: COLORS.white,
  },
  submitButton: {
    borderRadius: 8,
    paddingVertical: 12,
    marginHorizontal: 16,
    alignItems: 'center',
    marginTop: 16,
  },
  submitButtonEnabled: {
    backgroundColor: COLORS.primary,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.light_grey, // Replace COLORS.lightGray
  },
  submitButtonText: {
    fontSize: 16,
    fontWeight: 'bold', // Replace FONTS.bold
    color: COLORS.white,
  },
  carImagePreview: {
    width: 150, // Adjust width as needed
    height: 150, // Adjust height as needed
    borderRadius: 10,
    marginVertical: 10,
  },
  uploadButton: {
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 10,
    padding: 10,
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 10,
  },
  uploadButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.primary,
  },
});

export default CarFormDetails;
