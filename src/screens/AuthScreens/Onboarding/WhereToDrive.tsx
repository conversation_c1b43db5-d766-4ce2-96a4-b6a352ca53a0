import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Platform,
} from 'react-native';
import {COLORS, icons} from '../../../constants';
import GradientBackground from '../../../components/shared/GradientBackground';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import GooglePlacesAutocompleteComponent from '../../../components/shared/GooglePlaceAutoComplete';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import {API_BASE_URL} from '../../../config/apiConfig';

// Define interfaces for type safety
interface CarImage {
  uri: string;
  type?: string;
  name: string;
}

interface CarDetails {
  registrationNumber: string;
  year: string;
  model: string;
  color: string;
  noOfSeats: string;
  carImg: CarImage;
}

interface DriverPayload {
  mobileNumber: string;
  firstName: string;
  lastName: string;
  middleName: string;
  email: string;
  opreatingCity: string;
  ssn: string;
  state: string;
  zipcode: string;
  driverLincenseNumber: string;
  driverLincenseState: string;
  dob: string;
  signature: string;
  userConsent: boolean;
  coordinates: [number, number];
  driverLincenseImgFront: CarImage;
  driverLincenseImgBack: CarImage;
  profileImg: CarImage;
  carDetails?: CarDetails;
}

// For FormData._parts workaround
interface ExtendedFormData extends FormData {
  _parts: Array<[string, any]>;
}

// Define navigation types
type RootStackParamList = {
  WelcomeBack: undefined;
  FAQ: undefined;
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

const WhereToDrive = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RouteProp<Record<string, any>, string>>();
  const [payload, setPayload] = useState<DriverPayload>({
    mobileNumber: '',
    firstName: '',
    lastName: '',
    middleName: '',
    email: '',
    opreatingCity: '',
    ssn: '',
    state: '',
    zipcode: '',
    driverLincenseNumber: '',
    driverLincenseState: '',
    dob: '',
    signature: '',
    userConsent: false,
    coordinates: [0, 0],
    driverLincenseImgFront: { uri: '', name: '' },
    driverLincenseImgBack: { uri: '', name: '' },
    profileImg: { uri: '', name: '' },
    ...(route.params || {})
  });
  const [loader, setLoader] = useState(false);

  console.log('bukunmu ', 'nnn');

  const handleSubmit = async () => {
    if (!payload.opreatingCity) {
      Alert.alert('Error', 'Please fill all fields before proceeding.');
      return;
    }

    console.log(JSON.stringify(payload, null, 2));

    try {
      setLoader(true);

      const formData = new FormData();

      // Append existing data to formData
      formData.append('coordinates', JSON.stringify(payload.coordinates));
      formData.append('mobileNumber', payload.mobileNumber);
      formData.append('firstName', payload.firstName.trim());
      formData.append('lastName', payload.lastName.trim());
      formData.append('middleName', payload.middleName.trim());
      formData.append('email', payload.email);
      formData.append('opreatingCity', payload.opreatingCity);
      formData.append('ssn', payload.ssn);
      formData.append('state', payload.state);
      formData.append('zipcode', payload.zipcode);
      formData.append('driverLincenseNumber', payload.driverLincenseNumber);
      formData.append('driverLincenseState', payload.driverLincenseState);
      formData.append('dob', payload.dob);
      formData.append('signature', payload.signature);
      formData.append('userConsent', payload.userConsent ? 'true' : 'false');
      formData.append('pricePerKm', 10000);
      formData.append('idCardType', 'driverLicense');

      // Append images
      formData.append('driverLincenseImgFront', {
        uri: payload.driverLincenseImgFront.uri,
        type: payload.driverLincenseImgFront.type || 'image/jpeg',
        name: payload.driverLincenseImgFront.uri.split('/').pop(),
      });

      formData.append('driverLincenseImgBack', {
        uri: payload.driverLincenseImgBack.uri,
        type: payload.driverLincenseImgBack.type || 'image/jpeg',
        name: payload.driverLincenseImgBack.uri.split('/').pop(),
      });

      formData.append('profileImg', {
        uri: payload.profileImg.uri,
        type: payload.profileImg.type || 'image/jpeg',
        name: payload.profileImg.uri.split('/').pop(),
      });

      // Add car details if available
      formData.append(
        'carDetails[registrationNumber]',
        payload?.carDetails?.registrationNumber,
      );
      formData.append('carDetails[year]', payload?.carDetails?.year);
      formData.append('carDetails[model]', payload?.carDetails?.model);
      formData.append('carDetails[color]', payload?.carDetails?.color);
      formData.append('carDetails[noOfSeats]', payload?.carDetails?.noOfSeats);

      // Add car image if available
      formData.append('carDetails[carImg]', {
        uri: payload?.carDetails?.carImg.uri,
        type: payload?.carDetails?.carImg.type || 'image/jpeg',
        name: payload?.carDetails?.carImg.name.split('/').pop(),
      });

      console.log('Form Data:', formData);

      console.log('FormData Content:');

      // Type assertion to access _parts
      (formData as ExtendedFormData)._parts.forEach(([key, value]: [string, any]) => {
        console.log(`${key}:`, value);
      });

      const response = await fetch(
        `${API_BASE_URL}/driver/auth/completeDriverRegistration`,
        {
          credentials: 'include',
          method: 'POST',
          headers: {'Content-Type': 'multipart/form-data'},
          body: formData,
        },
      );

      console.log('Response Status:', JSON.stringify(response.status, null, 2));
      console.log('Response Headers:', response.headers);

      const responseBody = await response.text();
      console.log('Raw Response Body:', responseBody);

      let result;
      try {
        result = JSON.parse(responseBody);
        console.log(
          'Parsed JSON Response:',
          JSON.stringify(result?.data, null, 2),
        );
      } catch (parseError) {
        console.error('Error parsing JSON:', parseError);
        result = {message: 'Invalid JSON response from server'};
      }

      if (response.ok) {
        Toast.show({
          type: 'success',
          text1: 'Registration Complete',
          text2: 'Your details have been successfully submitted.',
        });
        navigation.navigate('WelcomeBack');
      } else {
        Toast.show({
          type: 'error',
          text1: 'Registration Failed',
          text2:
            result.data || 'Unable to complete registration. Please try again.',
          text1Style: {
            fontSize: 15,
            fontWeight: 'bold',
          },
          text2Style: {
            fontSize: 14,
          },
        });
        Alert.alert(
          'Error',
          result.data || 'Unable to complete registration. Please try again.',
        );
      }
    } catch (error) {
      console.error('API Error:', error);
      Toast.show({
        type: 'error',
        text1: 'Error',
        text2: 'Something went wrong. Please try again.',
      });
    } finally {
      setLoader(false);
    }
  };

  // Handle city selection from the Google Places Autocomplete component
  const handleCitySelect = (selectedLocation: { description: string; location: [number, number] }) => {
    const {description, location} = selectedLocation;

    // Update the payload with the selected data
    setPayload(prevPayload => ({
      ...prevPayload,
      opreatingCity: description,
      coordinates: location, // Store coordinates [lat, lng]
    }));
  };

  return (
    <GradientBackground style={styles.container}>
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      {/* Header Section */}
      <Spinner visible={loader} />
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <TouchableOpacity>
          <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Where do you plan to drive?</Text>
      <View style={{marginHorizontal: 20}}>
        {/* Google Places Autocomplete Component */}
        <GooglePlacesAutocompleteComponent
          placeholder="Enter your city"
          onSelect={handleCitySelect} // Pass the selected location data
        />
      </View>
      {/* Subtitle */}
      <Text style={styles.subtitle}>
        This will be based on the requirement of where you plan to drive
      </Text>

      {/* Next Button */}
      <TouchableOpacity
        onPress={() => handleSubmit()}
        style={[styles.nextButton, {opacity: payload.opreatingCity ? 1 : 0.5}]}
        disabled={!payload.opreatingCity}>
        <Image source={icons.ArrowIcon} style={styles.icon} />
      </TouchableOpacity>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // alignItems: 'center',
  },
  header: {
    padding: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 30,
  },
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginVertical: 20,
    color: '#000',
    marginBottom: 10,
  },
  searchContainer: {
    marginHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
    paddingHorizontal: 10,
    backgroundColor: '#fff',
  },
  searchInput: {
    flex: 1,
    height: 50,
    fontSize: 16,
    fontWeight: '500',
    paddingLeft: 10,
  },
  suggestionsList: {
    marginHorizontal: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    minHeight: 50,
    maxHeight: 300, // Limit the height of the suggestion
  },
  suggestionItem: {
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  suggestionText: {
    fontSize: 14,
    fontWeight: '400',
    color: '#000',
  },
  subtitle: {
    fontSize: 14,
    fontWeight: '400',
    textAlign: 'center',
    color: '#555',
    margin: 25,
  },
  nextButton: {
    marginTop: 50,
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
  helpText: {
    fontSize: 14,
    fontWeight: '400',
    color: COLORS.black,
  },
});

export default WhereToDrive;
