import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Platform,
} from 'react-native';
import * as ImagePicker from 'react-native-image-picker'; // Ensure this library is installed
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';
import Spinner from 'react-native-loading-spinner-overlay';

// const AddProfilePicture = () => {
//   const navigation = useNavigation();
//   const route = useRoute();

//   // Retrieve all data from the previous screen
//   const {
//     firstName,
//     lastName,
//     email,
//     opreatingCity,
//     ssn,
//     driverLincenseImgFront,
//     driverLincenseImgBack,
//     mobileNumber,
//   } = route.params || {};

//   const [profileImg, setProfileImg] = useState(null);

//   const handleImagePick = async () => {
//     try {
//       const result = await ImagePicker.launchImageLibrary({
//         mediaType: 'photo',
//         includeBase64: false,
//         selectionLimit: 1,
//       });

//       if (result.didCancel) {
//         console.log('User cancelled image picker');
//         return;
//       }

//       if (result.errorCode) {
//         console.log('Error Code:', result.errorCode);
//         Alert.alert('Error', result.errorMessage || 'Unable to pick an image.');
//         return;
//       }

//       if (result.assets && result.assets[0]) {
//         const asset = result.assets[0];

//         // Format the image in the expected object structure
//         const formattedImage = {
//           uri: asset.uri,
//           type: asset.type || 'image/jpeg', // Default MIME type if not provided
//           name: asset.fileName || `profile_${Date.now()}.jpg`, // Generate name if not provided
//         };

//         setProfileImg(formattedImage);
//       } else {
//         Alert.alert('Error', 'No image selected.');
//       }
//     } catch (error) {
//       console.error('Image picker error:', error);
//       Alert.alert('Error', 'Unable to pick an image. Please try again.');
//     }
//   };

//   const handleNext = () => {
//     if (!profileImg) {
//       Alert.alert('Incomplete', 'Please upload a profile picture.');
//       return;
//     }

//     // Pass all data, including formatted profileImg, to the next screen
//     navigation.navigate('CheckConsent', {
//       firstName,
//       lastName,
//       email,
//       mobileNumber,
//       opreatingCity,
//       ssn,
//       driverLincenseImgFront,
//       driverLincenseImgBack,
//       profileImg, // Pass the formatted image object
//     });
//   };

//   return (
//     <GradientBackground>
//       {/* Header Section */}
//       <View style={styles.header}>
//         <TouchableOpacity onPress={() => navigation.goBack()}>
//           <Image
//             source={icons.BackIcon} // Replace with your back icon path
//             style={styles.icon}
//           />
//         </TouchableOpacity>
//         <Text style={styles.helpText}>Help</Text>
//       </View>

//       {/* Title Section */}
//       <Text style={styles.title}>Upload Profile Picture</Text>

//       {/* Profile Image Section */}
//       <View style={styles.imageSection}>
//         <Text style={styles.imageLabel}>Profile Picture</Text>
//         {profileImg ? (
//           <>
//             <Image source={{ uri: profileImg?.uri }} style={styles.uploadedImage} />
//             <TouchableOpacity onPress={handleImagePick}>
//               <Text style={styles.repickText}>Repick Image</Text>
//             </TouchableOpacity>
//           </>
//         ) : (
//           <TouchableOpacity style={styles.imagePlaceholder} onPress={handleImagePick}>
//             <Image source={icons?.UploadIcon} style={styles.uploadIcon} />
//             <Text style={styles.uploadText}>Upload Image</Text>
//           </TouchableOpacity>
//         )}
//       </View>

//       {/* Next Button */}
//       <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
//         <Text style={styles.nextButtonText}>Next</Text>
//       </TouchableOpacity>
//     </GradientBackground>
//   );
// };

// const styles = StyleSheet.create({
//   header: {
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//     padding: 16,
//   },
//   icon: {
//     width: 18,
//     height: 18,
//     tintColor: COLORS.black,
//     resizeMode:'contain'
//   },
//   helpText: {
//     fontSize: 14,
//     fontFamily: FONTS.body3,
//     color: COLORS.black,
//   },
//   title: {
//     ...FONTS.h2,
//     textAlign: 'center',
//     marginVertical: 16,
//   },
//   imageSection: {
//     marginVertical: 16,
//     alignItems: 'center',
//   },
//   imageLabel: {
//     fontSize: 16,
//     fontFamily: FONTS.medium,
//     marginBottom: 8,
//   },
//   imagePlaceholder: {
//     width: 150,
//     height: 150,
//     borderRadius: 75,
//     borderWidth: 1,
//     borderColor: COLORS.lightGray,
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: COLORS.white,
//   },
//   uploadIcon: {
//     width: 40,
//     height: 40,
//     tintColor: COLORS.primary,
//   },
//   uploadText: {
//     fontSize: 14,
//     fontFamily: FONTS.medium,
//     color: COLORS.primary,
//     marginTop: 8,
//   },
//   uploadedImage: {
//     width: 150,
//     height: 150,
//     borderRadius: 75,
//   },
//   repickText: {
//     fontSize: 14,
//     fontFamily: FONTS.medium,
//     color: COLORS.secondary,
//     marginTop: 8,
//     textDecorationLine: 'underline',
//   },
//   nextButton: {
//     backgroundColor: COLORS.primary,
//     borderRadius: 8,
//     paddingVertical: 12,
//     paddingHorizontal: 24,
//     alignItems: 'center',
//     marginTop: 24,
//     marginHorizontal:20
//   },
//   nextButtonText: {
//     fontSize: 16,
//     fontFamily: FONTS.bold,
//     color: COLORS.white,
//   },
// });

// export default AddProfilePicture;



const AddProfilePicture = () => {
  const navigation = useNavigation();
  const route = useRoute();

  const payload = route.params || {};
  

  const [profileImg, setProfileImg] = useState(null);
  const [loader, setLoader] = useState(false);

  const handleImagePick = async () => {
    try {
      const result = await ImagePicker.launchImageLibrary({
        mediaType: 'photo',
        includeBase64: false,
        selectionLimit: 1,
      });

      if (result.didCancel) {
        console.log('User cancelled image picker');
        return;
      }

      if (result.errorCode) {
        console.log('Error Code:', result.errorCode);
        Alert.alert('Error', result.errorMessage || 'Unable to pick an image.');
        return;
      }

      if (result.assets && result.assets[0]) {
        const asset = result.assets[0];
        const formattedImage = {
          uri: asset.uri,
          type: asset.type || 'image/jpeg',
          name: asset.fileName || `profile_${Date.now()}.jpg`,
        };

        setProfileImg(formattedImage);
      } else {
        Alert.alert('Error', 'No image selected.');
      }
    } catch (error) {
      console.error('Image picker error:', error);
      Alert.alert('Error', 'Unable to pick an image. Please try again.');
    }
  };

  const handleNext = () => {
    if (!profileImg) {
      Alert.alert('Incomplete', 'Please upload a profile picture.');
      return;
    }

    navigation.navigate('TermOfService', {
      ...payload,
      profileImg
    });
  };

  return (
    <GradientBackground>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
        {/* Loader */}
        <Spinner visible={loader} />
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon} // Replace with your back icon path
            style={styles.icon}
          />
        </TouchableOpacity>
         <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>
      <View style={styles.container}>
        <Text style={styles.title}>Add Profile Picture</Text>
        <TouchableOpacity onPress={handleImagePick} style={styles.imagePicker}>
          {profileImg ? (
            <Image
              source={{ uri: profileImg.uri }}
              style={styles.profileImage}
            />
          ) : (
            <>
              <Image
                source={images.uploadimage}
                style={styles.profileImagee}
              />
              <Text style={styles.imagePickerText}>Upload Profile Picture</Text>
            </>
          )}
        </TouchableOpacity>
  
        <TouchableOpacity onPress={handleNext} style={styles.nextButton}>
          <Text style={styles.nextButtonText}>Next</Text>
        </TouchableOpacity>
      </View>
    </GradientBackground>
  );
  
  };
  const styles = StyleSheet.create({
    container: {
      // flex: 1,
      paddingHorizontal: 20,
      justifyContent: 'space-between',
      // backgroundColor: '#F8FBFF',
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: 20,
      marginBottom: 100,
      marginHorizontal: 20,
    },
    icon: {
      width: 18,
      height: 18,
      resizeMode: 'contain',
    },
    helpText: {
      fontSize: 14,
      color: '#343A40',
    },
  
    title: {
      marginBottom: 50,
  
      fontSize: 18,
      fontWeight: 'bold',
      textAlign: 'center',
    },
    imagePicker: {
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: COLORS.light_grey,
      borderRadius: 100,
      width: 150,
      height: 150,
      alignSelf: 'center',
      marginBottom: 30,
    },
    imagePickerText: {
      marginTop: 30,
      textAlign: 'center',
      color: COLORS.grey,
    },
    profileImage: {
      width: 150,
      height: 150,
      borderRadius: 100,
    },
    profileImagee: {
      width: 150,
      height: 150,
      borderRadius: 100,
    },
    nextButton: {
      backgroundColor: COLORS.primary,
      padding: 15,
      borderRadius: 10,
      alignItems: 'center',
      marginTop: 20,
    },
    nextButtonText: {
      color: COLORS.white,
      fontSize: 16,
      fontWeight: 'bold',
    },
  });
   

export default AddProfilePicture;











// import React, { useState, useEffect } from 'react';
// import {
//   View,
//   Text,
//   TextInput,
//   Button,
//   TouchableOpacity,
//   Alert,
//   Platform,
//   Image,
// } from 'react-native';
// import * as ImagePicker from 'expo-image-picker';

// export default function Register() {
//   const [formData, setFormData] = useState({
//     firstName: '',
//     lastName: '',
//     email: '',
//     ssn: '',
//     idCardType: '',
//   });
//   const [idCardImgFront, setIdCardImgFront] = useState(null);
//   const [idCardImgBack, setIdCardImgBack] = useState(null);
//   const [profileImg, setProfileImg] = useState(null);

//   const handleInputChange = (name, value) => {
//     setFormData({ ...formData, [name]: value });
//   };

//   const pickImage = async (setImage) => {
//     let result = await ImagePicker.launchImageLibraryAsync({
//       mediaTypes: ImagePicker.MediaTypeOptions.Images,
//       allowsEditing: true,
//       quality: 1,
//     });

//     if (!result.cancelled) {
//       setImage(result);
//     }
//   };

//   const handleSubmit = async () => {
//     const form = new FormData();
//     console.log('form')

//     {
//       "driverLincenseImgBack": {
//           "name": "A709B74B-205F-41C0-A71E-C3B2E5D93131.jpg",
//           "type": "image/jpg",
//           "uri": "file:///Users/<USER>/Library/Developer/CoreSimulator/Devices/06BC3E12-0B64-440F-A130-3FEE2A056B68/data/Containers/Data/Application/832F1579-09F4-4FFE-9457-57E186E83A23/tmp/A709B74B-205F-41C0-A71E-C3B2E5D93131.jpg"
//       },
//       "driverLincenseImgFront": {
//           "name": "023402E8-8C60-42AB-8737-A6E89128CB87.jpg",
//           "type": "image/jpg",
//           "uri": "file:///Users/<USER>/Library/Developer/CoreSimulator/Devices/06BC3E12-0B64-440F-A130-3FEE2A056B68/data/Containers/Data/Application/832F1579-09F4-4FFE-9457-57E186E83A23/tmp/023402E8-8C60-42AB-8737-A6E89128CB87.jpg"
//       },
//       "email": "<EMAIL>",
//       "firstName": "Asdfgh",
//       "lastName": "Asdfghjk",
//       "mobileNumber": "+2348124079525",
//       "opreatingCity": "Los Angeles",
//       "profileImg": {
//           "name": "DF7559B9-992C-4437-893B-590785A7AE41.jpg",
//           "type": "image/jpg",
//           "uri": "file:///Users/<USER>/Library/Developer/CoreSimulator/Devices/06BC3E12-0B64-440F-A130-3FEE2A056B68/data/Containers/Data/Application/832F1579-09F4-4FFE-9457-57E186E83A23/tmp/DF7559B9-992C-4437-893B-590785A7AE41.jpg"
//       },
//       "ssn": "1234567890"
//   }

//     form.append('mobileNumber', allData.mobileNumber);
//     form.append('firstName', allData.firstName);
//     form.append('lastName', allData.lastName);
//     form.append('email', allData.email);
//     form.append('ssn', allData.ssn);
//     form.append('idCardType', allData.idCardType);
//     form.append('idCardImgFront', {
//       uri: allData.driverLincenseImgFront.uri,
//       type: allData.driverLincenseImgFront.type || 'image/jpeg',
//       name: allData.driverLincenseImgFront.uri.split('/').pop(),
//     });
//     form.append('idCardImgBack', {
//       uri: allData.driverLincenseImgBack.uri,
//       type: allData.driverLincenseImgBack.type || 'image/jpeg',
//       name: allData.driverLincenseImgBack.uri.split('/').pop(),
//     });
//     form.append('profileImg', {
//       uri: allData.profileImg.uri,
//       type: allData.profileImg.type || 'image/jpeg',
//       name: allData.profileImg.uri.split('/').pop(),
//     });

//     console.log(form, "form to server")
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/passenger/auth/registerUser', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'multipart/form-data',
//         },
//         body: form,
//       });

//       const result = await response.json();
//       if (response.ok) {
//         Alert.alert('Success', result?.data);
//       } else {
//         Alert.alert('Error', result.data || 'Registration failed.');
//       }
//     } catch (error) {
//       console.error('Error:', error);
//       Alert.alert('Error', 'An error occurred while registering the user.');
//     }
//   };

//   useEffect(() => {
//     console.log('REGISTER USER DATA', formData);
//   }, [formData]);

//   return (
//     <View style={{ padding: 20 }}>
//       <Text>First Name:</Text>
//       <TextInput
//         value={formData.firstName}
//         onChangeText={(text) => handleInputChange('firstName', text)}
//         placeholder="Enter first name"
//         style={{ borderBottomWidth: 1, marginBottom: 10 }}
//       />

//       <Text>Last Name:</Text>
//       <TextInput
//         value={formData.lastName}
//         onChangeText={(text) => handleInputChange('lastName', text)}
//         placeholder="Enter last name"
//         style={{ borderBottomWidth: 1, marginBottom: 10 }}
//       />

//       <Text>Email:</Text>
//       <TextInput
//         value={formData.email}
//         onChangeText={(text) => handleInputChange('email', text)}
//         placeholder="Enter email"
//         keyboardType="email-address"
//         style={{ borderBottomWidth: 1, marginBottom: 10 }}
//       />

//       <Text>SSN:</Text>
//       <TextInput
//         value={formData.ssn}
//         onChangeText={(text) => handleInputChange('ssn', text)}
//         placeholder="Enter SSN"
//         style={{ borderBottomWidth: 1, marginBottom: 10 }}
//       />

//       <Text>ID Card Type:</Text>
//       <TextInput
//         value={formData.idCardType}
//         onChangeText={(text) => handleInputChange('idCardType', text)}
//         placeholder="Enter ID card type (e.g., voterCard)"
//         style={{ borderBottomWidth: 1, marginBottom: 10 }}
//       />

//       <Text>ID Card Front Image:</Text>
//       <TouchableOpacity onPress={() => pickImage(setIdCardImgFront)}>
//         <Text style={{ color: 'blue' }}>{idCardImgFront ? 'Change Image' : 'Select Image'}</Text>
//       </TouchableOpacity>
//       {idCardImgFront && <Image source={{ uri: idCardImgFront.uri }} style={{ width: 100, height: 100 }} />}

//       <Text>ID Card Back Image:</Text>
//       <TouchableOpacity onPress={() => pickImage(setIdCardImgBack)}>
//         <Text style={{ color: 'blue' }}>{idCardImgBack ? 'Change Image' : 'Select Image'}</Text>
//       </TouchableOpacity>
//       {idCardImgBack && <Image source={{ uri: idCardImgBack.uri }} style={{ width: 100, height: 100 }} />}

//       <Text>Profile Image:</Text>
//       <TouchableOpacity onPress={() => pickImage(setProfileImg)}>
//         <Text style={{ color: 'blue' }}>{profileImg ? 'Change Image' : 'Select Image'}</Text>
//       </TouchableOpacity>
//       {profileImg && <Image source={{ uri: profileImg.uri }} style={{ width: 100, height: 100 }} />}

//       <Button title="Register" onPress={handleSubmit} />
//     </View>
//   );
// }

