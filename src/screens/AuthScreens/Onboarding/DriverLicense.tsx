import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Platform,
} from 'react-native';
import GradientBackground from '../../../components/shared/GradientBackground';
import { COLORS, FONTS, icons, images } from '../../../constants';
import { useRoute, useNavigation } from '@react-navigation/native';

const DriverLicense = () => {
  const navigation = useNavigation();
  const route = useRoute();

  // Retrieve all data from the previous screen
  const payload= route.params || {};
console.log( JSON.stringify(payload,null,2));

  const handleNext = () => {
    // Pass all data to the next screen
    navigation.navigate('DriverLicenseImage',  payload);
  
  };

  return (
    <GradientBackground>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Header Section */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={icons.BackIcon}  
            style={styles.icon}
          />
        </TouchableOpacity>
         <TouchableOpacity onPress={() => navigation.navigate('FAQ')}>
        <Text style={styles.helpText}>Help</Text>
        </TouchableOpacity>
      </View>

      {/* Title Section */}
      <Text style={styles.title}>Snap a photo of your driver’s license</Text>

      {/* Illustration */}
      <Image
        source={images.id} // Replace with your placeholder image
        style={styles.illustration}
        resizeMode="contain"
      />

      {/* Description */}
      <Text style={styles.description}>
        We’ll use this to confirm your identity.
      </Text>

      {/* Buttons */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity style={styles.primaryButton} onPress={handleNext}>
          <Text style={styles.primaryButtonText}>Add Driver’s License</Text>
        </TouchableOpacity>

        {/* <TouchableOpacity
          style={styles.secondaryButton}
          onPress={handleNext} // Optional skip logic
        >
          <Text style={styles.secondaryButtonText}>Skip</Text>
        </TouchableOpacity> */}
      </View>
    </GradientBackground>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    justifyContent: 'space-between',
    backgroundColor: '#F8FBFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 20,
    marginHorizontal:20
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  helpText: {
    fontSize: 14,
    color: '#343A40',
  },
  title: {
   ...FONTS.body2,
    textAlign: 'center',
    marginVertical: 20,
    marginHorizontal:20,
    marginTop:100,
    color: '#000',
  },
  illustration: {
    marginTop:50,
    width: '80%',
    height: 100,
    alignSelf: 'center',
  },
  description: {
    ...FONTS.body3,
    textAlign: 'center',
    color: '#6C757D',
    marginVertical: 20,
  },
  buttonContainer: {
    marginVertical: 30,
    marginHorizontal:20

  },
  primaryButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButtonText: {
    color: '#FFF',
    ...FONTS.h3

  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor:COLORS.white,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: COLORS.primary,
    ...FONTS.h3
  },
});

export default DriverLicense;
