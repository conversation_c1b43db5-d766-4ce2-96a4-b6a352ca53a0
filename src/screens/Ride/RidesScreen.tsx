import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  FlatList,
  Dimensions,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import type { StackNavigationProp } from '@react-navigation/stack';
import { overwriteStore } from '../../../redux/ActionCreator';
import { useDispatch } from 'react-redux';
import Icon from 'react-native-vector-icons/Feather';
import { API_BASE_URL } from '../../config/apiConfig';

const RidesScreen = () => {
  const [selectedTab, setSelectedTab] = useState('Available');
  const navigation = useNavigation() as any;
 
  
  const [upcomingRides, setUpcomingRides] = useState([]);
  const dispatch = useDispatch();
const [pastRides, setPastRides] = useState([]);
const [availableRides, setAvailableRides] = useState([]);
const [loading, setLoading] = useState(false);
const [refreshing, setRefreshing] = useState(false);

const fetchPastRides = async () => {
  setLoading(true);
  try {
    console.log('🔄 [RidesScreen] Fetching past rides...');
    const response = await fetch(`${API_BASE_URL}/rides/getDriverRides`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();
    console.log('📋 [RidesScreen] Past Rides Response Status:', response.status);
    console.log('📋 [RidesScreen] Past Rides Response:', JSON.stringify(data, null, 2));

    if (response.ok) {
      const ridesData = data?.message?.rides || [];
      dispatch(overwriteStore({ name: 'DriverRides', value: ridesData }));
      setPastRides(ridesData);
      console.log('📊 [RidesScreen] Set past rides count:', ridesData.length);
    } else {
      console.error('❌ [RidesScreen] Past rides API failed:', response.status, data);
    }
  } catch (error) {
    console.error('❌ [RidesScreen] Error fetching past rides:', error);
  } finally {
    setLoading(false);
  }
};

const fetchAvailableRides = async () => {
  setLoading(true);
  try {
    console.log('🔄 [RidesScreen] Fetching available rides...');
    console.log('🌐 [RidesScreen] API URL:', `${API_BASE_URL}/rides/getAvailableRideRequest`);

    const response = await fetch(`${API_BASE_URL}/rides/getAvailableRideRequest`, {
      method: 'GET',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('📡 [RidesScreen] Response Status:', response.status);
    console.log('📡 [RidesScreen] Response Headers:', response.headers);

    const data = await response.json();

    console.log('='.repeat(50));
    console.log('📋 [RidesScreen] AVAILABLE RIDES API RESPONSE:');
    console.log('📋 Status:', response.status);
    console.log('📋 Response Data:', JSON.stringify(data, null, 2));
    console.log('='.repeat(50));

    if (response.ok) {
      // Handle the actual response structure: { success: true, data: [...] }
      const ridesData = data?.data || [];
      setAvailableRides(ridesData);
      console.log('✅ [RidesScreen] Available rides loaded successfully');
      console.log('📊 [RidesScreen] Total available rides:', ridesData.length);
      console.log('📊 [RidesScreen] First ride sample:', ridesData[0] ? JSON.stringify(ridesData[0], null, 2) : 'No rides');
    } else {
      console.error('❌ [RidesScreen] Available rides API failed');
      console.error('❌ Status:', response.status);
      console.error('❌ Error data:', JSON.stringify(data, null, 2));
      setAvailableRides([]);
    }
  } catch (error) {
    console.error('❌ [RidesScreen] Network/Parse error fetching available rides:');
    console.error('❌ Error details:', error);
    console.error('❌ Error message:', error.message);
    setAvailableRides([]);
  } finally {
    setLoading(false);
  }
};

useEffect(() => {
  fetchPastRides();
  fetchAvailableRides(); // Fetch available rides on component mount
}, []);



  const fetchUpcomingRides = async () => {
    setLoading(true);
    try {
      console.log('🔄 [RidesScreen] Fetching upcoming rides...');
      const response = await fetch(`${API_BASE_URL}/rides/getUpcomingDriverRides`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('📋 [RidesScreen] Upcoming Rides Response Status:', response.status);
      console.log('📋 [RidesScreen] Upcoming Rides Response:', JSON.stringify(data, null, 2));

      if (response.ok) {
        // Check different possible data structures
        let ridesData = [];

        if (data?.message?.rides) {
          ridesData = data.message.rides;
          console.log('✅ [RidesScreen] Found rides in data.message.rides:', ridesData.length);
        } else if (data?.data?.rides) {
          ridesData = data.data.rides;
          console.log('✅ [RidesScreen] Found rides in data.data.rides:', ridesData.length);
        } else if (data?.rides) {
          ridesData = data.rides;
          console.log('✅ [RidesScreen] Found rides in data.rides:', ridesData.length);
        } else if (Array.isArray(data)) {
          ridesData = data;
          console.log('✅ [RidesScreen] Data is array:', ridesData.length);
        } else {
          console.warn('⚠️ [RidesScreen] Unexpected upcoming rides data structure:', data);
        }

        dispatch(overwriteStore({ name: 'UpcomingDriverRides', value: ridesData }));
        setUpcomingRides(ridesData);
        console.log('📊 [RidesScreen] Set upcoming rides count:', ridesData.length);
      } else {
        console.error('❌ [RidesScreen] Upcoming rides API failed:', response.status, data);
      }
    } catch (error) {
      console.error('❌ [RidesScreen] Error fetching upcoming rides:', error);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchUpcomingRides();
  }, []);
  
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const day = date.getDate();
    const month = date.toLocaleString('default', { month: 'long' });
    const ampm = hours >= 12 ? 'pm' : 'am';
    const formattedHours = hours % 12 || 12;
    const formattedMinutes = minutes.toString().padStart(2, '0');
    
    // Add suffix to day
    const getDaySuffix = (day) => {
      if (day > 3 && day < 21) return 'th';
      switch (day % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    };
  
    return `${day}${getDaySuffix(day)} ${month} ${formattedHours}:${formattedMinutes}${ampm}`;
  };

  const renderRideItem = ({ item }) => (
    <TouchableOpacity
      style={styles.rideItem}
      onPress={() => navigation.navigate('RidesDetail', { rideId:item?.rideId })}
    >
      <Image
        source={item.carDetails?.carImgUrl ? { uri: item.carDetails.carImgUrl } : images.CarSingle}
        style={styles.rideIcon}
      />
      <View style={styles.rideDetails}>
        <Text style={styles.rideType}>{item.rideType?.charAt(0).toUpperCase() + item.rideType?.slice(1)}</Text>
        {/* <Text style={styles.rideDate}>
          {item.from?.length > 10 ? `${item.from.substring(0, 10)}...` : item.from} →
          {item.to[0]?.place?.length > 10 ? `${item.to[0]?.place.substring(0, 10)}...` : item.to[0]?.place}
        </Text> */}
        <Text style={{marginVertical: 5}}>
          {item.createdAt ? formatDate(item.createdAt) : (item.passengerName ? `Passenger: ${item.passengerName}` : 'Available Ride')}
        </Text>
        {/* <Text style={styles.rideDate}>KM: {item.kmDistance}</Text> */}
        {/* <Text style={styles.rideDate}>Charge: ${item.charge}</Text> */}
        <Text style={[styles.rideStatus, item.status === 'Canceled' && { color: 'green' }]}>
          {item.status || 'Available'}
        </Text>
      </View>
      <TouchableOpacity
        style={styles.viewButton}
        onPress={() => navigation.navigate('RidesDetail', { rideId: item?.rideId })}
      >
        <View style={{flexDirection: 'row', alignItems: 'center', }}>
          <Icon name="eye" size={16} color={COLORS.primary} style={{marginRight: 10}} />
          <Text style={styles.viewButtonText}>View</Text>
        </View>
      </TouchableOpacity>
    </TouchableOpacity>
  );


  

  return (
    <View style={styles.container}>
      {/* Header */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View style={styles.header}>
        <TouchableOpacity>
          {/* <Image source={images.goback} style={styles.backIcon} /> */}
        </TouchableOpacity>
        <Text style={styles.headerTitle}>            Rides</Text>
        <Text style={styles.headerTitle}>             </Text>
      </View>

      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'Available' && styles.activeTab]}
          onPress={() => {
            setSelectedTab('Available');
            fetchAvailableRides();
          }}
        >
          <Text style={[styles.tabText, selectedTab === 'Available' && styles.activeTabText]}>Available</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'Upcoming' && styles.activeTab]}
          onPress={() => setSelectedTab('Upcoming')}
        >
          <Text style={[styles.tabText, selectedTab === 'Upcoming' && styles.activeTabText]}>Upcoming</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, selectedTab === 'Past' && styles.activeTab]}
          onPress={() => setSelectedTab('Past')}
        >
          <Text style={[styles.tabText, selectedTab === 'Past' && styles.activeTabText]}>Past</Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {selectedTab === 'Available' ? (
        <FlatList
          data={availableRides}
          renderItem={renderRideItem}
          keyExtractor={(item) => item.rideId || item.id || item._id}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle}>No available rides</Text>
              <TouchableOpacity onPress={() => fetchAvailableRides()}>
                <Text style={styles.emptyAction}>Refresh to check →</Text>
              </TouchableOpacity>
            </View>
          }
          refreshing={refreshing}
          onRefresh={() => {
            setRefreshing(true);
            fetchAvailableRides().finally(() => setRefreshing(false));
          }}
        />
      ) : selectedTab === 'Upcoming' ? (
        <FlatList

          data={upcomingRides}
          renderItem={renderRideItem}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle}>No ride yet, Do you want a ride</Text>
              <TouchableOpacity onPress={() => navigation.navigate('TabStack', { screen: 'Home' })}>
                <Text style={styles.emptyAction}>Get a passenger </Text>
              </TouchableOpacity>
            </View>
          }
        />
      ) : (
        <FlatList
          data={pastRides}
          renderItem={renderRideItem}
          keyExtractor={(item) => item.id}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle}>No ride yet, Do you want a ride</Text>
              <TouchableOpacity onPress={() => navigation.navigate('TabStack', { screen: 'Home' })}>
                <Text style={styles.emptyAction}>Get a passenger →</Text>
              </TouchableOpacity>
            </View>}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  backIcon: {
    width: 24,
    height: 24,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  tabs: {
    flexDirection: 'row',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    paddingBottom: 10,
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.primary,
  },
  tabText: {
    ...FONTS.h3,
    color: COLORS.black,
    fontSize: 14,
  },
  activeTabText: {
    color: COLORS.primary,
  },
  rideItem: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: COLORS.white,
    borderRadius: 7,
    padding: 15,
    marginBottom: 15,
  },
  rideIcon: {
    width: 60,
    height: 80,
    marginRight: 15,
    resizeMode: 'cover',
  },
  rideDetails: {
    flex: 1,
  },
  rideType: {
    ...FONTS.body3,
    fontSize: 15,
    color: COLORS.black,
  },
  rideDate: {
    ...FONTS.body4,
    color: COLORS.black,
    fontSize: 12,

  },
  rideStatus: {
    ...FONTS.body4,
    color: COLORS.red,
    fontSize: 12,

  },
  viewButton: {
    // backgroundColor: COLORS.primary,
      backgroundColor: COLORS.white,
    borderRadius: 20,
    paddingHorizontal: 25,
    paddingVertical: 10,
  },
  viewButtonText: {
    ...FONTS.body4,
    color: COLORS.primary,
  },
  emptyState: {
    // alignItems: 'center',
    marginTop: 50,
    backgroundColor: "#fff",
    padding: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.border,

  },
  emptyTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 10,
  },
  emptyAction: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
});

export default RidesScreen;
