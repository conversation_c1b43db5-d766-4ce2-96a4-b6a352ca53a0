import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Alert,
  Platform,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import {COLORS, images} from '../../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socketService from '../../services/socketService';
import {
  launchCamera,
  launchImageLibrary,
  MediaType,
} from 'react-native-image-picker';
import {PERMISSIONS, request, check, RESULTS} from 'react-native-permissions';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';
import {useSelector, useDispatch} from 'react-redux';
import {API_BASE_URL} from '../../config/apiConfig';
import {overwriteStore} from '../../../redux/ActionCreator';

// Define types
interface Message {
  id?: string;
  _id?: string;
  text?: string;
  message?: string;
  mediaLink?: string | null;
  isSent?: boolean;
  from?: string;
  status?: 'pending' | 'sent' | 'failed';
  rideId?: string;
  file?: any;
}

interface SelectedFile {
  uri: string;
  type?: string;
  name?: string;
  // Note: base64 removed to prevent storage issues
}

export default function ChatScreen() {
  const navigation = useNavigation<any>();
  const [messages, setMessages] = useState<Message[]>([]);
  const [message, setMessage] = useState<string>('');
  const [selectedFile, setSelectedFile] = useState<SelectedFile | null>(null);
  const route = useRoute();
  const {rideId, passengerId, passengerName, passengerImg, driverId, fromMessages} = route.params as {
    rideId: string;
    passengerId?: string;
    passengerName?: string;
    passengerImg?: string;
    driverId?: string;
    fromMessages?: boolean;
  };
  const [isOptionsVisible, setIsOptionsVisible] = useState<boolean>(false);
  const [recordedAudioPath, setRecordedAudioPath] = useState<string>('');

  // State for passenger info (in case route params are missing)
  const [passengerInfo, setPassengerInfo] = useState({
    name: passengerName || '',
    image: passengerImg || '',
    id: passengerId || ''
  });

  // Get driver information from Redux store
  const dispatch = useDispatch();
  const userProfile = useSelector((state: any) => state.store.GetProfile || {});
  const fullReduxStore = useSelector((state: any) => state.store);

  console.log('💬 [ChatScreen] Initializing chat for ride:', rideId);
  console.log('💬 [ChatScreen] Passenger info:', { passengerName, passengerImg, passengerId });
  console.log('💬 [ChatScreen] Navigation source:', fromMessages ? 'Messages Screen' : 'Other');
  console.log('🔍 [ChatScreen] Full Redux Store:', JSON.stringify(fullReduxStore, null, 2));
  console.log('🔍 [ChatScreen] User Profile from Redux:', JSON.stringify(userProfile, null, 2));

  // Fetch profile if not available
  useEffect(() => {
    const fetchProfile = async () => {
      if (!userProfile || Object.keys(userProfile).length === 0) {
        try {
          const response = await fetch(`${API_BASE_URL}/driver/profile/getProfile`, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          const data = await response.json();
          console.log('ChatScreen - Profile fetched:', data?.data);

          if (response.ok) {
            dispatch(overwriteStore({name: 'GetProfile', value: data?.data || {}}));
          }
        } catch (error) {
          console.error('ChatScreen - Error fetching profile:', error);
        }
      }
    };

    fetchProfile();
  }, [userProfile, dispatch]);

  // Fetch passenger profile details
  useEffect(() => {
    const fetchPassengerProfile = async () => {
      const currentPassengerId = passengerId || passengerInfo.id;

      if (currentPassengerId && (!passengerInfo.name || !passengerInfo.image)) {
        try {
          console.log('🔄 [ChatScreen] Fetching passenger profile for ID:', currentPassengerId);

          const response = await fetch(`${API_BASE_URL}/passenger/profile/getApassengerDetail/${currentPassengerId}`, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          });

          const data = await response.json();
          console.log('👤 [ChatScreen] Passenger profile response:', JSON.stringify(data, null, 2));

          if (response.ok && data) {
            // Update passenger info with fetched data
            const profileData = data.data || data.message || data;

            setPassengerInfo(prev => ({
              ...prev,
              name: profileData.firstName || profileData.name || prev.name || 'Passenger',
              image: profileData.profileImg || profileData.profileImage || profileData.image || prev.image || '',
              id: currentPassengerId
            }));

            console.log('✅ [ChatScreen] Passenger profile updated:', {
              name: profileData.firstName || profileData.name,
              image: profileData.profileImg || profileData.profileImage || profileData.image
            });
          } else {
            console.warn('⚠️ [ChatScreen] Failed to fetch passenger profile:', response.status, data);
          }
        } catch (error) {
          console.error('❌ [ChatScreen] Error fetching passenger profile:', error);
        }
      } else {
        console.log('ℹ️ [ChatScreen] Passenger profile not needed or already available:', {
          passengerId: currentPassengerId,
          hasName: !!passengerInfo.name,
          hasImage: !!passengerInfo.image
        });
      }
    };

    fetchPassengerProfile();
  }, [passengerId, passengerInfo.id]);

  // Debug: Log passenger info changes
  useEffect(() => {
    console.log('👤 [ChatScreen] Passenger info updated:', {
      name: passengerInfo.name,
      image: passengerInfo.image,
      id: passengerInfo.id,
      fromRoute: {
        passengerId,
        passengerName,
        passengerImg
      }
    });
  }, [passengerInfo]);

  // Check all possible driver ID fields - use computed driver ID if route param is missing
  const computedDriverId = userProfile.driverId ||
                           userProfile._id ||
                           userProfile.id ||
                           userProfile.userId ||
                           'RFROK0CZRDDR';

  // Use route param driverId if available, otherwise use computed one
  const finalDriverId = driverId || computedDriverId;

  console.log('Route Driver ID:', driverId);
  console.log('Computed Driver ID:', computedDriverId);
  console.log('Final Driver ID:', finalDriverId);

  useEffect(() => {
    console.log({rideId, passengerName});

    console.log('🔄 [ChatScreen] Using unified socket service for chat and calls');

    // If passenger info is missing, try to fetch it from ride details
    if (!passengerName || !passengerImg) {
      fetchPassengerInfo();
    }
  }, []);

  const fetchPassengerInfo = async () => {
    try {
      // Try to get ride details to extract passenger information
      const response = await fetch(`${API_BASE_URL}/ride/${rideId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const rideData = await response.json();
        if (rideData.success && rideData.data) {
          const ride = rideData.data;
          // Update passenger info from ride data
          setPassengerInfo({
            name: ride.passengerName || ride.passenger?.name || 'Passenger',
            image: ride.passengerImg || ride.passenger?.profileImg || ride.passenger?.image || '',
            id: ride.passengerId || ride.passenger?.id || ride.passenger?._id || ''
          });
        }
      }
    } catch (error) {
      console.error('Error fetching passenger info:', error);
      // Keep default values if fetch fails
    }
  };
  const renderMessage = ({item}: {item: Message}) => {
    const isImage =
      item.mediaLink &&
      typeof item.mediaLink === 'string' &&
      item.mediaLink.trim();
    const isText =
      (item.text && typeof item.text === 'string' && item.text.trim() !== '') ||
      (item.message &&
        typeof item.message === 'string' &&
        item.message.trim() !== '');

    if (!isText && !isImage) return null;

    return (
      <View
        style={[
          styles.messageContainer,
          item.isSent ? styles.sentMessage : styles.receivedMessage,
        ]}>
        {isImage && (
          <Image
            source={{uri: item.mediaLink || ''}}
            style={{width: 120, height: 120, borderRadius: 10}}
            resizeMode="cover"
          />
        )}

        {isText && (
          <Text style={item.isSent ? styles.messageText : styles.messageTextt}>
            {item.text || item.message}
          </Text>
        )}

        {item.isSent && (
          <Text style={{fontSize: 10, color: '#D1D5DB', alignSelf: 'flex-end'}}>
            {item.isSent ? '✓✓' : '✓'}
          </Text>
        )}
      </View>
    );
  };

  // Helper function to safely save messages to AsyncStorage
  const saveMessagesToStorage = async (messages: Message[]) => {
    try {
      // Create a lightweight version of messages for storage (without large base64 data)
      const lightweightMessages = messages.map(msg => ({
        ...msg,
        // Remove heavy data that might cause storage issues
        file: msg.file ? {
          uri: msg.file.uri,
          type: msg.file.type,
          name: msg.file.name,
          // Don't store base64 data in AsyncStorage
        } : null,
      }));

      await AsyncStorage.setItem(`chat_${rideId}`, JSON.stringify(lightweightMessages));
    } catch (error) {
      console.error('Error saving messages to storage:', error);
      // If storage fails, continue without saving locally
    }
  };

  // Load messages when component mounts
  useEffect(() => {
    const loadMessages = async () => {
      try {
        // First, try to load messages from AsyncStorage
        const savedMessages = await AsyncStorage.getItem(`chat_${rideId}`);
        if (savedMessages) {
          try {
            const parsedMessages = JSON.parse(savedMessages);
            setMessages(parsedMessages);
          } catch (parseError) {
            console.error('Error parsing saved messages:', parseError);
            // Clear corrupted data
            await AsyncStorage.removeItem(`chat_${rideId}`);
          }
        }

        // Then, fetch messages from server to get the latest using getRideChat
        console.log('🔄 [ChatScreen] Fetching chat history for rideId:', rideId);

        // Try multiple approaches to get chat history
        const chatRequest = { rideId, driverId: finalDriverId, passengerId };
        console.log('💬 [ChatScreen] Chat request payload:', chatRequest);

        if (socketService.driverSocket && socketService.isDriverConnected) {
          console.log('📡 [ChatScreen] Using driver socket for chat history');
          socketService.emitChatEvent("getRideChat", chatRequest);

          // Also try alternative event names
          setTimeout(() => {
            socketService.emitChatEvent("getChatHistory", chatRequest);
            socketService.emitChatEvent("getMessages", chatRequest);
          }, 1000);
        } else {
          console.warn('⚠️ [ChatScreen] Driver socket not connected, trying call socket');
          if (socketService.socket) {
            console.log('📡 [ChatScreen] Using call socket for chat history');
            socketService.socket.emit("getRideChat", chatRequest);

            // Also try alternative event names
            setTimeout(() => {
              socketService.socket.emit("getChatHistory", chatRequest);
              socketService.socket.emit("getMessages", chatRequest);
            }, 1000);
          }
        }
      } catch (error) {
        console.error('❌ [ChatScreen] Error loading messages:', error);
        // If loading fails, still try to fetch from server
        console.log('🔄 [ChatScreen] Retrying chat history fetch after error');
        const retryRequest = { rideId, driverId: finalDriverId, passengerId };

        if (socketService.driverSocket && socketService.isDriverConnected) {
          socketService.emitChatEvent("getRideChat", retryRequest);
        } else if (socketService.socket) {
          socketService.socket.emit("getRideChat", retryRequest);
        }
      }
    };

    loadMessages();
  }, [rideId]);

  // Listen for new messages and chat history
  useEffect(() => {
    // Helper function to check if message already exists
    const messageExists = (existingMessages: Message[], newMessageId: string) => {
      return existingMessages.some(msg =>
        msg._id === newMessageId ||
        msg.id === newMessageId
      );
    };

    // Helper function to format messages consistently
    const formatMessage = (msg: any, index: number, prefix: string = 'msg') => {
      console.log(`💬 [ChatScreen] Formatting message ${index} (${prefix}):`, JSON.stringify(msg, null, 2));

      // Use server ID if available, otherwise create stable ID based on content
      const messageId = msg._id || msg.id || `${rideId}-${prefix}-${index}`;

      // Extract message text from various possible fields
      const messageText = msg.message || msg.text || msg.content || msg.body || '';

      // Determine sender - check various possible fields
      const sender = msg.from || msg.sender || msg.senderId || msg.user || '';
      const isFromPassenger = sender === 'Passenger' ||
                             sender === 'passenger' ||
                             msg.isPassenger === true ||
                             msg.userType === 'passenger';

      // Extract media/image links
      const mediaLink = msg.mediaLink || msg.media || msg.image || msg.attachment || null;

      const formattedMessage = {
        id: messageId,
        _id: messageId,
        text: messageText,
        message: messageText,
        mediaLink: mediaLink,
        isSent: !isFromPassenger, // Driver's messages are "sent", passenger's are "received"
        timestamp: msg.timestamp || msg.createdAt || msg.time || Date.now(),
        sender: sender,
        originalData: msg // Keep original for debugging
      };

      console.log(`💬 [ChatScreen] Formatted message ${index}:`, formattedMessage);
      return formattedMessage;
    };

    // Listen for chat history response from getRideChat
    const handleChatHistory = async (data: any) => {
      console.log('💬 [ChatScreen] Chat history received:', JSON.stringify(data, null, 2));

      let messagesArray = [];
      let dataSource = '';

      // Check multiple possible data structures
      if (data?.message && Array.isArray(data.message)) {
        messagesArray = data.message;
        dataSource = 'data.message';
      } else if (data?.data && Array.isArray(data.data)) {
        messagesArray = data.data;
        dataSource = 'data.data';
      } else if (data?.messages && Array.isArray(data.messages)) {
        messagesArray = data.messages;
        dataSource = 'data.messages';
      } else if (data?.chat && Array.isArray(data.chat)) {
        messagesArray = data.chat;
        dataSource = 'data.chat';
      } else if (Array.isArray(data)) {
        messagesArray = data;
        dataSource = 'data (array)';
      } else if (data?.success && data?.data && Array.isArray(data.data)) {
        messagesArray = data.data;
        dataSource = 'data.success.data';
      } else {
        console.warn('⚠️ [ChatScreen] Unexpected chat history data structure:', data);
        console.log('💬 [ChatScreen] Available keys:', Object.keys(data || {}));
        return;
      }

      console.log(`💬 [ChatScreen] Found ${messagesArray.length} messages in ${dataSource}`);

      if (messagesArray.length > 0) {
        console.log('💬 [ChatScreen] Sample message structure:', JSON.stringify(messagesArray[0], null, 2));

        const formattedMessages = messagesArray.map((msg: any, index: number) => {
          const formatted = formatMessage(msg, index, 'history');
          console.log(`💬 [ChatScreen] Formatted message ${index}:`, formatted);
          return formatted;
        });

        console.log('💬 [ChatScreen] Setting formatted messages:', formattedMessages.length);
        setMessages(formattedMessages);
        await saveMessagesToStorage(formattedMessages);
      } else {
        console.log('💬 [ChatScreen] No messages found in chat history');
      }
    };

    // Use driver socket for chat events, fallback to call socket
    if (socketService.driverSocket && socketService.isDriverConnected) {
      socketService.onChatEvent("getRideChat", handleChatHistory);
      socketService.onChatEvent("getChatHistory", handleChatHistory);
      socketService.onChatEvent("getMessages", handleChatHistory);
      socketService.onChatEvent("chatHistory", handleChatHistory);
    } else if (socketService.socket) {
      socketService.socket.on("getRideChat", handleChatHistory);
      socketService.socket.on("getChatHistory", handleChatHistory);
      socketService.socket.on("getMessages", handleChatHistory);
      socketService.socket.on("chatHistory", handleChatHistory);
    }

    // Listen for new messages from passengers
    const handleNewMessage = async (data: any) => {
      console.log('💬 [ChatScreen] New message from Passenger:', data);

      if (data?.message && Array.isArray(data.message)) {
        const newFormattedMessages = data.message.map((msg: any, index: number) =>
          formatMessage(msg, index, 'new')
        );

        setMessages(prevMessages => {
          // Filter out duplicates before adding
          const filteredNewMessages = newFormattedMessages.filter((newMsg: Message) =>
            !messageExists(prevMessages, newMsg._id || newMsg.id || '')
          );

          if (filteredNewMessages.length > 0) {
            console.log('💬 [ChatScreen] Adding new messages:', filteredNewMessages.length);
            const updatedMessages = [...prevMessages, ...filteredNewMessages];
            saveMessagesToStorage(updatedMessages);
            return updatedMessages;
          }

          return prevMessages;
        });
      } else {
        console.warn('⚠️ [ChatScreen] Unexpected new message data structure:', data);
      }
    };

    // Use driver socket for chat events, fallback to call socket
    if (socketService.driverSocket && socketService.isDriverConnected) {
      socketService.onChatEvent('chatWithPassenger', handleNewMessage);
    } else {
      socketService.onEvent('chatWithPassenger', handleNewMessage);
    }

    // Optional: Listen for legacy chat history response (if still needed)
    const handleLegacyChatHistory = async (data: any) => {
      console.log('💬 [ChatScreen] Legacy chat history response:', data);
      if (data?.success && data?.data && Array.isArray(data.data)) {
        const formattedMessages = data.data.map((msg: any, index: number) =>
          formatMessage(msg, index, 'legacy')
        );

        // Only set if we don't already have messages to avoid overwriting
        setMessages(prevMessages => {
          if (prevMessages.length === 0) {
            console.log('💬 [ChatScreen] Setting legacy chat history:', formattedMessages.length);
            saveMessagesToStorage(formattedMessages);
            return formattedMessages;
          }
          return prevMessages;
        });
      }
    };

    // Use driver socket for chat events, fallback to call socket
    if (socketService.driverSocket && socketService.isDriverConnected) {
      socketService.onChatEvent('getChatHistoryResponse', handleLegacyChatHistory);
    } else {
      socketService.onEvent('getChatHistoryResponse', handleLegacyChatHistory);
    }

    return () => {
      // Clean up event listeners to prevent memory leaks
      console.log('💬 [ChatScreen] Cleaning up chat event listeners');

      const chatEvents = ['getRideChat', 'getChatHistory', 'getMessages', 'chatHistory'];
      const messageEvents = ['chatWithPassenger'];
      const legacyEvents = ['getChatHistoryResponse'];

      // Clean up driver socket listeners
      if (socketService.driverSocket) {
        chatEvents.forEach(event => {
          socketService.driverSocket.off(event, handleChatHistory);
        });
        messageEvents.forEach(event => {
          socketService.driverSocket.off(event, handleNewMessage);
        });
        legacyEvents.forEach(event => {
          socketService.driverSocket.off(event, handleLegacyChatHistory);
        });
      }

      // Clean up call socket listeners (fallback)
      if (socketService.socket) {
        chatEvents.forEach(event => {
          socketService.socket.off(event, handleChatHistory);
        });
        messageEvents.forEach(event => {
          socketService.socket.off(event, handleNewMessage);
        });
        legacyEvents.forEach(event => {
          socketService.socket.off(event, handleLegacyChatHistory);
        });
      }
    };
  }, [rideId]);

  const handleMessageChange = (text: string) => {
    setMessage(text);
    setSelectedFile(null);
  };

  const handleMediaSelection = (type: string) => {
    // Don't include base64 to avoid storage issues
    const options = {mediaType: 'photo' as const, includeBase64: false};

    const callback = (response: any) => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
        return;
      }
      if (response.errorMessage) {
        Alert.alert('Error', response.errorMessage);
        return;
      }
      if (!response.assets || response.assets.length === 0) {
        console.log('No file selected');
        return;
      }

      const file = response.assets[0];

      setSelectedFile({
        uri: file.uri,
        type: file.type,
        name: file.fileName,
        // Don't store base64 to prevent storage issues
      });

      setMessage(''); // Prevent message input when file is selected
    };

    if (type === 'camera') {
      launchCamera(options, callback);
    } else {
      launchImageLibrary(options, callback);
    }
  };

  const sendMessage = async () => {
    if (!message.trim() && !selectedFile && !recordedAudioPath) {
      Alert.alert('Error', 'Enter a message, select a file, or record audio.');
      return;
    }

    // Generate a stable unique ID for the message
    const timestamp = Date.now();
    const uniqueId = `${rideId}-sent-${timestamp}`;

    const chatData: Message = {
      id: uniqueId,
      _id: uniqueId, // Use same ID to avoid conflicts
      rideId: rideId,
      message: selectedFile ? '' : recordedAudioPath ? '' : message.trim(),
      text: selectedFile ? '' : recordedAudioPath ? '' : message.trim(), // Backup text field
      file: selectedFile || null,
      isSent: true,
      status: 'pending',
    };

    setMessages(prevMessages => {
      // Check if message already exists to prevent duplicates
      const messageExists = prevMessages.some(msg =>
        msg._id === chatData._id || msg.id === chatData.id
      );

      if (!messageExists) {
        const updatedMessages = [...prevMessages, chatData];
        saveMessagesToStorage(updatedMessages);
        return updatedMessages;
      }

      return prevMessages;
    });

    try {
      console.log('💬 [ChatScreen] Sending message:', chatData);

      // Use driver socket for chat, fallback to call socket
      if (socketService.driverSocket && socketService.isDriverConnected) {
        socketService.emitChatEvent('chatWithPassenger', chatData);
      } else {
        console.warn('⚠️ [ChatScreen] Driver socket not connected, using call socket');
        socketService.emitEvent('chatWithPassenger', chatData);
      }
    } catch (error) {
      console.error('💬 [ChatScreen] Error sending message:', error);
      Alert.alert('Message Failed', 'Your message could not be sent.');
    }

    setMessage('');
    setSelectedFile(null);
  };

  return (
    <LinearGradient
      colors={[COLORS.light_blue, '#fff']}
      style={styles.container}>
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={images.goback}
            style={{width: 30, height: 30, resizeMode: 'contain'}}
          />
        </TouchableOpacity>
        <View style={styles.profileContainer}>
          <Image
            source={
              (passengerInfo.image || passengerImg)
                ? {uri: passengerInfo.image || passengerImg}
                : images.profile
            }
            style={styles.profileImage}
            onError={() => {
              console.log('❌ [ChatScreen] Failed to load passenger image:', passengerInfo.image || passengerImg);
            }}
            onLoad={() => {
              console.log('✅ [ChatScreen] Passenger image loaded successfully');
            }}
          />
          <Text style={styles.profileName}>
            {passengerInfo.name || passengerName || 'Passenger'}
          </Text>
        </View>
        <View style={styles.headerIcons}>
          <TouchableOpacity
            onPress={() => {
              // Initiate video call using unified socket service
              const currentPassengerId = passengerInfo.id || passengerId;
              if (currentPassengerId) {
                socketService.callUser(rideId, currentPassengerId, true);
              }

              // Navigate to video call screen
              const callInfo = {
                rideId: rideId,
                receiverId: currentPassengerId || 'passenger_id',
                recipientName: passengerInfo.name || passengerName || 'Passenger',
                recipientImg: passengerInfo.image || passengerImg || 'https://i.pravatar.cc/300',
                recipientPhone: '',
                isVideoCall: true,
                driverId: finalDriverId, // Pass the driver ID from Redux
              };
              navigation.navigate('CallScreen', callInfo);
            }}
            style={styles.icon}>
            <Image
              source={images.Videoicon}
              style={{
                width: 30,
                height: 30,
                resizeMode: 'contain',
                marginHorizontal: 5,
              }}
            />
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              // Initiate voice call using unified socket service
              const currentPassengerId = passengerInfo.id || passengerId;
              if (currentPassengerId) {
                socketService.callUser(rideId, currentPassengerId, false);
              }

              // Navigate to voice call screen
              const callInfo = {
                rideId: rideId,
                receiverId: currentPassengerId || 'passenger_id',
                recipientName: passengerInfo.name || passengerName || 'Passenger',
                recipientImg: passengerInfo.image || passengerImg || 'https://i.pravatar.cc/300',
                recipientPhone: '',
                isVideoCall: false,
                driverId: finalDriverId, // Pass the driver ID from Redux
              };
              navigation.navigate('CallScreen', callInfo);
            }}
            style={styles.icon}>
            <Image
              source={images.call}
              style={{width: 15, height: 15, resizeMode: 'contain'}}
            />
          </TouchableOpacity>
        </View>
      </View>

      <Text style={styles.infoText}>
        Keep your account safe—don’t share personal or account information here
      </Text>



      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item, index) => {
          // Use stable, consistent keys to prevent duplicates
          const key = item?._id ||
                     item?.id ||
                     `${rideId}-msg-${index}`;

          return key.toString();
        }}
        ListEmptyComponent={() => (
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>
            <Text style={{ color: '#666', textAlign: 'center' }}>
              No messages yet. Start a conversation!
            </Text>
          </View>
        )}
        style={{ flex: 1 }}
      />

      {isOptionsVisible && (
        <View style={styles.optionsContainer}>
          {[
            { id: '1', icon: images.camera, text: 'Camera' },
            { id: '2', icon: images.Micicon, text: 'Record' },
            { id: '3', icon: images.Contacticon, text: 'Contact' },
            {id: '4', icon: images.Imageicon, text: 'Gallery'},
          ].map(option => (
            <TouchableOpacity
              key={option.id}
              style={styles.option}
              onPress={() => {
                if (option.text === 'Camera') handleMediaSelection('camera');
                if (option.text === 'Gallery') handleMediaSelection('gallery');
              }}>
              <Image
                source={option.icon}
                style={[
                  styles.driverImage,
                  {width: 30, height: 30, marginRight: 0},
                ]}
              />
              <Text style={styles.optionText}>{option.text}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {selectedFile && (
        <Image
          source={{uri: selectedFile.uri}}
          style={{width: 120, height: 120, borderRadius: 10, marginTop: 5}}
          resizeMode="cover"
        />
      )}

      <View style={styles.inputContainer}>
        <TouchableOpacity
          onPress={() => setIsOptionsVisible(!isOptionsVisible)}>
          <Image
            source={images.addd}
            style={{width: 50, resizeMode: 'contain', height: 50}}
          />
        </TouchableOpacity>

        <TextInput
          style={styles.textInput}
          placeholderTextColor={COLORS.grey}
          placeholder="Type a message..."
          value={message}
          onChangeText={handleMessageChange}
          onSubmitEditing={sendMessage}
        />

        <TouchableOpacity onPress={sendMessage}>
          <Image
            source={images.send}
            style={{width: 50, resizeMode: 'contain', height: 50}}
          />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: COLORS.light_blue},
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 16,
  },
  profileImage: {width: 40, height: 40, borderRadius: 20, marginRight: 8},
  profileName: {fontSize: 16, fontWeight: 'bold'},
  headerIcons: {flexDirection: 'row', alignItems: 'center'},
  icon: {marginRight: 16},
  infoText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#6B7280',
    padding: 8,
    backgroundColor: '#fff',
  },
  messagesList: {flex: 1, padding: 16},
  messageContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    maxWidth: '70%',
    marginHorizontal: 10,
  },
  sentMessage: {alignSelf: 'flex-end', backgroundColor: COLORS.primary},
  receivedMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#FFFFFF',
    borderColor: '#E5E7EB',
    borderWidth: 1,
  },
  messageText: {color: '#FFF', fontSize: 14},
  messageTextt: {color: '#000', fontSize: 14},
  messageTime: {
    marginTop: 4,
    fontSize: 10,
    color: '#D1D5DB',
    alignSelf: 'flex-end',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    backgroundColor: '#FFFFFF',
    padding: 16,
    elevation: 3,
    borderRadius: 5,
  },
  driverImage: {width: 50, height: 50, borderRadius: 25, marginRight: 16},
  option: {alignItems: 'center', marginBottom: 16},
  optionText: {marginTop: 8, fontSize: 12, color: '#000'},
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  textInput: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 8,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    padding: 8,
    borderRadius: 8,
    resizeMode: 'contain',
    height: 40,
    width: 40,
  },
});
