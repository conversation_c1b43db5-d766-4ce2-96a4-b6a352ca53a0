import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  FlatList,
  Image,
  Alert,
} from 'react-native';
// import Icon from 'react-native-vector-icons/Ionicons';  // Changed from Expo icons
import {useNavigation, useRoute} from '@react-navigation/native';
import LinearGradient from 'react-native-linear-gradient';
import {COLORS, images} from '../../constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socketService from '../../services/socketService';
import {launchCamera, launchImageLibrary} from 'react-native-image-picker';
import {PERMISSIONS, request, check, RESULTS} from 'react-native-permissions';
import {Platform} from 'react-native';
import AudioRecorderPlayer from 'react-native-audio-recorder-player';
import RNFS from 'react-native-fs';

export default function ChatScreen() {
  const navigation = useNavigation();
  const [messages, setMessages] = useState([]);
  const [messageText, setMessageText] = useState('');
  const [message, setMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const route = useRoute();
  const {rideId} = route.params;
  console.log('Ride ID:', rideId);
  const [isOptionsVisible, setIsOptionsVisible] = useState(false);
  const audioRecorderPlayer = new AudioRecorderPlayer();
  const [recording, setRecording] = useState(false);
  const [recordedAudio, setRecordedAudio] = useState(null);
  const [recordedAudioPath, setRecordedAudioPath] = useState(null);

  const renderMessage = ({item}) => {
    const isImage =
      item.mediaLink &&
      typeof item.mediaLink === 'string' &&
      item.mediaLink.trim();
    const isText =
      item.text && typeof item.text === 'string' && item.text.trim() !== '';

    if (!isText && !isImage) return null;

    return (
      <View
        style={[
          styles.messageContainer,
          item.isSent ? styles.sentMessage : styles.receivedMessage,
        ]}>
        {isImage && (
          <Image
            source={{uri: item.mediaLink}}
            style={{width: 120, height: 120, borderRadius: 10}}
            resizeMode="cover"
          />
        )}

        {isText && (
          <Text style={item.isSent ? styles.messageText : styles.messageTextt}>
            {item.text}
          </Text>
        )}

        {item.isSent && (
          <Text style={{fontSize: 10, color: '#D1D5DB', alignSelf: 'flex-end'}}>
            {item.isSent ? '✓✓' : '✓'}
          </Text>
        )}
      </View>
    );
  };

  useEffect(() => {
    const loadMessages = async () => {
      try {
        const savedMessages = await AsyncStorage.getItem(`chat_${rideId}`);
        if (savedMessages) {
          const parsedMessages = JSON.parse(savedMessages);
          setMessages(parsedMessages);
        }
      } catch (error) {
        console.error('Error loading messages:', error);
      }
    };

    loadMessages();

    return () => {};
  }, [rideId]);

  useEffect(() => {
    socketService.onEvent('chatWithPassenger', data => {
      console.log('New message from Passenger:', data);

      if (data?.message && Array.isArray(data.message)) {
        const formattedMessages = data.message.map(msg => ({
          id: msg._id,
          text: msg.message,
          mediaLink: msg.mediaLink || null,
          isSent: msg.from !== 'Passenger',
        }));

        setMessages(prevMessages => [...prevMessages, ...formattedMessages]); // Append correctly
      }
    });

    return () => {
      socketService.socket.off('chatWithPassenger');
    };
  }, []);

  const handleMessageChange = text => {
    setMessage(text);
    setSelectedFile(null);
  };

  const handleMediaSelection = type => {
    const options = {mediaType: 'photo', includeBase64: true};

    const callback = response => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
        return;
      }
      if (response.errorMessage) {
        Alert.alert('Error', response.errorMessage);
        return;
      }
      if (!response.assets || response.assets.length === 0) {
        console.log('No file selected');
        return;
      }

      const file = response.assets[0];

      // Immediately display the selected image before sending
      setSelectedFile({
        uri: file.uri,
        base64: file.base64,
        type: file.type,
        name: file.fileName,
      });

      setMessage(''); // Prevent message input when file is selected
    };

    if (type === 'camera') {
      launchCamera(options, callback);
    } else {
      launchImageLibrary(options, callback);
    }
  };

  const sendMessage = async () => {
    if (!message.trim() && !selectedFile && !recordedAudioPath) {
      Alert.alert('Error', 'Enter a message, select a file, or record audio.');
      return;
    }

    let chatData = {
      // id: Date.now().toString(),
      rideId: rideId,
      massage: selectedFile ? '' : recordedAudioPath ? '' : message.trim(),
      mediaLink: selectedFile
        ? selectedFile.uri
        : recordedAudioPath
        ? recordedAudioPath
        : null,
      // isSent: true,
      // status: "pending",
    };

    setMessages(prevMessages => {
      const updatedMessages = [...prevMessages, chatData];

      AsyncStorage.setItem(`chat_${rideId}`, JSON.stringify(updatedMessages)); // Save messages immediately

      return updatedMessages;
    });

    try {
      socketService.emitEvent('chatWithPassenger', chatData, response => {
        if (response?.success) {
          setMessages(prevMessages =>
            prevMessages.map(msg =>
              msg.id === chatData.id ? {...msg, status: 'sent'} : msg,
            ),
          );
        } else {
          throw new Error('Message failed');
        }
      });
    } catch (error) {
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === chatData.id ? {...msg, status: 'failed'} : msg,
        ),
      );
      Alert.alert('Message Failed', 'Your message could not be sent.');
    }

    setMessage('');
    setSelectedFile(null);
    setRecordedAudioPath(null);
  };

  return (
    <LinearGradient
      colors={[COLORS.light_blue, '#fff']}
      style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          {/* <Icon name="chevron-back" size={24} color="#000" /> */}
          <Image
            source={images.goback}
            style={{width: 30, height: 30, resizeMode: 'contain'}}
          />
        </TouchableOpacity>
        <View style={styles.profileContainer}>
          <Image source={images.driver1} style={styles.profileImage} />
          <Text style={styles.profileName}>Olive Rodrigo</Text>
        </View>
        <View style={styles.headerIcons}>
          <Image
            source={images.Videoicon}
            style={{
              width: 30,
              height: 30,
              resizeMode: 'contain',
              marginHorizontal: 5,
            }}
          />
          <Image
            source={images.call}
            style={{width: 15, height: 15, resizeMode: 'contain'}}
          />
        </View>
      </View>

      {/* Info */}
      <Text style={styles.infoText}>
        Keep your account safe—don’t share personal or account information here
      </Text>

      <FlatList
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item, index) => {
          // Create a unique key using multiple fallbacks
          const key = item?._id || item?.id || `message-${index}`;
          return key.toString();
        }}
      />

      {isOptionsVisible && (
        <View style={styles.optionsContainer}>
          {[
            {id: '1', icon: images.camera, text: 'Camera'},
            {id: '2', icon: images.Micicon, text: 'Record'},
            {id: '3', icon: images.Contacticon, text: 'Contact'},
            {id: '4', icon: images.Imageicon, text: 'Gallery'},
          ].map(option => (
            <TouchableOpacity
              key={option.id}
              style={styles.option}
              onPress={() => {
                if (option.text === 'Camera') handleMediaSelection('camera');
                if (option.text === 'Gallery') handleMediaSelection('gallery');
              }}>
              <Image
                source={option.icon}
                style={[
                  styles.driverImage,
                  {width: 30, height: 30, marginRight: 0},
                ]}
              />
              <Text style={styles.optionText}>{option.text}</Text>
            </TouchableOpacity>
          ))}
        </View>
      )}
      {selectedFile && (
        <Image
          source={{uri: selectedFile.uri}}
          style={{width: 120, height: 120, borderRadius: 10, marginTop: 5}}
          resizeMode="cover"
        />
      )}

      <View style={styles.inputContainer}>
        <TouchableOpacity
          onPress={() => setIsOptionsVisible(!isOptionsVisible)}>
          <Image
            source={images.addd}
            style={{width: 50, resizeMode: 'contain', height: 50}}
          />
        </TouchableOpacity>

        <TextInput
          style={styles.textInput}
          placeholder="Type a message..."
          placeholderTextColor={COLORS.grey}
          value={message}
          onChangeText={handleMessageChange}
          onSubmitEditing={sendMessage}
        />

        <TouchableOpacity onPress={sendMessage}>
          <Image
            source={images.send}
            style={{width: 50, resizeMode: 'contain', height: 50}}
          />
        </TouchableOpacity>
      </View>
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {flex: 1, backgroundColor: COLORS.light_blue},
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 16,
  },
  profileImage: {width: 40, height: 40, borderRadius: 20, marginRight: 8},
  profileName: {fontSize: 16, fontWeight: 'bold'},
  headerIcons: {flexDirection: 'row', alignItems: 'center'},
  icon: {marginRight: 16},
  infoText: {
    textAlign: 'center',
    fontSize: 12,
    color: '#6B7280',
    padding: 8,
    backgroundColor: '#E5E7EB',
  },
  messagesList: {flex: 1, padding: 16},
  messageContainer: {
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
    maxWidth: '70%',
    marginHorizontal: 10,
  },
  sentMessage: {alignSelf: 'flex-end', backgroundColor: COLORS.primary},
  receivedMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#FFFFFF',
    borderColor: '#E5E7EB',
    borderWidth: 1,
  },
  messageText: {color: '#FFF', fontSize: 14},
  messageTextt: {color: '#000', fontSize: 14},
  messageTime: {
    marginTop: 4,
    fontSize: 10,
    color: '#D1D5DB',
    alignSelf: 'flex-end',
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
    backgroundColor: '#FFFFFF',
    padding: 16,
    elevation: 3,
    borderRadius: 5,
  },
  driverImage: {width: 50, height: 50, borderRadius: 25, marginRight: 16},
  option: {alignItems: 'center', marginBottom: 16},
  optionText: {marginTop: 8, fontSize: 12, color: '#000'},
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  textInput: {
    flex: 1,
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 12,
    marginHorizontal: 8,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    padding: 8,
    borderRadius: 8,
    resizeMode: 'contain',
    height: 40,
    width: 40,
  },
});
