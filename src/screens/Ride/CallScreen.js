import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Platform,
  Vibration,
} from 'react-native';
import {
  StreamVideo,
  StreamCall,
  CallContent,
  StreamVideoClient,
} from '@stream-io/video-react-native-sdk';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import InCallManager from 'react-native-incall-manager';
import socketService from '../../services/socketService';
import { useSelector } from 'react-redux';

export default function CallScreen({ route }) {
  // Log route params for debugging
  useEffect(() => {
    console.log('CallScreen route params:', route?.params);
  }, []);

  const [callStatus, setCallStatus] = useState(null);
  const [caller, setCaller] = useState(null);
  const [profileImg, setProfileImg] = useState(null);
  const [callId, setCallId] = useState(route?.params?.callId || null);
  const [rideId] = useState(route?.params?.rideId || 'RFT3ZZSGHPRI'); // Updated with actual ride ID
  const [client, setClient] = useState(null);
  const [call, setCall] = useState(null);
  const [streamCallReady, setStreamCallReady] = useState(false);
  const [isOutgoingCall, setIsOutgoingCall] = useState(false);

  // Initialize call state from route params (for accepted incoming calls)
  useEffect(() => {
    if (route?.params) {
      const {
        isIncoming,
        callerName,
        callerImage,
        callId: paramCallId,
        rideId: paramRideId
      } = route.params;

      console.log('🔄 [CallScreen] Checking if this is an accepted incoming call:', { isIncoming, callerName, paramCallId });

      // If this is an accepted incoming call, set up the call state immediately
      if (isIncoming && paramCallId) {
        console.log('✅ [CallScreen] This is an accepted incoming call, setting up call state');
        setCaller((callerName || 'Unknown Caller').replace(/^Incoming call from\s*/i, ''));
        setProfileImg(callerImage || 'https://i.pravatar.cc/300');
        setCallStatus('Connected'); // Set to connected since call was already accepted
        setIsOutgoingCall(false);

        // Initialize Stream client immediately for accepted calls
        if (global.streamClient) {
          console.log('🔄 [CallScreen] Using existing Stream client for accepted call');
          setClient(global.streamClient);
        } else {
          console.log('⚠️ [CallScreen] Global Stream client not available, waiting for token...');
          // The token handlers will initialize the client when tokens are received
        }
      }
    }
  }, [route?.params]);

  // Audio call control states
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(false);
  const [isCameraOff, setIsCameraOff] = useState(false);
  const [callEnded, setCallEnded] = useState(false); // Track if call has ended

  // Get recipient information from route params (for calls)
  // The RideDetail screen passes receiverId as the recipient ID
  const [recipientId] = useState(route?.params?.receiverId || 'RF1N7EIDLEPA'); // Default from ride details
  const [recipientName] = useState(route?.params?.recipientName || 'Recipient');
  const [recipientImg] = useState(route?.params?.recipientImg || 'https://i.pravatar.cc/300');
  const [recipientPhone] = useState(route?.params?.recipientPhone || '');
  const navigation = useNavigation();

  // Get user information from Redux store
  const userProfile = useSelector(state => state.store.GetProfile || {});
  // Use the driver ID from the route params or fallback to profile ID
  const driverId = route?.params?.driverId;
  const userName = `${userProfile.firstName || ''} ${userProfile.lastName || ''}`.trim() || 'Driver';
  const userImage = userProfile.profileImg || 'https://i.pravatar.cc/300';

  console.log('Driver ID for Stream.io:', driverId);
  // Note: Using video call socket service instead of regular socket service for calls

  // Initialize Stream.io client and handle socket events
  useEffect(() => {
    console.log('🔄 [CallScreen] Using unified socket service for call handling');
    // Set up unified socket event handlers
    socketService.setIncomingVideoCallHandler((data) => {
      console.log('📹 [CallScreen] Incoming video call data:', data);
      // Only handle as incoming call if it has proper incoming call structure
      if (data.caller && data.callId && data.message !== "Call initiated") {
        console.log("📞 [CallScreen] Processing incoming call");
        setCaller(data.caller?.name || 'Unknown');
        setProfileImg(data.caller?.profileImg || 'https://i.pravatar.cc/300');
        setCallStatus('Ringing');
        setCallId(data.callId);
        setIsOutgoingCall(false); // Mark as incoming call
      } else if (data.message === "Call initiated" && data.success) {
        // This is a response to our call initiation, not an incoming call
        console.log('✅ [CallScreen] Call initiation confirmed by server');
        // Keep the current outgoing call state
      } else {
        console.log('⚠️ [CallScreen] Unhandled incoming call data:', data);
      }
    });

    socketService.setVideoCallAcceptedHandler(() => {
      console.log('✅ [CallScreen] Video call accepted');
      setCallStatus('Connected');
      // isOutgoingCall state is preserved from the call initiation
    });

    socketService.setVideoCallEndedHandler(() => {
      console.log('🔚 [CallScreen] Video call ended remotely');
      setCallStatus('Rejected');
      setCallEnded(true); // Mark call as ended to prevent duplicate leave attempts

      // Clean up Stream.io call without trying to leave (already ended remotely)
      if (call) {
        console.log('🔚 [CallScreen] Cleaning up Stream.io call (ended remotely)');
        setCall(null);
      }

      // Clean up InCallManager
      try {
        InCallManager.stop();
        InCallManager.setKeepScreenOn(false);
        console.log('✅ [CallScreen] InCallManager cleaned up');
      } catch (error) {
        console.error('❌ [CallScreen] Error cleaning up InCallManager:', error);
      }

      // Reset states and navigate back
      setCallStatus(null);
      setCaller(null);
      setStreamCallReady(false);
      setIsOutgoingCall(false);
      setIsMuted(false);
      setIsSpeakerOn(false);
      setIsCameraOff(false);

      navigation.goBack();
    });

    // Listen for Stream.io tokens from server (similar to web implementation)
    socketService.setCallerTokenHandler(({ token, callId: receivedCallId }) => {
      console.log('📞 [CallScreen] Caller Token Received:', token, receivedCallId);
      console.log('👤 [CallScreen] Using Driver ID:', driverId);
      try {
        const streamClient = new StreamVideoClient({
          apiKey: 'vjw8jjkqz6z8', // Your Stream.io API key
          token,
          user: {
            id: driverId, // Use driver ID instead of user ID
            name: userName, // Use actual user name from Redux
            image: userImage, // Use actual profile image from Redux
          },
        });

        setClient(streamClient);
        setCallId(receivedCallId);
        console.log('✅ [CallScreen] Stream client initialized as caller with driver ID:', driverId);
      } catch (error) {
        console.error('❌ [CallScreen] Failed to initialize Stream client as caller:', error);
      }
    });

    socketService.setReceiverTokenHandler(({ token, callId: receivedCallId }) => {
      console.log('📞 [CallScreen] Receiver Token Received:', token, receivedCallId);
      console.log('👤 [CallScreen] Using Driver ID:', driverId);
      try {
        const streamClient = new StreamVideoClient({
          apiKey: 'vjw8jjkqz6z8', // Your Stream.io API key
          token,
          user: {
            id: driverId, // Use driver ID instead of user ID
            name: userName, // Use actual user name from Redux
            image: userImage, // Use actual profile image from Redux
          },
        });

        setClient(streamClient);
        setCallId(receivedCallId);
        console.log('✅ [CallScreen] Stream client initialized as receiver with driver ID:', driverId);
      } catch (error) {
        console.error('❌ [CallScreen] Failed to initialize Stream client as receiver:', error);
      }
    });

    return () => {
      console.log('🧹 [CallScreen] Cleaning up unified socket event handlers');
      // Clean up unified socket event handlers
      socketService.setIncomingVideoCallHandler(null);
      socketService.setVideoCallAcceptedHandler(null);
      socketService.setVideoCallEndedHandler(null);
      socketService.setCallerTokenHandler(null);
      socketService.setReceiverTokenHandler(null);
    };
  }, [callId]);

  // Initialize and join Stream call when client and callId are available
  useEffect(() => {
    if (client && callId) {
      try {
        // Determine if it's a video call based on route params
        const isVideoCall = route?.params?.isVideoCall !== false; // Default to video call

        // Create call with specific options like the web implementation
        const streamCall = client.call('default', callId, {
          audio: true,
          video: isVideoCall
        });

        setCall(streamCall);

        streamCall
          .join({ create: true, ring: true, data: { rideId } })
          .then(() => {
            console.log(`Joined call successfully with ${isVideoCall ? 'video' : 'audio only'}.`);
            setStreamCallReady(true);
            setCallStatus('Connected');

            // Initialize InCallManager for audio controls
            InCallManager.start({ media: 'audio', auto: true });
            InCallManager.setKeepScreenOn(true);

            // Set initial speaker state based on call type
            if (!isVideoCall) {
              InCallManager.setSpeakerphoneOn(false); // Default to earpiece for audio calls
              setIsSpeakerOn(false);
            } else {
              InCallManager.setSpeakerphoneOn(true); // Default to speaker for video calls
              setIsSpeakerOn(true);
            }
          })
          .catch(error => {
            console.error('Failed to join call:', error);
            setCallStatus('Failed');
          });
      } catch (error) {
        console.error('Error creating call:', error);
        setCallStatus('Failed');
      }
    }
  }, [client, callId, rideId]);

  // Audio control functions
  const toggleMute = () => {
    console.log('🎤 Toggle mute pressed, current state:', isMuted);
    try {
      const newMutedState = !isMuted;

      if (call && call.microphone) {
        // For Stream.io calls (video calls), use Stream.io methods
        console.log('🎤 Using Stream.io microphone toggle');
        call.microphone.toggle();
        setIsMuted(newMutedState);
      } else {
        // For audio-only calls, use InCallManager
        console.log('🎤 Using InCallManager microphone toggle');
        InCallManager.setMicrophoneMute(newMutedState);
        setIsMuted(newMutedState);
      }

      console.log(`🎤 Microphone ${newMutedState ? 'muted' : 'unmuted'}`);
    } catch (error) {
      console.error('❌ Error toggling mute:', error);
      // Fallback to InCallManager
      const newMutedState = !isMuted;
      InCallManager.setMicrophoneMute(newMutedState);
      setIsMuted(newMutedState);
    }
  };

  const toggleSpeaker = () => {
    console.log('🔊 Toggle speaker pressed, current state:', isSpeakerOn);
    try {
      const newSpeakerState = !isSpeakerOn;
      InCallManager.setSpeakerphoneOn(newSpeakerState);
      setIsSpeakerOn(newSpeakerState);
      console.log(`🔊 Speaker ${newSpeakerState ? 'on' : 'off'}`);
    } catch (error) {
      console.error('❌ Error toggling speaker:', error);
    }
  };

  const toggleCamera = () => {
    console.log('📹 Toggle camera pressed, current state:', isCameraOff);
    try {
      if (call && call.camera) {
        call.camera.toggle();
        const newCameraState = !isCameraOff;
        setIsCameraOff(newCameraState);
        console.log(`📹 Camera ${newCameraState ? 'off' : 'on'}`);
      }
    } catch (error) {
      console.error('❌ Error toggling camera:', error);
    }
  };

  // Start a call using video call socket service
  const handleStartCall = () => {
    // Always treat as a driver-initiated call with the passenger ID from ride details
    // Set UI to show we're calling the passenger
    setCaller(recipientName);
    setProfileImg(recipientImg);
    setIsOutgoingCall(true);

    console.log(`Initiating video call to passenger ${recipientId} for ride ${rideId}`);

    // Determine if it's a video call based on route params
    const isVideoCall = route?.params?.isVideoCall !== false; // Default to video call

    // Initiate the call with passenger ID using unified socket service
    socketService.callUser(rideId, recipientId, isVideoCall);
    setCallStatus('Ringing');
  };

  // End a call using unified socket service and Stream SDK
  const handleEndCall = () => {
    console.log('🔚 [CallScreen] Ending call...');

    // Prevent multiple end attempts
    if (callEnded) {
      console.log('ℹ️ [CallScreen] Call already ended, skipping...');
      return;
    }

    setCallEnded(true);

    // End call using unified socket service
    const isVideoCall = route?.params?.isVideoCall !== false;
    socketService.endCall(rideId, callId, isVideoCall);

    // Safely leave Stream.io call with state checking
    if (call) {
      console.log('🔚 [CallScreen] Attempting to leave Stream.io call...');

      try {
        // Check if call is still active by checking its state
        const callState = call.state;
        console.log('🔍 [CallScreen] Current call state:', callState?.callingState);

        // Only try to leave if call is still active
        if (callState?.callingState === 'joined' || callState?.callingState === 'ringing') {
          call.leave()
            .then(() => {
              console.log('✅ [CallScreen] Successfully left Stream.io call');
              setCall(null);
            })
            .catch(error => {
              console.log('⚠️ [CallScreen] Call leave error (likely already ended):', error.message);
              setCall(null);
            });
        } else {
          console.log('ℹ️ [CallScreen] Call already ended, just cleaning up reference');
          setCall(null);
        }
      } catch (error) {
        console.log('⚠️ [CallScreen] Error checking call state, cleaning up:', error.message);
        setCall(null);
      }
    } else {
      console.log('ℹ️ [CallScreen] No active Stream.io call to leave');
    }

    // Clean up InCallManager
    try {
      InCallManager.stop();
      InCallManager.setKeepScreenOn(false);
      console.log('✅ [CallScreen] InCallManager cleaned up');
    } catch (error) {
      console.error('❌ [CallScreen] Error cleaning up InCallManager:', error);
    }

    // Reset all call-related state
    setCallStatus(null);
    setCaller(null);
    setStreamCallReady(false);
    setIsOutgoingCall(false);
    setIsMuted(false);
    setIsSpeakerOn(false);
    setIsCameraOff(false);
    // Note: callEnded state is already set to true above

    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      <View style={styles.headerSpace} />

      {/* Stream Video Call UI - Only shown when call is connected and ready */}
      {call && streamCallReady && callStatus === 'Connected' && !route?.params?.isIncoming && (
        <View style={styles.videoContainer}>
          {client && call && (
            <StreamVideo client={client}>
              <StreamCall call={call}>
                {/* Use CallContent for video calls with custom controls, or a simpler layout for audio calls */}
                {route?.params?.isVideoCall !== false ? (
                  <View style={styles.videoCallWrapper}>
                    <CallContent
                      CallControls={() => null} // Hide default controls
                    />
                    {/* Custom Controls Overlay */}
                    <View style={styles.customControlsOverlay}>
                      <View style={styles.controlsRow}>
                        {/* Mute Button */}
                        <TouchableOpacity
                          style={[
                            styles.controlButton,
                            styles.muteButton,
                            isMuted && styles.activeButton
                          ]}
                          onPress={toggleMute}
                          activeOpacity={0.7}>
                          <Ionicons
                            name={isMuted ? "mic-off" : "mic"}
                            size={24}
                            color="white"
                          />
                        </TouchableOpacity>

                        {/* End Call Button */}
                        <TouchableOpacity
                          style={[styles.controlButton, styles.endCallButton]}
                          onPress={handleEndCall}>
                          <Ionicons name="call" size={24} color="white" style={styles.endCallIcon} />
                        </TouchableOpacity>

                        {/* Camera Toggle Button */}
                        <TouchableOpacity
                          style={[
                            styles.controlButton,
                            styles.cameraButton,
                            isCameraOff && styles.activeButton
                          ]}
                          onPress={toggleCamera}
                          activeOpacity={0.7}>
                          <Ionicons
                            name={isCameraOff ? "videocam-off" : "videocam"}
                            size={24}
                            color="white"
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                ) : (
                  <View style={styles.audioCallContainer}>
                    <Text style={styles.audioCallText}>Audio Call in Progress</Text>
                    <Text style={styles.audioCallSubtext}>
                      {isOutgoingCall ? `Calling ${recipientName}` : `Call with ${caller}`}
                    </Text>
                    {/* Audio Call Controls */}
                    <View style={styles.audioControlsContainer}>
                      <TouchableOpacity
                        style={[
                          styles.controlButton,
                          styles.muteButton,
                          isMuted && styles.activeButton
                        ]}
                        onPress={toggleMute}
                        activeOpacity={0.7}>
                        <Ionicons
                          name={isMuted ? "mic-off" : "mic"}
                          size={24}
                          color="white"
                        />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[styles.controlButton, styles.endCallButton]}
                        onPress={handleEndCall}>
                        <Ionicons name="call" size={24} color="white" style={styles.endCallIcon} />
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={[
                          styles.controlButton,
                          styles.speakerButton,
                          isSpeakerOn && styles.activeButton
                        ]}
                        onPress={toggleSpeaker}
                        activeOpacity={0.7}>
                        <Ionicons
                          name={isSpeakerOn ? "volume-high" : "volume-medium"}
                          size={24}
                          color="white"
                        />
                      </TouchableOpacity>
                    </View>
                  </View>
                )}
              </StreamCall>
            </StreamVideo>
          )}
        </View>
      )}

      {/* Failed Call State */}
      {callStatus === 'Failed' && (
        <View style={styles.callingContainer}>
          <View style={styles.profileContainer}>
            <Text style={styles.callerName}>Call Failed</Text>
            <Text style={styles.callStatus}>Unable to connect to the call</Text>
          </View>
          <TouchableOpacity
            style={[styles.actionButton, styles.acceptButton]}
            onPress={() => {
              setCallStatus(null);
              navigation.goBack();
            }}>
            <Text style={{ color: 'white' }}>Try Again</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Ringing UI - Outgoing Call (Driver calling Recipient) */}
      {callStatus === 'Ringing' && isOutgoingCall && (
        <View style={styles.callingContainer}>
          <View style={styles.profileContainer}>
            <Text style={styles.callerName}>Calling {recipientName}...</Text>
            <Image
              source={{ uri: recipientImg }}
              style={styles.profileImg}
            />
            <Text style={styles.callStatus}>Recipient</Text>
            <Text style={styles.no}>{recipientPhone}</Text>
          </View>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={handleEndCall}>
            <Ionicons name="call" size={30} color="white" />
          </TouchableOpacity>
        </View>
      )}

      {/* Ringing UI - Incoming Call */}
      {callStatus === 'Ringing' && !isOutgoingCall && (
        <View style={styles.callingContainer}>
          <View style={styles.profileContainer}>
            <Text style={styles.callerName}>{caller || 'Incoming Call...'}</Text>
            <Image
              source={{ uri: profileImg || userImage }}
              style={styles.profileImg}
            />
            <Text style={styles.callStatus}>{userName}</Text>
            <Text style={styles.no}>{userProfile.mobileNumber || ''}</Text>
          </View>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={handleEndCall}>
            <Ionicons name="call" size={30} color="white" />
          </TouchableOpacity>
        </View>
      )}

      {/* Connected UI - Outgoing Call (Driver to Recipient) */}
      {callStatus === 'Connected' && !streamCallReady && isOutgoingCall && (
        <View style={styles.callingContainer}>
          <View style={styles.profileContainer}>
            <Image
              source={{ uri: recipientImg }}
              style={styles.profileImg}
            />
            <Text style={styles.callerName}>{recipientName}</Text>
            <Text style={styles.callStatus}>Call in progress</Text>
          </View>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={handleEndCall}>
            <Ionicons name="call" size={30} color="white" />
          </TouchableOpacity>
        </View>
      )}

      {/* Connected UI - Incoming Call (Legacy - only for non-route-based calls) */}
      {callStatus === 'Connected' && !streamCallReady && !isOutgoingCall && !route?.params?.isIncoming && (
        <View style={styles.callingContainer}>
          <View style={styles.profileContainer}>
            <Image
              source={{ uri: profileImg || userImage }}
              style={styles.profileImg}
            />
            <Text style={styles.callerName}>
              {(caller || userName).replace(/^Incoming call from\s*/i, '')}
            </Text>
            <Text style={styles.callStatus}>Call in progress</Text>
          </View>
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={handleEndCall}>
            <Ionicons name="call" size={30} color="white" />
          </TouchableOpacity>
        </View>
      )}

      {/* Initial/Idle UI - Only show for outgoing calls that haven't started yet */}
      {(!callStatus ||
        callStatus === 'Rejected' ||
        callStatus === 'No Answer') &&
        !route?.params?.isIncoming && (
          <View style={styles.callButtonContainer}>
            <TouchableOpacity
              style={[styles.actionButton, styles.acceptButton]}
              onPress={handleStartCall}>
              <Ionicons name="call" size={30} color="white" />
            </TouchableOpacity>
          </View>
        )}

      {/* Accepted Incoming Call UI - Show when this is an accepted incoming call */}
      {route?.params?.isIncoming && callStatus === 'Connected' && (
        <>
          {/* Show Stream Video UI if it's a video call and Stream is ready */}
          {call && streamCallReady && route?.params?.isVideoCall !== false ? (
            <View style={styles.videoContainer}>
              <StreamVideo client={client}>
                <StreamCall call={call}>
                  <View style={styles.videoCallWrapper}>
                    <CallContent
                      CallControls={() => null} // Hide default controls
                    />
                    {/* Custom Controls Overlay */}
                    <View style={styles.customControlsOverlay}>
                      <View style={styles.controlsRow}>
                        <TouchableOpacity
                          style={[styles.controlButton, styles.muteButton, isMuted && styles.activeButton]}
                          onPress={toggleMute}>
                          <Ionicons name={isMuted ? "mic-off" : "mic"} size={24} color="white" />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.controlButton, styles.endCallButton]}
                          onPress={handleEndCall}>
                          <Ionicons name="call" size={24} color="white" />
                        </TouchableOpacity>

                        <TouchableOpacity
                          style={[styles.controlButton, styles.speakerButton, isSpeakerOn && styles.activeButton]}
                          onPress={toggleSpeaker}>
                          <Ionicons name={isSpeakerOn ? "volume-high" : "volume-low"} size={24} color="white" />
                        </TouchableOpacity>
                      </View>
                    </View>
                  </View>
                </StreamCall>
              </StreamVideo>
            </View>
          ) : (
            /* Audio Call UI or Loading State */
            <View style={styles.callingContainer}>
              <View style={styles.profileContainer}>
                <Text style={styles.callerName}>
                  {(caller || route?.params?.callerName || 'Connected Call').replace(/^Incoming call from\s*/i, '')}
                </Text>
                <Image
                  source={{ uri: profileImg || route?.params?.callerImage || 'https://i.pravatar.cc/300' }}
                  style={styles.profileImg}
                />
                <Text style={styles.callStatus}>
                  {client && streamCallReady ? 'Call in Progress' : 'Connecting...'}
                </Text>
              </View>

              {/* Call Controls */}
              <View style={styles.controlsContainer}>
                <TouchableOpacity
                  style={[styles.controlButton, styles.muteButton, isMuted && styles.activeButton]}
                  onPress={toggleMute}
                  disabled={!client || !streamCallReady}>
                  <Ionicons name={isMuted ? "mic-off" : "mic"} size={24} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, styles.endCallButton]}
                  onPress={handleEndCall}>
                  <Ionicons name="call" size={24} color="white" />
                </TouchableOpacity>

                <TouchableOpacity
                  style={[styles.controlButton, styles.speakerButton, isSpeakerOn && styles.activeButton]}
                  onPress={toggleSpeaker}>
                  <Ionicons name={isSpeakerOn ? "volume-high" : "volume-low"} size={24} color="white" />
                </TouchableOpacity>
              </View>
            </View>
          )}
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a1a',
    padding: 20,
    justifyContent: 'center',
  },
  headerSpace: {
    height: Platform.OS === 'ios' ? 60 : 40,
  },
  videoContainer: {
    flex: 1,
    backgroundColor: '#000',
    borderRadius: 12,
    overflow: 'hidden',
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 40,
    marginTop: 40,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255,255,255,0.2)',
    marginHorizontal: 10,
  },
  muteButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  speakerButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  endCallButton: {
    backgroundColor: '#FF4444',
  },
  activeButton: {
    backgroundColor: '#FF4444',
  },
  videoCallWrapper: {
    flex: 1,
    position: 'relative',
  },
  customControlsOverlay: {
    position: 'absolute',
    bottom: 20,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingVertical: 20,
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  callingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  profileImg: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: '#fff',
    marginBottom: 20,
  },
  callerName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 30,
  },
  callStatus: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 20,
    marginBottom: 10,
  },
  no: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 16,
    marginBottom: 10,
  },
  callButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  actionButton: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#FF4444',
  },
  audioCallContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  audioCallText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: '600',
    marginBottom: 10,
  },
  audioCallSubtext: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 18,
    textAlign: 'center',
  },
  videoCallWrapper: {
    flex: 1,
    position: 'relative',
  },
  customControlsOverlay: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  controlsRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  audioControlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 40,
    paddingHorizontal: 20,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 15,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  endCallButton: {
    backgroundColor: '#FF4444',
    width: 70,
    height: 70,
    borderRadius: 35,
  },
  muteButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  cameraButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  speakerButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  endCallIcon: {
    transform: [{ rotate: '135deg' }],
  },
  activeButton: {
    backgroundColor: '#FF6B6B', // Red background when active (muted/off)
  },
});
