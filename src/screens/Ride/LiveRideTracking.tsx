import React, { useEffect, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
  Alert,
  Animated,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, AnimatedRegion } from 'react-native-maps';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import locationService from '../../services/locationService';
import socketService from '../../services/socketService';
import { API_BASE_URL } from '../../config/apiConfig';

const { width, height } = Dimensions.get('window');

interface RideTrackingProps {
  rideId: string;
  destination: {
    latitude: number;
    longitude: number;
    address: string;
  };
  passenger: {
    name: string;
    phone: string;
    image?: string;
    id?: string;
    passengerId?: string;
  };
  passengerId?: string;
}

export default function LiveRideTracking() {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const { rideId, destination, passenger, passengerId } = route.params as RideTrackingProps;
  
  const mapRef = useRef<MapView>(null);
  const markerRef = useRef<any>(null);
  
  // State for tracking
  const [currentLocation, setCurrentLocation] = useState<{latitude: number, longitude: number} | null>(null);
  const [region, setRegion] = useState({
    latitude: destination.latitude,
    longitude: destination.longitude,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  
  // Animated marker position for smooth movement
  const [markerCoordinate] = useState(new AnimatedRegion({
    latitude: destination.latitude,
    longitude: destination.longitude,
    latitudeDelta: 0,
    longitudeDelta: 0,
  }));
  
  // Route and tracking state
  const [routeCoordinates, setRouteCoordinates] = useState<Array<{latitude: number, longitude: number}>>([]);
  const [estimatedTime, setEstimatedTime] = useState<string>('Calculating...');
  const [distance, setDistance] = useState<string>('Calculating...');
  const [isTracking, setIsTracking] = useState(false);

  // Passenger profile state
  const [passengerProfile, setPassengerProfile] = useState({
    name: passenger.name || 'Passenger',
    phone: passenger.phone || '',
    image: passenger.image || '',
    id: passenger.id || passenger.passengerId || passengerId || null
  });

  // Initialize tracking when component mounts
  useEffect(() => {
    console.log('🚗 [LiveRideTracking] Starting live ride tracking for:', rideId);
    initializeTracking();
    fetchPassengerProfile();

    return () => {
      console.log('🛑 [LiveRideTracking] Stopping live ride tracking');
      stopTracking();
    };
  }, []);

  // Debug: Log passenger profile changes
  useEffect(() => {
    console.log('👤 [LiveRideTracking] Passenger profile updated:', {
      name: passengerProfile.name,
      phone: passengerProfile.phone,
      image: passengerProfile.image,
      id: passengerProfile.id,
      fromRoute: {
        passengerName: passenger.name,
        passengerPhone: passenger.phone,
        passengerImage: passenger.image
      }
    });
  }, [passengerProfile]);

  // Fetch passenger profile details
  const fetchPassengerProfile = async () => {
    // Extract passenger ID from the passenger object or route params
    const currentPassengerId = passenger.id || passenger.passengerId || passengerId;

    if (!currentPassengerId) {
      console.warn('⚠️ [LiveRideTracking] No passenger ID available for profile fetch');
      return;
    }

    try {
      console.log('🔄 [LiveRideTracking] Fetching passenger profile for ID:', currentPassengerId);

      const response = await fetch(`${API_BASE_URL}/passenger/profile/getApassengerDetail/${currentPassengerId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('👤 [LiveRideTracking] Passenger profile response:', JSON.stringify(data, null, 2));

      if (response.ok && data) {
        // Update passenger profile with fetched data
        const profileData = data.data || data.message || data;

        setPassengerProfile(prev => ({
          ...prev,
          name: profileData.firstName || profileData.name || prev.name,
          phone: profileData.phoneNumber || profileData.phone || prev.phone,
          image: profileData.profileImg || profileData.profileImage || profileData.image || prev.image,
          id: currentPassengerId
        }));

        console.log('✅ [LiveRideTracking] Passenger profile updated:', {
          name: profileData.firstName || profileData.name,
          phone: profileData.phoneNumber || profileData.phone,
          image: profileData.profileImg || profileData.profileImage || profileData.image
        });
      } else {
        console.warn('⚠️ [LiveRideTracking] Failed to fetch passenger profile:', response.status, data);
      }
    } catch (error) {
      console.error('❌ [LiveRideTracking] Error fetching passenger profile:', error);
    }
  };

  const initializeTracking = async () => {
    try {
      // Get initial location
      const initialLocation = locationService.getLastKnownLocation();
      if (initialLocation) {
        const [longitude, latitude] = initialLocation.coordinates;
        const mapLocation = { latitude, longitude };
        setCurrentLocation(mapLocation);
        
        // Update animated marker
        markerCoordinate.setValue(mapLocation);
        
        // Calculate initial route
        await calculateRoute(mapLocation, destination);

        // Center map to show both current location and destination
        centerMapOnRoute(mapLocation, destination);
      }

      // Start location tracking
      await locationService.startTracking();
      
      // Add location listener for real-time updates
      const locationListener = (location: any) => {
        console.log('📍 [LiveRideTracking] Location updated:', location);

        const [longitude, latitude] = location.coordinates;
        const newLocation = { latitude, longitude };

        // Update current location
        setCurrentLocation(newLocation);

        // Animate marker to new position
        if (Platform.OS === 'android') {
          markerRef.current?.animateMarkerToCoordinate(newLocation, 1000);
        } else {
          markerCoordinate.timing({
            latitude,
            longitude,
            duration: 1000,
            useNativeDriver: false,
          }).start();
        }

        // Recalculate route and ETA
        calculateRoute(newLocation, destination);

        // Update map region to follow the car
        updateMapRegion(newLocation);
      };

      locationService.addLocationListener(locationListener);
      setIsTracking(true);
      
      console.log('✅ [LiveRideTracking] Live tracking initialized');
      
    } catch (error) {
      console.error('❌ [LiveRideTracking] Error initializing tracking:', error);
      Alert.alert('Tracking Error', 'Failed to start live tracking');
    }
  };

  const calculateRoute = async (start: {latitude: number, longitude: number}, end: {latitude: number, longitude: number}) => {
    try {
      // Simple straight line for now - in production, use Google Directions API
      const route = [start, end];
      setRouteCoordinates(route);
      
      // Calculate distance
      const dist = calculateDistance(start.latitude, start.longitude, end.latitude, end.longitude);
      setDistance(`${dist.toFixed(1)} km`);
      
      // Estimate time (assuming average speed of 30 km/h in city)
      const timeInMinutes = (dist / 30) * 60;
      setEstimatedTime(`${Math.round(timeInMinutes)} min`);
      
    } catch (error) {
      console.error('❌ [LiveRideTracking] Error calculating route:', error);
    }
  };

  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Radius of the Earth in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const centerMapOnRoute = (start: {latitude: number, longitude: number}, end: {latitude: number, longitude: number}) => {
    const minLat = Math.min(start.latitude, end.latitude);
    const maxLat = Math.max(start.latitude, end.latitude);
    const minLng = Math.min(start.longitude, end.longitude);
    const maxLng = Math.max(start.longitude, end.longitude);

    const region = {
      latitude: (minLat + maxLat) / 2,
      longitude: (minLng + maxLng) / 2,
      latitudeDelta: Math.abs(maxLat - minLat) * 1.5,
      longitudeDelta: Math.abs(maxLng - minLng) * 1.5,
    };

    setRegion(region);
    mapRef.current?.animateToRegion(region, 1000);
  };

  const updateMapRegion = (newLocation: {latitude: number, longitude: number}) => {
    // Keep the car in view while showing the destination
    const newRegion = {
      latitude: (newLocation.latitude + destination.latitude) / 2,
      longitude: (newLocation.longitude + destination.longitude) / 2,
      latitudeDelta: Math.abs(newLocation.latitude - destination.latitude) * 1.2,
      longitudeDelta: Math.abs(newLocation.longitude - destination.longitude) * 1.2,
    };

    mapRef.current?.animateToRegion(newRegion, 1000);
  };

  const stopTracking = () => {
    locationService.stopTracking();
    setIsTracking(false);
  };

  const handleArrived = () => {
    Alert.alert(
      'Arrived at Destination',
      'Have you arrived at the destination?',
      [
        { text: 'Not Yet', style: 'cancel' },
        { 
          text: 'Yes, Arrived', 
          onPress: () => {
            stopTracking();
            // Navigate to ride completion screen
            navigation.navigate('RideComplete', { rideId });
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
      {/* Full Screen Map */}
      <MapView
        ref={mapRef}
        style={styles.fullScreenMap}
        region={region}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={true}
        showsScale={true}
        followsUserLocation={false}
        zoomEnabled={true}
      >
        {/* Route Line */}
        {routeCoordinates.length > 0 && (
          <Polyline
            coordinates={routeCoordinates}
            strokeWidth={4}
            strokeColor={COLORS.primary}
            lineDashPattern={[10, 5]}
          />
        )}

        {/* Driver Car Marker (Animated) */}
        {currentLocation && (
          <Marker.Animated
            ref={markerRef}
            coordinate={Platform.OS === 'ios' ? markerCoordinate : currentLocation}
            title="Your Location"
            description="Live driver position"
            anchor={{x: 0.5, y: 0.5}}
            flat={true}
          >
            <View style={styles.carMarkerContainer}>
              <View style={styles.carMarkerPulse} />
              <Image source={images.CarSingle} style={styles.carMarkerIcon} />
            </View>
          </Marker.Animated>
        )}

        {/* Destination Marker */}
        <Marker
          coordinate={destination}
          title="Destination"
          description={destination.address}
        >
          <View style={styles.destinationMarker}>
            <Image source={images.locate} style={styles.destinationIcon} />
          </View>
        </Marker>
      </MapView>

      {/* Center Location Button */}
      <TouchableOpacity
        style={styles.centerLocationButton}
        onPress={() => {
          if (currentLocation) {
            centerMapOnRoute(currentLocation, destination);
          }
        }}
      >
        <Image source={images.locate} style={styles.centerLocationIcon} />
      </TouchableOpacity>

      {/* Floating Header */}
      <View style={styles.floatingHeader}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image source={images.goback} style={styles.backIcon} />
        </TouchableOpacity>
        <View style={styles.headerCenter}>
          <Text style={styles.headerTitle}>Live Tracking</Text>
          <View style={styles.statusContainer}>
            <View style={[styles.statusIndicator, { backgroundColor: isTracking ? '#4CAF50' : '#FF5722' }]} />
            <Text style={styles.statusText}>{isTracking ? 'Live' : 'Offline'}</Text>
          </View>
        </View>
        <View style={styles.headerRight} />
      </View>

      {/* Floating Trip Info Card */}
      <View style={styles.floatingTripInfo}>
        <View style={styles.tripInfoRow}>
          <View style={styles.tripInfoItem}>
            <Text style={styles.tripInfoLabel}>ETA</Text>
            <Text style={styles.tripInfoValue}>{estimatedTime}</Text>
          </View>
          <View style={styles.tripInfoDivider} />
          <View style={styles.tripInfoItem}>
            <Text style={styles.tripInfoLabel}>Distance</Text>
            <Text style={styles.tripInfoValue}>{distance}</Text>
          </View>
        </View>
      </View>

      {/* Bottom Overlay Panel */}
      <View style={styles.bottomOverlay}>
        {/* Passenger Info Row */}
        <View style={styles.passengerRow}>
          <Image
            source={passengerProfile.image ? { uri: passengerProfile.image } : images.profile}
            style={styles.passengerAvatar}
            onError={() => {
              console.log('❌ [LiveRideTracking] Failed to load passenger image:', passengerProfile.image);
            }}
            onLoad={() => {
              console.log('✅ [LiveRideTracking] Passenger image loaded successfully');
            }}
          />
          <View style={styles.passengerDetails}>
            <Text style={styles.passengerName}>{passengerProfile.name}</Text>
            <View style={styles.destinationRow}>
              <Image source={images.locate} style={styles.destinationRowIcon} />
              <Text style={styles.destinationText} numberOfLines={1}>{destination.address}</Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.callButtonCompact}
            onPress={() => {
              console.log('📞 [LiveRideTracking] Calling passenger:', passengerProfile.phone);
              // Handle call functionality here
            }}
          >
            <Image source={images.call} style={styles.callIcon} />
          </TouchableOpacity>
        </View>

        {/* Action Button */}
        <TouchableOpacity
          style={styles.arrivedButtonFull}
          onPress={handleArrived}
        >
          <Text style={styles.arrivedButtonText}>Mark as Arrived</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.black,
  },
  fullScreenMap: {
    ...StyleSheet.absoluteFillObject,
    width: width,
    height: height,
  },
  floatingHeader: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 40,
    left: 20,
    right: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    paddingHorizontal: 15,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.black,
    marginBottom: 2,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusText: {
    fontSize: 12,
    color: COLORS.grey,
    fontWeight: '500',
  },
  headerRight: {
    width: 40,
  },
  floatingTripInfo: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 130 : 110,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    paddingVertical: 15,
    paddingHorizontal: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
  tripInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tripInfoItem: {
    flex: 1,
    alignItems: 'center',
  },
  tripInfoDivider: {
    width: 1,
    height: 30,
    backgroundColor: '#E5E7EB',
    marginHorizontal: 15,
  },
  tripInfoLabel: {
    fontSize: 12,
    color: COLORS.grey,
    marginBottom: 4,
    fontWeight: '500',
  },
  tripInfoValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.primary,
  },
  carMarkerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 50,
    height: 50,
  },
  carMarkerPulse: {
    position: 'absolute',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(33, 150, 243, 0.3)',
    borderWidth: 2,
    borderColor: 'rgba(33, 150, 243, 0.6)',
  },
  carMarkerIcon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
    zIndex: 1,
  },
  destinationMarker: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  destinationIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  bottomOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 12,
  },
  passengerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  passengerAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  passengerDetails: {
    flex: 1,
  },
  passengerName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.black,
    marginBottom: 6,
  },
  destinationRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  destinationRowIcon: {
    width: 16,
    height: 16,
    resizeMode: 'contain',
    marginRight: 6,
    tintColor: COLORS.primary,
  },
  destinationText: {
    fontSize: 14,
    color: COLORS.grey,
    flex: 1,
  },
  callButtonCompact: {
    width: 45,
    height: 45,
    borderRadius: 22.5,
    backgroundColor: COLORS.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  callIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    tintColor: COLORS.white,
  },
  arrivedButtonFull: {
    backgroundColor: COLORS.primary,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: COLORS.primary,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 6,
  },
  arrivedButtonText: {
    fontSize: 16,
    color: COLORS.white,
    fontWeight: 'bold',
  },
  centerLocationButton: {
    position: 'absolute',
    right: 20,
    top: Platform.OS === 'ios' ? 200 : 180,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 6,
  },
  centerLocationIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    tintColor: COLORS.primary,
  },
});
