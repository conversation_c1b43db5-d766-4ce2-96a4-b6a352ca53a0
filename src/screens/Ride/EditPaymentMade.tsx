 
import React, { useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { COLORS, FONTS, icons, images } from "../../constants";
import { useNavigation, useRoute } from "@react-navigation/native";
import socketService from "../../services/socketService";

const EditPaymentMade = () => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const { rideId } = route.params as { rideId: string };

  console.log('✅ [EditPaymentMade] Edit acceptance completed for rideId:', rideId);

  const region = {
    latitude: 33.5186,
    longitude: -86.8104,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };

  useEffect(() => {
    // Listen for any additional server responses about the edit acceptance
    socketService.onEvent('editAcceptanceConfirmed', (response) => {
      console.log('📨 [EditPaymentMade] Final edit confirmation:', response);
    });

    return () => {
      socketService.socket.off('editAcceptanceConfirmed');
    };
  }, [rideId]);

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Top Section - Map */}
      <View style={styles.mapSection}>
        <MapView
          style={styles.map}
          initialRegion={region}
          showsUserLocation={true}
        >
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Pickup Location"
            description="AMLI 7th Street Station..."
          />
        </MapView>
        <View style={styles.priceTag}>
          <Text style={styles.priceText}>$ 5000</Text>
        </View>
      </View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>

        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.headerText}>Passengers has made Payment for the Edited ride</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>
     

     <Image source={images.success} style={{ width: 100, height: 100, alignSelf: 'center' }} />

      

        {/* Okay Button */}
        <TouchableOpacity
          style={styles.okayButton}
          
          // onPress={() => navigation.navigate('GotAnotherDriver')}  
          onPress={() => navigation.navigate('RidesDetail',{rideId:rideId})}
        >
          <Text style={styles.okayButtonText}>Okay</Text>
        </TouchableOpacity>

        {/* Note */}
        <Text style={styles.note}>
        Ride activated, proceed to pickup and make some money
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  mapSection: {
    height: Dimensions.get("window").height * 0.5,
    width: "100%",
    position: "relative",
  },
  map: {
    height: "100%",
    width: "100%",
  },
  priceTag: {
    position: "absolute",
    top: 20,
    left: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    padding: 10,
  },
  priceText: {
    color: COLORS.white,
    fontWeight: "bold",
    fontSize: 18,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    // alignItems: "center",
  },
  // header: {
  //   alignItems: "center",
  //   marginBottom: 20,
  // },
  title: {
    ...FONTS.h2,
    color: COLORS.black,
    marginBottom: 10,
  },
  emoji: {
    fontSize: 40,
    textAlign: "center",
  },
  message: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
  },
  okayButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 40,
    borderRadius: 10,
    marginVertical: 10,
  },
  okayButtonText: {
    color: COLORS.white,
    ...FONTS.h4,
    textAlign: "center",
  },
  note: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
    // marginTop: 10,
    lineHeight: 18,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  headerText: {
    ...FONTS.body3,
    width: '60%',
    textAlign: 'center',
    color: COLORS.black,
  },
  closeButton: {
    fontSize: 20,
    marginLeft: 20,
    color: COLORS.grey,
  },
});

export default EditPaymentMade;
