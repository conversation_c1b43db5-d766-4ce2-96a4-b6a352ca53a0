


import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import MapView, { <PERSON><PERSON>, Polyline } from 'react-native-maps';
import { COLORS, FONTS, images } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import socketService from '../../services/socketService';

const RideRequestEdit = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { rideId} = route.params || {};  
  const { ride} = route.params || {};  
  const serverResponse = route.params || {};  
console.log('🚗 serverResponse:', JSON.stringify(serverResponse,null,2));

  const [region, setRegion] = useState({
    latitude: ride?.pickupCoordinates?.latitude || 33.5186,
    longitude: ride?.pickupCoordinates?.longitude || -86.8104,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  const routeCoordinates = [
    { latitude: ride?.pickupCoordinates?.latitude || 33.5186, longitude: ride?.pickupCoordinates?.longitude || -86.8104 },
    { latitude: ride?.dropoffCoordinates?.latitude || 33.5036, longitude: ride?.dropoffCoordinates?.longitude || -86.8084 },
  ];

  /** Accept Ride Edit Request */
  const handleAcceptEdit = () => {
    console.log('✅ [RideRequestEdit] Accepting edit request for rideId:', rideId);
    console.log('📋 [RideRequestEdit] Edit details:', JSON.stringify(serverResponse?.serverResponse, null, 2));

    // Navigate to pricing screen for the edited ride
    navigation.navigate('AddPriceForEdit', {
      serverResponse,
      rideId,
      editType: 'accept'
    });
  };
  useEffect(() => {
    console.log("📋 [RideRequestEdit] Initializing with serverResponse:", JSON.stringify(serverResponse, null, 2));
    console.log("📋 [RideRequestEdit] rideId:", rideId);

    // Listener for edit rejection confirmation from server
    socketService.socket.on('RejectEditRequest', (response) => {
      console.log('📨 [RideRequestEdit] Edit rejection response:', JSON.stringify(response, null, 2));

      if (response.success) {
        Alert.alert(
          "Edit Rejected Successfully",
          response?.message || "The ride edit request has been rejected.",
          [
            {
              text: "OK",
              onPress: () => {
                console.log('✅ [RideRequestEdit] Navigating back after successful rejection');
                navigation.goBack();
              }
            }
          ]
        );
      } else {
        Alert.alert(
          "Rejection Failed",
          response?.message || "Failed to reject the edit request. Please try again."
        );
        console.warn('❌ [RideRequestEdit] Edit rejection failed:', response);
        navigation.goBack();
      }
    });

    return () => {
      // Clean up the listener
      socketService.socket.off('RejectEditRequest');
    };
  }, [navigation]);

  const handleRejectEdit = () => {
    Alert.alert(
      "Reject Ride Edit",
      "Are you sure you want to reject this ride edit request?",
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Reject",
          style: "destructive",
          onPress: () => {
            console.log('🚫 [RideRequestEdit] Rejecting edit request for rideId:', rideId);

            // Emit reject edit request to server
            socketService.emitEvent('RejectEditRequest', {
              rideId: rideId,
              reason: 'Driver rejected the edit request'
            });

            console.log("Edit rejection request sent for rideId:", rideId);
          }
        }
      ]
    );
  };

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Map Section */}
      <MapView style={styles.map} initialRegion={region}>
        {/* Route */}
        <Polyline coordinates={routeCoordinates} strokeColor={COLORS.primary} strokeWidth={3} />

        {/* Pickup Marker */}
        <Marker coordinate={routeCoordinates[0]}>
          <View style={styles.marker}>
            <Text style={styles.markerText}>{ride?.from}</Text>
          </View>
        </Marker>

        {/* Drop-off Marker */}
        <Marker coordinate={routeCoordinates[1]}>
          <View style={styles.marker}>
            <Text style={styles.markerText}>{ride?.to?.[0]?.place}</Text>
          </View>
        </Marker>
      </MapView>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        {/* Ride Details */}
        <View style={styles.detailsHeader}>
          <Text></Text>
          <Text style={styles.rideTitle}>Passenger is waiting...</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Vehicle Image */}
        <Image source={images.CarSingle} style={styles.vehicleImage} />

        <Text style={styles.rideDescription}>
  {serverResponse?.serverResponse?.message} {serverResponse?.serverResponse?.ride?.from 
    ? `from ${serverResponse?.serverResponse?.ride.from} to ` 
    : 'from Unknown Pickup to '}
  {serverResponse?.serverResponse?.ride?.to?.length > 0
    ? serverResponse?.serverResponse?.ride.to.map((destination, index) => destination.place).join(', ')
    : 'Unknown Destination'}.
  Are you willing to accept or reject this ride?
</Text>


        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.acceptButton}
            onPress={handleAcceptEdit}
          >
            <Text style={styles.acceptButtonText}>Accept Edit</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.rejectButton}
            onPress={handleRejectEdit}
          >
            <Text style={styles.rejectButtonText}>Reject Edit</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.note}>
          Rides can be accepted within 60 seconds, otherwise they are automatically declined.
        </Text>
      </View>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  marker: {
    backgroundColor: COLORS.primary,
    padding: 5,
    borderRadius: 5,
  },
  markerText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginTop: -30,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rideTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  vehicleImage: {
    width: '100%',
    height: 150,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginVertical: 20,
  },
  rideDescription: {
    marginVertical: 10,
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  acceptButton: {
    flex: 1,
    marginRight: 10,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 15,
  },
  acceptButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  rejectButton: {
    flex: 1,
    marginLeft: 10,
    backgroundColor: COLORS.white,
    borderWidth: 0.5,
    borderColor: COLORS.red,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 0,
  },
  rejectButtonText: {
    color: COLORS.red,
    ...FONTS.h3,
  },
  note: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
});

export default RideRequestEdit;
