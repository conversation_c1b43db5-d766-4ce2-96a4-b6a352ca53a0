import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  ScrollView,
  TextInput,
  Share,
  ActivityIndicator,
  Alert,
  Platform,
  Linking,
  StyleProp,
  ImageStyle,
  ViewStyle,
  TextStyle,
} from 'react-native';
import MapView, { Marker, Polyline, Region, MapViewProps } from 'react-native-maps';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import GooglePlacesAutocompleteComponent from '../../components/shared/GooglePlaceAutoComplete';
import socketService from '../../services/socketService';
import { PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import Geolocation from '@react-native-community/geolocation';
import { API_BASE_URL } from '../../config/apiConfig';

// Define types for route params
type RideDetailParams = {
  rideId: string;
};

// Define types for navigation
type RootStackParamList = {
  RideRequestEdit: { serverResponse: any; rideId: string };
  TakeABreak: undefined;
  ChatScreen: {
    rideId: string;
    passengerId?: string;
    passengerName?: string;
    passengerImg?: string;
  };
  CallScreen: {
    rideId: string;
    receiverId: string;
    recipientName: string;
    recipientImg: string;
    recipientPhone: string;
  };
  ShareRideScreen: undefined;
};

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Define types for ride details
interface Coordinates {
  type: string;
  coordinates: number[];
}

interface Location {
  place: string;
  placeId: string;
  locationCoordinates: Coordinates;
}

interface CarDetails {
  model: string;
  color: string;
  registrationNumber: string;
  carImgUrl: string;
}

interface RideData {
  rideId: string;
  driverId: string;
  passengerId: string;
  from: string;
  to: Location[];
  fromCoordinates: Coordinates;
  pickupPoint: string;
  charge: number;
  carDetails: CarDetails;
}

interface RideDetails {
  data: RideData;
  success: boolean;
  message: string;
}

interface Profile {
  firstName: string;
  lastName: string;
  profileImg: string;
  totalRides: number;
}

interface LocationType {
  description: string;
  place_id: string;
  location: {
    lat: number;
    lng: number;
  };
}

const RideDetailScreen = () => {
  const route = useRoute<RouteProp<RootStackParamList & { RideDetail: { rideId: string } }, 'RideDetail'>>();
  const { rideId } = route.params;
  console.log('Ride ID:', rideId);
  const mapRef = useRef<MapView | null>(null);
  const [menuVisible, setMenuVisible] = useState<boolean>(false);
  const [isDisclaimerVisible, setDisclaimerVisible] = useState<boolean>(false);
  const [isReasonSheetVisible, setReasonSheetVisible] = useState<boolean>(false);
  const [isCancellationReasonVisible, setCancellationReasonVisible] = useState<boolean>(false);
  const [isConnectDriverVisible, setConnectDriverVisible] = useState<boolean>(false);
  const [isSafetyVisible, setSafetyVisible] = useState(false);
  const [isEmergencyVisible, setEmergencyVisible] = useState<boolean>(false);
  const [isReportSafetyVisible, setReportSafetyVisible] = useState<boolean>(false);
  const [isThankYouVisible, setThankYouVisible] = useState<boolean>(false);
  const [rideDetails, setRideDetails] = useState<RideDetails | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [show, setShow] = useState<boolean>(false);
  const [destinations, setDestinations] = useState<Location[]>([]);
  const [rideCancelled, setRideCancelled] = useState<string>('');
  const [rideStatus, setRideStatus] = useState<string>('Pending');
  const [profile, setProfile] = useState<Profile | undefined>();
  const [userLocation, setUserLocation] = useState<{ coordinates: number[] }>({ coordinates: [0, 0] });
  const [coordinates, setCoordinates] = useState<Array<{latitude: number, longitude: number}>>([]);
  const [region, setRegion] = useState<Region>({
    latitude: 0,
    longitude: 0,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  const navigation = useNavigation<NavigationProp>();

  const fetchRideDetails = async () => {
    try {
      console.log('Fetching ride details...', rideId);

      const response = await fetch(
        `${API_BASE_URL}/rides/getDriverRide/${rideId}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

      // Check if response is JSON before parsing
      const contentType = response.headers.get('content-type');
      const isJson = contentType && contentType.includes('application/json');

      if (!isJson) {
        const textResponse = await response.text();
        console.error('Non-JSON response received:', textResponse);
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();
      console.log("hhhhjhjhjhjhjhj", data);
      
      console.log('Ride Details:', JSON.stringify(data, null, 2));
      if (response.ok) {
        setRideDetails(data);
        // Update destinations from ride details
        if (data?.data?.to) {
          setDestinations(data.data.to);
        }
      } else {
        Alert.alert('Error', 'Failed to fetch ride details');
      }
    } catch (error) {
      console.error('Error fetching ride details:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchProfile = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/driver/profile/getProfile`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Profile Response:', data?.data);

      if (response.ok) {
        setProfile({
          firstName: data?.data?.firstName || '',
          lastName: data?.data?.lastName || '',
          profileImg: data?.data?.profileImg || images.profile,
          totalRides: data?.data?.totalRides || 0,
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  useEffect(() => {
    // Fit map to coordinates when ride details are loaded
    if (mapRef.current && rideDetails?.data?.fromCoordinates?.coordinates &&
        rideDetails?.data?.to?.[0]?.locationCoordinates?.coordinates) {
      const fromCoords = {
        latitude: rideDetails.data.fromCoordinates.coordinates[1],
        longitude: rideDetails.data.fromCoordinates.coordinates[0]
      };

      const toCoords = {
        latitude: rideDetails.data.to[0].locationCoordinates.coordinates[1],
        longitude: rideDetails.data.to[0].locationCoordinates.coordinates[0]
      };

      mapRef.current.fitToCoordinates([fromCoords, toCoords], {
        edgePadding: { top: 50, right: 50, bottom: 50, left: 50 },
        animated: true,
      });
    }
  }, [rideDetails]);

  useEffect(() => {
    fetchProfile();
  }, []);

  useEffect(() => {
    fetchRideDetails();
  }, []);

  const handleToSelect = (index: number, location: LocationType): void => {
    if (!location) return;

    setDestinations((prevDestinations) => {
      const updatedDestinations = [...prevDestinations];

      updatedDestinations[index] = {
        place: location.description,
        placeId: location.place_id,
        locationCoordinates: {
          type: "Point",
          coordinates: [location?.location?.lng, location?.location?.lat],
        },
      };

      console.log("Updated Destinations:", updatedDestinations);
      return updatedDestinations;
    });
  };

  const editRide = (): void => {
    if (destinations.length === 0) {
      Alert.alert("Error", "Please select at least one destination.");
      return;
    }

    const editData = {
      rideId: rideDetails?.data?.rideId,
      to: destinations.filter(dest => dest.place),
    };

    console.log("Edit Ride Data:", JSON.stringify(editData, null, 2));

    // Emit the edit request
    socketService.emitEvent('editRide', editData);
    setIsLoading(true);
  };

  useEffect(() => {
    socketService.onEvent("editRide", (response: any) => {
      console.log("Edit ride", JSON.stringify(response, null, 2));
      setIsLoading(false);
      if (response.success) {
        Alert.alert("Ride edited Successful!", ` ${response.message}`);
        // navigation.navigate("RideDetailScreen", { rideId: newRideId });?

      } else {
        Alert.alert("Ride Edit Failed", `${response.message}`);
      }
    });

    return () => {
      socketService.socket.off("editRide");
    };
  }, []);

  useEffect(() => {
    socketService.socket.on('passengerCancelRide', (serverResponse) => {
      console.log("Driver has been requested for a ride:", serverResponse);
      Alert.alert("Ride Cancelled", `${serverResponse?.message}`);
      setRideCancelled(serverResponse?.message);
      // navigation.navigate('TakeABreak',);
    });
    return () => {
      socketService.socket.off('passengerCancelRide');
    };
  }, []);

  useEffect(() => {
    socketService.socket.on('newEditRideRequest', (serverResponse: any) => {
      console.log("📝 [RidesDetail] New edit request received:", JSON.stringify(serverResponse, null, 2));

      const editMessage = serverResponse?.message || "Passenger has requested to edit the ride";

      Alert.alert(
        "Ride Edit Request",
        editMessage,
        [
          {
            text: "View Details",
            onPress: () => {
              console.log('👀 [RidesDetail] Navigating to edit request details');
              navigation.navigate('RideRequestEdit', {
                serverResponse,
                rideId,
                editRequestTime: Date.now()
              });
            }
          }
        ]
      );
    });

    return () => {
      socketService.socket.off('newEditRideRequest');
    };
  }, [navigation, rideId]);

  useEffect(() => {
    socketService.onEvent("rideComplete", (data: any) => {
      Alert.alert("Ride Completed", `Ride has been completed`);
      console.log("Ride Completed:", data);
      setRideStatus("Completed");
      setRideDetails(null);
    });

    return () => {
      socketService.socket.off("rideComplete");
    };
  }, []);

  const startRide = () => {
    if (!rideId) {
      Alert.alert("Error", "No active ride to start.");
      return;
    }

    console.log('🚗 [RidesDetail] Starting ride:', rideId);
    socketService.emitEvent('startRide', rideId);

    // Navigate to live tracking screen
    const destination = {
      latitude: rideDetails?.data?.to?.[0]?.locationCoordinates?.coordinates?.[1] || 0,
      longitude: rideDetails?.data?.to?.[0]?.locationCoordinates?.coordinates?.[0] || 0,
      address: rideDetails?.data?.to?.[0]?.place || 'Destination'
    };

    const passenger = {
      name: rideDetails?.data?.passengerName || 'Passenger',
      phone: rideDetails?.data?.passengerPhone || '',
      image: rideDetails?.data?.passengerImg || '',
      id: rideDetails?.data?.passengerId || rideDetails?.data?.passenger?._id || rideDetails?.data?.passenger?.id
    };

    console.log('📍 [RidesDetail] Navigating to live tracking with:', {
      destination,
      passenger,
      passengerId: passenger.id
    });

    navigation.navigate('LiveRideTracking', {
      rideId,
      destination,
      passenger,
      passengerId: passenger.id
    });
  }

  const requestLocationPermission = async () => {
    let permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    try {
      if (permission) {
        const result = await request(permission);
        if (result === RESULTS.GRANTED) {
          // console.log("✅ Location permission granted");
          return true;
        } else {
          console.log("❌ Location permission denied");
          Alert.alert("Permission Denied", "Please enable location permissions in Settings.");
          return false;
        }
      }
      return false;
    } catch (error) {
      console.error("⚠️ Permission Error:", error);
      return false;
    }
  };

  const checkLocationServices = async () => {
    return new Promise((resolve) => {
      Geolocation.getCurrentPosition(
        () => resolve(true), // ✅ Location services enabled
        (error) => {
          if (error.code === 2) { // 🚨 Location provider disabled
            Alert.alert(
              "Location Services Disabled",
              "Please enable location services in Settings.",
              [{ text: "Go to Settings", onPress: () => Linking.openSettings() }, { text: "Cancel", style: "cancel" }]
            );
          }
          resolve(false);
        },
        { enableHighAccuracy: false, timeout: 5000 }
      );
    });
  };

  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log("⛔ Location permission denied. Exiting function.");
      return null;
    }

    const isLocationEnabled = await checkLocationServices();
    if (!isLocationEnabled) {
      console.log("⛔ Location services disabled. Exiting function.");
      return null;
    }

    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;

          // const latitude = 4.2649;
          // const longitude = 8.1577;
          const newLocation = { type: 'Point', coordinates: [longitude, latitude] };

          console.log("✅ Location Retrieved:", newLocation);
          setUserLocation(newLocation);
          resolve(newLocation);
        },
        (_error) => {
          reject(null);
        },
        {
          enableHighAccuracy: false,
          timeout: 30000,
          maximumAge: 5000,
        }
      );
    });
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  console.log(userLocation, "userLocation");

  useEffect(() => {
    socketService.onEvent("startRide", (data: any) => {
      console.log("Ride Started:", data);
      setRideStatus("Started");
    });

    return () => {
      socketService.socket.off("startRide");
    };
  }, []);

  const completeRide = () => {
    if (!rideId) {
      Alert.alert("Error", "No active ride to complete.");
      return;
    }

    socketService.emitEvent("rideComplete", rideId);
    Alert.alert("Ride Ended", 'Ride has been completed');
  };

  const [isShareModalVisible, setShareModalVisible] = useState<boolean>(false);

  useEffect(() => {
    if (rideDetails?.data) {
      const fromCoords = rideDetails.data.fromCoordinates?.coordinates
        ? {
            latitude: rideDetails.data.fromCoordinates.coordinates[1],
            longitude: rideDetails.data.fromCoordinates.coordinates[0],
          }
        : null;

      const toCoords = rideDetails.data.to?.[0]?.locationCoordinates?.coordinates
        ? {
            latitude: rideDetails.data.to[0].locationCoordinates.coordinates[1],
            longitude: rideDetails.data.to[0].locationCoordinates.coordinates[0],
          }
        : null;

      if (fromCoords && toCoords) {
        setCoordinates([fromCoords, toCoords]);

        // Set the region to focus on the route
        setRegion({
          latitude: (fromCoords.latitude + toCoords.latitude) / 2,
          longitude: (fromCoords.longitude + toCoords.longitude) / 2,
          latitudeDelta: Math.abs(fromCoords.latitude - toCoords.latitude) * 2 + 0.02,
          longitudeDelta: Math.abs(fromCoords.longitude - toCoords.longitude) * 2 + 0.02,
        });
      }
    }
  }, [rideDetails]);

  const shareRide = async (): Promise<void> => {
    try {
      const result = await Share.share({
        message: 'I am sharing my ride details with you! 🚗\nPickup: AMLI 7th Street Station.\nDropoff: Fishermans Wharf.\nArrival: 13th July 10:00 PM.\nPrice: $54',
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        console.log('Error sharing:', error.message);
      } else {
        console.log('Unknown error sharing ride');
      }
    }
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 20 }} />}

      {/* Overlay backgrounds for modals */}
      {(isReasonSheetVisible || isShareModalVisible || isReportSafetyVisible ||
        isDisclaimerVisible || isThankYouVisible || isEmergencyVisible ||
        isSafetyVisible || isConnectDriverVisible || isCancellationReasonVisible) && (
        <View style={styles.overlay} />
      )}

      {/* Map Section with proper error handling */}
      {loading ? (
        <View style={[styles.map, {justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0'}]}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : (
        rideDetails?.data?.fromCoordinates?.coordinates &&
        rideDetails?.data?.to?.[0]?.locationCoordinates?.coordinates ? (
          <MapView
            ref={mapRef}
            style={styles.map}
            region={region}
          >
            {/* Pickup Location Marker */}
            <Marker
              coordinate={{
                latitude: rideDetails.data.fromCoordinates.coordinates[1],
                longitude: rideDetails.data.fromCoordinates.coordinates[0]
              }}
              title="Pickup Location"
              description={rideDetails.data.from}
            >
              <View style={[styles.markerContainer, styles.pickupMarker]}>
                <Text style={styles.markerText}>A</Text>
              </View>
            </Marker>

            {/* Drop-off Location Marker */}
            <Marker
              coordinate={{
                latitude: rideDetails.data.to[0].locationCoordinates.coordinates[1],
                longitude: rideDetails.data.to[0].locationCoordinates.coordinates[0]
              }}
              title="Drop-off Location"
              description={rideDetails.data.to[0].place}
            >
              <View style={[styles.markerContainer, styles.dropoffMarker]}>
                <Text style={styles.markerText}>B</Text>
              </View>
            </Marker>

            {/* Route Line */}
            <Polyline
              coordinates={[
                {
                  latitude: rideDetails.data.fromCoordinates.coordinates[1],
                  longitude: rideDetails.data.fromCoordinates.coordinates[0]
                },
                {
                  latitude: rideDetails.data.to[0].locationCoordinates.coordinates[1],
                  longitude: rideDetails.data.to[0].locationCoordinates.coordinates[0]
                }
              ]}
              strokeWidth={4}
              strokeColor="#FF4500"
              lineDashPattern={[10, 5]}
            />
          </MapView>
        ) : (
          <View style={[styles.map, {justifyContent: 'center', alignItems: 'center', backgroundColor: '#f0f0f0'}]}>
            <Text>No route coordinates available</Text>
          </View>
        )
      )}

      {/* Ride Information Section */}
      <ScrollView style={styles.bottomSheet}>
        {loading ? <ActivityIndicator size={'large'} color={COLORS.primary} /> :
          <>
            {/* Ride Info */}
            <Text style={styles.arrivalTime}>Arriving in 5 mins...</Text>
            <Text style={styles.carInfo}>
              {rideDetails?.data?.carDetails?.model || 'Car'} |
              {rideDetails?.data?.carDetails?.color || 'N/A'} |
              {rideDetails?.data?.carDetails?.registrationNumber || 'N/A'}
            </Text>
            <Text style={{ color: COLORS.red, fontSize: 14, fontWeight: '700' }}>{rideCancelled}</Text>

            <TouchableOpacity style={styles.rideDetailsContainer} >
              <View style={styles.rideDetailsHeader}>
                <Text style={styles.rideDetailsTitle}>Ride details</Text>
                <TouchableOpacity onPress={() => setMenuVisible(!menuVisible)}>
                  <Image source={images.more} style={styles.moreIcon as any} />
                </TouchableOpacity>
              </View>
              <Text style={styles.meetDriverText}>Meet driver at pick up spot </Text>
              <Text style={styles.pickupDescription}>
                {rideDetails?.data?.pickupPoint || 'Pick-up location'} ..., Driver will arrive here to pick you up
              </Text>

              {menuVisible && (
                <View style={styles.menuContainer}>
                  <TouchableOpacity style={styles.menuItem} onPress={startRide}>
                    <Image source={images.splitride} style={styles.moreIcon as any} />
                    <Text style={styles.menuText}>Start ride</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.menuItem} onPress={() => setDisclaimerVisible(true)}>
                    <Image source={images.endride} style={styles.moreIcon as any} />
                    <Text style={styles.menuTextRed}>End ride</Text>
                  </TouchableOpacity>
                </View>
              )}
            </TouchableOpacity>

            {/* Driver Info */}
            <View style={styles.driverInfoContainer}>
              <Image
                source={profile?.profileImg ? { uri: profile?.profileImg } : images.profile}
                style={styles.driverImage as any}
              />
              <Image
                source={(rideDetails?.data?.carDetails?.carImgUrl === "") ?
                  images?.CarSingle : { uri: rideDetails?.data?.carDetails?.carImgUrl }
                }
                style={[styles.driverImage as any, { position: 'relative', left: -20 }]}
              />

              <View style={styles.driverDetails}>
                <Text style={styles.driverName}>{profile?.firstName} {profile?.lastName}</Text>
              </View>
              <View style={{ position: 'absolute', right: 0, }}>
                <Text style={styles.driverRating}>4.4 ★ | {profile?.totalRides || 'N/A'}</Text>
                <Text style={styles.driverPrice}>${rideDetails?.data?.charge || 'N/A'}</Text>
              </View>
            </View>

            {/* Pickup and Drop-off Locations */}
            <View style={{ flexDirection: 'row', flex: 1, marginVertical: 15 , width: '80%'}}>
              <Image source={images.routeIndicator} style={styles.routeIndicator as any} />
              <View>
                <View style={styles.locationRow}>
                  <Text style={styles.locationText}>From: {rideDetails?.data?.from || 'N/A'}</Text>

                </View>

                <View style={styles.locationRow}>
                  <Text style={styles.locationText}>To: {rideDetails?.data?.to?.[0]?.place || 'N/A'}</Text>

                </View>

                {show && <View style={styles.locationRow}>
                  <View style={{ borderWidth: 0.5, borderColor: COLORS.border, borderRadius: 9 }} >
                    <GooglePlacesAutocompleteComponent
                      placeholder="Enter destination"
                      onSelect={(location: LocationType) => handleToSelect(0, location)}
                    />
                  </View>

                  <TouchableOpacity onPress={() => editRide()}>
                    <Image source={images.send} style={{ width: 50, height: 50, resizeMode: 'contain' }} />
                  </TouchableOpacity>
                </View>}
              </View>
            </View>

            {/* Contact Driver */}
            <View style={styles.contactContainer}>
              <TouchableOpacity
                onPress={() => {
                  if (rideDetails?.data?.rideId) {
                    navigation.navigate('ChatScreen', {
                      rideId: rideDetails.data.rideId,
                      passengerId: rideDetails.data.passengerId,
                      passengerName: 'Passenger', // Default name since passengerName doesn't exist in RideData
                      passengerImg: undefined // Default image since passengerImg doesn't exist in RideData
                    });
                  } else {
                    Alert.alert('Error', 'Ride ID not available');
                  }
                }}
                style={[styles.messageButton, { width: '70%' }]}>
                <Text style={styles.messageText}>Message Passenger</Text>
              </TouchableOpacity>
              <TouchableOpacity
                onPress={() => {
                  if (rideDetails?.data?.rideId && rideDetails?.data?.passengerId) {
                    // Get passenger information from ride details
                    const passengerInfo = {
                      rideId: rideDetails.data.rideId,
                      receiverId: rideDetails.data.passengerId,
                      recipientName: "Passenger", // Default name if not available
                      recipientImg: "https://i.pravatar.cc/300", // Default image if not available
                      recipientPhone: "", // Default phone if not available
                      driverId: rideDetails.data.driverId
                    };

                    console.log("Navigating to CallScreen with:", passengerInfo);
                    navigation.navigate('CallScreen', passengerInfo);
                  } else {
                    Alert.alert('Error', 'Ride or passenger information not available');
                  }
                }}
                style={[styles.messageButton, { backgroundColor: COLORS.white, flexDirection: 'row', alignItems: 'center' }]}
              >
                <Image source={images.call} style={styles.callIcon as any} />
                <Text style={styles.messageText}>Call</Text>
              </TouchableOpacity>
            </View>

            {/* Safety Section */}
            <TouchableOpacity style={styles.safetyButton}
              // onPress={() => navigation.navigate('RideTrackingScreen')}
              onPress={() => setSafetyVisible(true)}
            >
              <Text style={styles.safetyButtonText}>Safety</Text>
              <Image source={images.safety} style={styles.callIcon as any} />

            </TouchableOpacity>
          </>}


        <View style={styles.buttonContainer}>


          <TouchableOpacity onPress={completeRide} style={[styles.button, { backgroundColor: "#dc3545" }]}>
            <Text style={styles.buttonText}>🏁 Complete Ride</Text>
          </TouchableOpacity>
        </View>

      </ScrollView>

      {isShareModalVisible && (
        <View style={styles.shareModal}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShareModalVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.shareModalTitle}>
            Share your location to help driver find you faster
          </Text>

          <Image source={images.locationBeep} style={styles.locationImage as any} />

          <Text style={styles.shareDescription}>
            Location shared while the app is open
          </Text>
          <Text style={styles.shareDescription}>
            Get picked at the exact location
          </Text>
          <Text style={styles.shareDescription}>
            Make you and the driver safer
          </Text>
          <Text style={styles.shareDisclaimer}>
            You can disable location sharing anytime by tapping the share icon.
            Sharing only happens when drivers are 5 mins or 200 meters away.
          </Text>

          <TouchableOpacity style={styles.shareButton} onPress={() => navigation.navigate('ShareRideScreen')}>
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.dontShareButton}
            onPress={() => setShareModalVisible(false)}
          >
            <Text style={styles.dontShareButtonText}>Don’t share</Text>
          </TouchableOpacity>
        </View>
      )}

      {isDisclaimerVisible && (
        <View style={styles.disclaimerSheet}>
          <TouchableOpacity
            style={styles.disclaimerCloseButton}
            onPress={() => setDisclaimerVisible(false)}
          >
            <Text style={styles.disclaimerCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.disclaimerTitle}>Disclaimer</Text>
          <Image source={images.disclaimerImage} style={styles.disclaimerImage as any} />
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerBold}>1. Free cancellation:</Text> Within 2–5 minutes of requesting a ride (varies by location).
          </Text>
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerBold}>2. Cancellation fee:</Text> Applies if you cancel after the free cancellation window (typically $5–$10).
          </Text>
          <Text style={styles.disclaimerText}>
            <Text style={styles.disclaimerBold}>3. Driver arrival:</Text> If the driver has already arrived, you may be charged a cancellation fee.
          </Text>

          <TouchableOpacity style={styles.keepRideButton} onPress={() => setDisclaimerVisible(false)}>
            <Text style={styles.keepRideButtonText}>Keep my ride</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.cancelRideButton}
            onPress={() => {
              setDisclaimerVisible(false);
              setReasonSheetVisible(true);
            }}
          >
            <Text style={styles.cancelRideButtonText}>Yes, cancel</Text>
          </TouchableOpacity>
        </View>
      )}

      {isReasonSheetVisible && (
        <View style={styles.reasonSheet}>
          <TouchableOpacity
            style={styles.reasonCloseButton}
            onPress={() => setReasonSheetVisible(false)}
          >
            <Text style={styles.reasonCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.reasonTitle}>Why do you want end the ride?</Text>

          {[
            "Driver not getting closer",
            "Driver arrived early",
            "Driver didn't get here in time",
            "Driver asked me to cancel",
            "Could not find the driver",
            "Other reason",
          ].map((reason, index) => (
            <TouchableOpacity
              key={index}
              style={styles.reasonOption}
              onPress={() => {
                setReasonSheetVisible(false);
                // Add reason selection logic here
              }}
            >
              <Text style={styles.reasonOptionText}>{reason}</Text>
            </TouchableOpacity>
          ))}

          <TouchableOpacity
            style={styles.keepRideButton}
            onPress={() => {
              setReasonSheetVisible(false);
              setCancellationReasonVisible(true);
            }}
          >
            <Text style={styles.keepRideButtonText}> Submit your reason</Text>
          </TouchableOpacity>
        </View>
      )}

      {isCancellationReasonVisible && (
        <View style={styles.cancellationReasonSheet}>
          <TouchableOpacity
            style={styles.reasonCloseButton}
            onPress={() => setCancellationReasonVisible(false)}
          >
            <Text style={styles.reasonCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.reasonTitle}>Cancellation reason</Text>

          <View style={styles.textAreaContainer}>
            <TextInput
              style={styles.textArea}
              placeholder="Pls state your cancellation reason"
              placeholderTextColor={COLORS.grey}
              multiline={true}
              numberOfLines={5}
            />
          </View>

          <TouchableOpacity
            style={styles.sendButton}
            onPress={() => {
              setCancellationReasonVisible(false);
              setConnectDriverVisible(true);
            }}
          >
            <Text style={styles.sendButtonText}>Send</Text>
          </TouchableOpacity>
        </View>
      )}

      {isConnectDriverVisible && (
        <View style={styles.connectDriverSheet}>
          <TouchableOpacity
            style={styles.reasonCloseButton}
            onPress={() => setConnectDriverVisible(false)}
          >
            <Text style={styles.reasonCloseText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.connectDriverTitle}>Are you sure you want to end this ride?</Text>

          <TouchableOpacity
            style={styles.connectNewDriverButton}
            onPress={() => {
              // Add your "Connect New Driver" logic here
              setConnectDriverVisible(false);
              navigation.navigate('TakeABreak')
            }}
          >
            <Text style={styles.connectNewDriverText}>Connect to new Passenger</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.cancelRideButton}
            onPress={() => {
              // Add your "Cancel Ride" logic here
              setConnectDriverVisible(false);
            }}
          >
            <Text style={styles.cancelRideText}>Yes, cancel</Text>
          </TouchableOpacity>
        </View>
      )}
      {isSafetyVisible && (
        <View style={styles.safetySheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setSafetyVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.safetyTitle}>Safety</Text>
          <Text style={styles.safetySubtitle}>
            This is in place to help you feel safe and secure
          </Text>

          <TouchableOpacity style={styles.safetyOption} onPress={() => {
            setSafetyVisible(false)
            setEmergencyVisible(true)
          }}>
            <Image source={images.emergency} style={styles.safetyIcon as any} />
            <Text style={styles.safetyOptionText}>Emergency Assistance</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.safetyOption} onPress={() => { setReportSafetyVisible(true) }}>
            <Image source={images.report} style={styles.safetyIcon as any} />
            <Text style={styles.safetyOptionText}>Report safety issues</Text>
          </TouchableOpacity>
          <View style={styles.divider} />
          <TouchableOpacity style={styles.safetyOption} onPress={shareRide}>
            <Image source={images.sharerideinfo} style={styles.safetyIcon as any} />
            <Text style={styles.safetyOptionText}>Share ride info</Text>
          </TouchableOpacity>
        </View>
      )}
      {isEmergencyVisible && (
        <View style={styles.emergencySheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setEmergencyVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.emergencyTitle}>Emergency Assistance</Text>
          <Text style={styles.emergencySubtitle}>
            Share your location information and car details with admin
          </Text>

          <View style={styles.rideDetails}>
            <Text style={styles.rideDetailsTitle}>Ride details</Text>
            <Text style={styles.pickupText}>Meet driver at pick up spot</Text>
            <Text style={styles.pickupDescription}>
              AMLI 7th Street Station..., Driver will arrive here to pick you up
            </Text>
          </View>

          <View style={styles.driverDetails}>
            <View style={{ flexDirection: 'row' }}>
              <Image source={images.CarSingle} style={styles.carImage as any} />
              <Image source={images.driver1} style={[styles.carImage as any, { position: 'relative', left: -20 }]} />
            </View>
            <View>
              <Text style={styles.driverName}>Olive Rodrigo</Text>

            </View>
            <View>
              <Text style={styles.driverPrice}>$12.5</Text>
              <Text style={styles.driverRating}>4.4 ★ | 53 rides</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.panicButton} onPress={() => {
            setEmergencyVisible(false)
            setReportSafetyVisible(true)
          }}>
            <Text style={styles.panicButtonText}>Panic alarm</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.callButton}>
            <Text style={styles.callButtonText}>Call 911</Text>
          </TouchableOpacity>
        </View>
      )}
      {isReportSafetyVisible && (
        <View style={styles.reportSafetySheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setReportSafetyVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.reportSafetyTitle}>Report safety issues</Text>
          <Text style={styles.reportSafetySubtitle}>
            Tell us about any safety concern on this trip...it's confidential
          </Text>

          <View style={styles.inputContainer}>
            <TextInput
              style={styles.input}
              placeholder="Describe the situation"
              placeholderTextColor={COLORS.grey}
              multiline
            />
          </View>

          <TouchableOpacity style={styles.shareButton} onPress={() => {
            setReportSafetyVisible(false)
            setThankYouVisible(true)
          }}>
            <Text style={styles.shareButtonText}>Share</Text>
          </TouchableOpacity>
        </View>
      )}

      {isThankYouVisible && (
        <View style={styles.thankYouSheet}>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setThankYouVisible(false)}
          >
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>

          <Text style={styles.thankYouText}>Thank you for this important information</Text>

          <TouchableOpacity
            style={styles.doneButton}
            onPress={() => setThankYouVisible(false)}
          >
            <Text style={styles.doneButtonText}>Done</Text>
          </TouchableOpacity>
        </View>
      )}





    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginTop: 20,
    marginBottom: 30,
  },
  button: {
    padding: 12,
    borderRadius: 8,
    alignItems: "center",
    width: "45%",
  },
  buttonText: {
    fontSize: 16,
    color: "#fff",
    fontWeight: "bold",
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  thankYouSheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  thankYouText: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginVertical: 20,
  },
  doneButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  doneButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },

  reportSafetySheet: {
    position: 'absolute',
    bottom: 0,
    zIndex: 1,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  reportSafetyTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 10,
  },
  reportSafetySubtitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.grey,
    marginBottom: 20,
  },
  inputContainer: {
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    ...FONTS.body4,
    padding: 10,
    marginBottom: 20,
  },
  input: {
    ...FONTS.body3,
    color: COLORS.black,
    minHeight: 100,
    textAlignVertical: 'top',
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginTop: 10,
  },
  shareButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  } as any,
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },

  emergencySheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  emergencyTitle: {
    ...FONTS.body3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 10,
  },
  emergencySubtitle: {
    ...FONTS.body4,
    textAlign: 'center',
    marginHorizontal: 40,
    color: COLORS.grey,
    marginBottom: 20,
  },
  rideDetails: {
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 15,
    borderRadius: 10,
    marginBottom: 20,
  },
  rideDetailsTitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  pickupText: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 5,
  },
  pickupDescription: {
    ...FONTS.body4,
    color: COLORS.red,
  } as any,
  driverDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  carImage: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
    marginRight: 10,
    borderRadius: 25,
  },
  driverName: {
    ...FONTS.h3,
    color: COLORS.black,
  } as any,
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'right'
  } as any,
  driverPrice: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'right'
  },
  panicButton: {
    backgroundColor: COLORS.red,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },
  panicButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  callButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.red,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  callButtonText: {
    ...FONTS.h3,
    color: COLORS.red,
  },

  safetySheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  safetyTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginVertical: 10,
    // marginHorizontal:60
  },
  safetySubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginHorizontal: 30,
    marginBottom: 20,
    marginVertical: 10,

  },
  safetyOption: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
  },
  safetyIcon: {
    width: 30,
    height: 30,
    marginRight: 15,
  },
  safetyOptionText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  divider: {
    height: 1,
    backgroundColor: COLORS.border,
    marginVertical: 5,
  },

  connectDriverSheet: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    height: SIZES.width * 0.9,
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  connectDriverTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  connectDriverSubtitle: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  connectDriverInfo: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 20,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  driverCarImage: {
    width: 40,
    height: 40,
    marginRight: 10,
    resizeMode: 'contain'
  },
  driverAvatar: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
    // marginRight:20,
    borderRadius: 20,
    marginRight: 10,
  },
  // driverDetails: {
  //   // flex: 1,
  // },
  driverName: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  driverStats: {
    ...FONTS.body4,
    color: COLORS.grey,
  },

  connectNewDriverButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    marginVertical: 20,
    alignItems: 'center',
    marginBottom: 10,
  },
  connectNewDriverText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  cancelRideButton: {
    backgroundColor: COLORS.white,
    borderColor: COLORS.red,
    borderWidth: 1,
    borderRadius: SIZES.radius,
    padding: 15,
    marginTOp: 30,

    alignItems: 'center',
  },
  cancelRideText: {
    ...FONTS.h3,
    color: COLORS.red,
  },

  cancellationReasonSheet: {
    zIndex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  textAreaContainer: {
    borderColor: COLORS.border,
    borderWidth: 1,
    borderRadius: SIZES.radius,
    marginVertical: 20,
    padding: 10,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
    color: COLORS.black,
    ...FONTS.body3,
  },
  sendButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  sendButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },

  reasonSheet: {
    position: 'absolute',
    zIndex: 1,
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  reasonCloseButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  reasonCloseText: {
    fontSize: 24,
    color: COLORS.black,
  },
  reasonTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  reasonOption: {
    borderBottomWidth: 1,
    borderBottomColor: COLORS.border,
    paddingVertical: 15,
  },
  reasonOptionText: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
  },

  disclaimerSheet: {
    zIndex: 1,

    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  disclaimerCloseButton: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  disclaimerCloseText: {
    fontSize: 24,
    color: COLORS.black,
  },
  disclaimerTitle: {
    ...FONTS.h3,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  disclaimerImage: {
    width: 120,
    height: 120,
    resizeMode: 'contain',
    alignSelf: 'center',
    marginBottom: 20,
  },
  disclaimerText: {
    ...FONTS.body4,
    color: COLORS.grey,
    lineHeight: 20,
    marginHorizontal: 20,
    marginBottom: 10,
    textAlign: 'center'
  },
  disclaimerBold: {
    // fontWeight: 'bold',
    color: COLORS.black,
  },
  keepRideButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
    marginTop: 10,

  },
  keepRideButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  cancelRideButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.red,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  cancelRideButtonText: {
    ...FONTS.h3,
    color: COLORS.red,
  },

  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1,
  },

  rideDetailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  moreIcon: {
    marginHorizontal: 10,
    width: 15,
    height: 15,
    resizeMode: 'contain',
  },
  menuContainer: {
    position: 'absolute',
    top: 50,
    right: 10,
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
    paddingVertical: 10,
    paddingHorizontal: 15,
    zIndex: 1,
  },
  shareModal: {
    zIndex: 1,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  closeButton: {
    position: 'absolute',
    top: 10,
    right: 10,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  shareModalTitle: {
    ...FONTS.body3,
    marginHorizontal: 50,
    color: COLORS.black,
    textAlign: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  locationImage: {
    width: 60,
    height: 60,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginBottom: 20,
  },
  shareDescription: {
    ...FONTS.body4,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 10,
  },
  shareDisclaimer: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 20,
    fontSize: 12,
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
    marginBottom: 10,
  },

  dontShareButton: {
    backgroundColor: COLORS.white,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  dontShareButtonText: {
    ...FONTS.body3,
    color: COLORS.black,
  },

  menuItem: {
    flexDirection: 'row',
    paddingVertical: 10,
    // borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  menuText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  menuTextRed: {
    ...FONTS.body4,
    color: COLORS.red,
  },

  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: SIZES.padding,
    marginTop: -30,
  },
  arrivalTime: {
    ...FONTS.body3,
    fontSize: 15,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 5,
  },
  carInfo: {
    ...FONTS.body4,
    fontSize: 12,

    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
    paddingBottom: 15,
  },
  rideDetailsContainer: {
    marginBottom: 20,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 15,
    borderRadius: SIZES.radius,
  },
  rideDetailsTitle: {
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.black,
    marginBottom: 5,
  },
  meetDriverText: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 5,
  },
  pickupDescription: {
    ...FONTS.body4,
    fontSize: 10,
    color: COLORS.black,
  },
  driverInfoContainer: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 20,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    resizeMode: 'contain',
    marginRight: 10,
  },
  // driverDetails: {
  //   // flex: 1,
  // },
  driverName: {
    ...FONTS.h3,
    fontSize: 15,
    color: COLORS.black,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
    fontSize: 12,
  },

  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  locationIndicator: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: COLORS.primary,
    marginRight: 10,
  },
  dropoffIndicator: {
    backgroundColor: COLORS.red,
  },
  locationText: {
    ...FONTS.body3,
    fontSize: 13,
    width: '100%',

    color: COLORS.black,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    padding: 10,
    borderRadius: 7,
  },
  editIcon: {
    marginHorizontal: 10,
    width: 25,
    height: 25,
  },
  routeIndicator: {
    marginTop: 15,
    width: 60,
    height: 90,
    resizeMode: 'contain',
  },
  contactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    // justifyContent: 'space-between',
    marginBottom: 20,

  },
  messageButton: {
    marginVertical: 15,
    backgroundColor: COLORS.light_blue,
    borderRadius: SIZES.radius,
    padding: 10,
  },
  messageText: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  callIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    marginHorizontal: 15
  },
  safetyButton: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    flexDirection: 'row',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
    padding: 10,
    alignItems: 'center',
  },
  safetyButtonText: {
    ...FONTS.h3,
    color: COLORS.primary,
  },




  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
  },
  pickupMarker: {
    backgroundColor: 'green',
    borderColor: 'darkgreen',
  },
  dropoffMarker: {
    backgroundColor: 'red',
    borderColor: 'darkred',
  },
  markerText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 18,
  },
}) as any;

export default RideDetailScreen;
