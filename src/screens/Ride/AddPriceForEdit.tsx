import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  Alert,
  Platform,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { COLORS, FONTS, icons, images } from "../../constants";
import { useNavigation, useRoute } from "@react-navigation/native";
import socketService from "../../services/socketService";

const AddPriceForEdit = () => {
  const [pricePerMile, setPricePerMile] = useState("");
  const region = {
    latitude: 33.5186,
    longitude: -86.8104,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };
  const route = useRoute();

    const { rideId} = route.params || {};  
    const serverResponse = route.params || {};  
  console.log('🚗 serverResponse:', JSON.stringify(serverResponse,null,2));
  console.log('🚗 rideId:', JSON.stringify(rideId,null,2));
  
  const navigation = useNavigation();

  const handleConfirmRate = () => {
    if (!pricePerMile || isNaN(parseFloat(pricePerMile))) {
      Alert.alert("Invalid Input", "Please enter a valid price");
      return;
    }

    const price = parseFloat(pricePerMile);
    console.log(`✅ [AddPriceForEdit] Confirming rate: $${price} for rideId: ${rideId}`);

    Alert.alert(
      "Confirm Rate",
      `Are you sure you want to accept this ride edit with a rate of $${price}?`,
      [
        {
          text: "Cancel",
          style: "cancel"
        },
        {
          text: "Confirm",
          onPress: () => {
            console.log('📤 [AddPriceForEdit] Sending accept edit request to server');

            socketService.emitEvent('acceptEditRideRquest', {
              price: price,
              rideId: rideId,
              timestamp: Date.now()
            });
          }
        }
      ]
    );
  };

  useEffect(() => {
    socketService.onEvent('acceptEditRideRquest', (response) => {
      console.log('📨 [AddPriceForEdit] Server response:', JSON.stringify(response, null, 2));

      if (response.success) {
        Alert.alert(
          "Edit Accepted Successfully",
          response?.message || "The ride edit has been accepted with your pricing.",
          [
            {
              text: "OK",
              onPress: () => {
                console.log('✅ [AddPriceForEdit] Navigating back to ride details');
                navigation.navigate('EditPaymentMade', { rideId });
              }
            }
          ]
        );
      } else {
        let errorMessage = response?.message || "Failed to accept ride edit";

        if (response?.message === "Price is not within the range") {
          errorMessage = `Price $${pricePerMile} is not within the acceptable range. Please try a different amount.`;
        }

        Alert.alert("Edit Acceptance Failed", errorMessage);
        console.warn('❌ [AddPriceForEdit] Edit acceptance failed:', response);
      }
    });

    return () => {
      socketService.socket.off('acceptEditRideRquest');
    };
  }, [pricePerMile, rideId, navigation]);

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Top Section (Map with Pricing Overlay) */}
      <View style={styles.mapSection}>
        <MapView
          style={styles.map}
          initialRegion={region}
          showsUserLocation={true}
        >
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Your Location"
            description="Pickup Location"
          />
        </MapView>
        <View style={styles.priceTag}>
          <Text style={styles.priceText}>$ {pricePerMile}</Text>
        </View>
      </View>

      {/* Bottom Sheet Section */}
      <ScrollView style={styles.bottomSheet}>
        {/* Header */}
        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.headerText}>Set Price</Text>
          <TouchableOpacity onPress={()=>navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Illustration */}
        <Image source={images.setprice} style={styles.illustration} />

        {/* Free Cancellation Info */}
        <Text style={styles.freeCancellationText}>
        {serverResponse?.serverResponse?.serverResponse?.message || "Ride request received"}
        </Text>

        <View
          style={{
            flex: 1,
            borderWidth: 0.5,
            borderColor: COLORS.grey,
            borderRadius: 10,
            padding: 10,
            marginBottom: 30,
          }}
        >
          {/* Ride Details */}
          <View style={styles.rideDetails}>
  <Text style={styles.rideDetailsTitle}>Ride details</Text>
  
  {/* Display Request Message */}
  {/* <Text style={styles.rideDetailsSubtitle}>
  
  </Text> */}

  {/* Display From and To Locations */}
  <Text style={styles.rideDetailsTitle}>
    {serverResponse?.serverResponse?.serverResponse?.ride?.from 
      ? `From ${serverResponse?.serverResponse?.serverResponse?.ride?.from} to ` 
      : "From Unknown Pickup to "}
      
    {serverResponse?.serverResponse?.serverResponse?.ride?.to?.length > 0
      ? serverResponse?.serverResponse?.serverResponse?.ride?.to
          .map((destination) => destination?.place)
          .join(", ")
      : "Unknown Destination"}.
  </Text>

  {/* Prompt for pricing */}
  <Text style={styles.rideDetailsSubtitle}>
    Enter the amount you want to charge for the ride.
  </Text>
</View>


          {/* Price Input */}
          <TextInput
            style={styles.input}
            placeholder="Enter price per miles"
            placeholderTextColor={COLORS.grey}
            keyboardType="numeric"
            value={pricePerMile}
            onChangeText={(text) => setPricePerMile(text)}
          />
        </View>

        {/* Link to Market Fares */}
        <Text style={styles.marketFaresLink}>
          View prevailing market fares and your price is between{" "}
          <Text style={styles.highlightText}>${serverResponse?.serverResponse?.serverResponse?.priceRange}</Text>
        </Text>

        {/* Action Buttons */}
        <View style={styles.actionButtonsContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={() => {
            Alert.alert(
              "Cancel Edit",
              "Are you sure you want to cancel this ride edit?",
              [
                { text: "No", style: "cancel" },
                {
                  text: "Yes, Cancel",
                  style: "destructive",
                  onPress: () => {
                    console.log('🚫 [AddPriceForEdit] User cancelled edit acceptance');
                    navigation.goBack();
                  }
                }
              ]
            );
          }}>
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity style={styles.confirmButton} onPress={handleConfirmRate}>
            <Text style={styles.confirmButtonText}>Confirm rate</Text>
            <Image source={icons.dollar} style={styles.dollarIcon} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};


 


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  mapSection: {
    height: Dimensions.get("window").height * 0.2,
    width: "100%",
    position: "relative",
  },
  map: {
    height: "100%",
    width: "100%",
  },
  priceTag: {
    position: "absolute",
    top: 20,
    left: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    padding: 10,
  },
  priceText: {
    color: COLORS.white,
    fontWeight: "bold",
    fontSize: 18,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  headerText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  illustration: {
    height: 120,
    width: "100%",
    resizeMode: "contain",
    alignSelf: "center",
    marginBottom: 20,
  },
  freeCancellationText: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    lineHeight: 18,
    marginHorizontal: 10,
    textAlign: "center",
  },
  boldText: {
    fontWeight: "bold",
    color: COLORS.black,
  },
  highlightText: {
    color: COLORS.primary,
  },
  rideDetails: {
    marginBottom: 20,
  },
  rideDetailsTitle: {
    ...FONTS.body6,
    fontSize: 11,
    color: COLORS.black,
    marginVertical: 5,
  },
  rideDetailsSubtitle: {
    ...FONTS.h4,
    color: COLORS.black,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
  },
  marketFaresLink: {
    ...FONTS.body4,
    color: COLORS.primary,
    marginBottom: 20,
    textAlign: "center",
    textDecorationLine: "underline",
    marginHorizontal: 20,
  },
  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.light_grey,
    borderRadius: 10,
    paddingVertical: 15,
    borderWidth: 1,
    borderColor: COLORS.grey,
  },
  cancelButtonText: {
    color: COLORS.black,
    ...FONTS.h4,
    fontWeight: "600",
  },
  confirmButton: {
    flex: 1,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
  },
  confirmButtonText: {
    color: COLORS.white,
    ...FONTS.h4,
    fontWeight: "600",
    marginRight: 8,
  },
  dollarIcon: {
    width: 18,
    height: 18,
    resizeMode: "contain",
  },
});

export default AddPriceForEdit;
