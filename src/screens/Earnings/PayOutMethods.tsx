// import React, { useEffect, useState } from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   TouchableOpacity,
//   Dimensions,
//   Image,
//   FlatList,
//   TextInput,
//   Modal,
// } from 'react-native';
// import { COLORS, FONTS, images, icons } from '../../constants';
// import MapView, { Circle, Marker } from 'react-native-maps';
// import { useDispatch, useSelector } from 'react-redux';
// import { overwriteStore } from '../../../redux/ActionCreator';
// import Spinner from 'react-native-loading-spinner-overlay';

// const PayOutMethods = ({ navigation }) => {
//   const dispatch = useDispatch();
//   const bankDetails = useSelector(state => state?.store?.GetBankDetails?.bankDetails) || [];
//   const [modalVisible, setModalVisible] = useState(false);
//   const [modalVisiblee, setModalVisiblee] = useState(false);
//   const [selectedBank, setSelectedBank] = useState(null);
//   const [loading, setLoading] = useState(false);
// const [amount, setAmount] = useState('');

//   useEffect(() => {
//     fetchBankDetails();
//   }, []);

//   const openModal = (bank) => {
//     setSelectedBank(bank);
//     setModalVisible(true);
//   };

//   const openModalPay = (bank) => {
//     setModalVisiblee(true);
//   };
//   const handleUpdateBank = async () => {

//     if (!selectedBank) return;
//     setLoading(true);
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/driver/bank/updateBankDetails', {
//         method: 'POST',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           bankId: selectedBank.id,
//           accountName: selectedBank.accountName,
//           accountNumber: selectedBank.accountNumber,
//           bankName: selectedBank.bankName,
//         }),
//       });

//       const data = await response.json();
//       console.log('Update Bank Response:', data);

//       if (response.ok) {
//         fetchBankDetails(); // Refresh the list
//         setModalVisible(false);
//       }
//     } catch (error) {
//       console.error('Error updating bank details:', error);
//     }
//     setLoading(false);
//   };

//   const handleDeleteBank = async () => {
//     if (!selectedBank) return;
//     setLoading(true);
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/driver/bank/deleteBankDetails', {
//         method: 'POST',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ bankId: selectedBank.id }),
//       });

//       const data = await response.json();
//       console.log('Delete Bank Response:', data);

//       if (response.ok) {
//         fetchBankDetails(); // Refresh the list
//         setModalVisible(false);
//       }
//     } catch (error) {
//       console.error('Error deleting bank details:', error);
//     }
//     setLoading(false);
//   };

//   const handlePayoutRequest = async () => {
//     if (!selectedBank || !amount) {
//       alert('Please enter an amount.');
//       return;
//     }

//     setLoading(true);
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/driver/payout/newPayout', {
//         method: 'POST',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           bankId: selectedBank.id,
//           amount,
//         }),
//       });

//       const data = await response.json();
//       console.log('Payout Response:', data);

//       if (response.ok) {
//         alert('Payout request successful!');
//         setModalVisiblee(false);
//         navigation.navigate('PayOutHistory');
//       } else {
//         alert(data.message || 'Payout request failed.');
//       }
//     } catch (error) {
//       console.error('Error processing payout:', error);
//     }
//     setLoading(false);
//   };

//   const fetchBankDetails = async () => {
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/driver/bank/getBankDetails', {
//         method: 'GET',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       });

//       const contentType = response.headers.get("content-type");

//       if (contentType && contentType.includes("application/json")) {
//         const data = await response.json();
//         console.log('Bank Details Response (JSON):', JSON.stringify(data))

//         if (response.ok) {
//           dispatch(overwriteStore({ name: 'GetBankDetails', value: data?.message || [] }));
//         }
//       } else {
//         const textResponse = await response.text();
//         console.log('Bank Details Response (RAW TEXT):', textResponse);
//       }
//     } catch (error) {
//       console.error('Error fetching bank details:', error);
//     }
//   };

//   const payoutMethods = Array.isArray(bankDetails) ? bankDetails.map((bank, index) => ({
//     id: bank._id || index.toString(),
//     accountNumber: bank.accountNumber,
//     accountName: bank.accountName,
//     bankName: bank.bankName,
//     bankLogo: images[bank.bankName.toLowerCase()] || images.access,
//   })) : [];

//   // const renderPayoutItem = ({ item }) => (
//   //   <TouchableOpacity style={styles.payoutItem} onPress={() => navigation.navigate('PayOutHistory')}>
//   //     <View style={styles.payoutDetails}>
//   //       <Text style={styles.accountNumber}>{item.accountNumber}</Text>
//   //       <Text style={styles.accountName}>{item.accountName}</Text>
//   //     </View>
//   //     <View style={styles.bankDetails}>
//   //       <Image source={item.bankLogo} style={styles.bankLogo} />
//   //       <Text style={styles.bankName}>{item.bankName}</Text>
//   //     </View>
//   //   </TouchableOpacity>
//   // );

//   const fetchBankPaymentDetails = async (bankId) => {
//     setLoading(true);
//     try {
//       const response = await fetch(`https://inride-server.onrender.com/api/driver/bank/getBankDetail?bankId=${bankId}`, {
//         method: 'GET',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       });

//       console.log('Bank Payment Details Response:', response);

//       const contentType = response.headers.get("content-type");

//       if (contentType && contentType.includes("application/json")) {
//         const data = await response.json();
//         console.log('Bank Payment Details Response:', JSON.stringify(data, null, 2));

//         if (response.ok) {
//           navigation.navigate('PayOutHistory', { bankPaymentDetails: data?.message });
//         }
//       } else {
//         const textResponse = await response.text();
//         console.log('Bank Payment Details (RAW TEXT):', textResponse);
//         navigation.navigate('PayOutHistory');
//       }
//     } catch (error) {
//       console.error('Error fetching bank payment details:', error);
//     }
//     setLoading(false);
//   };

//   const renderPayoutItem = ({ item }) => (
//     <View style={styles.payoutItem}>
//       <TouchableOpacity style={styles.payoutDetails} onPress={() => {
//         fetchBankPaymentDetails(item.id)
//         openModalPay(item)
//       }}>
//         <Text style={styles.accountNumber}>{item.accountNumber}</Text>
//         <Text style={styles.accountName}>{item.accountName}</Text>
//       </TouchableOpacity>
//        <TouchableOpacity onPress={() => openModal(item)}>
//           <Image source={images.edit} style={styles.actionIcon} />
//         </TouchableOpacity>
//       <View style={styles.bankDetails}>
//         <Image source={item.bankLogo} style={styles.bankLogo} />
//         <Text style={styles.bankName}>{item.bankName}</Text>
//       </View>

//       {/* Edit and Delete Icons */}

//     </View>
//   );

//   return (
//     <View style={styles.container}>
//       {/* Map Section */}
//       <View style={styles.mapContainer}>
//         <MapView
//           style={styles.map}
//           initialRegion={{
//             latitude: 33.5186,
//             longitude: -86.8104,
//             latitudeDelta: 0.05,
//             longitudeDelta: 0.05,
//           }}
//         >
//           <Circle
//             center={{ latitude: 33.5186, longitude: -86.8104 }}
//             radius={3000}
//             strokeColor={COLORS.primary}
//             fillColor="rgba(0, 123, 255, 0.2)"
//           />
//           <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
//             <Image source={images.CarSingle} style={styles.markerImage} />
//           </Marker>
//         </MapView>
//         <View style={styles.earningsOverlay}>
//           <Text style={styles.earningsText}>$ 5000</Text>
//         </View>
//       </View>

//       <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 20, marginTop: 20 }}>
//         <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
//           <Image
//             source={icons.BackIcon}
//             style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }}
//           />
//         </TouchableOpacity>
//         <Text style={styles.headerTitle}>Payout Methods</Text>
//       </View>

// <Modal visible={modalVisible} transparent animationType="slide">
// <Spinner visible={loading} />

//   <View style={styles.modalContainer}>
//     <View style={styles.modalContent}>
//       <Text style={styles.modalTitle}>Edit Bank Details</Text>

//       <TextInput
//         style={styles.input}
//         placeholder="Bank Name"
//         value={selectedBank?.bankName}
//         onChangeText={(text) => setSelectedBank({ ...selectedBank, bankName: text })}
//       />

//       <TextInput
//         style={styles.input}
//         placeholder="Account Name"
//         value={selectedBank?.accountName}
//         onChangeText={(text) => setSelectedBank({ ...selectedBank, accountName: text })}
//       />

//       <TextInput
//         style={styles.input}
//         placeholder="Account Number"
//         keyboardType="numeric"
//         value={selectedBank?.accountNumber}
//         onChangeText={(text) => setSelectedBank({ ...selectedBank, accountNumber: text })}
//       />

//       {/* Buttons for Update & Delete */}
//       <View style={styles.modalButtons}>
//         <TouchableOpacity style={styles.updateButton} onPress={handleUpdateBank}>
//           <Text style={styles.buttonText}>Update</Text>
//         </TouchableOpacity>
//         <TouchableOpacity style={styles.deleteButton} onPress={handleDeleteBank}>
//           <Text style={styles.buttonText}>Delete</Text>
//         </TouchableOpacity>
//       </View>

//       <TouchableOpacity onPress={() => setModalVisible(false)}>
//         <Text style={styles.closeModalText}>Cancel</Text>
//       </TouchableOpacity>
//     </View>
//   </View>
// </Modal>

// <Modal visible={modalVisiblee} transparent animationType="slide">
//   <Spinner visible={loading} />
//   <View style={styles.modalContainer}>
//     <View style={styles.modalContent}>
//       <Text style={styles.modalTitle}>Request Payout</Text>
//       <Text style={styles.modalBankName}>{selectedBank?.bankName}</Text>

//       <TextInput
//         style={styles.input}
//         placeholder="Enter Amount"
//         keyboardType="numeric"
//         value={amount}
//         onChangeText={setAmount}
//       />

//       <TouchableOpacity style={styles.confirmButton} onPress={handlePayoutRequest}>
//         <Text style={styles.confirmButtonText}>Confirm Payout</Text>
//       </TouchableOpacity>

//       <TouchableOpacity onPress={() => setModalVisible(false)}>
//         <Text style={styles.closeModalText}>Cancel</Text>
//       </TouchableOpacity>
//     </View>
//   </View>
// </Modal>

//       {/* Payout List */}
//       <FlatList
//         data={payoutMethods}
//         renderItem={renderPayoutItem}
//         keyExtractor={(item) => item.id}
//         contentContainerStyle={styles.payoutList}
//         showsVerticalScrollIndicator={false}
//       />

//       {/* Add New Bank Button */}
//       <TouchableOpacity style={styles.addBankButton} onPress={() => navigation.navigate('AddNewBank')}>
//         <Text style={styles.addBankText}>Add new bank →</Text>
//       </TouchableOpacity>
//     </View>
//   );
// };

import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  Image,
  FlatList,
  TextInput,
  Modal,
  Alert,
  StyleSheet,
} from 'react-native';
import {COLORS, FONTS, images, icons} from '../../constants';
import MapView, {Circle, Marker} from 'react-native-maps';
import {useDispatch, useSelector} from 'react-redux';
import {overwriteStore} from '../../../redux/ActionCreator';
import Spinner from 'react-native-loading-spinner-overlay';
import {API_BASE_URL} from '../../config/apiConfig';

const PayOutMethods = ({navigation}) => {
  const dispatch = useDispatch();
  const bankDetails =
    useSelector(state => state?.store?.GetBankDetails?.bankDetails) || [];
  const [modalVisible, setModalVisible] = useState(false);
  const [modalVisiblePay, setModalVisiblePay] = useState(false);
  const [selectedBank, setSelectedBank] = useState(null);
  const [loading, setLoading] = useState(false);
  const [amount, setAmount] = useState(0);

  useEffect(() => {
    fetchBankDetails();
  }, []);

  const openModal = bank => {
    setSelectedBank(bank);
    setModalVisible(true);
  };

  const openModalPay = bank => {
    setSelectedBank(bank);
    setModalVisiblePay(true);
  };

  const handleUpdateBank = async () => {
    if (!selectedBank) return;
    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/bank/updateBankDetails`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bankId: selectedBank.id,
            accountName: selectedBank.accountName,
            accountNumber: selectedBank.accountNumber,
            bankName: selectedBank.bankName,
          }),
        },
      );

      const data = await response.json();
      console.log('Update Bank Response:', data);

      if (response.ok) {
        fetchBankDetails();
        setModalVisible(false);
      }
    } catch (error) {
      console.error('Error updating bank details:', error);
    }
    setLoading(false);
  };

  const handlePayoutRequest = async () => {
    if (!selectedBank || !amount) {
      alert('Please enter an amount.');
      return;
    }

    // Convert amount to a number
    const numericAmount = parseFloat(amount);

    if (isNaN(numericAmount) || numericAmount <= 0) {
      alert('Please enter a valid amount.');
      return;
    }

    setLoading(true);
    const body = JSON.stringify({
      bankId: selectedBank.id,
      amount: numericAmount, // Ensure the amount is a number
    });

    console.log('Payout Request Body:', body);

    try {
      const response = await fetch(`${API_BASE_URL}/driver/payout/newPayout`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: body,
      });

      const data = await response.json();
      console.log('Payout Response:', data);

      if (response.ok) {
        Alert.alert('Payout request successful!');
        setModalVisiblePay(false);
        navigation.navigate('PayOutHistory');
      } else {
        alert(data.message || 'Payout request failed.');
      }
    } catch (error) {
      console.error('Error processing payout:', error);
    }
    setLoading(false);
  };

  const fetchBankDetails = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/bank/getBankDetails`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      const data = await response.json();
      console.log('Bank Details Response:', JSON.stringify(data));

      if (response.ok) {
        dispatch(
          overwriteStore({name: 'GetBankDetails', value: data?.message || []}),
        );
      }
    } catch (error) {
      console.error('Error fetching bank details:', error);
    }
  };

  const handleDeleteBank = async () => {
    if (!selectedBank) return;
    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/bank/deleteBankDetails`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({bankId: selectedBank.id}),
        },
      );

      const data = await response.json();
      console.log('Delete Bank Response:', data);

      if (response.ok) {
        fetchBankDetails(); // Refresh the list
        setModalVisible(false);
      }
    } catch (error) {
      console.error('Error deleting bank details:', error);
    }
    setLoading(false);
  };

  const payoutMethods = Array.isArray(bankDetails)
    ? bankDetails.map((bank, index) => ({
        id: bank._id || index.toString(),
        accountNumber: bank.accountNumber,
        accountName: bank.accountName,
        bankName: bank.bankName,
        bankLogo: images[bank.bankName.toLowerCase()] || images.access,
      }))
    : [];

  const renderPayoutItem = ({item}) => (
    <View style={styles.payoutItem}>
      <TouchableOpacity style={styles.payoutDetails}>
        <Text style={styles.accountNumber}>{item.accountNumber}</Text>
        <Text style={styles.accountName}>{item.accountName}</Text>
      </TouchableOpacity>

      <View style={styles.bankDetails}>
        {/* <Image source={item.bankLogo} style={styles.bankLogo} /> */}
        <Text style={styles.bankName}>{item.bankName}</Text>
      </View>

      <TouchableOpacity onPress={() => openModal(item)}>
        <Image source={images.edit} style={styles.actionIcon} />
      </TouchableOpacity>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Map Section */}
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: 33.5186,
            longitude: -86.8104,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}>
          <Circle
            center={{latitude: 33.5186, longitude: -86.8104}}
            radius={3000}
            strokeColor={COLORS.primary}
            fillColor="rgba(0, 123, 255, 0.2)"
          />
          <Marker coordinate={{latitude: 33.5286, longitude: -86.8204}}>
            <Image source={images.CarSingle} style={styles.markerImage} />
          </Marker>
        </MapView>
        <View style={styles.earningsOverlay}>
          <Text style={styles.earningsText}>$ 5000</Text>
        </View>
      </View>

      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginLeft: 20,
          marginTop: 20,
        }}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}>
          <Image
            source={icons.BackIcon}
            style={{
              width: 18,
              height: 18,
              resizeMode: 'contain',
              marginRight: 100,
            }}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payout Methods</Text>
      </View>

      <FlatList
        data={payoutMethods}
        renderItem={renderPayoutItem}
        keyExtractor={item => item.id}
        contentContainerStyle={styles.payoutList}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => (
          <View style={styles.emptyComponent}>
            <Text style={styles.emptyText}>No payout methods added yet</Text>
            <TouchableOpacity
              style={styles.addBankButton}
              onPress={() => navigation.navigate('AddNewBank')}>
              <Text style={styles.addBankText}>Add new bank →</Text>
            </TouchableOpacity>
          </View>
        )}
      />

      <TouchableOpacity
        style={styles.addBankButton}
        onPress={() => navigation.navigate('AddNewBank')}>
        <Text style={styles.addBankText}>Add new bank →</Text>
      </TouchableOpacity>
      {/* Payout Request Modal */}
      <Modal visible={modalVisiblePay} transparent animationType="slide">
        <Spinner visible={loading} />
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Request Payout</Text>
            <Text style={styles.modalBankName}>{selectedBank?.bankName}</Text>

            <TextInput
              style={styles.input}
              placeholder="Enter Amount"
              placeholderTextColor={COLORS.grey}
              keyboardType="numeric"
              value={amount}
              onChangeText={setAmount}
            />

            <TouchableOpacity
              style={styles.confirmButton}
              onPress={handlePayoutRequest}>
              <Text style={styles.confirmButtonText}>Confirm Payout</Text>
            </TouchableOpacity>

            <TouchableOpacity onPress={() => setModalVisiblePay(false)}>
              <Text style={styles.closeModalText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Edit Bank Modal */}
      <Modal visible={modalVisible} transparent animationType="slide">
        <Spinner visible={loading} />
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Edit Bank Details</Text>

            <TextInput
              style={styles.input}
              placeholderTextColor={COLORS.grey}
              placeholder="Bank Name"
              value={selectedBank?.bankName}
              onChangeText={text =>
                setSelectedBank({...selectedBank, bankName: text})
              }
            />

            <TextInput
              style={styles.input}
              placeholder="Account Name"
              placeholderTextColor={COLORS.grey}
              value={selectedBank?.accountName}
              onChangeText={text =>
                setSelectedBank({...selectedBank, accountName: text})
              }
            />

            <TextInput
              style={styles.input}
              placeholder="Account Number"
              keyboardType="numeric"
              placeholderTextColor={COLORS.grey}
              value={selectedBank?.accountNumber}
              onChangeText={text =>
                setSelectedBank({...selectedBank, accountNumber: text})
              }
            />

            {/* Buttons for Update & Delete */}
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={styles.updateButton}
                onPress={handleUpdateBank}>
                <Text style={styles.buttonText}>Update</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.deleteButton}
                onPress={handleDeleteBank}>
                <Text style={styles.buttonText}>Delete</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity onPress={() => setModalVisible(false)}>
              <Text style={styles.closeModalText}>Cancel</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
    padding: 20,
  },
  emptyText: {
    textAlign: 'center',
    ...FONTS.h3,
    color: COLORS.black,
  },
  payoutList: {
    paddingBottom: 20,
  },
  payoutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: COLORS.light_blue,
    borderRadius: 10,
    marginBottom: 10,
    elevation: 2,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  payoutDetails: {
    flex: 1,
  },
  accountNumber: {
    ...FONTS.h4,
    color: COLORS.black,
  },
  accountName: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  bankDetails: {
    alignItems: 'flex-end',
  },
  bankLogo: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  bankName: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    padding: 20,
    borderRadius: 10,
    width: '80%',
    alignItems: 'center',
  },
  modalTitle: {
    ...FONTS.h3,
    marginBottom: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 5,
    padding: 10,
    width: '100%',
    marginBottom: 10,
    backgroundColor: COLORS.white,
  },
  confirmButton: {
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 5,
    width: '100%',
  },
  confirmButtonText: {
    color: COLORS.white,
    textAlign: 'center',
  },
  closeModalText: {
    textAlign: 'center',
    marginTop: 10,
    color: COLORS.primary,
  },

  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  mapContainer: {
    height: 200,
  },
  map: {
    width: Dimensions.get('window').width,
    height: 200,
  },
  markerImage: {
    width: 30,
    height: 30,
  },
  earningsOverlay: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: COLORS.white,
    padding: 10,
    borderRadius: 10,
  },
  earningsText: {
    ...FONTS.h2,
    color: COLORS.primary,
  },
  headerTitle: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  payoutList: {
    padding: 20,
  },
  payoutItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 10,
    alignItems: 'center',
    backgroundColor: COLORS.light_blue,
    borderRadius: 10,
    marginBottom: 5,
    elevation: 2,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  payoutDetails: {},
  accountNumber: {
    ...FONTS.h4,
    color: COLORS.black,
  },
  accountName: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  bankDetails: {
    alignItems: 'flex-end',
  },
  bankLogo: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  bankName: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  addBankButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    alignItems: 'center',
    borderRadius: 10,
    margin: 20,
  },
  addBankText: {
    ...FONTS.h3,
    color: COLORS.white,
  },

  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: 60,
  },
  actionIcon: {
    width: 20,
    height: 20,
    marginHorizontal: 5,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: COLORS.white,
    padding: 20,
    borderRadius: 10,
    width: '80%',
  },
  modalTitle: {
    ...FONTS.h3,
    marginBottom: 10,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 5,
    padding: 10,
    marginBottom: 10,
    backgroundColor: COLORS.white,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  updateButton: {
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 5,
  },
  deleteButton: {
    backgroundColor: COLORS.red,
    padding: 10,
    borderRadius: 5,
  },
  buttonText: {
    color: COLORS.white,
    textAlign: 'center',
  },
  closeModalText: {
    textAlign: 'center',
    marginTop: 10,
    color: COLORS.primary,
  },
});

export default PayOutMethods;
