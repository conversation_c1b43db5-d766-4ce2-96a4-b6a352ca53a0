import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Dimensions,
  Image,
} from 'react-native';
import { COLORS, FONTS, icons, images } from '../../constants';
import MapView, { Circle, Marker } from 'react-native-maps';
import { overwriteStore } from '../../../redux/ActionCreator';
import { useDispatch, useSelector } from 'react-redux';
import { API_BASE_URL } from '../../config/apiConfig';

const PayOutHistory = ({ navigation }) => {
   

    const dispatch = useDispatch();
    const GetPayouts = useSelector(state => state?.store?.GetPayouts) || [];
  
    useEffect(() => {
      GetPayout();
    }, [dispatch]); // Ensure dispatch is included
  
    const GetPayout = async () => {
      try {
        const response = await fetch(
          `${API_BASE_URL}/driver/payout/getPayouts`,
          {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          }
        );
  
        const data = await response.json();
        console.log(data, 'response in GetPayouts');
  
        if (response.ok) {
          dispatch(overwriteStore({ name: 'GetPayouts', value: data.message.payouts || [] }));
        }
      } catch (error) {
        console.error('Error fetching payouts:', error);
      }
    };
  
    console.log('GetPayouts:', JSON.stringify(GetPayouts,null,2));
  
    const recentRides = GetPayouts.map((payout, index) => ({
      id: index.toString()
      ,bankName: payout.bankName || 'Unknown Bank',
      title: payout.accountName || 'Ride completed',
      date: payout.updatedAt || 'Unknown Date',
      amount: `${payout.amount || '0.00'}`,
      status: payout.status || 'Completed',
      reason: payout.reason || 'Unknown Reason',
      accountNumber: payout.accountNumber || 'Unknown Account Number',
      statusColor: payout.status === 'Canceled' ? COLORS.red : payout.status === 'Pending' ? COLORS.orange : 'green',
    }));
  
  const renderPayoutItem = ({ item }) => (
    <View style={styles.payoutItem}>
      <View>
        <Text style={styles.cardNumber}>{item.accountNumber} {item.bankName}</Text>
        <Text style={styles.payoutDate}>{item.title}</Text>

        <Text style={styles.payoutDate}>{item.date}</Text>
        <Text style={styles.payoutDate}>{item.reason}</Text>

      </View>
      <View style={styles.payoutDetails}>
        <Text style={styles.payoutAmount}>${item.amount}</Text>
          <Text style={[styles.rideStatus, { color: item.statusColor }]}>{item.status}</Text>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      

     
          {/* Map Section */}
          <View style={styles.mapContainer}>
            <MapView
              style={styles.map}
              initialRegion={{
                latitude: 33.5186,
                longitude: -86.8104,
                latitudeDelta: 0.05,
                longitudeDelta: 0.05,
              }}
            >
              {/* Circle */}
              <Circle
                center={{ latitude: 33.5186, longitude: -86.8104 }}
                radius={3000}
                strokeColor={COLORS.primary}
                fillColor="rgba(0, 123, 255, 0.2)"
              />
              {/* Example Marker */}
              <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
                <Image source={images.CarSingle} style={styles.markerImage} />
              </Marker>
            </MapView>
            {/* Overlay Amount */}
            <View style={styles.earningsOverlay}>
              <Text style={styles.earningsText}>$ 5000</Text>
            </View>
          </View>

          <View style={{ flexDirection: 'row', alignItems: 'center', marginLeft: 20 }}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image
            source={icons.BackIcon}
            style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }}
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payout History</Text>
      </View>

      {recentRides.length > 0 ? (
        <>
          {/* Payout List */}
          <FlatList
            data={recentRides}
            renderItem={renderPayoutItem}
            keyExtractor={(item) => item._id}
            contentContainerStyle={styles.payoutList}
            showsVerticalScrollIndicator={false}
          />

          {/* Total Payout */}
          <View style={styles.totalPayout}>
            <Text style={styles.totalPayoutText}>Total payout</Text>
            <Text style={styles.totalPayoutAmount}>$458.88</Text>
          </View>
        </>
      ) : (<>
        <Image source={icons.empty} style={styles.emptyImage} />

        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyTitle}>You want a payout?</Text>
          <TouchableOpacity style={styles.getPassengerButton}>
            <Text style={styles.getPassengerText}>Get a passenger →</Text>
          </TouchableOpacity>
        </View>
        </>
      )}
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  rideStatus: {
    ...FONTS.body4,
    fontSize:11,

  },
  mapContainer: {
    height: width * 0.5,
    position: 'relative',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  markerImage: {
    width: 30,
    height: 30,
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  backButton: {
    marginTop: 10,
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.dark_blue,
    textAlign: 'center',
    marginVertical: 10,
  },
  payoutList: {
    paddingVertical: 20,
  },
  payoutItem: {
    marginHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  cardNumber: {
    ...FONTS.body3,
    color: COLORS.dark_blue,
    fontSize: 14,
  },
  payoutDate: {
    ...FONTS.body4,
    color: COLORS.grey,
    fontSize: 12,
  },
  payoutDetails: {
    alignItems: 'flex-end',
  },
  payoutAmount: {
    ...FONTS.body3,
    color: COLORS.dark_blue,
    fontSize: 14,
  },
  payoutStatus: {
    ...FONTS.body4,
    fontSize: 12,
  },
  totalPayout: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    borderTopWidth: 0.5,
    borderTopColor: COLORS.border,
    paddingTop: 10,
    marginTop: 10,
    alignItems: 'center',
  },
  totalPayoutText: {
    ...FONTS.body4,
    color: COLORS.black,
    fontSize: 14,
  },
  totalPayoutAmount: {
    ...FONTS.body3,
    color: COLORS.primary,
    fontSize: 16,
    fontWeight: '800',
    marginTop: 5,
  },
  emptyStateContainer: {
    // flex: 1,
    marginTop: 20,
    marginHorizontal: 20,
    borderColor: COLORS.border,
    justifyContent: 'center',
    // alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 20,
    paddingHorizontal: 20,
    borderWidth: 1,
  },
  emptyImage: {
    alignSelf: 'center',
    width: 150,
    height: 150,
    resizeMode: 'contain',
    marginTop: 20,
  },
  emptyTitle: {
    ...FONTS.h3,
    color: COLORS.dark_blue,
    marginBottom: 10,
    // textAlign: 'center',
  },
  getPassengerButton: {
    // marginTop: 10,
  },
  getPassengerText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
});

export default PayOutHistory;
