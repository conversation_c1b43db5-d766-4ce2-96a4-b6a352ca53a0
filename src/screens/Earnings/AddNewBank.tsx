 

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Dimensions,
  Image,
  Platform,
} from 'react-native';
import MapView, { Circle, Marker } from 'react-native-maps';
import { COLORS, FONTS, images, icons } from '../../constants';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { overwriteStore } from '../../../redux/ActionCreator';
import { useDispatch } from 'react-redux';
import { API_BASE_URL } from '../../config/apiConfig';

const AddNewBank = ({ navigation }) => {
  const dispatch = useDispatch(); // ✅ Move inside the component
  const [bankDetails, setBankDetails] = useState({
    accountName: '',
    accountNumber: '',
    bankName: '',
  });
  const [loading, setLoading] = useState(false);
  

  const handleSubmit = async () => {
    if (!bankDetails.accountName || !bankDetails.accountNumber || !bankDetails.bankName) {
      alert('Please fill in all fields.');
      return;
    }
  
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/driver/bank/newBankDetails`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bankDetails),
      });
  
      const data = await response.json();
      console.log('New Bank Submission Response:', data);
  
      if (response.ok) {
        dispatch(overwriteStore({ name: 'GetBankDetails', value: data?.message })); // ✅ Update Redux Store
        Toast.show({ type: 'success', text1: data?.data });
        navigation.navigate('PayOutMethods');
      }
       else {
        Toast.show({ type: 'error', text1: data?.data });

      }
    } catch (error) {
      console.error('Error adding bank details:', error);
      Toast.show({ type: 'error', text1: data?.data });

    }
    setLoading(false);
  };
  
  const handleInputChange = (field, value) => {
    setBankDetails(prevState => ({
      ...prevState,
      [field]: value,
    }));
  };
  
  return (
    <View style={styles.container}>
      {/* Map Section */}
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <Spinner visible={loading} />
   

      {/* Header Section */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image source={icons.BackIcon} style={styles.backIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Add Bank Account</Text>
      </View>
      {/* <View style={styles.earningsDetails}>
        <Text style={styles.dateText}>Mar 11 - Mar 17</Text>
        <Text style={styles.earningsAmount}>$458.88</Text>
        <Text style={styles.ridesText}>23 rides over the last 5 days</Text>
      </View> */}

      {/* Form Section */}
      <View style={styles.formContainer}>
        <Text style={styles.formTitle}>Add New Bank</Text>
        <Text style={styles.formSubtitle}>Input the details of the new bank</Text>
        <Text style={styles.inputLabel}>Bank Name *</Text>
        
        <TextInput
  style={styles.input}
  placeholder="Bank of America"
  placeholderTextColor={COLORS.grey}
  value={bankDetails.bankName}
  onChangeText={(value) => handleInputChange('bankName', value)}
/>

<Text style={styles.inputLabel}>Account Name *</Text>

<TextInput
  style={styles.input}
  placeholder="Bank of America"
  placeholderTextColor={COLORS.grey}
  value={bankDetails.accountName}
  onChangeText={(value) => handleInputChange('accountName', value)}
/>

<Text style={styles.inputLabel}>Account No *</Text>

<TextInput
  style={styles.input}
  placeholder="Bank of America"
  placeholderTextColor={COLORS.grey}
  value={bankDetails.accountNumber}
  onChangeText={(value) => handleInputChange('accountNumber', value)}
/>
       
      </View>
      <TouchableOpacity style={styles.continueButton} onPress={handleSubmit}>

      {/* <TouchableOpacity style={styles.continueButton} onPress={() => navigation.navigate('PayOutMethods')}> */}
          <Text style={styles.continueButtonText}>Add New account</Text>
        </TouchableOpacity>
    </View>
  );
};

const { width } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  mapContainer: {
    height: width * 0.5,
    position: 'relative',
  },
  inputLabel:{
...FONTS.body3,
fontSize:13,
marginBottom:5,
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  markerImage: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 10,
  },
  backButton: {
    marginRight: 10,
  },
  backIcon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.black,
    marginLeft:80
  },
  earningsDetails: {
    alignItems: 'center',
    marginTop: 10,
  },
  dateText: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
  },
  earningsAmount: {
    ...FONTS.h1,
    color: COLORS.primary,
    marginVertical: 5,
    fontWeight: '800',
  },
  ridesText: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
  },
  formContainer: {
    backgroundColor: COLORS.light_blue,
    borderRadius: 10,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 20,
  },
  formTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 5,
    textAlign: 'center',
    fontSize:14
  },
  formSubtitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 15,
    textAlign: 'center',

  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 5,
    padding: 10,
    backgroundColor: COLORS.white,
    marginBottom: 20,
    color: COLORS.black,
    ...FONTS.body4,
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 10,
    alignItems: 'center',
    margin:20
  },
  continueButtonText: {
    ...FONTS.h3,
    fontSize:14,
    color: COLORS.white,
  },
});

export default AddNewBank;
