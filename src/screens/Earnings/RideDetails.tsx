import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  ScrollView,
} from 'react-native';
import MapView, { Polyline, Marker } from 'react-native-maps';
import { COLORS, FONTS, icons, images } from '../../constants';

const RideDetails = ({ navigation }) => {
  return (
    <View style={styles.container}>
      {/* Map Section */}
      <View style={styles.mapContainer}>
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: 33.5186,
            longitude: -86.8104,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}
        >
          {/* Example Polyline */}
          <Polyline
            coordinates={[
              { latitude: 33.5186, longitude: -86.8104 },
              { latitude: 33.5286, longitude: -86.8204 },
            ]}
            strokeColor={COLORS.primary} // Blue line
            strokeWidth={3}
          />
          {/* Start Marker */}
          <Marker coordinate={{ latitude: 33.5186, longitude: -86.8104 }}>
            <Image source={images.GreenMarker} style={styles.markerImage} />
          </Marker>
          {/* End Marker */}
          <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
            <Image source={images.RedMarker} style={styles.markerImage} />
          </Marker>
        </MapView>
        {/* Overlay Amount */}
        <View style={styles.earningsOverlay}>
          <Text style={styles.earningsText}>$ 5000</Text>
        </View>
      </View>

      {/* Ride Details Section */}
      <ScrollView>
      <View style={styles.detailsContainer}>
         <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                        <Image
                          source={icons.BackIcon}
                          style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }}
                        />
                      </TouchableOpacity>
        <Text style={styles.headerTitle}>Ride details</Text>
        <Text style={styles.dateText}>Mar 11, 2024 21:42:39</Text>
        <Text style={styles.subHeader}>Your earnings</Text>
        <Text style={styles.earningsAmount}>$458.88</Text>
      </View>

      {/* Route Details Section */}
<View style={styles.detailsContainerWithMap}>
      <View style={styles.mapContainerr}>
        <MapView
          style={styles.mapp}
          initialRegion={{
            latitude: 33.5186,
            longitude: -86.8104,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          }}
        >
          {/* Example Polyline */}
          <Polyline
            coordinates={[
              { latitude: 33.5186, longitude: -86.8104 },
              { latitude: 33.5286, longitude: -86.8204 },
            ]}
            strokeColor={COLORS.primary} // Blue line
            strokeWidth={3}
          />
          {/* Start Marker */}
          <Marker coordinate={{ latitude: 33.5186, longitude: -86.8104 }}>
            <Image source={images.GreenMarker} style={styles.markerImage} />
          </Marker>
          {/* End Marker */}
          <Marker coordinate={{ latitude: 33.5286, longitude: -86.8204 }}>
            <Image source={images.RedMarker} style={styles.markerImage} />
          </Marker>
        </MapView>
        
      </View>
      
      <View style={styles.routeDetailsContainer}>
        <Text style={styles.routeDuration}>9 mins ride</Text>
        <View style={styles.routeDetails}>
          <Image source={icons.RouteLine} style={styles.routeLine} />
          <View>
            <Text style={styles.locationText}>AMLI 7th Street Station</Text>
            <Text style={styles.locationText}>AMLI 7th Street Station</Text>
          </View>
        </View>
      </View>
      </View>
      </ScrollView>
    </View>
  );
};

const { width,height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  mapContainer: {
    height: height * 0.2,
    position: 'relative',
  },
  mapContainerr: {
    height: height * 0.2,
    margin:20,
    borderRadius:20,
    // position: 'relative',
  },
  map: {
    // ...StyleSheet.absoluteFillObject,
    height: height * 0.2,

  },
  mapp: {
    // ...StyleSheet.absoluteFillObject,
    borderRadius:10,
    height: height * 0.2,
  },
  markerImage: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  earningsOverlay: {
    position: 'absolute',
    top: 10,
    left: width * 0.4,
    backgroundColor: COLORS.primary,
    borderRadius: 20,
    padding: 10,
    alignItems: 'center',
  },
  earningsText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  detailsContainerWithMap: {
   margin:20,
    borderWidth: 0.5,
    borderColor: COLORS.border,
    borderRadius:10,
  },
  detailsContainer: {
    margin:20,
    //  borderWidth: 0.5,
    //  borderColor: COLORS.border,
   },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.dark_blue,
    textAlign: 'center',
  },
  dateText: {
    ...FONTS.body3,
    fontSize: 12,
    textAlign: 'center',
    color: COLORS.black,
    marginTop: 5,
  },
  subHeader: {
    ...FONTS.body3,
    fontSize: 13,
    textAlign: 'center',
    color: COLORS.black,
    marginTop: 10,
  },
  earningsAmount: {
    ...FONTS.h1,
    fontSize: 24,
    fontWeight: '800',
    color: COLORS.primary,
    textAlign: 'center',
    marginTop: 5,
  },
  routeDetailsContainer: {
    paddingHorizontal: 20,
    marginTop: 10,
  },
  routeDuration: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
    marginBottom: 10,
  },
  routeDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  routeLine: {
    width: 40,
    height: 80,
    resizeMode: 'contain',
    // marginRight: 10,
  },
  locationText: {
    marginTop: 5,
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.dark_blue,
    marginBottom: 10,
    borderWidth: 0.5,
    padding:10,
    borderRadius:10,
    borderColor:COLORS.border,
    width:width*0.7
  },
});

export default RideDetails;
