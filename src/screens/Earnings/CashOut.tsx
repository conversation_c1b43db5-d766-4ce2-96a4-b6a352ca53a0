import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
  Image,
  TextInput,Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../constants';
import { overwriteStore } from '../../../redux/ActionCreator';
import { useDispatch, useSelector } from 'react-redux';
import { API_BASE_URL } from '../../config/apiConfig';

const CashOut = ({ navigation }) => {
  const [currentWeek, setCurrentWeek] = useState(0);
  const [weeklyRides, setWeeklyRides] = useState([]);
  const [bankDetails, setBankDetails] = useState([]);
  const [selectedBankId, setSelectedBankId] = useState(null);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [isBottomSheetVisible, setIsBottomSheetVisible] = useState(false);

  const dispatch = useDispatch();
  const GetDriverRide = useSelector(state => state?.store?.GetDriverRide) || [];

  useEffect(() => {
    GetDriverRidee();
    fetchBankDetails();
  }, [dispatch]);

  const GetDriverRidee = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `${API_BASE_URL}/rides/getLastSevenDays`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();
      console.log(JSON.stringify(data, null, 2), 'response in GetDriverRide');

      const rides = data?.message?.rides || [];
      const formattedRides = rides.map((ride, index) => ({
        id: ride.rideId || index.toString(),
        title: ride.from || 'Unknown Ride',
        date: ride.updatedAt || 'Unknown Date',
        amount: `$${ride.charge || '0.00'}`,
        status: ride.status || 'Unknown Status',
        statusColor:
          ride.status === 'Requested' ? COLORS.orange :
          ride.status === 'Active' ? 'green' :
          ride.status === 'Canceled' ? COLORS.red : COLORS.grey,
      }));

      setWeeklyRides([formattedRides]);
      dispatch(overwriteStore({ name: 'GetDriverRide', value: formattedRides }));
    } catch (error) {
      console.error('Error fetching rides:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchBankDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `${API_BASE_URL}/driver/bank/getBankDetails`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      const data = await response.json();
      console.log('Bank Details Response:', JSON.stringify(data));

      if (response.ok) {
        setBankDetails(data?.message?.bankDetails || []);
        dispatch(overwriteStore({ name: 'GetBankDetails', value: data?.message?.bankDetails || [] }));
      }
    } catch (error) {
      console.error('Error fetching bank details:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePayoutRequest = async () => {
    if (!selectedBankId || !withdrawAmount) {
      alert('Please select a bank and enter an amount.');
      return;
    }

    try {
      setSubmitting(true);
      const response = await fetch(
        `${API_BASE_URL}/driver/payout/payoutRequest`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            bankId: selectedBankId,
            amount: withdrawAmount,
          }),
        }
      );

      const data = await response.json();
      console.log('Payout Response:', JSON.stringify(data));

      if (response.ok) {
        alert('Payout request successful!');
        setWithdrawAmount('');
        setSelectedBankId(null);
        navigation.navigate('RecentRides');
      } else {
        Alert.alert('Payout request failed.',data?.data || 'An error occurred.');
      }
    } catch (error) {
      console.error('Error making payout request:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderBankItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.bankItem,
        selectedBankId === item._id && styles.selectedBankItem,
      ]}
      onPress={() => setSelectedBankId(item._id)}
    >
      <Text style={styles.bankName}>{item.bankName}</Text>
      <Text style={styles.accountNumber}>{item.accountNumber}</Text>
    </TouchableOpacity>
  );

  const renderRide = ({ item }) => (
    <TouchableOpacity style={styles.rideItem}>
      <View>
        <Text style={styles.rideTitle}>{item.title}</Text>
        <Text style={styles.rideDate}>{item.date}</Text>
      </View>
      <View style={styles.rideDetails}>
        <Text style={styles.rideAmount}>{item.amount}</Text>
        <Text style={[styles.rideStatus, { color: item.statusColor }]}>{item.status}</Text>
      </View>
    </TouchableOpacity>
  );

  const currentData = weeklyRides[currentWeek] || [];

  return (
    <View style={styles.container}>
      {/* Background Overlay */}
{isBottomSheetVisible && (
  <TouchableOpacity 
    style={styles.overlay} 
    activeOpacity={1} 
    onPress={() => setIsBottomSheetVisible(false)} 
  />
)}

{Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
{Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Header Section */}
      <View style={{ flexDirection: 'row', alignItems: 'center',marginLeft:20 }}>
       
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Image source={icons.BackIcon} style={{ width: 18, height: 18, resizeMode: 'contain', marginRight: 100 }} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Earnings Summary</Text>
      </View>
  
      {/* Earnings Summary */}
      <View style={styles.summary}>
        {/* <Text style={styles.summaryTitle}>Earnings Summary</Text> */}
        {loading ? (
          <ActivityIndicator size="large" color={COLORS.primary} />
        ) : currentData?.length > 0 ? (
          <FlatList
            data={currentData}
            renderItem={renderRide}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
          />
        ) : (
          <View style={styles.noRidesContainer}>
            <Text style={styles.noRidesMessage}>No Earning History</Text>
            <TouchableOpacity style={styles.getPassengerButton}>
              <Text style={styles.getPassengerText}>Get a passenger →</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
  
      {/* Total Earnings */}
      <View style={styles.totalEarningss}>
        <View style={styles.totalEarnings}>
          <Text style={styles.totalEarningsText}>Total Earnings</Text>
          <Text style={styles.totalEarningsAmount}>{currentData?.earnings}</Text>
        </View>
  
        <TouchableOpacity onPress={() => navigation.navigate('PayOutMethods')} style={styles.totalEarnings}>
          <Text style={styles.totalEarningsText}>Add Payment Method</Text>
          <Text style={styles.totalEarningsAmountt}></Text>
        </TouchableOpacity>
  
        {/* Payout History */}
        <TouchableOpacity 
  style={styles.payoutHistory} 
  onPress={() => setIsBottomSheetVisible(true)}
>
  <Text style={styles.payoutHistoryText}>Continue →</Text>
</TouchableOpacity>

      </View>
  
     
{/* Bottom Sheet */}
{isBottomSheetVisible && (
  <View style={styles.bottomSheetContainer}>
    {/* Header */}
    <View style={styles.bottomSheetHeader}>
    <Text></Text>
      <Text style={styles.sectionTitle}>Withdraw Funds</Text>
      <TouchableOpacity onPress={() => setIsBottomSheetVisible(false)}>
        <Text style={styles.closeText}>✕</Text>
      </TouchableOpacity>
    </View>

    {/* Bank Selection */}
    <Text style={styles.sectionTitle}>Select Bank</Text>
    {bankDetails.length > 0 ? (
      <FlatList
        data={bankDetails}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.bankItem, selectedBankId === item._id && styles.selectedBankItem]}
            onPress={() => setSelectedBankId(item._id)}
          >
            <Text style={styles.bankName}>{item.bankName}</Text>
            <Text style={styles.accountNumber}>{item.accountNumber}</Text>
          </TouchableOpacity>
        )}
        keyExtractor={(item) => item._id}
        showsVerticalScrollIndicator={false}
      />
    ) : (
      <Text style={styles.noBanksMessage}>No bank accounts available</Text>
    )}

    {/* Amount Input */}
    <View style={styles.amountInputContainer}>
      <Text style={styles.sectionTitle}>Enter Amount</Text>
      <TextInput
        style={styles.input}
        placeholderTextColor={COLORS.grey}
        placeholder="Enter withdrawal amount"
        keyboardType="numeric"
        value={withdrawAmount}
        onChangeText={setWithdrawAmount}
      />
    </View>

    {/* Withdraw Button */}
    <TouchableOpacity style={styles.payoutButton} onPress={handlePayoutRequest} disabled={submitting}>
      {submitting ? <ActivityIndicator color={COLORS.white} /> : <Text style={styles.payoutButtonText}>Withdraw</Text>}
    </TouchableOpacity>
  </View>
)}

    </View>
  );
  
};

const { width } = Dimensions.get('window');
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent black
    zIndex: 1, // Ensure it appears above the main content
  },
  bottomSheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: COLORS.white,
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
    zIndex: 2, // Ensure it appears above the overlay
  },
  
  
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  
  closeText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  
  bankSelectionContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  
  sectionTitle: {
    ...FONTS.h3,
    color: COLORS.dark_blue,
    marginBottom: 10,
    textAlign: 'center',
  },
  
  bankItem: {
    padding: 7,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: COLORS.grey,
    marginBottom: 10,
    backgroundColor: COLORS.white,
    marginHorizontal: 20,
  },
  
  selectedBankItem: {
    borderColor: COLORS.primary,
    backgroundColor: COLORS.lightGrey,
  },
  
  bankName: {
    fontWeight: 'bold',
    color: COLORS.black,
  },
  
  accountNumber: {
    color: COLORS.grey,
  },
  
  noBanksMessage: {
    textAlign: 'center',
    color: COLORS.grey,
    marginVertical: 10,
  },
  
  amountInputContainer: {
    paddingHorizontal: 20,
    marginTop: 20,
  },
  
  input: {
    borderWidth: 1,
    padding: 12,
    borderRadius: 8,
    borderColor: COLORS.grey,
    backgroundColor: COLORS.white,
    fontSize: 16,
    color: COLORS.black,
  },
  
  payoutButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    marginTop: 20,
    marginHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  
  payoutButtonText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 16,
  },
  
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.grey,
  },
  navigationButton: {
    fontSize: 24,
    color: COLORS.primary,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    ...FONTS.h2,
    fontSize: 16,
    color: COLORS.dark_blue,
  },
  weekText: {
    ...FONTS.body3,
    color: COLORS.black,
    fontSize: 12,
  },
  earningsText: {
    ...FONTS.h1,
    fontWeight: '800',
    color: COLORS.primary,
    marginVertical: 5,
  },
  ridesInfo: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  summary: {
    padding: 20,
  },
  summaryTitle: {
    ...FONTS.h3,
    color: COLORS.dark_blue,
    marginBottom: 10,
    textAlign:'center'
  },
  rideItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
    paddingBottom: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  rideTitle: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.black,
  },
  rideDate: {
    ...FONTS.body4,
    fontSize: 11,
    color: COLORS.grey,
  },
  rideDetails: {
    alignItems: 'flex-end',
  },
  rideAmount: {
    ...FONTS.body3,
    fontSize: 13,
    color: COLORS.dark_blue,
  },
  rideStatus: {
    ...FONTS.body4,
    fontSize: 11,
  },
  noRidesContainer: {
    alignItems: 'center',
    marginVertical: 20,
  },
  noRidesMessage: {
    ...FONTS.h4,
    color: COLORS.black,
    marginBottom: 10,
  },
  getPassengerButton: {
    marginTop: 10,
  },
  getPassengerText: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  totalEarnings: {
    paddingHorizontal: 20,
    paddingVertical: 10,
   
    flexDirection: 'row',
    justifyContent: 'space-between',
    
  },  totalEarningss: {
    backgroundColor: COLORS.white,
    paddingHorizontal: 0,
    width: width,
    paddingVertical: 10,
    borderTopWidth: 0.5,
    borderTopColor: COLORS.grey,
    justifyContent: 'space-between',
    position: 'absolute',
    bottom: 0,
  },
totalEarningsText: {
    ...FONTS.h4,
    color: COLORS.black,
  },
  totalEarningsAmount: {
    ...FONTS.h3,
    fontWeight: '800',
    color: COLORS.primary,
  },
  totalEarningsAmountt: {
    ...FONTS.body5,
    color: COLORS.black,
  },
  payoutHistory: {
    backgroundColor:COLORS.primary,
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginVertical: 5,
    marginHorizontal: 20,
    borderRadius: 10,
    color: COLORS.black,

  },
  payoutHistoryText: {
    ...FONTS.h3,
    color: COLORS.white,
textAlign:'center'
  },
});

export default CashOut;
