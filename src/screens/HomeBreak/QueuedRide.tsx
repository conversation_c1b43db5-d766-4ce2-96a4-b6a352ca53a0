import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import GradientBackground from '../../components/shared/GradientBackground';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import { API_BASE_URL } from '../../config/apiConfig';

const QueuedRide = ({ route }) => {
  const navigation = useNavigation();
      const [loader, setLoader] = useState(false);
  
  const [selectedOption, setSelectedOption] = useState(false); // Default to false (manual accept)

  const handleOptionChange = (option) => {
    setSelectedOption(option);
  };
  const handleContinue = async () => {
    setLoader(true);
    try {
      const response = await fetch(`${API_BASE_URL}/driver/homeBreak`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ autoAcceptRides: selectedOption ? true : false }), // Convert boolean to string
      });
  
      const data = await response.json();
      console.log('Auto Accept Submission Response:', data);
  
     if (response.ok) {
            Toast.show({ type: 'success', text1: 'Ride type updated successfully!' });
            navigation.navigate('HeadToDestination1'); // Navigate back after success
          } else {
            Toast.show({ type: 'error', text1: 'Failed to update ride type' });
          }
        } catch (error) {
          console.error('Error updating ride type:', error);
          Toast.show({ type: 'error', text1: 'Something went wrong' });
        }
        setLoader(false); // Hide loader
  };
  

  return (
    <GradientBackground style={styles.container}>
      {/* Header */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <Spinner visible={loader} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>Queued ride</Text>
      </View>

      {/* Options */}
      <View style={styles.options}>
        {/* Auto-Accept Option (True) */}
        <TouchableOpacity
          style={styles.optionContainer}
          onPress={() => handleOptionChange(true)} // Set to true
        >
          <View style={styles.row}>
            <Text style={styles.optionTitle}>Auto-accept</Text>
            <View
              style={[
                styles.checkbox,
                selectedOption === true && styles.checked,
              ]}
            >
              {selectedOption === true && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </View>
          </View>
          <Text style={styles.optionDescription}>
            Rides are automatically accepted and added to your queue after 30
            seconds. You can still decline manually.
          </Text>
        </TouchableOpacity>

        {/* Manual-Accept Option (False) */}
        <TouchableOpacity
          style={styles.optionContainer}
          onPress={() => handleOptionChange(false)} // Set to false
        >
          <View style={styles.row}>
            <Text style={styles.optionTitle}>Manual-accept</Text>
            <View
              style={[
                styles.checkbox,
                selectedOption === false && styles.checked,
              ]}
            >
              {selectedOption === false && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </View>
          </View>
          <Text style={styles.optionDescription}>
            Rides can be accepted and added to your queue by yourself after 30
            seconds. Rides are automatically declined otherwise.
          </Text>
        </TouchableOpacity>
      </View>

      {/* Continue Button */}
      <TouchableOpacity
  style={styles.continueButton}
  onPress={handleContinue} // Call API on button press
>
  <Text style={styles.continueButtonText}>Continue →</Text>
</TouchableOpacity>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  continueButton: {
    marginTop: 20,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    marginHorizontal: 20,
  },
  continueButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  options: {
    borderRadius: 10,
    padding: 20,
  },
  optionContainer: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.light_grey,
    paddingBottom: 10,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionTitle: {
    ...FONTS.body3,
    fontSize: 16,
    color: COLORS.black,
  },
  checkbox: {
    width: 16,
    height: 16,
    borderWidth: 1,
    borderColor: COLORS.primary,
    backgroundColor: COLORS.white,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    borderColor: COLORS.primary,
  },
  checkmark: {
    color: COLORS.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
  optionDescription: {
    ...FONTS.body4,
    color: COLORS.black,
    marginTop: 10,
    fontSize: 13,
    lineHeight: 18,
  },
});

export default QueuedRide;
