// import React, { useState, useEffect } from 'react';
// import {
//   View,
//   Text,
//   TouchableOpacity,
//   Image,
//   ScrollView,
//   StyleSheet,
// } from 'react-native';
// import MapView, { <PERSON><PERSON>, Circle } from 'react-native-maps';
// import { useNavigation } from '@react-navigation/native';
// import Geolocation from 'react-native-geolocation-service';
// import io from 'socket.io-client';
// import { COLORS, FONTS, images } from '../../constants';

// const Home = () => {
//   const navigation = useNavigation();
//   const [distance, setDistance] = useState(3);
//   const [socket, setSocket] = useState(null);
//   const [isOnline, setIsOnline] = useState(false);
//   const [rideRequests, setRideRequests] = useState([]);

//   const [currentLocation, setCurrentLocation] = useState({
//     latitude: 6.5244,
//     longitude: 3.3792,
//   });

//   const [region, setRegion] = useState({
//     latitude: 6.5244,
//     longitude: 3.3792,
//     latitudeDelta: 0.05,
//     longitudeDelta: 0.05,
//   });

//   useEffect(() => {
//     const newSocket = io("https://inride-server.onrender.com", {
//       transports: ["websocket"],
//       withCredentials: true,
//       reconnection: true,
//     });

//     newSocket.on("connect", () => {
//       newSocket.emit("goOnline");
//       setIsOnline(true);
//     });

//     newSocket.on("newRideRequest", (rideData) => {
//       setRideRequests((prev) => [...prev, rideData]);
//     });

//     newSocket.on("disconnect", () => {
//       setIsOnline(false);
//     });

//     setSocket(newSocket);
//     return () => newSocket.disconnect();
//   }, []);

//   const handleAcceptRide = (ride) => {
//     navigation.navigate("RideDetails", { ride });
//   };

//   return (
//     <View style={{ flex: 1 }}>
//       <MapView
//         style={{ width: '100%', height: '30%' }}
//         region={region}
//         onRegionChangeComplete={setRegion}
//       >
//         <Circle
//           center={{ latitude: region.latitude, longitude: region.longitude }}
//           radius={distance * 200}
//           fillColor="rgba(0, 123, 255, 0.2)"
//           strokeColor="rgba(0, 123, 255, 0.5)"
//         />
//         {currentLocation && (
//           <Marker coordinate={currentLocation} title="You are here">
//             <Image source={images.CarSingle} style={{ width: 24, height: 24 }} />
//           </Marker>
//         )}
//       </MapView>

//       <View style={{ padding: 10 }}>
//         <Text>{isOnline ? "🟢 Online" : "🔴 Offline"}</Text>
//       </View>

//       <ScrollView>
//         {rideRequests.length === 0 ? (
//           <Text>No ride yet.</Text>
//         ) : (
//           rideRequests.map((ride, index) => (
//             <TouchableOpacity key={index} onPress={() => handleAcceptRide(ride)}>
//               <View style={{ padding: 10, borderBottomWidth: 1 }}>
//                 <Text>Pickup: {ride.pickup}</Text>
//                 <Text>Destination: {ride.destination}</Text>
//                 <Text>Price: {ride.price || "Set your price"}</Text>
//               </View>
//             </TouchableOpacity>
//           ))
//         )}
//       </ScrollView>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   offlineButton: {
//     position: 'absolute',
//     bottom: 80,
//     left: '50%',
//     transform: [{ translateX: -50 }],
//     backgroundColor: 'red',
//     paddingVertical: 10,
//     paddingHorizontal: 20,
//     borderRadius: 10,
//     elevation: 5,
//   },
//   offlineText: {
//     color: COLORS.white,
//     fontSize: 16,
//     fontWeight: 'bold',
//   },
//   onlineText: {
//     fontSize: 14,
//     fontWeight: 'bold',
//     color: COLORS.black,
//   },
//   markerText: {
//     fontSize: 14,
//     fontWeight: 'bold',
//     color: COLORS.black,
//   },
//   onlineBadge: {
//     position: 'absolute',
//     top: 50,
//     left: '50%',
//     transform: [{ translateX: -50 }],
//     backgroundColor: 'green',
//     padding: 8,
//     borderRadius: 10,
//   },
//   onlineText: {
//     color: COLORS.white,
//     fontWeight: 'bold',
//   },
//   containerr: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     paddingHorizontal: 10,
//     paddingVertical: 10,
//     backgroundColor: COLORS.white,
//   },
//   filterButton: {
//     width: 50,
//     height: 50,
//     backgroundColor: COLORS.lightGray,
//     borderRadius: 10,
//     alignItems: 'center',
//     justifyContent: 'center',
//   },
//   filterIcon: {
//     width: 60,
//     height: 60,
//     resizeMode: 'contain',
//   },
//   passengerButton: {
//     flex: 1,
//     marginLeft: 10,
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'center',
//     backgroundColor: COLORS.primary,
//     paddingVertical: 13,
//     borderRadius: 10,
//   },
//   passengerButtonText: {
//     color: COLORS.white,
//     fontSize: 16,
//     fontWeight: '600',
//     marginRight: 10,
//   },
//   carIcon: {
//     width: 24,
//     height: 24,
//     resizeMode: 'contain',
//   },
//   container: {
//     flex: 1,
//   },
//   map: {
//     width: '100%',
//     height: '30%',
//   },
//   budgetBadge: {
//     position: 'absolute',
//     top: 10,
//     left: 10,
//     backgroundColor: COLORS.primary,
//     padding: 10,
//     borderRadius: 20,
//     flexDirection: 'row',
//     alignItems: 'center',
//   },
//   budgetText: {
//     color: COLORS.white,
//     ...FONTS.h4,
//   },
//   budgetAmount: {
//     color: COLORS.white,
//     ...FONTS.h4,
//     marginLeft: 5,
//   },

//   distanceContainer: {
//     position: 'absolute',
//     top: '60%',
//     left: 20,
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: COLORS.white,
//     borderRadius: 15,
//     elevation: 5,
//   },
//   distanceButton: {
//     padding: 10,
//     // backgroundColor: COLORS.grey,
//     borderRadius: 5,
//   },
//   distanceText: {
//     ...FONTS.body3,
//   },
//   distanceValue: {
//     ...FONTS.h4,
//     marginHorizontal: 10,
//     color: COLORS.black,
//   },
//   infoSection: {
//     padding: 20,
//     backgroundColor: COLORS.white,
//     borderTopLeftRadius: 30,
//     borderTopRightRadius: 30,
//     marginTop: -30,
//   },
//   infoText: {
//    ...FONTS.body3,
//     marginBottom: 5,
//   },
//   infoSubText: {
//    ...FONTS.body4,
//     color: COLORS.black,
//     marginBottom: 20,
//   },
//   upcomingRideContainer: {
//     padding: 10,
//     backgroundColor: COLORS.white,
//     borderRadius: 7,
//     borderWidth:0.5,
//     borderColor:COLORS.light_grey,
//     marginBottom: 20,
//   },
//   upcomingRideTitle: {
//     ...FONTS.body4,

//   },
//   upcomingRideSubtitle: {
//     marginTop:4,
//     ...FONTS.body5,
//     color: COLORS.grey,
//   },
//   getPassengerButton: {
//     backgroundColor: COLORS.primary,
//     padding: 15,
//     borderRadius: 10,
//     alignItems: 'center',
//   },
//   getPassengerText: {
//     color: COLORS.white,
//    ...FONTS.h3
//   },
//   footer: {
//     flexDirection: 'row',
//     justifyContent: 'space-around',
//     paddingVertical: 15,
//     backgroundColor: COLORS.white,
//   },
//   footerItem: {
//     alignItems: 'center',
//   },
//   footerIcon: {
//     width: 25,
//     height: 25,
//     marginBottom: 5,
//   },
//   footerText: {
//     fontSize: 12,
//     color: COLORS.gray,
//   },
// });

// export default Home;

import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  Platform,
  StatusBar,
} from 'react-native';
import MapView, {Marker, Circle, PROVIDER_GOOGLE, PROVIDER_DEFAULT} from 'react-native-maps';
import {useNavigation} from '@react-navigation/native';
import Geolocation from 'react-native-geolocation-service';
import io from 'socket.io-client';
import {COLORS, images} from '../../constants'; // Adjust asset imports
import {INRIDE_URL} from '../../config/apiConfig';
import {useSelector} from 'react-redux';
import socketService from '../../services/socketService';
import locationService from '../../services/locationService';

// Define navigation type
type NavigationProp = {
  navigate: (screen: string, params?: any) => void;
};

// Define Redux state type
interface StoreState {
  store: {
    user: any;
  };
}

const {width} = Dimensions.get('window');

const Home = () => {
  const navigation = useNavigation<NavigationProp>();
  const [distance, setDistance] = useState(3);
  const [socket, setSocket] = useState<any>(null);
  const [isOnline, setIsOnline] = useState(false); // Track driver online status
  const user = useSelector((state: StoreState) => state?.store?.user) || [];
  console.log(user);

  // Initialize region with driver's current location
  const [currentLocation, setCurrentLocation] = useState({
    latitude: 6.5244, // Default: Lagos, Nigeria (if GPS unavailable)
    longitude: 3.3792,
  });

  const [region, setRegion] = useState({
    latitude: 6.5244,
    longitude: 3.3792,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });

  // Sample car locations
  const cars = [
    {id: 1, latitude: 6.5206, longitude: 3.3724},
    {id: 2, latitude: 6.5286, longitude: 3.3824},
    {id: 3, latitude: 6.5306, longitude: 3.3894},
  ];

  /** Initialize Location Service and Start Tracking */
  const initializeLocationTracking = async () => {
    console.log('🗺️ [Home] Initializing location tracking...');

    try {
      // Initialize location service
      const initialized = await locationService.initialize();
      if (!initialized) {
        console.warn('⚠️ [Home] Location service initialization failed');
        return;
      }

      // Start continuous location tracking
      await locationService.startTracking();

      // Add location listener to update map and car position
      const locationListener = (location) => {
        console.log('📍 [Home] Location updated - moving car icon:', location);

        // Convert standardized format to map format
        const [longitude, latitude] = location.coordinates;
        const mapLocation = { latitude, longitude };

        console.log('🚗 [Home] Updating car position to:', mapLocation);
        setCurrentLocation(mapLocation);

        // Smoothly update map region to follow the car
        setRegion(prev => ({
          ...prev,
          latitude,
          longitude,
        }));
      };

      locationService.addLocationListener(locationListener);

      // Get initial location for map
      const currentLoc = locationService.getLastKnownLocation();
      if (currentLoc) {
        const [longitude, latitude] = currentLoc.coordinates;
        const mapLocation = { latitude, longitude };
        setCurrentLocation(mapLocation);
        setRegion(prev => ({
          ...prev,
          latitude,
          longitude,
        }));
      }

      return locationListener;
    } catch (error) {
      console.error('❌ [Home] Error initializing location tracking:', error);
    }
  };

  /** Initialize Socket Connection and Location Tracking */
  useEffect(() => {
    let locationListener = null;

    const initializeHomeScreen = async () => {
      // Use the existing socket from socketService instead of creating a new one
      const existingSocket = socketService.socket;

      if (existingSocket) {
        console.log('🔗 [Home] Using existing socket connection');
        setSocket(existingSocket);

        // Listen for connection status
        existingSocket.on('connect', () => {
          console.log('✅ [Home] Socket connected');
          setIsOnline(true);
        });

        existingSocket.on('disconnect', reason => {
          console.warn('⚠️ [Home] Socket disconnected:', reason);
          setIsOnline(false);
        });

        existingSocket.on('statusUpdated', response => {
          console.log('✅ [Home] Driver status updated:', response);
          setIsOnline(true);
        });

        existingSocket.on('locationUpdated', data => {
          console.log('✅ [Home] Server confirmed location update:', data);
        });

        // Check if already connected
        if (existingSocket.connected) {
          setIsOnline(true);
        }
      } else {
        console.warn('⚠️ [Home] No existing socket found, socket service may not be initialized');
      }

      // Initialize location tracking
      try {
        locationListener = await initializeLocationTracking();
        console.log('✅ [Home] Location tracking initialized with listener');
      } catch (error) {
        console.error('❌ [Home] Failed to initialize location tracking:', error);
      }
    };

    initializeHomeScreen();

    // Cleanup function
    return () => {
      const existingSocket = socketService.socket;
      if (existingSocket) {
        existingSocket.off('connect');
        existingSocket.off('disconnect');
        existingSocket.off('statusUpdated');
        existingSocket.off('locationUpdated');
      }

      // Remove location listener
      if (locationListener) {
        locationService.removeLocationListener(locationListener);
      }
    };
  }, []);

  /** Manually Go Online Function */
  const goOnline = async () => {
    console.log('🟢 [Home] Manually going online...');

    try {
      // Use the enhanced socketService goOnline method that includes location
      await socketService.goOnline();
      setIsOnline(true);

      // Ensure location tracking is active
      if (!locationService.isTracking) {
        await locationService.startTracking();
      }
    } catch (error) {
      console.error('❌ [Home] Error going online:', error);
    }
  };

  /** Auto Go Online When Socket Connects */
  useEffect(() => {
    if (socket && socket.connected && !isOnline) {
      console.log('🔄 [Home] Socket connected, automatically going online...');
      goOnline();
    }
  }, [socket]);

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <StatusBar
        barStyle="dark-content"
        backgroundColor="transparent"
        translucent={true}
      />

      <Text style={styles.welcomeText}>
        Welcome, {user?.firstName} {user?.lastName}
      </Text>
      {/* Map Section */}
      <MapView
        style={styles.map}
        region={region}
        onRegionChangeComplete={reg => setRegion(reg)}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsCompass={false}
        showsScale={false}
        showsBuildings={Platform.OS === 'android'}
        showsTraffic={false}
        showsIndoors={false}
        mapType="standard"
        provider={Platform.OS === 'ios' ? PROVIDER_DEFAULT : PROVIDER_GOOGLE}
        showsPointsOfInterest={false}
        toolbarEnabled={false}
        loadingEnabled={true}
        loadingIndicatorColor="#007AFF"
        loadingBackgroundColor="#FFFFFF"
        onMapReady={() => {
          console.log('Map is ready');
          // Force a small region change to trigger map rendering on iOS
          if (Platform.OS === 'ios') {
            setTimeout(() => {
              setRegion(prev => ({
                ...prev,
                latitude: prev.latitude + 0.0001,
                longitude: prev.longitude + 0.0001,
              }));
            }, 100);
          }
        }}>
        {/* Circle indicating range */}
        <Circle
          center={{latitude: region.latitude, longitude: region.longitude}}
          radius={distance * 200}
          fillColor="rgba(0, 123, 255, 0.2)"
          strokeColor="rgba(0, 123, 255, 0.5)"
        />

        {/* Driver Marker - Live Position */}
        {currentLocation && (
          <Marker
            coordinate={currentLocation}
            title="Your Location (Live)"
            description="Driver position updates every 5 seconds"
            anchor={{x: 0.5, y: 0.5}}
            flat={true}
          >
            <View style={styles.driverMarkerContainer}>
              <View style={styles.driverMarkerPulse} />
              <Image source={images.CarSingle} style={styles.carIcon as any} />
            </View>
          </Marker>
        )}

        {/* Car Markers */}
        {cars.map(car => (
          <Marker
            key={car.id}
            coordinate={{latitude: car.latitude, longitude: car.longitude}}>
            <Image source={images.CarSingle} style={styles.carIcon as any} />
          </Marker>
        ))}
      </MapView>

      {/* Top Budget Badge */}
      {/* <View style={styles.budgetBadge}>
        <Text style={styles.budgetText}>$</Text>
        <Text style={styles.budgetAmount}>5000</Text>



      </View> */}
      {/* Display Online Status (Always Visible) */}
      <View style={styles.onlineBadge}>
        <Text style={styles.onlineText}>
          {isOnline ? '🟢 You are online' : '🔴 Offline'}
        </Text>
        {!isOnline && (
          <TouchableOpacity style={styles.goOnlineButton} onPress={goOnline}>
            <Text style={styles.goOnlineText}>Go Online</Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Distance Selector */}
      <View style={styles.distanceContainer}>
        <TouchableOpacity
          style={styles.distanceButton}
          onPress={() => setDistance(Math.max(1, distance - 1))}>
          <Text style={styles.distanceText}>-</Text>
        </TouchableOpacity>
        <Text style={styles.distanceValue}>{distance} km</Text>
        <TouchableOpacity
          style={styles.distanceButton}
          onPress={() => setDistance(distance + 1)}>
          <Text style={styles.distanceText}>+</Text>
        </TouchableOpacity>
      </View>

      {/* Ride Info Section */}
      <ScrollView style={styles.infoSection}>
        <Text style={styles.infoText}>3 to 6 km wait in your area</Text>
        <Text style={styles.infoSubText}>
          Average wait for 23 rides over the last 30 minutes
        </Text>

        <View style={styles.upcomingRideContainer}>
          <Text style={styles.upcomingRideTitle}>No upcoming ride</Text>
          <Text style={styles.upcomingRideSubtitle}>Upcoming ride</Text>
        </View>

        {/* Get Passenger Button */}
        <View style={styles.containerr}>
          {/* Filter Button */}
          <TouchableOpacity
            style={styles.filterButton}
            // onPress={() => navigation.navigate('StayInArea')}
            onPress={() => navigation.navigate('HeadToDestination1')}>
            <Image
              source={images.FilterIcon}
              style={styles.filterIcon as any}
            />
          </TouchableOpacity>

          {/* Get a Passenger Button */}
          <TouchableOpacity
            style={styles.passengerButton}
            onPress={() => navigation.navigate('TakeABreak')}>
            <Text style={styles.passengerButtonText}>Get a passenger</Text>
            <Image source={images.CarIcon} style={styles.carIcon as any} />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  offlineButton: {
    position: 'absolute',
    bottom: 80,
    left: '50%',
    transform: [{translateX: -50}],
    backgroundColor: 'red',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
    elevation: 5,
  },
  offlineText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  markerText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.black,
  },
  onlineBadge: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 50, 
    // left: '50%',
    alignSelf: "center",
    // transform: [{ translateX: -50 }],
    backgroundColor: 'green',
    padding: 8,
    borderRadius: 10,
    elevation: 5,
    zIndex: 1,
  },
  onlineText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  goOnlineButton: {
    marginTop: 5,
    backgroundColor: COLORS.primary,
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 5,
  },
  goOnlineText: {
    color: COLORS.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  containerr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  filterButton: {
    width: 50,
    height: 50,
    backgroundColor: COLORS.light_grey,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterIcon: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
  passengerButton: {
    flex: 1,
    marginLeft: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: 13,
    borderRadius: 10,
  },
  passengerButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
  },
  carIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  driverMarkerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 40,
    height: 40,
  },
  driverMarkerPulse: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 123, 255, 0.3)',
    borderWidth: 2,
    borderColor: 'rgba(0, 123, 255, 0.6)',
  },
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  welcomeText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.black,
    textAlign: 'center',
    paddingTop: Platform.OS === 'ios' ? 50 : 30,
    paddingBottom: 10,
    backgroundColor: COLORS.white,
  },
  map: {
    width: '100%',
    height: '50%',
    minHeight: 300, // Ensure minimum height for map visibility
    backgroundColor: '#f0f0f0', // Fallback background color
    ...Platform.select({
      ios: {
        overflow: 'hidden',
      },
      android: {
        elevation: 1,
      },
    }),
  },
  budgetBadge: {
    position: 'absolute',
    top: 10,
    left: width / 2 - 50,
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 16,
  },
  budgetAmount: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 16,
    marginLeft: 5,
  },

  distanceContainer: {
    position: 'absolute',
    top: '60%',
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 15,
    elevation: 5,
  },
  distanceButton: {
    padding: 10,
    // backgroundColor: COLORS.grey,
    borderRadius: 5,
  },
  distanceText: {
    fontSize: 14,
    fontWeight: 'normal',
  },
  distanceValue: {
    fontSize: 16,
    fontWeight: 'bold',
    marginHorizontal: 10,
    color: COLORS.black,
  },
  infoSection: {
    padding: 20,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -30,
  },
  infoText: {
    fontSize: 14,
    fontWeight: 'normal',
    marginBottom: 5,
  },
  infoSubText: {
    fontSize: 12,
    fontWeight: 'normal',
    color: COLORS.black,
    marginBottom: 20,
  },
  upcomingRideContainer: {
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: 7,
    borderWidth: 0.5,
    borderColor: COLORS.light_grey,
    marginBottom: 20,
  },
  upcomingRideTitle: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  upcomingRideSubtitle: {
    marginTop: 4,
    fontSize: 12,
    fontWeight: 'normal',
    color: COLORS.grey,
  },
  getPassengerButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  getPassengerText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 15,
    backgroundColor: COLORS.white,
  },
  footerItem: {
    alignItems: 'center',
  },
  footerIcon: {
    width: 25,
    height: 25,
    marginBottom: 5,
  },
  footerText: {
    fontSize: 12,
    color: COLORS.grey,
  },
});

export default Home;
