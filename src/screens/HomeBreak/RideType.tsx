import React, {useState} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import {COLORS, FONTS, icons} from '../../constants';
import {useNavigation} from '@react-navigation/native';
import GradientBackground from '../../components/shared/GradientBackground';
import {HomeBreak} from '../../../redux/api';
import Toast from 'react-native-toast-message';
import Spinner from 'react-native-loading-spinner-overlay';
import {API_BASE_URL} from '../../config/apiConfig';

const RideType = ({route}) => {
  const navigation = useNavigation();
  const [loader, setLoader] = useState(false);

  const [selectedOptions, setSelectedOptions] = useState({
    personal: false,
    group: false,
    delivery: false,
    reservation: false,
    all: false,
  });

  const handleSubmit = async () => {
    setLoader(true); // Show loader
    try {
      // Find the selected ride type
      const selectedRideType = Object.keys(selectedOptions).find(
        key => selectedOptions[key],
      );
      if (!selectedRideType) {
        Toast.show({type: 'error', text1: 'Please select a ride type'});
        setLoader(false);
        return;
      }
      console.log('Selected Ride Type:', selectedRideType);
      const response = await fetch(`${API_BASE_URL}/driver/homeBreak`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({rideType: selectedRideType}),
      });

      const data = await response.json();
      console.log('Ride Type Submission Response:', data);
      if (response.ok) {
        Toast.show({type: 'success', text1: 'Ride type updated successfully!'});
        navigation.navigate('HeadToDestination1'); // Navigate back after success
      } else {
        Toast.show({type: 'error', text1: 'Failed to update ride type'});
      }
    } catch (error) {
      console.error('Error updating ride type:', error);
      Toast.show({type: 'error', text1: 'Something went wrong'});
    }
    setLoader(false); // Hide loader
  };

  // const handleOptionChange = (key) => {
  //   if (key === 'all') {
  //     const newState = !selectedOptions.all;
  //     setSelectedOptions({
  //       normal: newState,
  //       group: newState,
  //       delivery: newState,
  //       reservation: newState,
  //       all: newState,
  //     });
  //   } else {
  //     setSelectedOptions((prevState) => ({
  //       ...prevState,
  //       [key]: !prevState[key],
  //       all: false, // If one of the individual options is toggled, disable "All"
  //     }));
  //   }
  // };
  const handleOptionChange = key => {
    setSelectedOptions({
      personal: key === 'personal',
      group: key === 'group',
      delivery: key === 'delivery',
      reservation: key === 'reservation',
      all: key === 'all',
    });
  };

  return (
    <GradientBackground style={styles.container}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <Spinner visible={loader} />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}> Ride type</Text>
      </View>

      {/* Ride Options */}
      <View style={styles.options}>
        {Object.keys(selectedOptions).map((key, index) => (
          <TouchableOpacity
            key={index}
            style={styles.optionContainer}
            onPress={() => handleOptionChange(key)}>
            <Text style={styles.optionTitle}>
              {key.charAt(0).toUpperCase() + key.slice(1).replace('-', ' ')}
            </Text>
            <View
              style={[styles.checkbox, selectedOptions[key] && styles.checked]}>
              {selectedOptions[key] && (
                <View style={styles.checkboxMark}>
                  <Text style={styles.checkText}>✓</Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        ))}

        {/* Continue Button */}
        <TouchableOpacity
          style={[
            styles.continueButton,
            {
              backgroundColor: Object.values(selectedOptions).some(val => val)
                ? COLORS.primary
                : COLORS.grey,
            },
          ]}
          disabled={!Object.values(selectedOptions).some(val => val)}
          onPress={handleSubmit}>
          <Text style={styles.continueButtonText}>Continue →</Text>
        </TouchableOpacity>
      </View>
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  continueButton: {
    marginTop: 20,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  continueButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
  },

  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  options: {
    borderRadius: 10,
    padding: 20,
  },
  optionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  optionTitle: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  checkbox: {
    width: 18,
    height: 18,
    borderWidth: 1,
    borderColor: COLORS.primary,
    borderRadius: 5,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checked: {
    borderColor: COLORS.primary,
  },
  checkboxMark: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkText: {
    color: COLORS.primary,
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default RideType;
