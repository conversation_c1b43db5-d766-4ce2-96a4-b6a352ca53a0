import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  Platform,
} from 'react-native';
import GradientBackground from '../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const HeadToDestination2 = () => {
  const navigation = useNavigation();

  // Data for locations
  const [locations, setLocations] = useState([
    { id: '1', title: 'AMLI 7th Street Station', address: '2601 West 7th St. Fort Worth, Texas 7' },
    { id: '2', title: 'Victory Park', address: '2500 Victory Ave, Dallas, TX 75219' },
    { id: '3', title: 'Deep Ellum', address: '2901 Commerce St, Dallas, TX 75226' },
    { id: '4', title: 'Downtown Dallas', address: '500 Elm St, Dallas, TX 75202' },
  ]);

  const [recentSearches, setRecentSearches] = useState([
    { id: '5', title: 'AMLI 7th Street Station', address: '2601 West 7th St. Fort Worth, Texas 7' },
    { id: '6', title: 'Uptown Dallas', address: '3224 Knox St, Dallas, TX 75205' },
  ]);

  const [searchQuery, setSearchQuery] = useState('');

  // Filtered results based on search query
  const filteredLocations = locations.filter((location) =>
    location.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectLocation = (location) => {
    // Save recent searches and navigate
    setRecentSearches((prev) => [location, ...prev]);
    navigation.navigate('DestinationDetails', { location });
  };

  return (
    <GradientBackground>
      {/* Header Section */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>Head to destination</Text>
      </View>

      {/* Search Section */}
      <View style={styles.searchContainer}>
        <Image source={icons.SearchIcon} style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Search location"
          placeholderTextColor={COLORS.grey}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Select Location via Map */}
      <TouchableOpacity
        style={styles.mapButton}
        onPress={() => navigation.navigate('MapSelect')}
      >
        <Image source={icons.MapPinIcon} style={styles.rowIcon} />
        <Text style={styles.rowText}>Select Location via Map</Text>
        <Image source={icons.ArrowRightIcon} style={[styles.arrowIcon,{marginLeft:30}]} />
      </TouchableOpacity>

      {/* Close by Pickups Section */}
      <Text style={styles.sectionTitle}>Close by Pickups</Text>
      <FlatList
        data={filteredLocations}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.locationRow}
            onPress={() => handleSelectLocation(item)}
          >
            <Image source={icons.MapPinIcon} style={styles.rowIcon} />
            <View style={styles.locationDetails}>
              <Text style={styles.locationTitle}>{item.title}</Text>
              <Text style={styles.locationAddress}>{item.address}</Text>
            </View>
            <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
          </TouchableOpacity>
        )}
      />

      {/* Recent Searches Section */}
      <Text style={styles.sectionTitle}>Recent Search</Text>
      <FlatList
        data={recentSearches}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.locationRow}
            onPress={() => handleSelectLocation(item)}
          >
            <Image source={icons.LocationIcon} style={styles.rowIcon} />
            <View style={styles.locationDetails}>
              <Text style={styles.locationTitle}>{item.title}</Text>
              <Text style={styles.locationAddress}>{item.address}</Text>
            </View>
            <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
          </TouchableOpacity>
        )}
      />
    </GradientBackground>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginHorizontal: 20,
    marginVertical: 15,
  },
  searchIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
    // tintColor: COLORS.gray,
  },
  rowText:{
marginLeft:10
  },
  searchInput: {
    flex: 1,
    ...FONTS.body3,
    color: COLORS.black,
    backgroundColor: COLORS.white,
    padding:10

  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // justifyContent: 'space-between',
    padding: 15,
    marginHorizontal: 20,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    // elevation: 2,
  },
  sectionTitle: {
    ...FONTS.h4,
    color: COLORS.primary,
    marginHorizontal: 20,
    marginBottom: 0,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    marginHorizontal: 20,
    // backgroundColor: COLORS.white,
    borderRadius: 10,
    marginBottom: 5,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderBottomWidth:0.5,
    borderBottomColor:COLORS.light_grey,
    // elevation: 2, 
  },
  locationDetails: {
    flex: 1,
    marginHorizontal: 15,
  },
  locationTitle: {
    ...FONTS.body3,
    fontSize:14,

    color: COLORS.black,
  },
  locationAddress: {
    ...FONTS.body1,
    fontSize:13,
    marginTop:2,
    color: COLORS.grey,
  },
  rowIcon: {
    width: 24,
    height: 24,
  },
  arrowIcon: {
    width: 16,
    height: 16,
  },
});

export default HeadToDestination2;
