 
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Image,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import MapView, { Circle, Marker } from 'react-native-maps';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import Slider from '@react-native-community/slider';
import Geolocation from 'react-native-geolocation-service';
import Spinner from 'react-native-loading-spinner-overlay';
import Toast from 'react-native-toast-message';


const StayInArea = () => {
  const navigation = useNavigation();
  const [radius, setRadius] = useState(300000); // Initial radius in meters (30km)
  // const [loading, setLoader] = useState(false);
      const [loader, setLoader] = useState(false);
  
  const [currentLocation, setCurrentLocation] = useState({
    latitude: 6.5244, // Default: Lagos, Nigeria (fallback)
    longitude: 3.3792,
  });
 
  // Map region
  const [region, setRegion] = useState({
    latitude: currentLocation.latitude,
    longitude: currentLocation.longitude,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });
  

  const handleContinue = async () => {
    setLoader(true);
    try {
      const kmRange = Math.round(radius / 1); // Convert radius (meters) to km as a number
  console.log('Km Range:', kmRange);
  // setLoader(false);
  // return
      const response = await fetch(`${API_BASE_URL}/driver/homeBreak`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ kmRange }), // Send kmRange as a number
      });
  
      const data = await response.json();
      console.log('Home Break Response:', data);
  
       if (response.ok) {
             Toast.show({ type: 'success', text1: 'Ride type updated successfully!' });
             navigation.goBack(); // Navigate back after success
           } else {
             Toast.show({ type: 'error', text1: 'Failed to update ride type' });
           }
         } catch (error) {
           console.error('Error updating ride type:', error);
           Toast.show({ type: 'error', text1: 'Something went wrong' });
         }
         setLoader(false); // Hide loader
  };
  

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
   <Spinner visible={loader} />
      {/* Header Section */}
      <View style={styles.header}>
      <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Image source={icons.BackIcon} style={styles.icon} />
        </TouchableOpacity>
        <Text style={styles.title}>                   Stay in area</Text>
      </View>

      {/* Map Section */}
      <MapView
        style={styles.map}
        region={{
          latitude: currentLocation.latitude,
          longitude: currentLocation.longitude,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}
        onRegionChangeComplete={(newRegion) => setRegion(newRegion)}
      >
        {/* Circle showing the search area */}
        <Circle
          center={{ latitude: region.latitude, longitude: region.longitude }}
          radius={radius} // Circle radius in meters
          strokeColor={COLORS.primary}
          fillColor="rgba(0, 122, 255, 0.2)"
        />
        {/* Marker in the center */}
        <Marker
          coordinate={{ latitude: region.latitude, longitude: region.longitude }}
          draggable
          onDragEnd={(e) => {
            const { latitude, longitude } = e.nativeEvent.coordinate;
            setRegion({ ...region, latitude, longitude });
          }}
        />
      </MapView>

      {/* Details Section */}
      <View style={styles.details}>
        <Text style={styles.titlee}>Stay in area</Text>
        <Text style={styles.subtitle}>
          We’ll search for rides within this location, you can widen the area to
          get more potential rides.
        </Text>

        {/* Slider for Radius Selection */}
        <View style={styles.sliderContainer}>
          <Text style={styles.sliderText}>{Math.round(radius / 1000)}km</Text>
          <Slider
            style={styles.slider}
            minimumValue={100} // 10km
            maximumValue={10000} // 100km
            step={10000} // 1km
            value={radius}
            onValueChange={(value) => setRadius(value)}
            minimumTrackTintColor={COLORS.primary}
            maximumTrackTintColor={COLORS.grey}
          
          /> 
          <View style={styles.sliderLabels}>
            <Text style={styles.label}>1km</Text>
            <Text style={styles.label}>10000km</Text>
          </View>
        </View>

        {/* Continue Button */}
        <TouchableOpacity style={styles.continueButton} onPress={handleContinue}>
          <Text style={styles.continueButtonText}>Submit</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    // padding: 20,
    paddingHorizontal: 20,
    // backgroundColor: COLORS.white,
  },
  backButton: {
    padding: 10,
  },
  backText: {
    fontSize: 15,
    color: COLORS.primary,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 20,
    color: COLORS.black,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  details: {
    padding: 20,
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    flex: 1,
  },
  subtitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 20,
    textAlign: 'left',
    fontSize: 13,
  },

  titlee: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 20,
    textAlign: 'left',
    fontSize: 16,
  },
  sliderContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  slider: {
    width: '100%',
    height: 80,
  },
  sliderLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  label: {
    ...FONTS.body4,
    color: COLORS.darkGray,
  },
  sliderText: {
    ...FONTS.h3,
    fontSize: 14,
    color: COLORS.primary,
    marginBottom: 10,
    backgroundColor: COLORS.white,
    padding:5,
    borderRadius:10
  },
  continueButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  continueButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
});

export default StayInArea;
