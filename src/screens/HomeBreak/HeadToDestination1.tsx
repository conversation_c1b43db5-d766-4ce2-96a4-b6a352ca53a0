import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
  Platform
} from 'react-native';
import GradientBackground from '../../components/shared/GradientBackground';
import { COLORS, FONTS, icons } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const HeadToDestination1 = ({ route }) => {
  const navigation = useNavigation();

  // State for tracking selected values
  const [selectedValues, setSelectedValues] = useState({
    kmRange: ""
  });
   
  
  // Handle form submission
  const handleSubmit = () => {
    if (!isComplete) {
      Alert.alert("Incomplete", "Please fill in all details before submitting.");
      return;
    }
  
    const data = {
      
      kmRange: selectedValues.kmRange,
    };
  
    console.log("Submitting data:", data);
 
  };

  return (
    <GradientBackground>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
  {/* Header Section */}
  <View style={styles.header}>
    <TouchableOpacity
      style={styles.backButton}
      onPress={() => navigation.goBack()}
    >
      <Image source={icons.BackIcon} style={styles.icon} />
    </TouchableOpacity>
    <Text style={styles.title}>Head to destination</Text>
  </View>

  {/* Content Section */}
  <View style={styles.container}>
    {/* Location Section */}
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Location</Text>
       

      <TouchableOpacity
        style={styles.row}
        onPress={() => navigation.navigate('StayInArea')}
      >
        <View style={styles.rowContent}>
          <Image source={icons.DestinationIcon} style={styles.rowIcon} />
          <Text style={styles.rowText}>Stay in area</Text>
        </View>
        <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
      </TouchableOpacity>
    </View>

    {/* Ride Section */}
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>Ride</Text>
      <TouchableOpacity
        style={styles.row}
        onPress={() => navigation.navigate('RideType')}
      >
        <View style={styles.rowContent}>
          <Image source={icons.CarIcon} style={styles.rowIcon} />
          <View>
            <Text style={styles.rowText}>
              {selectedValues.rideType ? `Ride Type: ${selectedValues.rideType}` : "Choose Ride Type"}
            </Text>
            <Text style={styles.rowSubText}>
              Choose the type of ride you will like
            </Text>
          </View>
        </View>
        <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.row}
        onPress={() => navigation.navigate('QueuedRide')}
      >
        <View style={styles.rowContent}>
          <Image source={icons.QueueIcon} style={styles.rowIcon} />
          <View>
            <Text style={styles.rowText}>
              {selectedValues.autoAcceptRides ? `Auto Accept: ${selectedValues.autoAcceptRides}` : "Choose Auto Accept"}
            </Text>
            <Text style={styles.rowSubText}>
              Choose how to accept new ride while...
            </Text>
          </View>
        </View>
        <Image source={icons.ArrowRightIcon} style={styles.arrowIcon} />
      </TouchableOpacity>
    </View>
  </View>

 
</GradientBackground>

  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.light_blue,
  },
  backButton: {
    padding: 10,
  },
  icon: {
    width: 18,
    height: 18,
    resizeMode: 'contain',
  },
  title: {
    ...FONTS.body3,
    marginLeft: 70,
  },
  container: {
    padding: 20,
  },
  rowSubText: {
    ...FONTS.body6,
    fontSize:12,
    marginVertical:5,
    color: COLORS.grey,

  },
  section: {
    backgroundColor: COLORS.white,
    borderRadius: 8,
    padding: 15,
    marginBottom: 20,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 2 },
  },
  sectionTitle: {
    ...FONTS.h4,
    lineHeight: 20,
    paddingBottom: 10,
    borderBottomWidth: 0.3,
    borderBottomColor: COLORS.light_grey,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.3,
    borderBottomColor: COLORS.light_grey,
  },
  rowContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowIcon: {
    width: 20,
    height: 20,
    marginRight: 15,
    tintColor: COLORS.black,
  },
  rowText: {
    ...FONTS.body5,
    color: COLORS.black,
  },
  arrowIcon: {
    width: 16,
    height: 16,
    tintColor: COLORS.black,
  },
  submitButton: {
    margin: 20,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  submitButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
});

export default HeadToDestination1;
