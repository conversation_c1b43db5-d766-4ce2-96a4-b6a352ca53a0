import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
  Alert,
  ActivityIndicator,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import { COLORS, FONTS, images, icons } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';

const SetRate = () => {
  const route = useRoute();
  const ride = route.params || '';
  const navigation = useNavigation();

  // State for map region and coordinates
  const [region, setRegion] = useState({
    latitude: 0,
    longitude: 0,
    latitudeDelta: 10,
    longitudeDelta: 10,
  });

  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const geocodeLocations = async () => {
      try {
        setIsLoading(true);

        if (!ride?.ride?.from || !ride?.ride?.to?.[0]?.place) {
          throw new Error('Missing location information');
        }

        // Get coordinates for pickup location
        const pickupResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(ride?.ride?.from)}&format=json&limit=1`);

        // Check if pickup response is JSON
        const pickupContentType = pickupResponse.headers.get('content-type');
        const isPickupJson = pickupContentType && pickupContentType.includes('application/json');

        if (!isPickupJson) {
          const textResponse = await pickupResponse.text();
          console.error('Non-JSON response from pickup geocoding:', textResponse);
          throw new Error('Geocoding service returned invalid response for pickup location');
        }

        const pickupData = await pickupResponse.json();

        // Get coordinates for dropoff location
        const dropoffResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(ride?.ride?.to?.[0].place)}&format=json&limit=1`);

        // Check if dropoff response is JSON
        const dropoffContentType = dropoffResponse.headers.get('content-type');
        const isDropoffJson = dropoffContentType && dropoffContentType.includes('application/json');

        if (!isDropoffJson) {
          const textResponse = await dropoffResponse.text();
          console.error('Non-JSON response from dropoff geocoding:', textResponse);
          throw new Error('Geocoding service returned invalid response for dropoff location');
        }

        const dropoffData = await dropoffResponse.json();

        if (pickupData.length > 0 && dropoffData.length > 0) {
          const pickupCoords = {
            latitude: parseFloat(pickupData[0].lat),
            longitude: parseFloat(pickupData[0].lon)
          };

          const dropoffCoords = {
            latitude: parseFloat(dropoffData[0].lat),
            longitude: parseFloat(dropoffData[0].lon)
          };

          // Create midpoint between pickup and dropoff
          const midpointCoords = {
            latitude: (pickupCoords.latitude + dropoffCoords.latitude) / 2,
            longitude: (pickupCoords.longitude + dropoffCoords.longitude) / 2
          };

          // Set route coordinates
          setRouteCoordinates([pickupCoords, midpointCoords, dropoffCoords]);

          // Calculate proper delta for the region to ensure both points are visible
          const latDelta = Math.abs(pickupCoords.latitude - dropoffCoords.latitude) * 1.5;
          const lngDelta = Math.abs(pickupCoords.longitude - dropoffCoords.longitude) * 1.5;

          // Set map region to center on the route
          setRegion({
            latitude: midpointCoords.latitude,
            longitude: midpointCoords.longitude,
            latitudeDelta: Math.max(latDelta, 0.05), // Ensure minimum zoom level
            longitudeDelta: Math.max(lngDelta, 0.05), // Ensure minimum zoom level
          });
        } else {
          throw new Error('Could not find coordinates for one or both locations');
        }
      } catch (error) {
        console.error("Error geocoding locations:", error);
        Alert.alert("Map Error", error.message || "Failed to load map coordinates");

        // Fallback to a default region showing the entire country/area
        setRegion({
          latitude: 0,
          longitude: 0,
          latitudeDelta: 50, // Very zoomed out
          longitudeDelta: 50,
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (ride?.ride?.from && ride?.ride?.to?.[0]?.place) {
      geocodeLocations();
    }
  }, [ride]);

  const handleSetDriveRate = () => {
    navigation.navigate('SetPrice', {ride: ride?.ride});
  };

  // Calculate estimated time based on distance
  const calculateEstimatedTime = (distanceKm) => {
    // Average speed assumption: 60km/h
    const distanceNum = parseFloat(distanceKm);
    if (isNaN(distanceNum)) return 'Unknown';

    const hours = Math.floor(distanceNum / 60);
    const minutes = Math.round((distanceNum / 60 - hours) * 60);

    if (hours > 0) {
      return `${hours} hr${hours > 1 ? 's' : ''} ${minutes > 0 ? `${minutes} min${minutes > 1 ? 's' : ''}` : ''}`;
    }
    return `${minutes} min${minutes > 1 ? 's' : ''}`;
  };

  const distance = ride?.ride?.kmDistance ? `${ride.ride.kmDistance} km` : 'Calculating...';
  const estimatedTime = ride?.ride?.kmDistance ? calculateEstimatedTime(ride.ride.kmDistance) : 'Calculating...';

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}

      {/* Map Section */}
      {isLoading ? (
        <View style={[styles.map, styles.loadingContainer]}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading map...</Text>
        </View>
      ) : (
        <MapView
          style={styles.map}
          region={region}
          showsUserLocation={true}
        >
          {/* Route Line */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor={COLORS.primary}
              strokeWidth={3}
            />
          )}

          {/* Start Marker */}
          {routeCoordinates.length > 0 && (
            <Marker coordinate={routeCoordinates[0]}>
              <View style={styles.marker}>
                <Text style={styles.markerText}>{ride?.ride?.from}</Text>
              </View>
            </Marker>
          )}

          {/* End Marker */}
          {routeCoordinates.length > 0 && (
            <Marker coordinate={routeCoordinates[2]}>
              <View style={styles.marker}>
                <Text style={styles.markerText}>{ride?.ride.to?.[0].place}</Text>
              </View>
            </Marker>
          )}
        </MapView>
      )}

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        {/* Ride Info */}
        <View style={styles.infoHeader}>
          <Image source={images.upArrow} style={styles.navIcon} />
          <View>
            <Text style={styles.arrivalText}>Arriving in {estimatedTime} | {distance}</Text>
            <Text style={styles.carInfo}>
              Toyota Corolla | Black | EPE-546-633
            </Text>
          </View>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>        ✕</Text>
          </TouchableOpacity>
        </View>

        {/* Ride Details */}
        <View style={styles.rideDetails}>
          <Text style={styles.rideDetailsTitle}>Ride details</Text>
          <Text style={styles.rideDescription}>
            Meet {ride?.ride?.passengerName || 'Passenger'} at {ride?.ride?.pickupPoint || 'pickup point'}
          </Text>
          <Text style={[styles.rideDetailsTitle, {fontSize: 10}]}>
            {ride?.ride?.from || 'Origin'} to {ride?.ride?.to?.[0].place || 'Destination'}
          </Text>
        </View>

        {/* Driver Info */}
        <View style={styles.driverInfo}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Image source={images.driver1} style={styles.driverImage} />
            <Text style={styles.driverName}>Olive Rodrigo</Text>
          </View>
          <Text style={styles.driverRating}>4.4 ★ | 53 rides</Text>
        </View>

        {/* Set Drive Rate */}
        <TouchableOpacity
          style={styles.driveRateButton}
          onPress={handleSetDriveRate}
        >
          <Text style={styles.driveRateText}>Set drive rate</Text>
          <Image source={images.CarIcon} style={styles.carIcon} />
        </TouchableOpacity>

        {/* Note */}
        <Text style={styles.note}>
          Rides can be accepted and added to your queue after 30 seconds, rides
          are automatically declined otherwise.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loadingText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
  marker: {
    backgroundColor: COLORS.primary,
    padding: 5,
    borderRadius: 5,
    maxWidth: 150,
  },
  markerText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 10,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginTop: -30,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    borderBottomWidth: 0.5,
    paddingBottom: 20,
    borderColor: COLORS.border,
  },
  navIcon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  arrivalText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginHorizontal: 20,
  },
  carInfo: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginHorizontal: 20,
    fontSize: 10
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  rideDetails: {
    marginBottom: 20,
    borderWidth: 0.5,
    padding: 10,
    borderRadius: 7,
    borderColor: COLORS.border,
  },
  rideDetailsTitle: {
    ...FONTS.body5,
    color: COLORS.black,
  },
  rideDescription: {
    marginVertical: 3,
    ...FONTS.body3,
    color: COLORS.black,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  driverName: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driveRateButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 10,
    marginBottom: 10,
  },
  driveRateText: {
    color: COLORS.white,
    ...FONTS.h4,
    marginRight: 10,
  },
  carIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  note: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
    fontSize: 12,
  },
});

export default SetRate;