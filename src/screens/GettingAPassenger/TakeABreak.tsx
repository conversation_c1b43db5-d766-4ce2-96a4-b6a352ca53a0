

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  PermissionsAndroid,
  Platform,
  StyleSheet,
  Alert,
  Linking,
} from 'react-native';
import MapView, { <PERSON>er, Circle } from 'react-native-maps';
import { useNavigation } from '@react-navigation/native';
import { COLORS, FONTS, images } from '../../constants'; // ✅ Adjust asset imports
import Geolocation from '@react-native-community/geolocation';
import { PERMISSIONS, request, RESULTS } from 'react-native-permissions';
import { get } from 'react-native/Libraries/TurboModule/TurboModuleRegistry';
import socketService from '../../services/socketService';
import locationService from '../../services/locationService';
import { user } from '../../../redux/reducers';

const { width } = Dimensions.get('window');

const TakeABreak = () => {
    const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [0, 0] });
  
  const navigation = useNavigation();
  const [distance, setDistance] = useState(3);
  const [isOnline, setIsOnline] = useState(true); // Default: Driver is online
  const [rideRequests, setRideRequests] = useState([]); // Store ride requests

  useEffect(() => {
    initializeLocationService();
  }, []);

  const initializeLocationService = async () => {
    try {
      console.log('🗺️ [TakeABreak] Initializing location service...');

      // Initialize location service
      const initialized = await locationService.initialize();
      if (!initialized) {
        console.warn('⚠️ [TakeABreak] Location service initialization failed');
        return;
      }

      // Start location tracking
      await locationService.startTracking();

      // Get current location and update state
      const currentLocation = locationService.getLastKnownLocation();
      if (currentLocation) {
        setUserLocation(currentLocation);
        console.log('✅ [TakeABreak] Location initialized:', currentLocation);
      }
    } catch (error) {
      console.error('❌ [TakeABreak] Error initializing location service:', error);
    }
  };

  //   const toggleOnlineStatus = () => {
  //   if (isOnline) {
  //     console.log("🔴 Going Offline...",userLocation);
  //     socketService.emitEvent("goOffline", userLocation);
  //     socketService.disconnect();
  //   } else {
  //     console.log("🟢 Going Online...",userLocation);
  //     socketService.connect();
  //     socketService.emitEvent("goOnline", userLocation);
  //   }
  //   setIsOnline((prevStatus) => !prevStatus);
  // };
  const updateLocation = (locationData) => {
    // Location updates are now handled automatically by locationService
    console.log("📍 [TakeABreak] Location update requested:", locationData);

    // The locationService automatically sends location updates to the server
    // No need to manually emit here as it's handled in the service
  };

  const toggleOnlineStatus = async () => {
    if (isOnline) {
      // Currently online, going offline
      console.log("🔴 [TakeABreak] Going Offline...");

      try {
        // Use socketService goOffline method (keep socket connected)
        await socketService.goOffline();
        setIsOnline(false);
        console.log("✅ [TakeABreak] Driver is now offline");
      } catch (error) {
        console.error("❌ [TakeABreak] Error going offline:", error);
      }

    } else {
      // Currently offline, going online
      console.log("🟢 [TakeABreak] Going Online...");

      try {
        // Connect socket first
        socketService.connect();
        setIsOnline(true);

        // Use socketService goOnline method with location
        setTimeout(async () => {
          await socketService.goOnline();

          // Ensure location tracking is active
          if (!locationService.isTracking) {
            await locationService.startTracking();
          }

          console.log("✅ [TakeABreak] Driver is now online");
        }, 1000);
      } catch (error) {
        console.error("❌ [TakeABreak] Error going online:", error);
      }
    }
  };
  
  const requestLocationPermission = async () => {
    let permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    try {
      const result = await request(permission);
      console.log("🛰️ Permission Result:", result);
      
      if (result === RESULTS.GRANTED) {
        // console.log("✅ Location permission granted");
        return true;
      } else {
        console.log("❌ Location permission denied");
        Alert.alert("Permission Denied", "Please enable location permissions in Settings.");
        return false;
      }
    } catch (error) {
      console.error("⚠️ Permission Error:", error);
      return false;
    }
  };


  const checkLocationServices = async () => {
    return new Promise((resolve) => {
      Geolocation.getCurrentPosition(
        () => resolve(true),  
        (error) => {
          if (error.code === 2) { 
            Alert.alert(
              "Location Services Disabled",
              "Please enable location services in Settings.",
              [{ text: "Go to Settings", onPress: () => Linking.openSettings() }, { text: "Cancel", style: "cancel" }]
            );
          }
          resolve(false);
        },
        { enableHighAccuracy: false, timeout: 5000 }
      );
    });
  };


  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log("⛔ Location permission denied. Exiting function.");
      return null;
    }

    const isLocationEnabled = await checkLocationServices();
    if (!isLocationEnabled) {
      console.log("⛔ Location services disabled. Exiting function.");
      return null;
    }

    console.log("📍 Fetching location...");

    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;

          // const latitude = 4.2649;
          // const longitude = 8.1577;
          const newLocation = { type: 'Point', coordinates: [latitude,longitude] };

          console.log("✅ Location Retrieved:", newLocation);
          setUserLocation(newLocation);
          // resolve(newLocation); // ✅ Return the location
        },
        (error) => {
          // console.error("❌ Location Error:", error);
          // Alert.alert("Location Error", error.message);
          reject(null); // ❌ Return `null` if failed
        },
        {
          enableHighAccuracy: false,
          timeout: 30000,
          maximumAge: 5000,
        }
      );
    });
  };


  // Function to request location permission
// const requestLocationPermission = async () => {
//   let permission = Platform.select({
//     ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
//     android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
//   });

//   try {
//     const result = await request(permission);
//     console.log("🛰️ Permission Result:", result);
    
//     if (result === RESULTS.GRANTED) {
//       return true; // Permission granted
//     } else {
//       console.log("❌ Location permission denied");
//       Alert.alert("Permission Denied", "Please enable location permissions in Settings.");
//       return false; // Permission denied
//     }
//   } catch (error) {
//     console.error("⚠️ Permission Error:", error);
//     return false; // Error handling
//   }
// };

// // Function to check if location services are enabled
// const checkLocationServices = async () => {
//   return new Promise((resolve) => {
//     Geolocation.getCurrentPosition(
//       () => resolve(true),  // If successful, resolve with true
//       (error) => {
//         if (error.code === 2) {  // Location services are disabled
//           Alert.alert(
//             "Location Services Disabled",
//             "Please enable location services in Settings.",
//             [{ text: "Go to Settings", onPress: () => Linking.openSettings() }, { text: "Cancel", style: "cancel" }]
//           );
//         } else {
//           Alert.alert("Location Error", "Unable to fetch location. Please check your device settings.");
//         }
//         resolve(false);  // If an error occurs, resolve with false
//       },
//       { enableHighAccuracy: false, timeout: 5000 }
//     );
//   });
// };

// // Function to fetch current location
// const getCurrentLocation = async () => {
//   const hasPermission = await requestLocationPermission();
//   if (!hasPermission) {
//     console.log("⛔ Location permission denied. Exiting function.");
//     return null; // Exit if permission is not granted
//   }

//   const isLocationEnabled = await checkLocationServices();
//   if (!isLocationEnabled) {
//     console.log("⛔ Location services disabled. Exiting function.");
//     return null; // Exit if location services are not enabled
//   }

//   console.log("📍 Fetching location...");

//   return new Promise((resolve, reject) => {
//     Geolocation.getCurrentPosition(
//       (position) => {
//         const { latitude, longitude } = position.coords;
//         const newLocation = { type: 'Point', coordinates: [longitude, latitude] };
//         console.log("✅ Location Retrieved:", newLocation);
//         resolve(newLocation); // Return the actual location
//       },
//       (error) => {
//         console.error("❌ Location Error:", error);
//         Alert.alert("Location Error", "Unable to fetch your current location. Please try again.");
//         reject(null); // Reject if fetching location fails
//       },
//       {
//         enableHighAccuracy: true, // Use high accuracy for better precision
//         timeout: 30000,           // 30 seconds timeout for fetching location
//         maximumAge: 5000,        // Cache the location for 5 seconds
//       }
//     );
//   });
// };

  useEffect(() => {
    const handleNewRideRequest = (newRide) => {
      console.log("New ride request received:", JSON.stringify(newRide, null, 2));
      // setRideRequests(prevRides => [newRide, ...prevRides]);
      setRideRequests(prevRides => [newRide.ride, ...prevRides]);
    };

    // Check if socket exists before trying to use it
    if (socketService.socket) {
      socketService.socket.on('newRideRequest', handleNewRideRequest);
    } else {
      console.warn('⚠️ [TakeABreak] Socket not available, skipping newRideRequest listener');
    }

    return () => {
      // Check if socket exists before trying to remove listener
      if (socketService.socket) {
        socketService.socket.off('newRideRequest', handleNewRideRequest);
      }
    };
  }, []);

 
  
  

  // Driver's Current Location (for map display)
  const [currentLocation, setCurrentLocation] = useState({
    latitude: userLocation?.coordinates?.[1] || 6.5244, // Default to Lagos
    longitude: userLocation?.coordinates?.[0] || 3.3792,
  });

  // Map reference for smooth animations
  const mapRef = useRef(null);
 
  const [region, setRegion] = useState({
    latitude: userLocation?.coordinates?.[1] || 6.5244, // Default to Lagos
    longitude: userLocation?.coordinates?.[0] || 3.3792,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });

  // Set up live location tracking for map updates
  useEffect(() => {
    let locationListener = null;

    const setupLiveTracking = async () => {
      try {
        console.log('🗺️ [TakeABreak] Setting up live location tracking for map...');

        // Initialize location service if not already done
        if (!locationService.isInitialized) {
          await initializeLocationService();
        }

        // Add location listener to update car position in real-time
        locationListener = (location) => {
          console.log('📍 [TakeABreak] Raw location received:', location);

          const [longitude, latitude] = location.coordinates;
          const newPosition = { latitude, longitude };

          console.log('🔄 [TakeABreak] Converted coordinates:', {
            raw: location.coordinates,
            converted: newPosition
          });

          // Always update the car position (remove distance threshold for debugging)
          console.log('🚗 [TakeABreak] Updating car marker to:', newPosition);
          setCurrentLocation(newPosition);

          // Update region to center on new location (only for significant moves)
          const currentLat = region.latitude;
          const currentLng = region.longitude;
          const distance = Math.sqrt(
            Math.pow(currentLat - latitude, 2) +
            Math.pow(currentLng - longitude, 2)
          );

          if (distance > 0.01) { // Only update region for significant moves (>1km)
            console.log('🗺️ [TakeABreak] Updating map region to follow car');
            setRegion(prev => ({
              ...prev,
              latitude,
              longitude,
            }));
          }
        };

        locationService.addLocationListener(locationListener);
        console.log('🔗 [TakeABreak] Location listener added to service');

        // Debug: Check if location service is tracking
        console.log('🔍 [TakeABreak] Location service status:', {
          isInitialized: locationService.isInitialized,
          isTracking: locationService.isTracking,
          hasCurrentLocation: !!locationService.currentLocation,
          listenerCount: locationService.listeners?.length || 0
        });

        // Get initial location to set car position
        const initialLocation = locationService.getLastKnownLocation();
        console.log('🔍 [TakeABreak] Initial location from service:', initialLocation);

        if (initialLocation) {
          const [longitude, latitude] = initialLocation.coordinates;
          const initialPosition = { latitude, longitude };
          console.log('🎯 [TakeABreak] Setting initial car position:', initialPosition);
          setCurrentLocation(initialPosition);
          setRegion(prev => ({
            ...prev,
            latitude,
            longitude,
          }));
          console.log('✅ [TakeABreak] Initial car position set successfully');
        } else {
          console.warn('⚠️ [TakeABreak] No initial location available from service');
          // Try to get current location directly
          try {
            const currentLoc = await locationService.getCurrentLocation();
            if (currentLoc) {
              const [longitude, latitude] = currentLoc.coordinates;
              const initialPosition = { latitude, longitude };
              console.log('🎯 [TakeABreak] Got current location directly:', initialPosition);
              setCurrentLocation(initialPosition);
              setRegion(prev => ({
                ...prev,
                latitude,
                longitude,
              }));
            }
          } catch (error) {
            console.error('❌ [TakeABreak] Error getting current location:', error);
          }
        }

        console.log('✅ [TakeABreak] Live location tracking setup complete');

      } catch (error) {
        console.error('❌ [TakeABreak] Error setting up live tracking:', error);
      }
    };

    setupLiveTracking();

    // Cleanup function
    return () => {
      if (locationListener) {
        locationService.removeLocationListener(locationListener);
        console.log('🧹 [TakeABreak] Location listener removed');
      }
    };
  }, []);

  // Debug: Log current location state changes
  useEffect(() => {
    console.log('🗺️ [TakeABreak] Current location state updated:', currentLocation);
  }, [currentLocation]);

  // Debug: Log region state changes (throttled)
  useEffect(() => {
    // Only log significant region changes to avoid spam
    const isSignificantChange = Math.abs(region.latitude - 6.5244) > 0.001 || Math.abs(region.longitude - 3.3792) > 0.001;
    if (isSignificantChange) {
      console.log('🗺️ [TakeABreak] Significant map region change:', region);
    }
  }, [region]);

  // Debug: Check location permissions on mount
  useEffect(() => {
    const checkLocationStatus = async () => {
      console.log('🔍 [TakeABreak] Checking location permissions and service status...');

      try {
        // Check location permissions
        const hasPermission = await requestLocationPermission();
        console.log('📍 [TakeABreak] Location permission status:', hasPermission);

        // Check location service status
        console.log('📍 [TakeABreak] Location service initialized:', locationService.isInitialized);
        console.log('📍 [TakeABreak] Location service tracking:', locationService.isTracking);
        console.log('📍 [TakeABreak] Location service current location:', locationService.currentLocation);

      } catch (error) {
        console.error('❌ [TakeABreak] Error checking location status:', error);
      }
    };

    checkLocationStatus();
  }, []);

return (
  <View style={styles.container}>
   
    {/* Online/Offline Status */}
    <View style={styles.onlineBadge}>
      <Text style={styles.onlineText}>
        {isOnline ? '🟢 You are online' : '🔴 You are offline'}
      </Text>
    </View>

    {/* Map Section */}
    <MapView
      ref={mapRef}
      style={styles.map}
      region={region}
      onRegionChangeComplete={(reg) => setRegion(reg)}
      showsUserLocation={false}
      showsMyLocationButton={false}
      followsUserLocation={false}
    >
      {/* Circle Showing Driver's Range */}
      <Circle
        center={currentLocation}
        radius={distance * 100}
        fillColor="rgba(0, 123, 255, 0.2)"
        strokeColor="rgba(0, 123, 255, 0.5)"
      />

      {/* Driver Marker - Now updates in real-time */}
      {currentLocation && (
        <Marker
          coordinate={currentLocation}
          title="You are here"
          description={`Lat: ${currentLocation.latitude.toFixed(6)}, Lng: ${currentLocation.longitude.toFixed(6)}`}
        >
          <Image source={images.CarSingle} style={styles.carIcon} />
        </Marker>
      )}
    </MapView>

    {/* Distance Selector */}
    <View style={styles.distanceContainer}>
      <TouchableOpacity
        style={styles.distanceButton}
        onPress={() => setDistance(Math.max(1, distance - 1))}
      >
        <Text style={styles.distanceText}>-</Text>
      </TouchableOpacity>
      <Text style={styles.distanceValue}>{distance} miles</Text>
      <TouchableOpacity style={styles.distanceButton} onPress={() => setDistance(distance + 1)}>
        <Text style={styles.distanceText}>+</Text>
      </TouchableOpacity>
    </View>

    <ScrollView style={styles.infoSection}>

    <TouchableOpacity
            style={styles.upcomingRideContainer}
          >
      <Text style={styles.infoText}>3 to 6 km wait in your area 
        {/* {userLocation?.coordinates.join()} */}
        </Text>
      <Text style={styles.infoSubText}>
        Average wait for 23 rides over the last 30 minutes
      </Text>

</TouchableOpacity>
      <Text style={styles.infoText}>Nearby Ride Requests</Text>

      {rideRequests.length > 0 ? (
        rideRequests.map((ride, index) => (
          <TouchableOpacity
            key={ride.rideId || index}   
            style={styles.upcomingRideContainer}
            onPress={() => navigation.navigate('PassengerWaiting', { ride })}   
          >
            <Text style={styles.upcomingRideTitle}>New Ride Request</Text>
            <Text style={styles.upcomingRideDetail}>Pickup: {ride.pickupPoint}</Text>
            <Text style={styles.upcomingRideSubtitle}>Destination: {ride.to?.[0].place}</Text>
            <Text style={styles.upcomingRideSubtitle}>Price Range: {ride.priceRange}</Text>
          </TouchableOpacity>
        ))
      ) : (
        <TouchableOpacity 
          style={styles.noRidesContainer} 
         >
          <Text style={styles.noRidesText}>No new ride requests</Text>
        </TouchableOpacity>
      )}
    </ScrollView>




    <View style={styles.containerr}>
      {/* Filter Button */}
      <TouchableOpacity style={styles.filterButton} onPress={() => { navigation.navigate('HeadToDestination1') }}>
        <Image
          source={images.FilterIcon}
          style={styles.filterIcon}
        />
      </TouchableOpacity>

      {/* Center Map Button */}
      <TouchableOpacity
        style={styles.centerButton}
        onPress={() => {
          console.log('📍 [TakeABreak] Center button pressed');
          console.log('📍 [TakeABreak] Current location state:', currentLocation);
          console.log('📍 [TakeABreak] Location service current:', locationService.currentLocation);

          if (mapRef.current && currentLocation) {
            console.log('📍 [TakeABreak] Centering map on car location:', currentLocation);
            mapRef.current.animateToRegion({
              latitude: currentLocation.latitude,
              longitude: currentLocation.longitude,
              latitudeDelta: 0.01,
              longitudeDelta: 0.01,
            }, 1000);
          } else {
            console.warn('⚠️ [TakeABreak] Cannot center - no current location or map ref');
          }
        }}
      >
        <Image source={images.locate} style={styles.centerIcon} />
      </TouchableOpacity>

      {/* Take a Break / Go Online Button */}

      <TouchableOpacity style={styles.passengerButton} onPress={toggleOnlineStatus} >
        <Text style={styles.passengerButtonText}>
          {isOnline ? "Take a Break" : "Go Online"}
        </Text>
        <Image source={images.CarIcon} style={styles.carIcon} />
      </TouchableOpacity>


    </View>

  </View>
);


}


const styles = StyleSheet.create({
  rideRequestContainer: {
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 10,
    marginVertical: 10,
    elevation: 5,
  },
  rideRequestTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: COLORS.primary,
    marginBottom: 5,
  },
  rideRequestText: {
    fontSize: 14,
    color: COLORS.black,
  },
  acceptButton: {
    marginTop: 10,
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 5,
    alignItems: "center",
  },
  acceptButtonText: {
    color: COLORS.white,
    fontWeight: "bold",
  },

  onlineBadge: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? 60 : 50, 
    // left: '50%',
    alignSelf: "center",
    // transform: [{ translateX: -50 }],
    backgroundColor: 'green',
    padding: 8,
    borderRadius: 10,
    elevation: 5,
    zIndex: 1,
  },
  onlineText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  containerr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  filterButton: {
    width: 50,
    height: 50,
    backgroundColor: COLORS.lightGray,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterIcon: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
  passengerButton: {
    flex: 1,
    marginLeft: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: 13,
    borderRadius: 10,
  },
  passengerButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
  },
  carIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  centerButton: {
    position: 'absolute',
    right: 20,
    top: Platform.OS === 'ios' ? 200 : 180,
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 6,
    elevation: 6,
  },
  centerIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
    tintColor: COLORS.primary,
  },
  container: {
    flex: 1,
  },
  map: {
    width: '100%',
    height: '40%',
  },
  budgetBadge: {
    position: 'absolute',
    top: 10,
    left: width / 2 - 50,
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetText: {
    color: COLORS.white,
    ...FONTS.h4,
  },
  budgetAmount: {
    color: COLORS.white,
    ...FONTS.h4,
    marginLeft: 5,
  },

  distanceContainer: {
    position: 'absolute',
    top: '30%',
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 15,
    elevation: 5,
  },
  distanceButton: {
    padding: 10,
    // backgroundColor: COLORS.grey,
    borderRadius: 5,
  },
  distanceText: {
    ...FONTS.body3,
  },
  distanceValue: {
    ...FONTS.h4,
    marginHorizontal: 10,
    color: COLORS.black,
  },
  infoSection: {
    padding: 20,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -30,
  },
  infoText: {
    ...FONTS.body3,
    marginBottom: 5,
  },
  infoSubText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 20,
  },
  upcomingRideContainer: {
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: 7,
    borderWidth: 0.5,
    borderColor: COLORS.light_grey,
    marginBottom: 20,
  },
  upcomingRideTitle: {
    ...FONTS.body4,
    fontSize: 11,


  },
  upcomingRideSubtitle: {
    marginTop: 0,
    ...FONTS.body2,
    color: COLORS.black,
    fontSize: 10,
  },
  getPassengerButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  getPassengerText: {
    color: COLORS.white,
    ...FONTS.h3
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 15,
    backgroundColor: COLORS.white,
  },
  footerItem: {
    alignItems: 'center',
  },
  footerIcon: {
    width: 25,
    height: 25,
    marginBottom: 5,
  },
  footerText: {
    fontSize: 12,
    color: COLORS.gray,
  },
});

export default TakeABreak;