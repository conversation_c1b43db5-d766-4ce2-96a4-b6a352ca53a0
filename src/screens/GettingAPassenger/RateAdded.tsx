 

import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  ActivityIndicator,
  Platform,
} from "react-native";
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-native-maps";
import { COLORS, FONTS, images } from "../../constants";
import { useNavigation, useRoute } from "@react-navigation/native";
import socketService from "../../services/socketService";

const RateAdded = () => {
   const route = useRoute();
     const ridee  = route.params || '';  
     const ride = ridee?.ride?.ride;
   console.log('🚗 Ridee:', JSON.stringify(ride,null,2));
   const navigation = useNavigation();
   const [waiting, setWaiting] = useState(true);
   const [selected, setSelected] = useState('');
const [loader, setLoader] = useState(false);


   useEffect(() => {
    // Check if socket exists before setting up listeners
    if (!socketService.socket) {
      console.warn('⚠️ [RateAdded] Socket not available, skipping event listeners');
      return;
    }

    // Listen for event indicating another driver was selected
    socketService.socket.on('passengerFoundDriver', (serverResponse) => {
      console.log("Passenger has found another driver:", serverResponse);
      navigation.navigate('GotAnotherDriver');
    });


    socketService.socket.on('driverRequested', (serverResponse) => {
      console.log("Driver has been requested for a ride:", serverResponse);
setSelected(serverResponse?.message);
setWaiting(false);
    });

    // Listen for event indicating this driver was selected
    socketService.socket.on('newRideActivated', (serverResponse) => {
      console.log("Driver has been requested for a ride:", serverResponse);
      navigation.navigate('MadePayment',{rideId:ride?.rideId});
    });





    return () => {
      // Cleanup listeners when the component unmounts
      if (socketService.socket) {
        socketService.socket.off('passengerFoundDriver');
        socketService.socket.off('driverRequested');
        socketService.socket.off('newRideActivated');
      }
    };
  }, [navigation]);   



  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Map Section */}
      <MapView style={styles.map} initialRegion={{
        latitude: ride?.pickupCoordinates?.latitude || 33.5186,
        longitude: ride?.pickupCoordinates?.longitude || -86.8104,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      }}>
        <Polyline coordinates={ride?.route || []} strokeColor={COLORS.primary} strokeWidth={3} />
        <Marker coordinate={ride?.pickupCoordinates || { latitude: 33.5186, longitude: -86.8104 }}>
          <View style={styles.marker}>
            <Text style={styles.markerText}>{ride?.from}</Text>
          </View>
        </Marker>
        <Marker coordinate={ride?.dropoffCoordinates || { latitude: 33.5036, longitude: -86.8084 }}>
          <View style={styles.marker}>
            <Text style={styles.markerText}>{ride?.to?.[0].place}</Text>
          </View>
        </Marker>
      </MapView>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        <View style={styles.infoHeader}>
          <Image source={images.upArrow} style={styles.navIcon} />
          <View>
            <Text style={styles.arrivalText}>Drive Distance is {ride?.kmDistance} Miles</Text>
            <Text style={styles.carInfo}>{ride?.vehicle || "Vehicle Info"}</Text>
          </View>
        </View>

        {/* Ride Details */}
        <View style={styles.rideDetails}>
          <Text style={styles.rideDetailsTitle}>Ride details</Text>
          <Text style={styles.rideDescription}>Meet {ride?.passengerName} at {ride?.pickupPoint}</Text>
       <Text style={[styles.rideDetailsTitle,{fontSize:10}]}>
                {ride?.from} to {ride.to[0]?.place}
                </Text>
        </View>

        {/* Driver Info */}
        <View style={styles.driverInfo}>
          <View style={{ flexDirection: "row", alignItems: "center" }}>
            <Image source={images.driver1} style={styles.driverImage} />
            <Text style={styles.driverName}>Olive Rodrigo...</Text>
          </View>
          <Text style={styles.driverRating}>4.4 ★ | 53 rides</Text>
        </View>

        {/* Waiting for Passenger Approval */}
        {waiting ? (<>
          <Text style={{fontSize:14,textAlign:'center',marginVertical:15}}>Waiting for passenger to confirm...</Text>
          <ActivityIndicator size="large" color={COLORS.primary} />
          </>
        ) : 
        (<>
        <Text style={{fontSize:16,fontWeight:'700',textAlign:'center'}}>{selected} </Text>
        <Text style={{fontSize:14,textAlign:'center',marginVertical:15}}>Waiting for passenger to make payment </Text>
        <ActivityIndicator size="large" color={COLORS.primary} />
        </>
)
        }


      </View>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  marker: {
    backgroundColor: COLORS.primary,
    padding: 5,
    borderRadius: 5,
  },
  markerText: {
    color: COLORS.white,
    fontWeight: 'bold',
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginTop: -30,
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    borderBottomWidth: 0.5,
    paddingBottom: 20,
    borderColor: COLORS.border,
  },
  navIcon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
  arrivalText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginHorizontal: 20,

  },
  carInfo: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginHorizontal: 20,
    fontSize:10

  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  rideDetails: {
    marginBottom: 20,
    borderWidth: 0.5,
    padding: 10,
    borderRadius: 7,
    borderColor: COLORS.border,
  },
  rideDetailsTitle: {
    ...FONTS.body5,
    color: COLORS.black,
    // marginBottom: 10,
    // fontSize: 15
  },
  rideDescription: {
    marginVertical:3,
    ...FONTS.body3,
    color: COLORS.black,
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  driverImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  driverName: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  driverRating: {
    ...FONTS.body4,
    color: COLORS.grey,
  },
  driveRateButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 10,
    marginBottom: 10,
  },
  driveRateText: {
    color: COLORS.white,
    ...FONTS.h4,
    marginRight: 10,
  },
  carIcon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
  note: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
    fontSize: 12,
  },
});

export default RateAdded;
