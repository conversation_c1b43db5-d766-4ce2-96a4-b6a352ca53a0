import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Dimensions,
  ScrollView,
  Alert,
  Platform,
  ActivityIndicator,
} from "react-native";
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-native-maps";
import { COLORS, FONTS, icons, images } from "../../constants";
import { useNavigation, useRoute } from "@react-navigation/native";
import socketService from "../../services/socketService";

const SetPrice = () => {
  const [pricePerMile, setPricePerMile] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [region, setRegion] = useState({
    latitude: 0,
    longitude: 0,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  });
  const [routeCoordinates, setRouteCoordinates] = useState([]);

  const route = useRoute();
  const ride = route.params || '';
  console.log('🚗 Ridee:', JSON.stringify(ride, null, 2));

  const navigation = useNavigation();

  // Fetch coordinates for the locations
  useEffect(() => {
    const geocodeLocations = async () => {
      try {
        setIsLoading(true);

        if (!ride?.ride?.from || !ride?.ride?.to?.[0]?.place) {
          throw new Error('Missing location information');
        }

        // Get coordinates for pickup location
        const pickupResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(ride?.ride?.from)}&format=json&limit=1`);

        // Check if pickup response is JSON
        const pickupContentType = pickupResponse.headers.get('content-type');
        const isPickupJson = pickupContentType && pickupContentType.includes('application/json');

        if (!isPickupJson) {
          const textResponse = await pickupResponse.text();
          console.error('Non-JSON response from pickup geocoding:', textResponse);
          throw new Error('Geocoding service returned invalid response for pickup location');
        }

        const pickupData = await pickupResponse.json();

        // Get coordinates for dropoff location
        const dropoffResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(ride?.ride?.to?.[0].place)}&format=json&limit=1`);

        // Check if dropoff response is JSON
        const dropoffContentType = dropoffResponse.headers.get('content-type');
        const isDropoffJson = dropoffContentType && dropoffContentType.includes('application/json');

        if (!isDropoffJson) {
          const textResponse = await dropoffResponse.text();
          console.error('Non-JSON response from dropoff geocoding:', textResponse);
          throw new Error('Geocoding service returned invalid response for dropoff location');
        }

        const dropoffData = await dropoffResponse.json();

        if (pickupData.length > 0 && dropoffData.length > 0) {
          const pickupCoords = {
            latitude: parseFloat(pickupData[0].lat),
            longitude: parseFloat(pickupData[0].lon)
          };

          const dropoffCoords = {
            latitude: parseFloat(dropoffData[0].lat),
            longitude: parseFloat(dropoffData[0].lon)
          };

          // Create midpoint between pickup and dropoff
          const midpointCoords = {
            latitude: (pickupCoords.latitude + dropoffCoords.latitude) / 2,
            longitude: (pickupCoords.longitude + dropoffCoords.longitude) / 2
          };

          // Set route coordinates
          setRouteCoordinates([pickupCoords, midpointCoords, dropoffCoords]);

          // Set map region to center on the route (focusing on pickup point)
          setRegion({
            latitude: pickupCoords.latitude,
            longitude: pickupCoords.longitude,
            latitudeDelta: 0.05,
            longitudeDelta: 0.05,
          });
        } else {
          throw new Error('Could not find coordinates for one or both locations');
        }
      } catch (error) {
        console.error("Error geocoding locations:", error);
        // Fallback to a default view
        setRegion({
          latitude: 0,
          longitude: 0,
          latitudeDelta: 10,
          longitudeDelta: 10,
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (ride?.ride?.from && ride?.ride?.to?.[0]?.place) {
      geocodeLocations();
    }
  }, [ride]);

  // Socket event handlers
  useEffect(() => {
    socketService.onEvent('acceptRideRquest', (response) => {
      console.log('🚗 Ride:', response);

      if (response.success) {
        Alert.alert("Ride Accepted", response?.message);
        navigation.navigate('RateAdded', {ride});
      }

      if (!response?.success) {
        Alert.alert("Ride Acceptance Failed", response?.message);
        navigation.navigate('TakeABreak');
      }

      if (response?.message === "Price is not within the range") {
        Alert.alert("Ride Acceptance Failed", response?.message);
        return;
      }
    });
  }, []);

  const handleConfirmRate = () => {
    if (pricePerMile) {
      console.log(`Price set to $${pricePerMile} per mile`);

      // Emitting accept ride request event with price and ride ID
      socketService.emitEvent('acceptRideRequest', {
        price: pricePerMile,
        rideId: ride?.ride?.rideId
      });
    } else {
      Alert.alert("Invalid Input", "Please enter a valid price");
    }
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}

      {/* Top Section (Map with Pricing Overlay) */}
      <View style={styles.mapSection}>
        {isLoading ? (
          <View style={[styles.map, styles.loadingContainer]}>
            <ActivityIndicator size="large" color={COLORS.primary} />
            <Text style={styles.loadingText}>Loading map...</Text>
          </View>
        ) : (
          <MapView
            style={styles.map}
            region={region}
            showsUserLocation={true}
          >
            {/* Route Line */}
            {routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor={COLORS.primary}
                strokeWidth={3}
              />
            )}

            {/* Origin Marker */}
            {routeCoordinates.length > 0 && (
              <Marker coordinate={routeCoordinates[0]}>
                <View style={styles.marker}>
                  <Text style={styles.markerText}>{ride?.ride?.from}</Text>
                </View>
              </Marker>
            )}

            {/* Destination Marker */}
            {routeCoordinates.length > 0 && (
              <Marker coordinate={routeCoordinates[2]}>
                <View style={styles.marker}>
                  <Text style={styles.markerText}>{ride?.ride?.to?.[0].place}</Text>
                </View>
              </Marker>
            )}
          </MapView>
        )}

        <View style={styles.priceTag}>
          <Text style={styles.priceText}>$ {pricePerMile}</Text>
        </View>
      </View>

      {/* Bottom Sheet Section */}
      <ScrollView style={styles.bottomSheet}>
        {/* Header */}
        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.headerText}>Set Price</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Illustration */}
        <Image source={images.setprice} style={styles.illustration} />

        {/* Free Cancellation Info */}
        <Text style={styles.freeCancellationText}>
          <Text style={styles.boldText}>Free cancellation:</Text> this will
          display to the user as the price they will pay for this ride...
          remember <Text style={styles.highlightText}>RideFuze</Text> is taking
          30%.
        </Text>

        <View
          style={{
            flex: 1,
            borderWidth: 0.5,
            borderColor: COLORS.grey,
            borderRadius: 10,
            padding: 10,
            marginBottom: 30,
          }}
        >
          {/* Ride Details */}
          <View style={styles.rideDetails}>
            <Text style={styles.rideDetailsTitle}>Ride details</Text>
            <Text style={styles.rideDetailsSubtitle}>
              Meet {ride?.ride?.passengerName || 'Passenger'} at {ride?.ride?.pickupPoint || 'pickup point'}
            </Text>
            <Text style={styles.rideDetailsTitle}>
              {ride?.ride?.from || 'Origin'} to {ride?.ride?.to?.[0]?.place || 'Destination'}
            </Text>
          </View>

          {/* Price Input */}
          <TextInput
            style={styles.input}
            placeholder="Enter price per miles"
            placeholderTextColor={COLORS.grey}
            keyboardType="numeric"
            value={pricePerMile}
            onChangeText={(text) => setPricePerMile(text)}
          />
        </View>

        {/* Link to Market Fares */}
        <Text style={styles.marketFaresLink}>
          View prevailing market fares and your price is between{" "}
          <Text style={styles.highlightText}>${ride?.ride?.priceRange || 'N/A'}</Text>
        </Text>

        {/* Confirm Rate Button */}
        <TouchableOpacity style={styles.confirmButton} onPress={handleConfirmRate}>
          <Text style={styles.confirmButtonText}>Confirm rate</Text>
          <Image source={icons.dollar} style={styles.dollarIcon} />
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  mapSection: {
    height: Dimensions.get("window").height * 0.2,
    width: "100%",
    position: "relative",
  },
  map: {
    height: "100%",
    width: "100%",
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loadingText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
  marker: {
    backgroundColor: COLORS.primary,
    padding: 5,
    borderRadius: 5,
    maxWidth: 150,
  },
  markerText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 10,
  },
  priceTag: {
    position: "absolute",
    top: 20,
    left: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    padding: 10,
  },
  priceText: {
    color: COLORS.white,
    fontWeight: "bold",
    fontSize: 18,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  headerText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  illustration: {
    height: 120,
    width: "100%",
    resizeMode: "contain",
    alignSelf: "center",
    marginBottom: 20,
  },
  freeCancellationText: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginBottom: 20,
    lineHeight: 18,
    marginHorizontal: 10,
    textAlign: "center",
  },
  boldText: {
    fontWeight: "bold",
    color: COLORS.black,
  },
  highlightText: {
    color: COLORS.primary,
  },
  rideDetails: {
    marginBottom: 20,
  },
  rideDetailsTitle: {
    ...FONTS.body6,
    fontSize: 11,
    color: COLORS.black,
    marginVertical: 5,
  },
  rideDetailsSubtitle: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
  },
  marketFaresLink: {
    ...FONTS.body4,
    color: COLORS.primary,
    marginBottom: 20,
    textAlign: "center",
    textDecorationLine: "underline",
    marginHorizontal: 20,
  },
  confirmButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 10,
  },
  confirmButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
    marginRight: 10,
  },
  dollarIcon: {
    width: 20,
    height: 20,
    resizeMode: "contain",
  },
});

export default SetPrice;