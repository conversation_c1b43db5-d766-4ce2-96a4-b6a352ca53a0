import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-maps';
import { COLORS, FONTS, images } from '../../constants';
import { useNavigation, useRoute } from '@react-navigation/native';
import socketService from '../../services/socketService';

const PassengerWaiting = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { ride } = route.params || {};
  console.log('🚗 Ride:', ride);
  
  // State for map region and coordinates
  const [region, setRegion] = useState({
    latitude: 0, 
    longitude: 0,
    latitudeDelta: 10, 
    longitudeDelta: 10,
  });
  
  const [routeCoordinates, setRouteCoordinates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const geocodeLocations = async () => {
      try {
        setIsLoading(true);
        
        if (!ride?.from || !ride?.to?.[0]?.place) {
          throw new Error('Missing location information');
        }
        
        // Get coordinates for pickup location
        const pickupResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(ride.from)}&format=json&limit=1`);
        const pickupData = await pickupResponse.json();
        
        // Get coordinates for dropoff location
        const dropoffResponse = await fetch(`https://nominatim.openstreetmap.org/search?q=${encodeURIComponent(ride.to[0].place)}&format=json&limit=1`);
        const dropoffData = await dropoffResponse.json();

        if (pickupData.length > 0 && dropoffData.length > 0) {
          const pickupCoords = {
            latitude: parseFloat(pickupData[0].lat),
            longitude: parseFloat(pickupData[0].lon)
          };
          
          const dropoffCoords = {
            latitude: parseFloat(dropoffData[0].lat),
            longitude: parseFloat(dropoffData[0].lon)
          };

          // Create midpoint between pickup and dropoff
          const midpointCoords = {
            latitude: (pickupCoords.latitude + dropoffCoords.latitude) / 2,
            longitude: (pickupCoords.longitude + dropoffCoords.longitude) / 2
          };
          
          // Set route coordinates
          setRouteCoordinates([pickupCoords, midpointCoords, dropoffCoords]);
          
          // Calculate proper delta for the region to ensure both points are visible
          const latDelta = Math.abs(pickupCoords.latitude - dropoffCoords.latitude) * 1.5;
          const lngDelta = Math.abs(pickupCoords.longitude - dropoffCoords.longitude) * 1.5;
          
          // Set map region to center on the route
          setRegion({
            latitude: midpointCoords.latitude,
            longitude: midpointCoords.longitude,
            latitudeDelta: Math.max(latDelta, 0.05), // Ensure minimum zoom level
            longitudeDelta: Math.max(lngDelta, 0.05), // Ensure minimum zoom level
          });
        } else {
          throw new Error('Could not find coordinates for one or both locations');
        }
      } catch (error) {
        console.error("Error geocoding locations:", error);
        Alert.alert("Map Error", error.message || "Failed to load map coordinates");
        
        // Fallback to a default region showing the entire country/area
        setRegion({
          latitude: 33.5186,
          longitude: -86.8104,
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        });
        
        // Fallback coordinates if geocoding fails
        setRouteCoordinates([
          { latitude: 33.5186, longitude: -86.8104 }, // Default Start
          { latitude: 33.5096, longitude: -86.8094 }, // Default Midpoint
          { latitude: 33.5036, longitude: -86.8084 }, // Default End
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    if (ride?.from && ride?.to?.[0]?.place) {
      geocodeLocations();
    } else {
      setIsLoading(false);
      // Set default coordinates if ride data is missing
      setRegion({
        latitude: 33.5186,
        longitude: -86.8104,
        latitudeDelta: 0.05,
        longitudeDelta: 0.05,
      });
      
      setRouteCoordinates([
        { latitude: 33.5186, longitude: -86.8104 }, // Default Start
        { latitude: 33.5096, longitude: -86.8094 }, // Default Midpoint
        { latitude: 33.5036, longitude: -86.8084 }, // Default End
      ]);
    }
  }, [ride]);

  useEffect(() => {
    // Check if socket exists before setting up listeners
    if (!socketService.socket) {
      console.warn('⚠️ [PassengerWaiting] Socket not available, skipping event listeners');
      return;
    }

    // Listener for cancellation confirmation from server
    socketService.socket.on('cancelRideRequest', (response) => {
      if (response.success) {
        Alert.alert("Cancellation Confirmed", "The ride request has been successfully cancelled.");
        navigation.goBack();
      } else {
        Alert.alert("Cancellation Failed", response.message);
      }
    });

    return () => {
      // Clean up the listener
      if (socketService.socket) {
        socketService.socket.off('cancelRideRequest');
      }
    };
  }, []);

  /** Accept Ride Request */
  const handleAccept = () => {
    navigation.navigate('SetRate', {ride});
  };

  const handleCancelRide = () => {
    // Emit cancel ride request to server
    socketService.emitEvent('cancelRideRequest', ride?.rideId);
    console.log("Cancellation request sent for rideId:", ride?.rideId);
    navigation.goBack();
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      
      {/* Map Section */}
      {isLoading ? (
        <View style={[styles.map, styles.loadingContainer]}>
          <ActivityIndicator size="large" color={COLORS.primary} />
          <Text style={styles.loadingText}>Loading map...</Text>
        </View>
      ) : (
        <MapView 
          style={styles.map} 
          region={region}
          showsUserLocation={true}
        >
          {/* Route Line */}
          {routeCoordinates.length > 0 && (
            <Polyline
              coordinates={routeCoordinates}
              strokeColor={COLORS.primary}
              strokeWidth={3}
            />
          )}
          
          {/* Start Marker */}
          {routeCoordinates.length > 0 && (
            <Marker coordinate={routeCoordinates[0]}>
              <View style={styles.marker}>
                <Text style={styles.markerText}>{ride?.from || 'Pickup'}</Text>
              </View>
            </Marker>
          )}
          
          {/* End Marker */}
          {routeCoordinates.length > 0 && (
            <Marker coordinate={routeCoordinates[2]}>
              <View style={styles.marker}>
                <Text style={styles.markerText}>{ride?.to?.[0].place || 'Dropoff'}</Text>
              </View>
            </Marker>
          )}
        </MapView>
      )}

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        {/* Ride Details */}
        <View style={styles.detailsHeader}>
          <Text></Text>
          <Text style={styles.rideTitle}>Passenger is waiting...</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Vehicle Image */}
        <Image source={images.CarSingle} style={styles.vehicleImage} />

        <Text style={styles.rideDescription}>
          {ride?.passengerName || 'Passenger'} is waiting for you to take them from{' '}
          {ride?.from || 'Unknown Pickup'} to {ride?.to?.[0].place || 'Unknown Destination'}.
          Are you willing to accept or reject this ride?
        </Text>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.acceptButton} onPress={handleAccept}>
            <Text style={styles.acceptButtonText}>Accept</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.rejectButton} onPress={handleCancelRide}>
            <Text style={styles.rejectButtonText}>Reject</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.note}>
          Rides can be accepted within 60 seconds, otherwise they are automatically declined.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  map: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height * 0.4,
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  loadingText: {
    ...FONTS.body3,
    color: COLORS.grey,
    marginTop: 10,
  },
  marker: {
    backgroundColor: COLORS.primary,
    padding: 5,
    borderRadius: 5,
    maxWidth: 150,
  },
  markerText: {
    color: COLORS.white,
    fontWeight: 'bold',
    fontSize: 10,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    marginTop: -30,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rideTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  vehicleImage: {
    width: '100%',
    height: 150,
    alignSelf: 'center',
    resizeMode: 'contain',
    marginVertical: 20,
  },
  rideDescription: {
    marginVertical: 10,
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.black,
    marginBottom: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  acceptButton: {
    flex: 1,
    marginRight: 10,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 15,
  },
  acceptButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  rejectButton: {
    flex: 1,
    marginLeft: 10,
    backgroundColor: COLORS.white,
    borderWidth: 0.5,
    borderColor: COLORS.red,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 15,
  },
  rejectButtonText: {
    color: COLORS.red,
    ...FONTS.h3,
  },
  note: {
    ...FONTS.body4,
    textAlign: 'center',
    color: COLORS.grey,
  },
});

export default PassengerWaiting;