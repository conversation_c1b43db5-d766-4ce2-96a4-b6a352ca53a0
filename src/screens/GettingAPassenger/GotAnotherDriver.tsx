import React from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { COLORS, FONTS, icons } from "../../constants";
import { useNavigation } from "@react-navigation/native";

const GotAnotherDriver = () => {
  const navigation = useNavigation();
  const region = {
    latitude: 33.5186,
    longitude: -86.8104,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };

  return (
    <View style={styles.container}>
      {/* Top Section - Map */}
      <View style={styles.mapSection}>
        <MapView
          style={styles.map}
          initialRegion={region}
          showsUserLocation={true}
        >
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Pickup Location"
            description="AMLI 7th Street Station..."
          />
        </MapView>
        <View style={styles.priceTag}>
          <Text style={styles.priceText}>$ 5000</Text>
        </View>
      </View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>

        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.headerText}>Passengers got another driver</Text>
          <TouchableOpacity onPress={() => navigation.navigate('TakeABreak')}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>
        <Text style={styles.emoji}>🥲</Text>

        {/* <View style={styles.header}>
          <Text style={styles.title}>Passengers got another driver</Text>
        </View> */}
        <Text style={styles.message}>
          Unluckily the passenger has gone on with another driver
        </Text>

        {/* Okay Button */}
        <TouchableOpacity
          style={styles.okayButton}
          onPress={() => navigation.navigate('TakeABreak')} // Navigate back to the previous screen
        >
          <Text style={styles.okayButtonText}>Okay</Text>
        </TouchableOpacity>

        {/* Note */}
        <Text style={styles.note}>
          Rides can be accepted and added to your queue after 30 seconds, rides
          are automatically declined.
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  mapSection: {
    height: Dimensions.get("window").height * 0.5,
    width: "100%",
    position: "relative",
  },
  map: {
    height: "100%",
    width: "100%",
  },
  priceTag: {
    position: "absolute",
    top: 20,
    left: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    padding: 10,
  },
  priceText: {
    color: COLORS.white,
    fontWeight: "bold",
    fontSize: 18,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    // alignItems: "center",
  },
  // header: {
  //   alignItems: "center",
  //   marginBottom: 20,
  // },
  title: {
    ...FONTS.h2,
    color: COLORS.black,
    marginBottom: 10,
  },
  emoji: {
    fontSize: 40,
    textAlign: "center",
  },
  message: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 20,
  },
  okayButton: {
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 40,
    borderRadius: 10,
    marginVertical: 20,
  },
  okayButtonText: {
    color: COLORS.white,
    ...FONTS.h4,
    textAlign: "center",
  },
  note: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
    marginTop: 10,
    lineHeight: 18,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  headerText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  closeButton: {
    fontSize: 20,
    marginLeft: 20,
    color: COLORS.grey,
  },
});

export default GotAnotherDriver;
