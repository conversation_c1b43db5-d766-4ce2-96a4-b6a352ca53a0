import React, { useEffect, useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  Linking,
} from "react-native";
import io from "socket.io-client";
import { PermissionsAndroid, Platform } from "react-native";
import Geolocation from "@react-native-community/geolocation";
import { PERMISSIONS, request, check, RESULTS } from "react-native-permissions";
import { SOCKET_URL } from "../../config/apiConfig";
const socket = io(SOCKET_URL, {
  transports: ["websocket"],
  rejectUnauthorized: false,
  timeout: 10000,
  reconnection: true,
  reconnectionAttempts: 5,
  withCredentials: true,
});

const TakeABreak = () => {
  const [isOnline, setIsOnline] = useState(false);
  const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [0, 0] });
  const [locationUpdateMessage, setLocationUpdateMessage] = useState("null ");
  const lastUpdateTime = useRef(Date.now());
  const [newRideRequest, setNewRideRequest] = useState(null);
  const [driverRequested, setDriverRequested] = useState(null);
  const [priceRange, setPriceRange] = useState("");
  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState(null);
  const isOnlineRef = useRef(false);
  const [onlineStatus, setOnlineStatus] = useState(false);




const goOnline = async () => {
  console.log("🟢 Going Online...");
  socket.connect();
  console.log("🚀 Sending goOnline Event with Location:", userLocation);
  socket.emit("goOnline", userLocation, (response) => {
    console.log("📩 Server Response for goOnline:", response);
    if (response?.success) {
      isOnlineRef.current = true;
      // setIsOnline(true);
    } else {
      Alert.alert("Error", response?.message || "Failed to go online.");
    }
  });
};

const goOffline = async () => {
  console.log("🔴 Going Offline...");
  socket.emit("goOffline", userLocation, (response) => {
    console.log("📩 Server Response for goOffline:", response);
    if (response?.success) {
      isOnlineRef.current = false;
      // setIsOnline(false);
    } else {
      Alert.alert("Error", response?.message || "Failed to go offline.");
    }
  });
};
useEffect(() => {
  const handleStatusUpdate = (serverResponse) => {
    console.log("📩 Server Response for status update:", serverResponse);
    setLocationUpdateMessage(serverResponse?.message || "Status Updated");
  };

  socket.on("statusUpdated", handleStatusUpdate);

  return () => {
    socket.off("statusUpdated", handleStatusUpdate);
  };
}, []);



  useEffect(() => {
    if (!isOnline) {
      console.log("❌ Driver is offline. Skipping socket connection.");
      socket.disconnect();
      return;
    }

    socket.connect();

    // ✅ Automatically set driver online after connection
    const checkSocketConnection = setInterval(() => {
      if (socket.connected) {
        console.log("✅ Driver Socket Connected - Setting Online Status...");
        socket.emit("goOnline", userLocation);
        clearInterval(checkSocketConnection); // ✅ Stop checking once connected
      }
    }, 1000);

    // ✅ Listen for new ride requests
    socket.on("newRideRequest", (data) => {
      if (data.success) {
        console.log("📩 New Ride Request Received:", data?.ride);
        setNewRideRequest(data.ride);
      } else {
        console.log("❌ Failed to receive ride request");
      }
    });

    // ✅ Listen for when the driver is requested for a ride
    socket.on("driverRequested", (data) => {
      if (data.success) {
        console.log("🚖 Driver Requested for Ride:", data);
        Alert.alert(
          "Driver Requested",
          `You have been requested for a ride from ${data?.from} to ${data?.to}`
        );
        setDriverRequested(data);
      }
    });

    // ✅ Listen for online/offline status confirmation
    // socket.on("statusUpdated", (response) => {
    //   console.log(`🔄 Driver Status Updated: ${response.message}`);
    //   setLocationUpdateMessage(response.message);
    // });

    // ✅ Listen for location update confirmation
    socket.on("locationUpdated", (response) => {
      // console.log("✅ Location Update Received from Server:", response);
    });

    // Listen for ride activation (when the ride has been paid for)
    socket.on("newRideActivated", (serverResponse) => {
      console.log("Ride Activated:", serverResponse);
      Alert.alert(
        "Ride Activated",
        "The ride has been paid for. Please proceed to pick up the passenger/delivery or review scheduled ride details."
      );
    });

    socket.on("newRideActivated", (serverResponse) => {
      console.log("Ride Activated:", serverResponse);
      Alert.alert(
        "Ride Activated",
        "The ride has been paid for. Please proceed to pick up the passenger/delivery or review scheduled ride details."
      );
    });


    // Listen for when a passenger has already been assigned a driver
    socket.on("passengerFoundDriver", (serverResponse) => {
      console.log("Passenger Found Driver:", serverResponse);
      Alert.alert(
        "Driver Assigned",
        "A passenger has already found a driver for this ride."
      );
    });

    useEffect(() => {
      if (!location) {
        getCurrentLocation();
      }
    }, [location]);


    // ✅ Update location every 5 seconds when online
    let locationInterval;
    if (isOnline) {
      getCurrentLocation(); // ✅ Get initial location
      locationInterval = setInterval(() => {
        getCurrentLocation();
      }, 5000);
    }

    return () => {
      clearInterval(checkSocketConnection);
      clearInterval(locationInterval);
      socket.off("newRideRequest");
      socket.off("newRideActivated");
      socket.off("newRideActivated");
      socket.off("passengerFoundDriver");
      socket.off("statusUpdated");
      socket.off("locationUpdated");
      socket.off("driverRequested");
      socket.disconnect();
    };
  }, [isOnline]);


  const acceptRide = (rideId) => {
    if (!priceRange) {
      Alert.alert("Error", "Please enter a price range");
      return;
    }
    console.log("🚖 Accepting Ride Request:", rideId, priceRange);

    const data = { rideId, price: priceRange };
    socket.emit("acceptRideRequest", data);
  };

  const rejectRide = (rideId) => {
    socket.emit("cancelRideRequest", { rideId });
  };

  const requestLocationPermission = async () => {
    let permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    try {
      const result = await request(permission);
      if (result === RESULTS.GRANTED) {
        console.log("✅ Location permission granted");
        return true;
      } else {
        console.log("❌ Location permission denied");
        Alert.alert("Permission Denied", "Please enable location permissions in Settings.");
        return false;
      }
    } catch (error) {
      console.error("⚠️ Permission Error:", error);
      return false;
    }
  };


  const checkLocationServices = async () => {
    return new Promise((resolve) => {
      Geolocation.getCurrentPosition(
        () => resolve(true), // ✅ Location services enabled
        (error) => {
          if (error.code === 2) { // 🚨 Location provider disabled
            Alert.alert(
              "Location Services Disabled",
              "Please enable location services in Settings.",
              [{ text: "Go to Settings", onPress: () => Linking.openSettings() }, { text: "Cancel", style: "cancel" }]
            );
          }
          resolve(false);
        },
        { enableHighAccuracy: false, timeout: 5000 }
      );
    });
  };


  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log("⛔ Location permission denied. Exiting function.");
      return null;
    }

    const isLocationEnabled = await checkLocationServices();
    if (!isLocationEnabled) {
      console.log("⛔ Location services disabled. Exiting function.");
      return null;
    }

    console.log("📍 Fetching location...");

    return new Promise((resolve, reject) => {
      Geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newLocation = {type:'Point', coordinates: [longitude, latitude] };

          console.log("✅ Location Retrieved:", newLocation);
          setUserLocation(newLocation);
          // resolve(newLocation); // ✅ Return the location
        },
        (error) => {
          console.error("❌ Location Error:", error);
          Alert.alert("Location Error", error.message);
          reject(null); // ❌ Return `null` if failed
        },
        {
          enableHighAccuracy: false,
          timeout: 30000,
          maximumAge: 5000,
        }
      );
    });
  };


  useEffect(() => {
    getCurrentLocation();
  }, []);
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Driver Home</Text>
      <Text>Current Location:{userLocation.coordinates.join(", ")}  </Text>
      <Text>Server Response: {locationUpdateMessage}</Text>

      <TouchableOpacity style={styles.button} onPress={goOffline}>
        <Text style={styles.buttonText}>Take a Break </Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.button} onPress={goOnline}>
        <Text style={styles.buttonText}>Go Online</Text>
      </TouchableOpacity>

      {newRideRequest?.from && (
        <View style={styles.rideContainer}>
          <Text style={styles.rideTitle}>New ride request</Text>
          <Text>
            You have a new ride request from {newRideRequest?.passengerName} from{" "}
            <Text>{newRideRequest?.from}</Text> to{" "}
            <Text>
              {newRideRequest?.to?.map((i, idx) => (
                <Text key={idx}>{i.place}, </Text>
              ))}
            </Text>
          </Text>

          <TextInput
            style={styles.input}
            placeholder="Enter price range"
            onChangeText={setPriceRange}
            value={priceRange}
          />

          <TouchableOpacity style={styles.acceptButton} onPress={() => acceptRide(newRideRequest?.rideId)}>
            <Text style={styles.buttonText}>Accept Ride</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.rejectButton} onPress={() => rejectRide(newRideRequest?.rideId)}>
            <Text style={styles.buttonText}>Reject Ride</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, padding: 20, justifyContent: "center", alignItems: "center" },
  title: { fontSize: 22, fontWeight: "bold", marginBottom: 10 },
  button: { backgroundColor: "blue", padding: 10, borderRadius: 5, marginTop: 10 },
  buttonText: { color: "white", fontSize: 16 },
  rideContainer: { marginTop: 20, padding: 15, borderWidth: 1, borderColor: "#ddd", borderRadius: 10 },
  rideTitle: { fontSize: 18, fontWeight: "bold", marginBottom: 5 },
  input: { borderWidth: 1, borderColor: "#ddd", padding: 8, marginTop: 10, width: 200, borderRadius: 5 },
  acceptButton: { backgroundColor: "green", padding: 10, borderRadius: 5, marginTop: 10 },
  rejectButton: { backgroundColor: "red", padding: 10, borderRadius: 5, marginTop: 10 },
});


export default TakeABreak;
