import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { COLORS, FONTS, icons, images } from "../../constants";
import { useNavigation } from "@react-navigation/native";

const RatePassenger = () => {
  const navigation = useNavigation();
  const region = {
    latitude: 33.5186,
    longitude: -86.8104,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };

  const [rating, setRating] = useState(0);

  const handleRating = (star) => {
    setRating(star);
  };

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Top Section - Map */}
      <View style={styles.mapSection}>
        <MapView
          style={styles.map}
          initialRegion={region}
          showsUserLocation={true}
        >
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Pickup Location"
            description="AMLI 7th Street Station..."
          />
        </MapView>
        <View style={styles.priceTag}>
          <Text style={styles.priceText}>$ 5000</Text>
        </View>
      </View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.title}>Rate passenger</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Star Rating */}
        <View style={styles.ratingContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => handleRating(star)}
              activeOpacity={0.8}
            >
              <Text
                style={[
                  styles.star,
                  { color: star <= rating ? COLORS.primary : COLORS.grey },
                ]}
              >
                ★
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.feedbackText}>Your feedback is anonymous</Text>

        {/* Contact Passenger Section */}
        <TouchableOpacity
          style={styles.contactContainer}
          onPress={() => navigation.navigate("Review")}
        >
          <View style={styles.contactIconContainer}>
            <Image source={images.forgotbag} style={styles.contactIcon} />
          </View>
          <View style={styles.contactTextContainer}>
            <Text style={styles.contactTitle}>Forgot something?</Text>
            <Text style={styles.contactSubtitle}>Contact Passenger</Text>
          </View>
          <Image source={images.rightarrow} style={styles.arrowIcon} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  mapSection: {
    height: Dimensions.get("window").height * 0.5,
    width: "100%",
    position: "relative",
  },
  map: {
    height: "100%",
    width: "100%",
  },
  priceTag: {
    position: "absolute",
    top: 20,
    left: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    padding: 10,
  },
  priceText: {
    color: COLORS.white,
    fontWeight: "bold",
    fontSize: 18,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    borderBottomWidth: 0.5,
    borderColor:COLORS.border,
    paddingBottom:15
  },
  title: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  ratingContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 20,
    marginTop:30
  },
  star: {
    fontSize: 30,
    marginHorizontal: 5,
  },
  feedbackText: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
    marginBottom: 20,
  },
  contactContainer: {
    borderWidth: 0.5,
    borderColor: COLORS.border,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 10,
    width: "100%",
    marginTop: 20,
  },
  contactIconContainer: {
    backgroundColor: COLORS.primary,
    borderRadius: 50,
    padding: 10,
  },
  contactIcon: {
    width: 20,
    height: 20,
    tintColor: COLORS.white,
  },
  contactTextContainer: {
    flex: 1,
    marginLeft: 15,
  },
  contactTitle: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  contactSubtitle: {
    ...FONTS.body4,
    color: COLORS.primary,
  },
  arrowIcon: {
    width: 15,
    height: 15,
    tintColor: COLORS.grey,
  },
});

export default RatePassenger;
