import React, { useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Dimensions,
  Platform,
} from "react-native";
import MapView, { Marker } from "react-native-maps";
import { COLORS, FONTS, icons } from "../../constants";
import { useNavigation } from "@react-navigation/native";

const Review = () => {
  const navigation = useNavigation();
  const region = {
    latitude: 33.5186,
    longitude: -86.8104,
    latitudeDelta: 0.05,
    longitudeDelta: 0.05,
  };

  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");

  const handleSubmit = () => {
    if (rating === 0) {
      alert("Please select a rating.");
    } else {
      alert(`Thank you for your feedback! Rating: ${rating}, Comment: ${comment}`);
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Top Section - Map */}
      <View style={styles.mapSection}>
        <MapView
          style={styles.map}
          initialRegion={region}
          showsUserLocation={true}
        >
          <Marker
            coordinate={{
              latitude: region.latitude,
              longitude: region.longitude,
            }}
            title="Pickup Location"
            description="AMLI 7th Street Station..."
          />
        </MapView>
        <View style={styles.priceTag}>
          <Text style={styles.priceText}>$ 5000</Text>
        </View>
      </View>

      {/* Bottom Sheet */}
      <View style={styles.bottomSheet}>
        <View style={styles.header}>
          <Text></Text>
          <Text style={styles.title}>How was the ride?</Text>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.closeButton}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Star Rating */}
        <View style={styles.ratingContainer}>
          {[1, 2, 3, 4, 5].map((star) => (
            <TouchableOpacity
              key={star}
              onPress={() => setRating(star)}
              activeOpacity={0.8}
            >
              <Text
                style={[
                  styles.star,
                  { color: star <= rating ? COLORS.primary : COLORS.grey },
                ]}
              >
                ★
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <Text style={styles.feedbackText}>Your feedback is anonymous</Text>

        {/* Comment Section */}
        <TextInput
          style={styles.commentInput}
          placeholder="Leave a comment"
          placeholderTextColor={COLORS.grey}
          value={comment}
          onChangeText={(text) => setComment(text)}
          multiline
        />

        {/* Submit Button */}
        <TouchableOpacity style={styles.doneButton} onPress={handleSubmit}>
          <Text style={styles.doneButtonText}>Done</Text>
        </TouchableOpacity>

        <Text style={styles.footerText}>
          Your review and comment will be posted
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  mapSection: {
    height: Dimensions.get("window").height * 0.4,
    width: "100%",
    position: "relative",
  },
  map: {
    height: "100%",
    width: "100%",
  },
  priceTag: {
    position: "absolute",
    top: 20,
    left: 20,
    backgroundColor: COLORS.primary,
    borderRadius: 15,
    padding: 10,
  },
  priceText: {
    color: COLORS.white,
    fontWeight: "bold",
    fontSize: 18,
  },
  bottomSheet: {
    flex: 1,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    alignItems: "center",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginBottom:20
  },
  title: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  closeButton: {
    fontSize: 20,
    color: COLORS.grey,
  },
  ratingContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginVertical: 20,
  },
  star: {
    fontSize: 30,
    marginHorizontal: 5,
  },
  feedbackText: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
    marginBottom: 20,
  },
  commentInput: {
    width: "100%",
    height: 100,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 10,
    padding: 15,
    textAlignVertical: "top",
    marginBottom: 20,
  },
  doneButton: {
    width: "100%",
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    paddingVertical: 15,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  doneButtonText: {
    color: COLORS.white,
    ...FONTS.h3,
  },
  footerText: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: "center",
  },
});

export default Review;
