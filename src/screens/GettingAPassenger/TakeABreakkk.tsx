import React, {useEffect, useState, useRef} from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  Alert,
  StyleSheet,
  Linking,
  Image,
  Dimensions,
  FlatList,
} from 'react-native';
import io from 'socket.io-client';
import {PermissionsAndroid, Platform} from 'react-native';
import Geolocation from '@react-native-community/geolocation';
import {PERMISSIONS, request, check, RESULTS} from 'react-native-permissions';
import MapView, {Circle, Marker} from 'react-native-maps';
import {COLORS, FONTS, images, SIZES} from '../../constants';
import RBSheet from 'react-native-raw-bottom-sheet';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {useDispatch} from 'react-redux';
import {overwriteStore} from '../../../redux/ActionCreator';
import {SOCKET_URL, RIDE_ENDPOINTS} from '../../config/apiConfig';

// Define types
type RootStackParamList = {
  RidesDetail: {rideDetails: any};
  PassengerWaiting: {ride: any};
  SetRate: undefined;
};

type NavigationProp = StackNavigationProp<RootStackParamList>;

interface LocationType {
  type: string;
  coordinates: number[];
}

interface RideRequestType {
  rideId: string;
  passengerName: string;
  from: string;
  to: Array<{place: string}>;
}

interface RideType {
  rideId?: string;
  carDetails?: {
    carImgUrl: string;
  };
  rideType: string;
  from: string;
  to: Array<{place: string}>;
  kmDistance: string;
  charge: string;
  status: string;
}

const socket = io(SOCKET_URL, {
  transports: ['websocket'],
  rejectUnauthorized: false,
  timeout: 10000,
  reconnection: true,
  reconnectionAttempts: 5,
  withCredentials: true,
});
const {width} = Dimensions.get('window');

const TakeABreakkk: React.FC = () => {
  const [userLocation, setUserLocation] = useState({
    type: 'Point',
    coordinates: [0, 0],
  });
  const [locationUpdateMessage, setLocationUpdateMessage] = useState('null ');
  const lastUpdateTime = useRef(Date.now());
  const [priceRange, setPriceRange] = useState('');
  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState(null);
  const isOnlineRef = useRef(false);
  const [onlineStatus, setOnlineStatus] = useState(false);
  const [distance, setDistance] = useState(3);
  const [currentLocation, setCurrentLocation] = useState({
    latitude: userLocation?.coordinates[0] || 0,
    longitude: userLocation?.coordinates[1] || 0,
  });
  const rideRequestSheetRef = useRef<any>(null);
  const driverRequestedSheetRef = useRef<any>(null);
  const rideActivatedSheetRef = useRef<any>(null);
  const passengerFoundSheetRef = useRef<any>(null);
  const [rideRequests, setRideRequests] = useState<RideType[]>([]);
  const [rideActivated, setRideActivated] = useState<any>(null);
  const [passengerFound, setPassengerFound] = useState<any>(null);
  const [isOnline, setIsOnline] = useState(true);
  const [upcomingRides, setUpcomingRides] = useState<any[]>([]);
  const [pastRides, setPastRides] = useState<RideType[]>([]);
  const [newRideRequest, setNewRideRequest] = useState<RideRequestType | null>(
    null,
  );
  const [driverRequested, setDriverRequested] = useState<any>(null);

  const navigation = useNavigation<NavigationProp>();
  const dispatch = useDispatch();
  const fetchPastRides = async () => {
    setLoading(true);
    try {
      const response = await fetch(RIDE_ENDPOINTS.GET_DRIVER_RIDES, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Past Rides Response:', JSON.stringify(data, null, 2));

      if (response.ok) {
        dispatch(
          overwriteStore({
            name: 'DriverRides',
            value: data?.message?.rides || [],
          }),
        );
        setPastRides(data?.message?.rides || []);
      }
    } catch (error) {
      console.error('Error fetching past rides:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPastRides();
  }, []);

  const renderRideItem = ({item}: {item: RideType}) => (
    <TouchableOpacity
      style={styles.rideItem}
      onPress={() => navigation.navigate('RidesDetail', {rideDetails: item})}>
      <Image
        source={
          item.carDetails?.carImgUrl
            ? {uri: item.carDetails.carImgUrl}
            : images.CarSingle
        }
        style={styles.rideIcon as any}
      />
      <View style={styles.rideDetails}>
        <Text style={styles.rideType}>
          {item.rideType.charAt(0).toUpperCase() + item.rideType.slice(1)}
        </Text>
        <Text style={styles.rideDate}>
          {item.from.length > 10
            ? `${item.from.substring(0, 10)}...`
            : item.from}{' '}
          →
          {item.to[0]?.place.length > 10
            ? `${item.to[0]?.place.substring(0, 10)}...`
            : item.to[0]?.place}
        </Text>
        <Text style={styles.rideDate}>KM: {item.kmDistance}</Text>
        <Text style={styles.rideDate}>Charge: ${item.charge}</Text>
        <Text
          style={[
            styles.rideStatus,
            item.status === 'Canceled' && {color: 'green'},
          ]}>
          {item.status}
        </Text>
      </View>
      <TouchableOpacity style={styles.viewButton}>
        <Text style={styles.viewButtonText}>View</Text>
      </TouchableOpacity>
    </TouchableOpacity>
  );

  useEffect(() => {
    if (!location) {
      getCurrentLocation();
    }
  }, [location]);

  useEffect(() => {
    if (!isOnline) {
      console.log('❌ Driver is offline. Skipping socket connection.');
      socket.disconnect();
      return;
    }

    socket.connect();

    const checkSocketConnection = setInterval(() => {
      if (socket.connected) {
        console.log('✅ Driver Socket Connected - Setting Online Status...');
        socket.emit('goOnline', userLocation);
        clearInterval(checkSocketConnection); // ✅ Stop checking once connected
      }
    }, 1000);

    // ✅ Listen for new ride requests
    socket.on('newRideRequest', data => {
      if (data.success) {
        console.log('📩 New Ride Request Received:', data.ride);
        setNewRideRequest(data.ride);
        rideRequestSheetRef?.current?.open();
      } else {
        console.log('❌ Failed to receive ride request');
      }
    });

    socket.on('driverRequested', data => {
      if (data.success) {
        console.log('🚖 Driver Requested for Ride:', data);
        Alert.alert(
          'Driver Requested',
          `You have been requested for a ride from ${data?.from} to ${data?.to}`,
        );
        setDriverRequested(data);
        driverRequestedSheetRef.current?.open();
      }
    });

    socket.on('locationUpdated', (_: any) => {});

    socket.on('newRideActivated', serverResponse => {
      console.log('Ride Activated:', serverResponse);
      Alert.alert(
        'Ride Activated',
        'The ride has been paid for. Please proceed to pick up the passenger/delivery or review scheduled ride details.',
      );
      rideActivatedSheetRef.current?.open();
    });

    socket.on('newRideActivated', serverResponse => {
      console.log('Ride Activated:', serverResponse);
      Alert.alert(
        'Ride Activated',
        'The ride has been paid for. Please proceed to pick up the passenger/delivery or review scheduled ride details.',
      );
      setRideActivated(serverResponse);
      rideActivatedSheetRef.current?.open();
    });

    socket.on('passengerFoundDriver', serverResponse => {
      console.log('Passenger Found Driver:', serverResponse);
      Alert.alert(
        'Driver Assigned',
        'A passenger has already found a driver for this ride.',
      );
      setPassengerFound(serverResponse);
      passengerFoundSheetRef.current?.open();
    });

    // ✅ Update location every 5 seconds when online
    let locationInterval: NodeJS.Timeout | undefined;
    if (isOnline) {
      getCurrentLocation(); // ✅ Get initial location
      locationInterval = setInterval(() => {
        getCurrentLocation();
      }, 5000);
    }

    return () => {
      clearInterval(checkSocketConnection);
      clearInterval(locationInterval);
      socket.off('newRideRequest');
      socket.off('newRideActivated');
      socket.off('newRideActivated');
      socket.off('passengerFoundDriver');
      // socket.off("statusUpdated");
      socket.off('locationUpdated');
      socket.off('driverRequested');
      socket.disconnect();
    };
  }, [isOnline]);

  const acceptRide = (rideId: string) => {
    if (!priceRange) {
      Alert.alert('Error', 'Please enter a price range');
      return;
    }
    console.log('🚖 Accepting Ride Request:', rideId, priceRange);

    const data = {rideId, price: priceRange};
    socket.emit('acceptRideRequest', data);
  };

  const rejectRide = (rideId: string) => {
    socket.emit('cancelRideRequest', {rideId});
  };

  const requestLocationPermission = async () => {
    let permission;
    if (Platform.OS === 'ios') {
      permission = PERMISSIONS.IOS.LOCATION_WHEN_IN_USE;
    } else {
      permission = PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
    }

    try {
      const result = await request(permission);
      if (result === RESULTS.GRANTED) {
        // console.log("✅ Location permission granted");
        return true;
      } else {
        console.log('❌ Location permission denied');
        Alert.alert(
          'Permission Denied',
          'Please enable location permissions in Settings.',
        );
        return false;
      }
    } catch (error) {
      console.error('⚠️ Permission Error:', error);
      return false;
    }
  };

  const checkLocationServices = async () => {
    return new Promise(resolve => {
      Geolocation.getCurrentPosition(
        () => resolve(true), // ✅ Location services enabled
        error => {
          if (error.code === 2) {
            // 🚨 Location provider disabled
            Alert.alert(
              'Location Services Disabled',
              'Please enable location services in Settings.',
              [
                {text: 'Go to Settings', onPress: () => Linking.openSettings()},
                {text: 'Cancel', style: 'cancel'},
              ],
            );
          }
          resolve(false);
        },
        {enableHighAccuracy: false, timeout: 5000},
      );
    });
  };

  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      console.log('⛔ Location permission denied. Exiting function.');
      return null;
    }

    const isLocationEnabled = await checkLocationServices();
    if (!isLocationEnabled) {
      console.log('⛔ Location services disabled. Exiting function.');
      return null;
    }

    // console.log("📍 Fetching location...");

    return new Promise<any>((_, reject) => {
      Geolocation.getCurrentPosition(
        _position => {
          // const { latitude, longitude } = position.coords;

          const latitude = 4.2649;
          const longitude = 8.1577;
          const newLocation = {
            type: 'Point',
            coordinates: [longitude, latitude],
          };

          // console.log("✅ Location Retrieved:", newLocation);
          setUserLocation(newLocation);
          // resolve(newLocation); // ✅ Return the location
        },
        _error => {
          // console.error("❌ Location Error:", error);
          // Alert.alert("Location Error", error.message);
          reject(null); // ❌ Return `null` if failed
        },
        {
          enableHighAccuracy: false,
          timeout: 30000,
          maximumAge: 5000,
        },
      );
    });
  };

  const [region, setRegion] = useState({
    // latitude: userLocation?.coordinates[0],
    // longitude: userLocation?.coordinates[1],
    latitude: 33.5186,
    longitude: -86.8104,
    latitudeDelta: 0.1,
    longitudeDelta: 0.1,
  });

  const goOnline = async () => {
    console.log('🟢 Going Online...');

    socket.connect();
    console.log('🚀 Sending goOnline Event with Location:', userLocation);

    socket.emit('goOnline', userLocation, (response: any) => {
      console.log('📩 Server Response for goOnline:', response);

      if (response?.success) {
        console.log('✅ Successfully went online!');
        setIsOnline(true);
      } else {
        console.log('❌ Server did not confirm online status.');
        Alert.alert('Error', response?.message || 'Failed to go online.');
      }
    });
  };

  const goOffline = async () => {
    console.log('🔴 Going Offline...');

    socket.emit('goOffline', userLocation, (response: any) => {
      console.log('📩 Server Response for goOffline:', response);

      if (response?.success) {
        console.log('✅ Successfully went offline!');
        setIsOnline(false); // ✅ Ensure React updates the state
        socket.disconnect();
      } else {
        console.log('❌ Server did not confirm offline status.');
        Alert.alert('Error', response?.message || 'Failed to go offline.');
      }
    });
  };

  // ✅ Always Listen for Server Status Updates
  useEffect(() => {
    const handleStatusUpdate = (serverResponse: any) => {
      console.log('📩 Server Response for status update:', serverResponse);
      setLocationUpdateMessage(serverResponse?.message || 'Status Updated');
    };

    socket.on('statusUpdated', handleStatusUpdate);

    return () => {
      socket.off('statusUpdated', handleStatusUpdate);
    };
  }, []); // ✅ Runs once on mount

  return (
    <View style={styles.container}>
      {/* ✅ Online/Offline Status */}
      <View style={styles.onlineBadge}>
        <Text style={styles.onlineText}>
          {/* {locationUpdateMessage} */}
          {isOnline ? '🟢 Online' : '🔴 Offline'}
        </Text>
      </View>

      {/* ✅ Map Section */}

      <MapView
        style={styles.map}
        region={region} // ✅ Now uses updated `region`
        onRegionChangeComplete={reg => setRegion(reg)}>
        {/* ✅ Circle Showing Driver's Range */}
        <Circle
          center={{latitude: region.latitude, longitude: region.longitude}}
          radius={distance * 20}
          // fillColor="rgba(0, 123, 255, 0.2)"
          // strokeColor="rgba(0, 123, 255, 0.5)"
        />

        {/* ✅ Driver Marker */}
        <Marker coordinate={currentLocation} title="You are here">
          <Image source={images.CarSingle} style={styles.carIcon as any} />
        </Marker>
      </MapView>
      <View style={styles.infoSection}>
        <View style={styles.infoHeader}>
          <Text style={styles.infoText as any}>
            🚖 3 to 6 km wait in your area
          </Text>
          <Text style={styles.infoSubText as any}>
            📊 Average wait for 23 rides over the last 30 minutes
          </Text>

          <Text style={styles.infoText as any}>Nearby Ride Requests</Text>
        </View>

        {/* Using FlatList as the main scrollable container */}
        <FlatList
          data={pastRides}
          renderItem={renderRideItem}
          keyExtractor={(item, index) => item.rideId || index.toString()}
          ListHeaderComponent={<View />}
          ListEmptyComponent={
            <View style={styles.emptyState}>
              <Text style={styles.emptyTitle as any}>
                No ride yet, Do you want a ride
              </Text>
              <TouchableOpacity>
                <Text style={styles.emptyAction as any}>Get a passenger →</Text>
              </TouchableOpacity>
            </View>
          }
          ListFooterComponent={
            <>
              {rideRequests.length > 0 ? (
                rideRequests.map((ride, index) => {
                  const pickupLocation = ride.from || 'Unknown Pickup';
                  const destination =
                    ride.to && ride.to.length > 0
                      ? ride.to[0].place
                      : 'Unknown Destination';

                  return (
                    <TouchableOpacity
                      key={index}
                      style={styles.upcomingRideContainer}
                      onPress={() => {
                        if (ride.rideId) {
                          navigation.navigate('PassengerWaiting', {ride});
                        } else {
                          console.warn('⚠️ Missing rideId for this request:', ride);
                        }
                      }}>
                      <Text style={styles.upcomingRideTitle as any}>
                        🚖 New Ride Request
                      </Text>
                      <Text style={styles.upcomingRideSubtitle as any}>
                        📍 Pickup: {pickupLocation}
                      </Text>
                      <Text style={styles.upcomingRideSubtitle as any}>
                        📍 Destination: {destination}
                      </Text>
                    </TouchableOpacity>
                  );
                })
              ) : (
                <View style={styles.rideContainer}>
                  <Text style={styles.rideTitle}>❌ No new ride requests</Text>
                  <Text style={{color: COLORS.grey, fontSize: 14}}>
                    Please wait for a new request.
                  </Text>
                </View>
              )}
            </>
          }
          style={styles.flatListContainer}
        />
      </View>
      {/* {newRideRequest?.from && (
              <View style={styles.rideContainer}>
                <Text style={styles.rideTitle}>New ride request</Text>
                <Text>
                  You have a new ride request from {newRideRequest?.passengerName} from{" "}
                  <Text>{newRideRequest?.from}</Text> to{" "}
                  <Text>
                    {newRideRequest?.to?.map((i, idx) => (
                      <Text key={idx}>{i.place}, </Text>
                    ))}
                  </Text>
                </Text>

                <TextInput
                  style={styles.input}
                  placeholder="Enter price range"
                  onChangeText={setPriceRange}
                  value={priceRange}
                />

                <TouchableOpacity style={styles.acceptButton} onPress={() => acceptRide(newRideRequest?.rideId)}>
                  <Text style={styles.buttonText}>Accept Ride</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.rejectButton} onPress={() => rejectRide(newRideRequest?.rideId)}>
                  <Text style={styles.buttonText}>Reject Ride</Text>
                </TouchableOpacity>
              </View>
            )} */}

      <RBSheet
        ref={rideRequestSheetRef}
        height={350}
        openDuration={300}
        {...({closeOnDragDown: true} as any)}
        closeOnPressMask={true}
        customStyles={{container: styles.bottomSheet}}>
        <Text style={styles.sheetTitle}>🚕 New Ride Request</Text>
        <Text>Pickup: {newRideRequest?.from}</Text>

        {/* ✅ Convert destination array into a readable string */}
        <Text>
          Destination:{' '}
          {newRideRequest?.to
            ?.map((item: {place: string}) => item.place)
            .join(', ')}
        </Text>

        <View style={styles.rideContainer}>
          <Text style={styles.rideTitle}>New ride request</Text>
          <Text>
            You have a new ride request from {newRideRequest?.passengerName}{' '}
            from <Text>{newRideRequest?.from}</Text> to{' '}
            <Text>
              {newRideRequest?.to?.map((i: {place: string}, idx: number) => (
                <Text key={idx}>
                  {i.place}
                  {idx !== newRideRequest?.to.length - 1 ? ', ' : ''}
                </Text>
              ))}
            </Text>
          </Text>

          <TextInput
            style={styles.input}
            placeholder="Enter price range"
            onChangeText={setPriceRange}
            placeholderTextColor={COLORS.grey}
            value={priceRange}
          />

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => navigation.navigate('SetRate')}
              // onPress={handleAccept}
            >
              <Text style={styles.acceptButtonText}>Accept</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.rejectButton}
              onPress={() => navigation.goBack()}
              // onPress={handleReject}
            >
              <Text
                style={styles.rejectButtonText}
                onPress={() =>
                  newRideRequest?.rideId
                    ? rejectRide(newRideRequest.rideId)
                    : Alert.alert('Error', 'Ride ID is missing')
                }>
                Reject
              </Text>
            </TouchableOpacity>
          </View>

          <TouchableOpacity
            style={styles.acceptButton}
            onPress={() =>
              newRideRequest?.rideId
                ? acceptRide(newRideRequest.rideId)
                : Alert.alert('Error', 'Ride ID is missing')
            }>
            <Text style={styles.buttonText}>Accept Ride</Text>
          </TouchableOpacity>
        </View>
      </RBSheet>

      <RBSheet
        ref={driverRequestedSheetRef}
        height={250}
        openDuration={300}
        {...({closeOnDragDown: true} as any)}
        closeOnPressMask={true}
        customStyles={{container: styles.bottomSheet}}>
        <Text style={styles.sheetTitle}>🚖 Driver Requested</Text>
        <Text>Pickup: {driverRequested?.from}</Text>
        <Text>Destination: {driverRequested?.to}</Text>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => driverRequestedSheetRef.current?.close()}>
          <Text style={styles.buttonText}>Close</Text>
        </TouchableOpacity>
      </RBSheet>

      <RBSheet
        ref={rideActivatedSheetRef}
        height={200}
        openDuration={300}
        {...({closeOnDragDown: true} as any)}
        closeOnPressMask={true}
        customStyles={{container: styles.bottomSheet}}>
        <Text style={styles.sheetTitle}>✅ Ride Activated</Text>
        <Text>The ride has been paid for. Proceed with the pickup.</Text>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => rideActivatedSheetRef.current?.close()}>
          <Text style={styles.buttonText}>Close</Text>
        </TouchableOpacity>
      </RBSheet>

      <RBSheet
        ref={passengerFoundSheetRef}
        height={200}
        openDuration={300}
        {...({closeOnDragDown: true} as any)}
        closeOnPressMask={true}
        customStyles={{container: styles.bottomSheet}}>
        <Text style={styles.sheetTitle}>🚖 Passenger Found Driver</Text>
        <Text>A passenger has already found a driver.</Text>
        <TouchableOpacity
          style={styles.closeButton}
          onPress={() => passengerFoundSheetRef.current?.close()}>
          <Text style={styles.buttonText}>Close</Text>
        </TouchableOpacity>
      </RBSheet>
    </View>
  );
};

const styles = StyleSheet.create({
  rideItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 7,
    padding: 15,
    marginBottom: 15,
  },
  rideIcon: {
    width: 50,
    height: 50,
    marginRight: 15,
    resizeMode: 'contain',
  },
  rideDetails: {
    flex: 1,
  },
  rideType: {
    fontFamily: 'Poppins-Regular',
    fontSize: 15,
    lineHeight: 20,
    color: COLORS.black,
  },
  rideDate: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    lineHeight: 18,
    color: COLORS.black,
  },
  rideStatus: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    lineHeight: 18,
    color: COLORS.red,
  },
  viewButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingHorizontal: 15,
    paddingVertical: 5,
  },
  viewButtonText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    lineHeight: 18,
    color: COLORS.white,
  } as any,
  emptyState: {
    alignItems: 'center',
    marginTop: 50,
  },
  emptyTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 10,
  },
  emptyAction: {
    ...FONTS.body3,
    color: COLORS.primary,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  acceptButton: {
    flex: 1,
    marginRight: 10,
    backgroundColor: COLORS.primary,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 15,
  },
  acceptButtonText: {
    color: COLORS.white,
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 20,
  } as any,
  rejectButton: {
    flex: 1,
    marginLeft: 10,
    backgroundColor: COLORS.white,
    borderWidth: 0.5,
    borderColor: COLORS.red,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    paddingVertical: 0,
  },
  rejectButtonText: {
    color: COLORS.white,
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 20,
  } as any,
  note: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    lineHeight: 18,
    textAlign: 'center',
    color: COLORS.grey,
  } as any,
  bottomSheet: {
    overlayColor: 'rgba(0,0,0,0.5)',
    padding: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  sheetTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  closeButton: {
    marginTop: 15,
    padding: 10,
    backgroundColor: 'blue',
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
  },
  title: {fontSize: 22, fontWeight: 'bold', marginBottom: 10},
  button: {
    backgroundColor: 'blue',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  buttonTextStyle: {color: 'white', fontSize: 16},
  rideContainer: {
    marginTop: 20,
    padding: 15,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
  },
  rideTitle: {fontSize: 18, fontWeight: 'bold', marginBottom: 5},
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    padding: 8,
    marginTop: 10,
    width: 200,
    borderRadius: 5,
  },
  acceptButtonPrimary: {
    backgroundColor: 'green',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  rejectButtonPrimary: {
    backgroundColor: 'red',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  rideRequestContainer: {
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 10,
    marginVertical: 10,
    elevation: 5,
  },
  rideRequestTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: 5,
  },
  rideRequestText: {
    fontSize: 14,
    color: COLORS.black,
  },
  acceptButtonSecondary: {
    marginTop: 10,
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
  },
  acceptButtonTextStyle: {
    color: COLORS.white,
    fontWeight: 'bold',
  },

  onlineBadge: {
    position: 'absolute',
    top: 10,
    left: '50%',
    transform: [{translateX: -50}],
    backgroundColor: 'green',
    padding: 8,
    borderRadius: 10,
    elevation: 5,
    zIndex: 1,
  },
  onlineText: {
    fontSize: 14,
    fontWeight: 'bold',
    color: COLORS.white,
  },
  containerr: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: COLORS.white,
  },
  filterButton: {
    width: 50,
    height: 50,
    backgroundColor: COLORS.light_grey,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  filterIcon: {
    width: 60,
    height: 60,
    resizeMode: 'contain',
  },
  passengerButton: {
    flex: 1,
    marginLeft: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.primary,
    paddingVertical: 13,
    borderRadius: 10,
  },
  passengerButtonText: {
    color: COLORS.white,
    fontSize: 16,
    fontWeight: '600',
    marginRight: 10,
  },
  carIcon: {
    width: 24,
    height: 24,
    resizeMode: 'contain',
  },
  container: {
    flex: 1,
  },
  map: {
    width: '100%',
    height: '40%',
  },
  budgetBadge: {
    position: 'absolute',
    top: 10,
    left: width / 2 - 50,
    backgroundColor: COLORS.primary,
    padding: 10,
    borderRadius: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetText: {
    color: COLORS.white,
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    lineHeight: 18,
  } as any,
  budgetAmount: {
    color: COLORS.white,
    ...FONTS.h4,
    marginLeft: 5,
  },

  distanceContainer: {
    position: 'absolute',
    top: '30%',
    left: 20,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 15,
    elevation: 5,
  },
  distanceButton: {
    padding: 10,
    // backgroundColor: COLORS.grey,
    borderRadius: 5,
  },
  distanceText: {
    ...FONTS.body3,
  },
  distanceValue: {
    ...FONTS.h4,
    marginHorizontal: 10,
    color: COLORS.black,
  },
  infoSection: {
    padding: 20,
    backgroundColor: COLORS.light_blue,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    marginTop: -30,
    flex: 1,
  },
  infoHeader: {
    marginBottom: 15,
  },
  flatListContainer: {
    flex: 1,
  },
  infoText: {
    ...FONTS.body3,
    marginBottom: 5,
  },
  infoSubText: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 20,
  },
  upcomingRideContainer: {
    padding: 10,
    backgroundColor: COLORS.white,
    borderRadius: 7,
    borderWidth: 0.5,
    borderColor: COLORS.light_grey,
    marginBottom: 20,
  },
  upcomingRideTitle: {
    fontFamily: 'Poppins-Regular',
    fontSize: 11,
    lineHeight: 16,
  } as any,
  upcomingRideSubtitle: {
    marginTop: 0,
    ...FONTS.body2,
    color: COLORS.black,
    fontSize: 10,
  },
  getPassengerButton: {
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  getPassengerText: {
    color: COLORS.white,
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 20,
  } as any,
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingVertical: 15,
    backgroundColor: COLORS.white,
  },
  footerItem: {
    alignItems: 'center',
  },
  footerIcon: {
    width: 25,
    height: 25,
    marginBottom: 5,
  },
  footerText: {
    fontSize: 12,
    color: COLORS.grey,
  },
});

export default TakeABreakkk;

// import React, { useEffect, useState, useRef } from "react";
// import {
//   View,
//   Text,
//   TouchableOpacity,
//   TextInput,
//   Alert,
//   StyleSheet,
// } from "react-native";
// import io from "socket.io-client";
// import Geolocation from "react-native-geolocation-service";

// const SOCKET_URL = "https://inride-server.onrender.com/driver";
// const socket = io(SOCKET_URL, {
//   transports: ["websocket"],
//   withCredentials: true,
// });

// const TakeABreak = () => {
//   const [isOnline, setIsOnline] = useState(false); // Default: Driver is offline
//   const [userLocation, setUserLocation] = useState({ type: "Point", coordinates: [] });
//   const [locationUpdateMessage, setLocationUpdateMessage] = useState("");
//   const lastUpdateTime = useRef(Date.now());
//   const [newRideRequest, setNewRideRequest] = useState(null);
//   const [driverRequested, setDriverRequested] = useState(null);
//   const [priceRange, setPriceRange] = useState("");

//   useEffect(() => {
//     if (!isOnline) {
//       console.log("❌ Driver is offline. Skipping socket connection.");
//       socket.disconnect();
//       return;
//     }

//     socket.connect();

//     // ✅ Automatically set driver online after connection
//     const checkSocketConnection = setInterval(() => {
//       if (socket.connected) {
//         console.log("✅ Driver Socket Connected - Setting Online Status...");
//         socket.emit("goOnline", userLocation);
//         clearInterval(checkSocketConnection); // ✅ Stop checking once connected
//       }
//     }, 1000);

//     // ✅ Listen for new ride requests
//     socket.on("newRideRequest", (data) => {
//       if (data.success) {
//         console.log("📩 New Ride Request Received:", data.ride);
//         setNewRideRequest(data.ride);
//       } else {
//         console.log("❌ Failed to receive ride request");
//       }
//     });

//     // ✅ Listen for when the driver is requested for a ride
//     socket.on("driverRequested", (data) => {
//       if (data.success) {
//         console.log("🚖 Driver Requested for Ride:", data);
//         setDriverRequested(data);
//       }
//     });

//     // ✅ Listen for online/offline status confirmation
//     socket.on("statusUpdated", (response) => {
//       console.log(`🔄 Driver Status Updated: ${response.message}`);
//       setLocationUpdateMessage(response.message);
//     });

//     // ✅ Listen for location update confirmation
//     socket.on("locationUpdated", (response) => {
//       // console.log("✅ Location Update Received from Server:", response);
//     });

//     // ✅ Update location every 5 seconds when online
//     let locationInterval;
//     if (isOnline) {
//       getCurrentLocation(); // ✅ Get initial location
//       locationInterval = setInterval(() => {
//         getCurrentLocation();
//       }, 5000);
//     }

//     return () => {
//       clearInterval(checkSocketConnection);
//       clearInterval(locationInterval);
//       socket.off("newRideRequest");
//       socket.off("statusUpdated");
//       socket.off("locationUpdated");
//       socket.off("driverRequested");
//       socket.disconnect();
//     };
//   }, [isOnline]);

//   /** ✅ Toggle Online/Offline Status */
//   const toggleOnlineStatus = () => {
//     if (isOnline) {
//       console.log("🔴 Going Offline...",userLocation);
//       socket.emit("goOffline", userLocation);
//       socket.disconnect();
//     } else {
//       console.log("🟢 Going Online...",userLocation);
//       socket.connect();
//       socket.emit("goOnline", userLocation);
//     }
//     setIsOnline((prevStatus) => !prevStatus);
//   };

//   /** ✅ Function to Get Driver's Current Location */
//   const getCurrentLocation = async () => {
//     Geolocation.getCurrentPosition(
//       (position) => {
//         // const { latitude, longitude } = position.coords;
// const longitude = 7.818791099999999;
// const latitude = 3.908815999999999;

//         const newLocation = {
//           type: "Point",
//           coordinates: [longitude, latitude], // ✅ GeoJSON format
//         };

//         setUserLocation(newLocation);

//         // console.log("📍 Updating Driver Location:", newLocation);
//         if (isOnline) {
//           socket.emit("updateLocation", newLocation);
//         }
//       },
//       (error) => console.log("❌ Location Error:", error),
//       { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
//     );
//   };

//   /** ✅ Accept Ride */
//   const acceptRide = (rideId) => {
//     if (!priceRange) {
//       Alert.alert("Error", "Please enter a price range");
//       return;
//     }
//     console.log("🚖 Accepting Ride Request:", rideId, priceRange);

//     const data = { rideId, price: priceRange };
//     socket.emit("acceptRideRequest", data);
//   };

//   /** ✅ Reject Ride */
//   const rejectRide = (rideId) => {
//     socket.emit("cancelRideRequest", { rideId });
//   };

//   return (
//     <View style={styles.container}>
//       <Text style={styles.title}>Driver Home</Text>
//       <Text>Current Location: {userLocation.coordinates.join(", ")}</Text>
//       <Text>Server Response: {locationUpdateMessage}</Text>

//       <TouchableOpacity style={styles.button} onPress={toggleOnlineStatus}>
//         <Text style={styles.buttonText}>{isOnline ? "Take a Break" : "Go Online"}</Text>
//       </TouchableOpacity>

//       {newRideRequest?.from && (
//         <View style={styles.rideContainer}>
//           <Text style={styles.rideTitle}>New ride request</Text>
//           <Text>
//             You have a new ride request from {newRideRequest?.passengerName} from{" "}
//             <Text>{newRideRequest?.from}</Text> to{" "}
//             <Text>
//               {newRideRequest?.to?.map((i, idx) => (
//                 <Text key={idx}>{i.place}, </Text>
//               ))}
//             </Text>
//           </Text>

//           <TextInput
//             style={styles.input}
//             placeholder="Enter price range"
//             onChangeText={setPriceRange}
//             value={priceRange}
//           />

//           <TouchableOpacity style={styles.acceptButton} onPress={() => acceptRide(newRideRequest?.rideId)}>
//             <Text style={styles.buttonText}>Accept Ride</Text>
//           </TouchableOpacity>
//           <TouchableOpacity style={styles.rejectButton} onPress={() => rejectRide(newRideRequest?.rideId)}>
//             <Text style={styles.buttonText}>Reject Ride</Text>
//           </TouchableOpacity>
//         </View>
//       )}
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: { flex: 1, padding: 20, justifyContent: "center", alignItems: "center" },
//   title: { fontSize: 22, fontWeight: "bold", marginBottom: 10 },
//   button: { backgroundColor: "blue", padding: 10, borderRadius: 5, marginTop: 10 },
//   buttonText: { color: "white", fontSize: 16 },
//   rideContainer: { marginTop: 20, padding: 15, borderWidth: 1, borderColor: "#ddd", borderRadius: 10 },
//   rideTitle: { fontSize: 18, fontWeight: "bold", marginBottom: 5 },
//   input: { borderWidth: 1, borderColor: "#ddd", padding: 8, marginTop: 10, width: 200, borderRadius: 5 },
//   acceptButton: { backgroundColor: "green", padding: 10, borderRadius: 5, marginTop: 10 },
//   rejectButton: { backgroundColor: "red", padding: 10, borderRadius: 5, marginTop: 10 },
// });

// export default TakeABreak;
