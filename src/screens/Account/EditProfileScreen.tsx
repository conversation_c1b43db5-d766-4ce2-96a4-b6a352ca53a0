// import React, { useEffect, useState } from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   Image,
//   TextInput,
//   TouchableOpacity,
// } from 'react-native';
// import { COLORS, FONTS, SIZES } from '../../constants/theme';
// import { images } from '../../constants';
// import { useNavigation } from '@react-navigation/native';

// const EditProfileScreen = () => {
//   const navigation = useNavigation();
//   const [firstName, setFirstName] = useState('');
//   const [lastName, setLastName] = useState('');
//   const [phoneNumber, setPhoneNumber] = useState('');
//   const [profileImage, setProfileImage] = useState('');
//   useEffect(() => {
//     fetchUserProfile();
//   }, []);

//   const fetchUserProfile = async () => {
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/driver/profile/getProfile', {
//         method: 'GET',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//       });

//       const data = await response.json();
//       console.log('Profile Data:', data?.data);

//       if (response.ok) {
//         setFirstName(data?.data?.firstName || '');
//         setLastName(data?.data?.lastName || '');
//         setPhoneNumber(data?.data?.mobileNumber || '');
//         setProfileImage(data?.data?.profileImg || images.profile);
//       }
//     } catch (error) {
//       console.error('Error fetching profile:', error);
//     }
//   };

//   const handleSave = async () => {
//     try {
//       const response = await fetch('https://inride-server.onrender.com/api/driver/profile/updateProfile', {
//         method: 'POST',
//         credentials: 'include',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({ firstName, lastName }),
//       });

//       const data = await response.json();
//       console.log('Profile Update Response:', data);

//       if (response.ok) {
//         alert('Profile updated successfully!');
//         navigation.navigate('ProfileScreen');
//       }
//     } catch (error) {
//       console.error('Error updating profile:', error);
//     }
//   };

//   return (
//     <View style={styles.container}>
//       {/* Header */}
//       <View style={styles.headerContainer}>
//         <TouchableOpacity onPress={() => navigation.goBack()}>
//           <Image source={images.goback} style={styles.backButton} />
//         </TouchableOpacity>
//         <Text style={styles.header}>Edit Profile</Text>
//         <Text></Text>
//       </View>

//     {/* Profile Picture */}
// <View style={styles.profileImageContainer}>
//   <Image source={{ uri: profileImage || images.profile }} style={styles.profileImage} />
//   <TouchableOpacity style={styles.editOverlay}>
//     <Text style={styles.editText}>Edit</Text>
//   </TouchableOpacity>
// </View>

//       {/* Input Fields */}
//       <View style={styles.inputContainer}>
//         <Text style={styles.inputLabel}>First name*</Text>
//         <TextInput
//           style={styles.input}
//           placeholder="Enter your First name"
//           placeholderTextColor={COLORS.grey}
//           value={firstName}
//           onChangeText={setFirstName}
//         />
//       </View>

//       <View style={styles.inputContainer}>
//         <Text style={styles.inputLabel}>Last name*</Text>
//         <TextInput
//           style={styles.input}
//           placeholder="Enter your Last name"
//           placeholderTextColor={COLORS.grey}
//           value={lastName}
//           onChangeText={setLastName}
//         />
//       </View>

//       <View style={styles.inputContainer}>
//         <Text style={styles.inputLabel}>Phone Number*</Text>
//         <TextInput
//           style={[styles.input, { backgroundColor: COLORS.grey, opacity: 0.5 }]}
//           placeholder="Enter your Phone number"
//           placeholderTextColor={COLORS.grey}
//           value={phoneNumber}
//           editable={false}
//           keyboardType="phone-pad"
//         />
//       </View>

//       {/* Save Button */}
//       <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
//         <Text style={styles.saveButtonText}>Save</Text>
//       </TouchableOpacity>
//     </View>
//   );
// };


// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: COLORS.light_blue,
//     padding: SIZES.padding,
//   },
//   headerContainer: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     marginBottom: 20,
//   },
//   header: {
//     ...FONTS.h3,
//     color: COLORS.black,
//   },
//   backButton: {
//     width: 25,
//     height: 25,
//   },
//   profileImageContainer: {
//     alignItems: 'center',
//     backgroundColor: COLORS.white,
// paddingVertical: 10,
//     marginBottom: 20,
//   },
//   profileImage: {
//     width: 100,
//     height: 100,
//     borderRadius: 50,
//     zIndex: 1,
//   },
//   editOverlay: {
//     position: 'absolute',
//     bottom: 0,
//     // backgroundColor: COLORS.white,
//     width: '100%',
//     alignItems: 'center',
//     paddingVertical: 5,
//     borderBottomLeftRadius: 50,
//     borderBottomRightRadius: 50,
//   },
//   editText: {
//     ...FONTS.body4,
//     color: COLORS.white,
//   },
//   inputContainer: {
//     marginBottom: 20,
//   },
//   inputLabel: {
//     ...FONTS.body4,
//     color: COLORS.black,
//     marginBottom: 5,
//   },
//   input: {
//     backgroundColor: COLORS.white,
//     borderRadius: SIZES.radius,
//     padding: 15,
//     ...FONTS.body3,
//     fontSize: 14,
//     color: COLORS.black,
//   },
//   saveButton: {
//     backgroundColor: COLORS.primary,
//     borderRadius: SIZES.radius,
//     padding: 15,
//     alignItems: 'center',
//   },
//   saveButtonText: {
//     ...FONTS.body3,
//     color: COLORS.white,
//   },
// });

// export default EditProfileScreen;


import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { overwriteStore } from '../../../redux/ActionCreator';
import { useDispatch } from 'react-redux';
import { API_BASE_URL } from '../../config/apiConfig';

const EditProfileScreen = () => {
  const navigation = useNavigation();
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [profileImage, setProfileImage] = useState('');
const dispatch = useDispatch();
 
  const fetchUserProfile = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/driver/profile/getProfile`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Profile Data:', data?.data);

      if (response.ok) {
        setFirstName(data?.data?.firstName || '');
        setLastName(data?.data?.lastName || '');
        setPhoneNumber(data?.data?.mobileNumber || '');
        setProfileImage(data?.data?.profileImg || images.profile);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };

  const handleSave = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/driver/profile/updateProfile`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ firstName, lastName }),
      });

      const data = await response.json();
      console.log('Profile Update Response:', data);

      if (response.ok) {
        alert('Profile updated successfully!');
        // Store updated profile data consistently under 'GetProfile' key
        dispatch(overwriteStore({ name: 'GetProfile', value: data?.data || {} }));
        navigation.navigate('ProfileScreen');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };
  
 useEffect(() => {
    fetchUserProfile();
  }, []);

  return (
    <View style={styles.container}>
           {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Edit Profile</Text>
        <Text></Text>
      </View>

    {/* Profile Picture */}
<View style={styles.profileImageContainer}>
<Image 
  source={profileImage ? { uri: profileImage } : images.profile} 
  style={styles.profileImage} 
/>

  <TouchableOpacity style={styles.editOverlay}>
    <Text style={styles.editText}>Edit</Text>
  </TouchableOpacity>
</View>

      {/* Input Fields */}
      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>First name*</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your First name"
          placeholderTextColor={COLORS.grey}
          value={firstName}
          onChangeText={setFirstName}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Last name*</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter your Last name"
          placeholderTextColor={COLORS.grey}
          value={lastName}
          onChangeText={setLastName}
        />
      </View>

      <View style={styles.inputContainer}>
        <Text style={styles.inputLabel}>Phone Number*</Text>
        <TextInput
          style={[styles.input, { backgroundColor: COLORS.grey, opacity: 0.5 }]}
          placeholder="Enter your Phone number"
          placeholderTextColor={COLORS.grey}
          value={phoneNumber}
          editable={false}
          keyboardType="phone-pad"
        />
      </View>

      {/* Save Button */}
      <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
        <Text style={styles.saveButtonText}>Save</Text>
      </TouchableOpacity>
    </View>
  );
};


const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  profileImageContainer: {
    alignItems: 'center',
    backgroundColor: COLORS.white,
paddingVertical: 10,
    marginBottom: 20,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    zIndex: 1,
  },
  editOverlay: {
    position: 'absolute',
    bottom: 0,
    // backgroundColor: COLORS.white,
    width: '100%',
    alignItems: 'center',
    paddingVertical: 5,
    borderBottomLeftRadius: 50,
    borderBottomRightRadius: 50,
  },
  editText: {
    ...FONTS.body4,
    color: COLORS.white,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  input: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: 15,
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  saveButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  saveButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
});

export default EditProfileScreen;
