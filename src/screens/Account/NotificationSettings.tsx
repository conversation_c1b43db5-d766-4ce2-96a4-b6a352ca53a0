


import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
  ImageSourcePropType,
} from 'react-native';
import { COLORS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useDispatch } from 'react-redux';
import { overwriteStore } from '../../../redux/ActionCreator';
import AsyncStorage from '@react-native-async-storage/async-storage';
import messaging from '@react-native-firebase/messaging';
import { API_BASE_URL } from '../../config/apiConfig';

// Define types for navigation
type RootStackParamList = {
  NotificationSettings: undefined;
};

type NotificationScreenNavigationProp = StackNavigationProp<RootStackParamList>;

// Define types for user data
interface UserData {
  email?: string;
  driverId?: string;
  pushNotification?: boolean;
  mailNotification?: boolean;
  [key: string]: any; // Allow for other properties
}

const NotificationSettings = () => {
  const navigation = useNavigation<NotificationScreenNavigationProp>();
  const dispatch = useDispatch();
  const [userDetails, setUserDetails] = useState<UserData | null>(null);

  const [pushNotification, setPushNotification] = useState(false);
  const [emailNotification, setEmailNotification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const subscribeToPushNotifications = async (isEnabled: boolean) => {
    if (!isEnabled) return; // Do nothing if notifications are turned off

    setIsLoading(true);
    try {
      let userData = userDetails;

      if (!userData) {
        const storedUserData = await AsyncStorage.getItem("UserProfile");
        if (storedUserData) {
          userData = JSON.parse(storedUserData) as UserData;
        }
      }

      if (!userData) {
        Alert.alert("Error", "User data not found.");
        return;
      }

      // Retrieve the device token
      const deviceToken = await getDeviceToken();

      if (!deviceToken) {
        Alert.alert("Error", "Failed to retrieve device token.");
        return;
      }

      const requestBody = {
        email: userData?.email,
        accountId: userData?.driverId,
        accountType: "driver",
        data: {
          deviceToken: deviceToken, // Send device token
        },
      };

      console.log("Push Notification Subscription Request:", requestBody);

      const response = await fetch(
        `${API_BASE_URL}/pushNotification/saveSubscription`,
        {
          method: "POST",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      const data = await response.json();
      console.log("Push Notification Subscription Response:", data);

      if (response.ok) {
        Alert.alert("Success", "Subscribed to push notifications successfully!");
        setPushNotification(true);
      } else {
        Alert.alert("Error", "Subscription failed.");
      }
    } catch (error) {
      console.error("Error subscribing to push notifications:", error);
      Alert.alert("Error", "An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  const subscribeToEmailNotifications = async (isEnabled: boolean) => {
    if (!isEnabled) return;
    try {
      let userData = userDetails;
      if (!userData) {
        const storedUserData = await AsyncStorage.getItem("UserProfile");
        if (storedUserData) {
          userData = JSON.parse(storedUserData) as UserData;
        }
      }
      if (!userData) {
        Alert.alert("Error", "User data not found.");
        return;
      }

      // Retrieve the device token
      const deviceToken = await getDeviceToken();

      if (!deviceToken) {
        Alert.alert("Error", "Failed to retrieve device token.");
        return;
      }
      const requestBody = {
        email: userData?.email,
        accountId: userData?.driverId,
        accountType: "driver",
        data: {
          deviceToken: deviceToken, // Send device token
        },
      };
      setIsLoading(true);
      console.log("Email Notification Subscription Request:", requestBody);

      const response = await fetch(
        `${API_BASE_URL}/pushNotification/subscribeEmail`,
        {
          method: "POST",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      const data = await response.json();
      console.log("Email Notification Subscription Response:", data);

      if (response.ok) {
       Alert.alert("Success", "Subscribed to email notifications successfully!");
        setEmailNotification(true);
      } else {
        Alert.alert("Error", "Email subscription failed.");
      }
    } catch (error) {
      console.error("Error subscribing to email notifications:", error);
      Alert.alert("Error", "An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };


  useEffect(() => {
    fetchUserSettings();
    getDeviceToken();
  }, []);

  const fetchUserSettings = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/driver/profile/getProfile`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Check if response is JSON before parsing
      const contentType = response.headers.get('content-type');
      const isJson = contentType && contentType.includes('application/json');

      if (!isJson) {
        const textResponse = await response.text();
        console.error('Non-JSON response received:', textResponse);
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();
      console.log('Profile Settings Response:', data?.data);

      if (response.ok) {
        // Store profile data consistently under 'GetProfile' key
        dispatch(overwriteStore({ name: 'GetProfile', value: data?.data || {} }));
        setUserDetails(data?.data || {}); // Save user details in state
        await AsyncStorage.setItem("UserProfile", JSON.stringify(data?.data || {})); // Persist in AsyncStorage

        setPushNotification(data?.data?.pushNotification || false);
        setEmailNotification(data?.data?.mailNotification || false);
      }
    } catch (error) {
      console.error('Error fetching profile settings:', error);
    }
  };


  const getDeviceToken = async () => {
    try {
      await messaging().requestPermission(); // Request permission for notifications
      const token = await messaging().getToken();
      console.log("Device Token:", token);
      return token;
    } catch (error) {
      console.error("Error getting device token:", error);
      return null; // Return null in case of an error
    }
  };


  return (
    <View style={styles.container}>
      {/* Header */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {isLoading && (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}

      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton as any} />
        </TouchableOpacity>
        <Text style={styles.header}>Notification</Text>
        <Text></Text>
      </View>

      {/* Notification Settings */}
      <View style={styles.settingRow}>
        <View style={styles.settingLabelContainer}>
          <Image source={images.noti1} style={styles.icon as any} />
          <Text style={styles.settingLabel}>Push Notification</Text>
        </View>
        <Switch
          style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
          value={pushNotification}
          onValueChange={(value) => {
            setPushNotification(value);
            subscribeToPushNotifications(value);
          }}
          trackColor={{ false: COLORS.white, true: COLORS.primary }}
          thumbColor={pushNotification ? COLORS.white : COLORS.white}
        />
      </View>

      <View style={styles.settingRow}>
        <View style={styles.settingLabelContainer}>
          <Image source={images.notificationtext} style={styles.icon as any} />
          <Text style={styles.settingLabel}>Email Notification</Text>
        </View>
        <Switch
          style={{ transform: [{ scaleX: 0.8 }, { scaleY: 0.8 }] }}
          value={emailNotification}
          onValueChange={(value) => {
            setEmailNotification(value);
            subscribeToEmailNotifications(value);
          }}
          trackColor={{ false: COLORS.white, true: COLORS.primary }}
          thumbColor={emailNotification ? COLORS.white : COLORS.white}
        />

      </View>



    </View>
  );
};

const styles = StyleSheet.create({
  saveButton: {
    marginTop: 20,
    backgroundColor: COLORS.primary,
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  saveButtonText: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 20,
    fontWeight: '600' as any,
    color: COLORS.white,
  } as any,
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 20,
    fontWeight: '600' as any,
    color: COLORS.black,
  } as any,
  backButton: {
    width: 25,
    height: 25,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  settingLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  settingLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400' as any,
    color: COLORS.black,
  } as any,
  loaderContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },

});

export default NotificationSettings;




