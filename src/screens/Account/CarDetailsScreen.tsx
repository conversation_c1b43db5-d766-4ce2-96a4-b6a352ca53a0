import React, {useState} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  ScrollView,
  ActivityIndicator,
  StyleSheet,
  Platform,
} from 'react-native';
import {COLORS, FONTS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useDispatch} from 'react-redux';
import {overwriteStore} from '../../../redux/ActionCreator';
import {API_BASE_URL} from '../../config/apiConfig';

const CarDetailsScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const dispatch = useDispatch();
  const {car} = route.params;
  console.log('car:', car);

  const [form, setForm] = useState({
    registrationNumber: car.registrationNumber,
    year: car.year,
    model: car.model,
    color: car.color,
    noOfSeats: car.noOfSeats.toString(),
    carId: car._id,
    carImgUrl: car.carImgUrl,
  });

  const [isEditable, setIsEditable] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleInputChange = (field, value) => {
    setForm({...form, [field]: value});
  };

  const saveCarDetails = async () => {
    setLoading(true);
    console.log('form:', form);

    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/car/updateCarDetails`,
        {
          credentials: 'include',
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(form),
        },
      );
      const data = await response.json();
      console.log('Car details updated:', data);

      if (response.ok) {
        dispatch(
          overwriteStore({name: 'GetCarDetails', value: data?.message?.cars}),
        );
        navigation.navigate('MyCarScreen');
      }
    } catch (error) {
      console.error('Error updating car details:', error);
    }
    setLoading(false);
  };

  return (
    <ScrollView style={styles.container}>
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      {loading && (
        <View style={styles.overlay}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}
      <View style={{paddingHorizontal: 20}}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image source={images.goback} style={styles.goBackIcon} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Car details</Text>
        </View>

        <Image source={{uri: form?.carImgUrl}} style={styles.carImage} />

        <View style={styles.form}>
          <Text style={styles.label}>Plate number</Text>
          <TextInput
            placeholderTextColor={COLORS.grey}
            style={styles.input}
            value={form.registrationNumber}
            onChangeText={value =>
              handleInputChange('registrationNumber', value)
            }
            editable={isEditable}
          />

          <Text style={styles.label}>Year</Text>
          <TextInput
            placeholderTextColor={COLORS.grey}
            style={styles.input}
            keyboardType="numeric"
            value={form.year}
            onChangeText={value => handleInputChange('year', value)}
            editable={isEditable}
          />

          <Text style={styles.label}>Model</Text>
          <TextInput
            placeholderTextColor={COLORS.grey}
            style={styles.input}
            value={form.model}
            onChangeText={value => handleInputChange('model', value)}
            editable={isEditable}
          />

          <Text style={styles.label}>Color</Text>
          <TextInput
            placeholderTextColor={COLORS.grey}
            style={styles.input}
            value={form.color}
            onChangeText={value => handleInputChange('color', value)}
            editable={isEditable}
          />

          <Text style={styles.label}>Seatbelts</Text>
          <TextInput
            placeholderTextColor={COLORS.grey}
            style={styles.input}
            keyboardType="numeric"
            value={form.noOfSeats}
            onChangeText={value => handleInputChange('noOfSeats', value)}
            editable={isEditable}
          />

          <TouchableOpacity
            style={styles.submitButton}
            onPress={() => {
              if (isEditable) saveCarDetails();
              setIsEditable(!isEditable);
            }}>
            <Text style={styles.submitButtonText}>
              {isEditable ? 'Save Car Profile' : 'Edit Car Profile'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },

  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    alignItems: 'center',
  },
  goBackIcon: {
    width: 25,
    height: 25,
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  headerTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    flex: 1,
    textAlign: 'center',
  },
  carImage: {
    width: '50%',
    height: 200,
    borderRadius: 10,
    marginBottom: 20,
    alignSelf: 'center',
    resizeMode: 'contain',
  },
  form: {
    flex: 1,
    marginTop: 20,
  },
  label: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
    backgroundColor: COLORS.white,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 7,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default CarDetailsScreen;
