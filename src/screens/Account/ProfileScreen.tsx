import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  Share,
  Platform,
  ImageSourcePropType,
  ActivityIndicator,
} from 'react-native';
import {COLORS, FONTS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {overwriteStore} from '../../../redux/ActionCreator';
import {useDispatch} from 'react-redux';
import {API_BASE_URL} from '../../config/apiConfig';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define types for navigation
type RootStackParamList = {
  RecentRides: undefined;
  MyRidesScreen: undefined;
  MyCarScreen: undefined;
  NotificationScreen: undefined;
  FAQ: undefined;
  NotificationSettings: undefined;
  AboutUs: undefined;
  Message: undefined;
  ViewProfileScreen: undefined;
};

type ProfileScreenNavigationProp = StackNavigationProp<RootStackParamList>;

// Define types for menu items
interface MenuItem {
  id: number;
  label: string;
  icon: ImageSourcePropType;
  onPress: () => void;
  line?: boolean;
  hasNotification?: boolean;
}

// Define type for profile state
interface ProfileState {
  firstName?: string;
  lastName?: string;
  profileImg?: string | ImageSourcePropType;
  email?: string;
  mobileNumber?: string;
  totalRides?: number;
  status?: string;
  earnings?: number;
  ratings?: number[];
  name?: string;
  image?: string;
}

const ProfileScreen = () => {
  const navigation = useNavigation<ProfileScreenNavigationProp>();

  const [profile, setProfile] = useState<ProfileState>({name: '', image: ''});
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);

  const dispatch = useDispatch();

  const fetchProfile = async () => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/profile/getProfile`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );

      // Check if response is JSON before parsing
      const contentType = response.headers.get('content-type');
      const isJson = contentType && contentType.includes('application/json');

      if (!isJson) {
        const textResponse = await response.text();
        console.error('Non-JSON response received:', textResponse);
        throw new Error('Server returned non-JSON response');
      }

      const data = await response.json();
      console.log('Profile Response:', data?.data);

      if (response.ok) {
        console.log('ProfileScreen - Storing profile data:', JSON.stringify(data?.data, null, 2));
        dispatch(overwriteStore({name: 'GetProfile', value: data?.data || {}}));
        setProfile({
          firstName: data?.data?.firstName || '',
          lastName: data?.data?.lastName || '',
          profileImg: data?.data?.profileImg || images.profile,
          email: data?.data?.email || '',
          mobileNumber: data?.data?.mobileNumber || '',
          totalRides: data?.data?.totalRides || 0,
          status: data?.data?.status || '',
          earnings: data?.data?.earnings || 0,
        });
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
    }
  };
  useEffect(() => {
    fetchProfile();
  }, []);

  // Logout function - COMMENTED OUT
  // const handleLogout = () => {
  //   setShowLogoutConfirm(true);
  // };

  // const confirmLogout = async () => {
  //   setIsLoggingOut(true);
  //   setShowLogoutConfirm(false);

  //   try {
  //     const response = await fetch(`${API_BASE_URL}/auth/signout`, {
  //       method: 'POST',
  //       credentials: 'include',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //     });

  //     console.log('Logout response:', response.status);

  //     // Clear user data from AsyncStorage regardless of response
  //     await AsyncStorage.removeItem('UserProfile');
  //     await AsyncStorage.removeItem('token');

  //     // Reset navigation to login screen
  //     navigation.reset({
  //       index: 0,
  //       routes: [{name: 'WelcomeBack'}] as any,
  //     });

  //     if (response.ok) {
  //       console.log('User logged out successfully');
  //     } else {
  //       console.log('Logout API failed, but local data cleared');
  //     }
  //   } catch (error) {
  //     console.error('Error logging out:', error);
  //     // Even if there's an error, clear local data and redirect
  //     await AsyncStorage.removeItem('UserProfile');
  //     await AsyncStorage.removeItem('token');
  //     navigation.reset({
  //       index: 0,
  //       routes: [{name: 'WelcomeBack'}] as any,
  //     });
  //   } finally {
  //     setIsLoggingOut(false);
  //   }
  // };

  // const cancelLogout = () => {
  //   setShowLogoutConfirm(false);
  // };

  const menuGroups = [
    [
      {
        id: 1,
        label: 'Earning',
        icon: images.creditcard,
        onPress: () => navigation.navigate('RecentRides'),
        line: true,
      },
      {
        id: 2,
        label: 'My Rides',
        icon: images.carr,
        onPress: () => navigation.navigate('MyRidesScreen'),
        line: true,
      },
      {
        id: 12,
        label: 'My Car',
        icon: images.carr,
        onPress: () => navigation.navigate('MyCarScreen'),
        line: true,
      },
      {
        id: 3,
        label: 'Notifications',
        icon: images.noti2,
        onPress: () => navigation.navigate('NotificationScreen'),
        hasNotification: true,
      },
    ],
    [
      {
        id: 4,
        label: 'Help',
        icon: images.help,
        onPress: () => navigation.navigate('FAQ'),
        line: true,
      },
      {
        id: 5,
        label: 'Settings',
        icon: images.setting,
        onPress: () => navigation.navigate('NotificationSettings'),
        line: true,
      },
      {
        id: 6,
        label: 'About us',
        icon: images.annotation,
        onPress: () => navigation.navigate('AboutUs'),
      },
    ],
    [
      {
        id: 7,
        label: 'Messages',
        icon: images.chat,
        onPress: () => navigation.navigate('Message'),
        hasNotification: true,
        line: true,
      },
      {
        id: 8,
        label: 'Tell a friend',
        icon: images.tellafriend,
        onPress: () => shareRide(),
      },
    ],
    // LOGOUT MENU ITEM - COMMENTED OUT
    // [
    //   {
    //     id: 9,
    //     label: 'Log out',
    //     icon: images.logout || images.setting, // Use logout icon if available, fallback to setting
    //     onPress: handleLogout,
    //   },
    // ],
  ];
  const shareRide = async () => {
    try {
      const result = await Share.share({
        message:
          'Hey! I am using RideFuze. Download the app now and enjoy your ride!',
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('Shared with activity type:', result.activityType);
        } else {
          console.log('Shared successfully');
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('Share dismissed');
      }
    } catch (error: any) {
      console.log('Error sharing:', error.message);
    }
  };

  const renderMenuItem = (item: MenuItem) => (
    <TouchableOpacity
      style={[styles.menuItem, item?.line && {borderBottomWidth: 0.5}]}
      onPress={item.onPress}
      key={item.id}>
      <View style={styles.menuItemContent}>
        <Image source={item.icon} style={styles.menuIcon as any} />
        <Text style={styles.menuLabel}>{item.label}</Text>
      </View>
      <Image
        source={images.next}
        style={[
          styles.menuIcon as any,
          {width: 12, height: 12, resizeMode: 'contain'},
        ]}
      />
    </TouchableOpacity>
  );

  const renderMenuGroup = (group: MenuItem[], index: number) => (
    <View style={styles.menuGroup} key={index}>
      {group.map(renderMenuItem)}
    </View>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <Text style={styles.header}>Profile</Text>

      {/* Profile Card */}
      <TouchableOpacity
        style={styles.profileCard}
        onPress={() => navigation.navigate('ViewProfileScreen')}>
        <View style={styles.carAndDriverContainer}>
          <Image source={images.CarSingle} style={styles.carImage as any} />
          <Image
            source={typeof profile.profileImg === 'string' ? {uri: profile.profileImg} : images.profile}
            style={styles.driverImage as any}
          />
        </View>
        <View style={styles.profileDetails}>
          <Text
            style={
              styles.profileName
            }>{`${profile?.firstName} ${profile.lastName}`}</Text>
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingText}>
              {profile?.ratings && profile.ratings.length > 0
                ? (
                    profile.ratings.reduce((a: number, b: number) => a + b, 0) /
                    profile.ratings.length
                  ).toFixed(1)
                : 'N/A'}
            </Text>
            <Text style={styles.ridesText}>| {profile?.totalRides || 0} rides</Text>
            <Text style={styles.percentageText}>
              |{' '}
              {profile?.status
                ? profile.status.charAt(0).toUpperCase() + profile.status.slice(1)
                : 'Unknown'}
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      {/* Menu Items */}
      {menuGroups.map(renderMenuGroup)}

      {/* Footer */}
      <TouchableOpacity
        style={styles.footerCard as any}
        onPress={() => console.log('Become a driver')}>
        <Image source={images.becomeadriver} style={styles.footerImage as any} />
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 20,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    marginBottom: 20,
    elevation: 5,
    shadowColor: COLORS.black,
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
  },
  carAndDriverContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  carImage: {
    width: 80,
    height: 50,
    resizeMode: 'contain',
    marginRight: -25,
  },
  driverImage: {
    width: 60,
    height: 60,
    borderRadius: 30,
    borderWidth: 2,
    borderColor: COLORS.white,
    zIndex: 1,
  },
  profileDetails: {
    flex: 1,
    marginLeft: 20,
  },
  profileName: {
    fontFamily: 'Poppins-Medium',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '600' as any,
    color: COLORS.black,
    // marginBottom: 5,
  } as any,
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    // marginTop: 5,
  },
  ratingText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 11,
    lineHeight: 16,
    fontWeight: '400' as any,
    color: COLORS.black,
  } as any,
  starIcon: {
    width: 14,
    height: 14,
    fontSize: 11,

    marginHorizontal: 5,
    resizeMode: 'contain',
  },
  ridesText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 11,
    lineHeight: 16,
    fontWeight: '400' as any,
    color: COLORS.black,
    marginLeft: 5,
  } as any,
  percentageText: {
    fontFamily: 'Poppins-Regular',
    fontSize: 11,
    lineHeight: 16,
    fontWeight: '400' as any,
    color: 'green',
    marginLeft: 5,
  } as any,
  menuGroup: {
    backgroundColor: COLORS.white,
    borderRadius: 5,
    paddingHorizontal: 20,
    marginBottom: 20,
    paddingVertical: 10,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 0,
    borderColor: COLORS.border,
    paddingVertical: 13,
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  menuIcon: {
    width: 20,
    height: 20,
    marginRight: 15,
  },
  menuLabel: {
    fontFamily: 'Poppins-Regular',
    fontSize: 14,
    lineHeight: 20,
    fontWeight: '400' as any,
    color: COLORS.black,
  } as any,
  notificationDot: {
    width: 10,
    height: 10,
    backgroundColor: COLORS.red,
    borderRadius: 5,
  },
  footerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    borderRadius: 10,
    padding: 15,
  } as any,
  footerTitle: {
    fontFamily: 'Poppins-Medium',
    fontSize: 16,
    lineHeight: 20,
    fontWeight: '600' as any,
    color: COLORS.white,
  } as any,
  footerSubtitle: {
    fontFamily: 'Poppins-Regular',
    fontSize: 12,
    lineHeight: 16,
    fontWeight: '400' as any,
    color: COLORS.white,
  } as any,
  footerImage: {
    width: '100%',
    height: 120,
    borderRadius: 5,
    resizeMode: 'contain',
    // marginLeft: 10,
  },
  // Logout Modal Styles
  modalOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 9999,
    elevation: 9999,
    width: '100%',
    height: '100%',
  },
  logoutModal: {
    backgroundColor: COLORS.white,
    borderRadius: 15,
    padding: 25,
    margin: 20,
    width: '90%',
    maxWidth: 400,
    elevation: 10,
    shadowColor: COLORS.black,
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
  },
  logoutTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    textAlign: 'center',
    marginBottom: 15,
    fontWeight: '600',
  },
  logoutMessage: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 25,
    lineHeight: 20,
  },
  logoutButtonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.primary,
  },
  cancelButtonText: {
    ...FONTS.body3,
    color: COLORS.primary,
    fontWeight: '600',
  },
  confirmLogoutButton: {
    flex: 1,
    backgroundColor: COLORS.red || '#FF4444',
    borderRadius: 10,
    paddingVertical: 12,
    alignItems: 'center',
  },
  confirmLogoutButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
    fontWeight: '600',
  },
  disabledButton: {
    backgroundColor: COLORS.grey,
    opacity: 0.6,
  },
});

export default ProfileScreen;
