import React, {useState} from 'react';
import {
  View,
  Text,
  Image,
  TextInput,
  TouchableOpacity,
  ScrollView,
  FlatList,
  ActivityIndicator,
  Platform,
  Alert,
} from 'react-native';
import {useNavigation} from '@react-navigation/native';
import {COLORS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import Spinner from 'react-native-loading-spinner-overlay';
import {API_BASE_URL, INRIDE_URL} from '../../config/apiConfig';

// Define types for suggestions
interface Suggestion {
  id: string;
  description: string;
}

// Define navigation type
type NavigationProp = {
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
};

const GOOGLE_API_KEY = 'AIzaSyC0VJu9ttMNPOWP-vxTuXtzAaR932hdKUc';

const AddHomeAddress = () => {
  const navigation = useNavigation<NavigationProp>();
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState('');

  const handleInputChange = async (text: string) => {
    setQuery(text);
    if (text.length > 2) {
      setLoading(true);
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&types=(cities)&key=${GOOGLE_API_KEY}`,
        );
        const data = await response.json();
        console.log('Google Places API Response:', data);

        if (data.status === 'OK') {
          const predictions = data.predictions.map((prediction: any) => ({
            id: prediction.place_id,
            description: prediction.description,
          }));
          setSuggestions(predictions);
        } else {
          console.error('Google Places API Error:', data.status);
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Google Places API Error:', error);
      } finally {
        setLoading(false);
      }
    } else {
      setSuggestions([]);
    }
  };

  const handleSelectAddress = (address: string) => {
    setSelectedAddress(address);
    setQuery(address);
    setSuggestions([]);
  };

  const [loadingSubmit, setLoadingSubmit] = useState(false);

  const handleSaveAddress = async () => {
    if (!selectedAddress) {
      Alert.alert('Error', 'Please select a valid address.');
      return;
    }

    setLoadingSubmit(true); // Show loader

    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/profile/updateProfile`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({homeAddress: selectedAddress}),
        },
      );

      const data = await response.json();
      console.log('Update Address Response:', data);

      if (response.ok) {
        Alert.alert('Success', 'Address updated successfully!');
        navigation.goBack();
      } else {
        Alert.alert('Error', 'Failed to update address.');
      }
    } catch (error) {
      console.error('Error updating address:', error);
      Alert.alert('Error', 'An error occurred while updating your address.');
    } finally {
      setLoadingSubmit(false); // Hide loader
    }
  };

  return (
    <View style={{flex: 1, backgroundColor: COLORS.light_blue}}>
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <Spinner
        visible={loadingSubmit}
        textContent={'Loading...'}
        textStyle={{color: COLORS.white}}
      />
      {/* Header */}
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          padding: 10,
          backgroundColor: COLORS.white,
        }}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image
            source={images.goback}
            style={{width: 30, height: 30, marginRight: 10}}
          />
        </TouchableOpacity>
        <Text
          style={{
            fontSize: 14,
            fontWeight: 'bold',
            color: COLORS.black,
          }}>
          {' '}
          Add Home Address
        </Text>
      </View>

      <ScrollView contentContainerStyle={{padding: SIZES.padding}}>
        {/* Address Input */}
        <View>
          <TextInput
            placeholder="Search for your home address"
            placeholderTextColor={COLORS.grey}
            style={{
              fontSize: 15,
              backgroundColor: COLORS.white,
              padding: 15,
              borderWidth: 0.5,
              borderColor: COLORS.border,
              borderRadius: 7,
              marginVertical: 5,
              color: COLORS.black,
            }}
            value={query}
            onChangeText={handleInputChange}
          />

          {loading && (
            <ActivityIndicator
              size="small"
              color={COLORS.primary}
              style={{marginTop: 10}}
            />
          )}

          {/* Address Suggestions */}
          <FlatList
            data={suggestions}
            keyExtractor={(item: Suggestion) => item.id}
            renderItem={({item}: {item: Suggestion}) => (
              <TouchableOpacity
                style={{
                  padding: 10,
                  borderBottomWidth: 0.5,
                  borderColor: COLORS.border,
                  backgroundColor: COLORS.white,
                }}
                onPress={() => handleSelectAddress(item.description)}>
                <Text
                  style={{
                    fontSize: 14,
                    fontWeight: 'normal',
                    color: COLORS.black,
                  }}>
                  {item.description}
                </Text>
              </TouchableOpacity>
            )}
          />
        </View>

        <TouchableOpacity
          style={{
            backgroundColor: loadingSubmit ? COLORS.grey : COLORS.primary,
            borderRadius: SIZES.radius,
            padding: 15,
            alignItems: 'center',
            marginTop: 20,
          }}
          onPress={handleSaveAddress}>
          <Text
            style={{
              fontSize: 14,
              fontWeight: 'bold',
              color: COLORS.white,
            }}>
            Save Address
          </Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

// We're using inline styles in the component, so we don't need a StyleSheet

export default AddHomeAddress;
