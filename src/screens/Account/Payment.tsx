import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const Payment = () => {
  const navigation = useNavigation();
  const funds = 20.5;
  const paymentMethods = [
    { id: 1, label: 'PayPal', details: '****<EMAIL>', icon: images.paypal },
    { id: 2, label: 'Apple Pay', details: '****2134', icon: images.Applepay },
    { id: 3, label: 'Discover', details: '****2000', icon: images.paypal },
    { id: 4, label: 'Mastercard', details: '****1307', icon: images.masterCard },
  ];

  // State for managing the selected fund amount and bottom sheet visibility
const [selectedAmount, setSelectedAmount] = useState(null);
const [showBottomSheet, setShowBottomSheet] = useState(false);

// Function to toggle bottom sheet visibility
const toggleBottomSheet = () => {
  setShowBottomSheet(!showBottomSheet);
};

  const renderPaymentMethod = ({ item }) => (
    <TouchableOpacity style={styles.paymentMethod}>
      <View style={styles.paymentMethodDetails}>
        <Image source={item.icon} style={styles.paymentIcon} />
        <Text style={styles.paymentText}>{item.details}</Text>
      </View>
      <Image source={images.next} style={styles.arrowIcon} />
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Payment</Text>
        <Text></Text>
      </View>

      {/* Funds Card */}
      <View style={styles.fundsCard}>
        <View>
          <Text style={styles.fundsLabel}>RIDEFUZE Funds</Text>
          <Text style={styles.fundsAmount}>${funds}</Text>
        </View>
        <TouchableOpacity style={styles.addFundsButton}>
          <Text style={styles.addFundsText}>+ Add funds</Text>
        </TouchableOpacity>
      </View>

      {/* Payment Methods */}
      <Text style={styles.sectionTitle}>Payment methods</Text>
      <FlatList
        data={paymentMethods}
        renderItem={renderPaymentMethod}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.paymentMethodsList}
      />

      {/* Add Payment Method Button */}
      <TouchableOpacity style={styles.addPaymentButton}  onPress={toggleBottomSheet}>
        <Text style={styles.addPaymentText}>+ Add payment method</Text>
      </TouchableOpacity>

      {/* Bottom Sheet */}
{showBottomSheet && (
  <View style={styles.bottomSheetContainer}>
    <View style={styles.bottomSheet}>
      {/* Header */}
      <View style={styles.bottomSheetHeader}>
        <Text></Text>
        <Text style={styles.bottomSheetTitle}>Add funds</Text>
        <TouchableOpacity onPress={toggleBottomSheet}>
          <Image source={images.cancel} style={styles.closeIcon} />
        </TouchableOpacity>
      </View>

      <Text style={styles.bottomSheetDescription}>
        How much do you want to add to your RideFuze funds
      </Text>

      {/* Amount Options */}
      {['$25', '$50', '$75', '$200', '$1000'].map((amount) => (
        <TouchableOpacity
          key={amount}
          style={styles.amountOption}
          onPress={() => setSelectedAmount(amount)}
        >
          <Text style={styles.amountText}>{amount}</Text>
          <View
            style={[
              styles.checkbox,
              selectedAmount === amount && styles.checkboxSelected,
            ]}
          />
        </TouchableOpacity>
      ))}

      {/* Add Funds Button */}
      <TouchableOpacity style={styles.addFundsButtonBottomSheet}>
        <Text style={styles.addFundsText}>+ Add funds</Text>
      </TouchableOpacity>
    </View>
  </View>
)}

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  bottomSheetContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    top: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'flex-end',
  },
  bottomSheet: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  bottomSheetTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    textAlign: 'center',
  },
  closeIcon: {
    width: 10,
    height: 10,
    tintColor: COLORS.black,
  },
  bottomSheetDescription: {
    ...FONTS.body3,
    fontSize: 13,
    marginHorizontal: 40,
    color: COLORS.grey,
    marginBottom: 20,
    textAlign: 'center',
  },
  amountOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  amountText: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 4,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
  },
  addFundsButtonBottomSheet: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  addFundsText: {
    ...FONTS.body3,
    color: COLORS.white,
  },
  
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
  fundsCard: {
    // flexDirection: 'row',
    // justifyContent: 'space-between',
    // alignItems: 'center',
    backgroundColor: COLORS.white,
    padding: 15,
    borderRadius: 7,
    marginBottom: 20,
  },
  fundsLabel: {
    ...FONTS.body3,
    color: COLORS.black,
    // marginBottom: 15,
  },
  fundsAmount: {
    ...FONTS.h2,
    color: COLORS.primary,
    marginVertical:25
  },
  addFundsButton: {
    backgroundColor: COLORS.primary,
    width: 150,
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  addFundsText: {
    ...FONTS.h4,
    color: COLORS.white,
  },
  sectionTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
  },
  paymentMethodsList: {
    marginVertical: 20,
  },
  paymentMethod: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  paymentMethodDetails: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentIcon: {
    width: 40,
    height: 30,
    resizeMode: 'contain',
    marginRight: 10,
  },
  paymentText: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  arrowIcon: {
    width: 10,
    height: 10,
    resizeMode: 'contain',
  },
  addPaymentButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 15,
    alignItems: 'center',
  },
  addPaymentText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default Payment;
