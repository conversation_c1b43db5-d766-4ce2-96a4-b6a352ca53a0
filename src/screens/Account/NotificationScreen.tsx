 

// import React from 'react';
// import {
//   View,
//   Text,
//   StyleSheet,
//   Image,
//   FlatList,
//   TouchableOpacity,
// } from 'react-native';
// import { COLORS, FONTS, SIZES } from '../../constants/theme';
// import { images } from '../../constants';
// import { useNavigation } from '@react-navigation/native';

// const NotificationScreen = () => {
//   const navigation = useNavigation();

//   const notifications = [
//     {
//       id: 1,
//       title: 'Your have successfully booked a car',
//       description: 'Far far away, behind the word mountains, far abdusdsassa from...',
//       timestamp: 'Today 13:23',
//     },
//     {
//       id: 2,
//       type: 'promo',
//       title: '25% OFF',
//       description: 'Far far away, behind the word mountains',
//       image: images.Banner,
//       timestamp: 'Today 13:23',

//     },
//     {
//       id: 3,
//       title: 'Your have successfully booked a car',
//       description: 'Far far away, behind the word mountains, far abdusdsassa from...',
//       timestamp: 'Today 13:23',
//     },
//     {
//       id: 4,
//       title: 'Your ride has arrived',
//       description: 'Far far away, behind the word mountains, far abdusdsassa from...',
//       timestamp: 'Today 13:23',
//     },
//   ];

//   const renderNotification = ({ item }) => {
//     if (item.type === 'promo') {
//       return (
//         <View style={styles.promoCard}>
//           <Image source={item.image} style={styles.promoImage} />

//           <Text style={styles.notificationTitle}>{item.title}</Text>
//           <Text style={styles.notificationDescription}>{item.description}</Text>
//           <View style={styles.notificationFooter}>
//           <Text style={styles.notificationTimestamp}>{item.timestamp}</Text>
//           <TouchableOpacity>
//             <Text style={styles.viewAllText}>View all</Text>
//           </TouchableOpacity>
//         </View>
//         </View>
//       );
//     }
//     return (
//       <View style={styles.notificationCard}>
//         <View style={styles.notificationHeader}>
//           <Image source={images.notif} style={styles.notificationIcon} />
//           <Text style={styles.notificationTitle}>{item.title}</Text>
//         </View>
//         <Text style={styles.notificationDescription}>{item.description}</Text>
//         <View style={styles.notificationFooter}>
//           <Text style={styles.notificationTimestamp}>{item.timestamp}</Text>
//           <TouchableOpacity>
//             <Text style={styles.viewAllText}>View all</Text>
//           </TouchableOpacity>
//         </View>
//       </View>
//     );
//   };

//   return (
//     <View style={styles.container}>
//       {/* Header */}
//       <View style={styles.header}>
//         <TouchableOpacity onPress={() => navigation.goBack()}>
//           <Image source={images.goback} style={styles.goBackIcon} />
//         </TouchableOpacity>
//         <Text style={styles.headerTitle}>Notifications</Text>
//         <Text></Text>
//       </View>

//       {/* Notification List */}
//       <FlatList
//         data={notifications}
//         renderItem={renderNotification}
//         keyExtractor={(item) => item.id.toString()}
//         contentContainerStyle={styles.notificationList}
//         showsVerticalScrollIndicator={false}
//       />
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: COLORS.light_blue,
//     padding: SIZES.padding,
//   },
//   header: {
//     justifyContent: 'space-between',
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginBottom: 20,
//   },
//   goBackIcon: {
//     width: 25,
//     height: 25,
//     marginRight: 15,
//   },
//   headerTitle: {
//     ...FONTS.body3,
//     color: COLORS.black,
//   },
//   notificationList: {
//     paddingBottom: 20,
//   },
//   notificationCard: {
//     backgroundColor: COLORS.white,
//     borderRadius: 10,
//     padding: 15,
//     marginBottom: 15,
//   },
//   notificationHeader: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     marginBottom: 5,
//   },
//   notificationIcon: {
//     width: 20,
//     height: 20,
//     marginRight: 10,
//   },
//   notificationTitle: {
//     ...FONTS.body4,
//     color: COLORS.black,
//   },
//   notificationDescription: {
//     marginVertical: 10,
//     ...FONTS.body4,
//     fontSize: 12,
//     color: COLORS.grey,
//     marginBottom: 10,
//   },
//   notificationFooter: {
//     borderColor: COLORS.border,
//     borderTopWidth: 0.5,
//     paddingTop: 10,
//     flexDirection: 'row',
//     justifyContent: 'space-between',
//     alignItems: 'center',
//   },
//   notificationTimestamp: {
//     ...FONTS.body5,
//     color: COLORS.grey,
//     fontSize: 10,
//   },
//   viewAllText: {
//     ...FONTS.body4,
//     color: COLORS.grey,
//     fontSize: 10,

//   },
//   promoCard: {
//     backgroundColor: COLORS.white,
//     borderRadius: 10,
//     padding: 15,
//     marginBottom: 15,
//     // flexDirection: 'row',
//     // alignItems: 'center',
//   },
//   promoTitle: {
//     ...FONTS.h3,
//     color: COLORS.red,
//     fontWeight: 'bold',
//   },
//   promoDescription: {
//     ...FONTS.body3,
//     color: COLORS.black,
//     marginBottom: 10,
//   },
//   bookNowButton: {
//     backgroundColor: COLORS.primary,
//     borderRadius: 5,
//     paddingVertical: 10,
//     paddingHorizontal: 20,
//   },
//   bookNowText: {
//     ...FONTS.body4,
//     color: COLORS.white,
//   },
//   promoImage: {
//     width: '100%',
//     height: 150,
//     resizeMode: 'contain',
//     marginLeft: 10,
//   },
// });

// export default NotificationScreen;


 

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  FlatList,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { API_BASE_URL } from '../../config/apiConfig';

const NotificationScreen = () => {
  const navigation = useNavigation();
  const [notifications, setNotifications] = useState([]);
const [loading, setLoading] = useState(false);


const fetchNotifications = async () => {
  setLoading(true);
  try {
    const response = await fetch(
      `${API_BASE_URL}/driver/profile/getNotifications`,
      {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    const data = await response.json();
    console.log('Passenger Notifications:', JSON.stringify(data, null, 2));

    if (response.ok && Array.isArray(data?.data)) {
      setNotifications(data.data);
    } else {
      alert('Failed to fetch notifications');
    }
  } catch (error) {
    console.error('Error fetching notifications:', error);
    alert('Error fetching notifications');
  } finally {
    setLoading(false);
  }
};

useEffect(() => {
  fetchNotifications();
}, []);


const renderNotification = ({ item }) => (
  <View style={styles.notificationCard}>
    <View style={styles.notificationHeader}>
      <Image source={images.notif} style={styles.notificationIcon} />
      <Text style={styles.notificationTitle}>{item.message}</Text>
    </View>
    <Text style={styles.notificationTimestamp}>
      {new Date(item.createdAt).toLocaleString()}
    </Text>
  </View>
);



  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.goBackIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notifications</Text>
        <Text></Text>
      </View>

      {/* Notification List */}
      <FlatList
  data={notifications}
  renderItem={renderNotification}
  keyExtractor={(item) => item._id?.toString() || Math.random().toString()}
  contentContainerStyle={styles.notificationList}
  showsVerticalScrollIndicator={false}
  ListEmptyComponent={
    <Text style={{ textAlign: 'center', color: COLORS.grey }}>
      No notifications available
    </Text>
  }
/>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  header: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  goBackIcon: {
    width: 25,
    height: 25,
    marginRight: 15,
  },
  headerTitle: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  notificationList: {
    paddingBottom: 20,
  },
  notificationCard: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  notificationHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  notificationIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  notificationTitle: {
    ...FONTS.body4,
    color: COLORS.black,
    marginRight:20

  },
  notificationDescription: {
    marginVertical: 10,
    ...FONTS.body4,
    fontSize: 12,
    color: COLORS.grey,
    marginBottom: 10,
  },
  notificationFooter: {
    borderColor: COLORS.border,
    borderTopWidth: 0.5,
    paddingTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  notificationTimestamp: {
    ...FONTS.body5,
    color: COLORS.grey,
    fontSize: 10,
  },
  viewAllText: {
    ...FONTS.body4,
    color: COLORS.grey,
    fontSize: 10,

  },
  promoCard: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    // flexDirection: 'row',
    // alignItems: 'center',
  },
  promoTitle: {
    ...FONTS.h3,
    color: COLORS.red,
    fontWeight: 'bold',
  },
  promoDescription: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
  },
  bookNowButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  bookNowText: {
    ...FONTS.body4,
    color: COLORS.white,
  },
  promoImage: {
    width: '100%',
    height: 150,
    resizeMode: 'contain',
    marginLeft: 10,
  },
});

export default NotificationScreen;
