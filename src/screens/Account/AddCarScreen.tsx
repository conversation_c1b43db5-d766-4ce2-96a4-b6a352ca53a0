import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import {COLORS, FONTS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import {useNavigation} from '@react-navigation/native';
import {NewCar} from '../../../redux/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import CookieManager from '@react-native-cookies/cookies';
import {API_BASE_URL} from '../../config/apiConfig';

const AddCarScreen = () => {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(false); // Loader state

  const [form, setForm] = useState({
    registrationNumber: '', // Matches API field name
    year: '',
    model: '',
    color: '',
    noOfSeats: '',
  });

  const handleInputChange = (field, value) => {
    setForm({...form, [field]: value});
  };

  const handleSubmit = async () => {
    if (
      !form.registrationNumber ||
      !form.year ||
      !form.model ||
      !form.color ||
      !form.noOfSeats
    ) {
      Alert.alert('Error', 'All fields are required!');
      return;
    }

    // Validate year (should be a 4-digit number)
    if (isNaN(form.year) || form.year.length !== 4) {
      Alert.alert('Error', 'Year must be a valid 4-digit number (e.g., 2022).');
      return;
    }

    // Validate noOfSeats (should be a number)
    if (isNaN(form.noOfSeats)) {
      Alert.alert('Error', 'Number of seats must be a valid number.');
      return;
    }

    try {
      setLoading(true);

      const token = await AsyncStorage.getItem('token'); // Replace with actual token (retrieve from storage if needed)
      const response = await NewCar({
        registrationNumber: form.registrationNumber,
        year: form.year,
        model: form.model,
        color: form.color,
        noOfSeats: parseInt(form.noOfSeats), // Ensure it's an integer
      });

      // const result = await response.json();
      console.log('API Response Body:', response);

      navigation.navigate('MyCarScreen'); // Navigate back after successful submission
    } catch (error) {
      console.error('API Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const hanndleSubmit = async () => {
    if (
      !form.registrationNumber ||
      !form.year ||
      !form.model ||
      !form.color ||
      !form.noOfSeats
    ) {
      Alert.alert('Error', 'All fields are required!');
      return;
    }

    // Validate year (should be a 4-digit number)
    if (isNaN(form.year) || form.year.toString().length !== 4) {
      Alert.alert('Error', 'Year must be a valid 4-digit number (e.g., 2022).');
      return;
    }

    // Validate noOfSeats (should be a number)
    if (isNaN(form.noOfSeats)) {
      Alert.alert('Error', 'Number of seats must be a valid number.');
      return;
    }

    try {
      setLoading(true);
      console.log('Fetching stored cookies...');
      const cookies = await CookieManager.get(API_BASE_URL);
      console.log('Stored Cookies:', cookies);
      const cookieString = Object.entries(cookies)
        .map(([name, cookie]) => `${name}=${cookie.value}`)
        .join('; ');
      console.log('Formatted Cookies:', cookieString);
      const response = await fetch(`${API_BASE_URL}/driver/car/newCar`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        credentials: 'include', // Ensures cookies are sent with the request
        body: JSON.stringify({
          registrationNumber: form.registrationNumber,
          year: form.year,
          model: form.model,
          color: form.color,
          noOfSeats: parseInt(form.noOfSeats, 10),
        }),
      });

      console.log('API Response Status:', response.status);

      const text = await response.text();
      const result = text ? JSON.parse(text) : {};

      console.log('API Response Body:', result);

      if (!response.ok || !result.success) {
        throw new Error(result.message || 'Failed to add car.');
      }

      Alert.alert('Success', 'Car details added successfully!');
      navigation.goBack();
    } catch (error) {
      console.error('API Error:', error);
      Alert.alert('Error', error.message || 'Something went wrong.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.goBackIcon} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Car details</Text>
        <Text></Text>
      </View>

      {/* Form */}
      <View style={styles.form}>
        <Text style={styles.label}>Plate number</Text>
        <TextInput
          style={styles.input}
          placeholderTextColor={COLORS.grey}
          placeholder="Enter Plate number"
          value={form.registrationNumber}
          onChangeText={value => handleInputChange('registrationNumber', value)}
        />

        <Text style={styles.label}>Year</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter year"
          placeholderTextColor={COLORS.grey}
          keyboardType="numeric"
          value={form.year}
          onChangeText={value => handleInputChange('year', value)}
        />

        <Text style={styles.label}>Model</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter model"
          value={form.model}
          placeholderTextColor={COLORS.grey}
          onChangeText={value => handleInputChange('model', value)}
        />

        <Text style={styles.label}>Color</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter color"
          value={form.color}
          placeholderTextColor={COLORS.grey}
          onChangeText={value => handleInputChange('color', value)}
        />

        <Text style={styles.label}>Number of Seats</Text>
        <TextInput
          style={styles.input}
          placeholder="Enter number of seats"
          keyboardType="numeric"
          value={form.noOfSeats}
          placeholderTextColor={COLORS.grey}
          onChangeText={value => handleInputChange('noOfSeats', value)}
        />

        {/* Submit Button */}
        <TouchableOpacity
          style={styles.submitButton}
          onPress={handleSubmit}
          disabled={loading}>
          {loading ? (
            <ActivityIndicator color={COLORS.white} />
          ) : (
            <Text style={styles.submitButtonText}>Submit</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  goBackIcon: {
    width: 25,
    height: 25,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  form: {
    flex: 1,
    marginTop: 20,
  },
  label: {
    ...FONTS.body4,
    color: COLORS.black,
    marginBottom: 5,
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 10,
    paddingHorizontal: 15,
    marginBottom: 20,
    backgroundColor: COLORS.white,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 7,
    paddingVertical: 15,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default AddCarScreen;
