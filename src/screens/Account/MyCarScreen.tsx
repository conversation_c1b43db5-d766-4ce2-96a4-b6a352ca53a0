import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Modal,
  ActivityIndicator,
  Platform,
} from 'react-native';
import {COLORS, FONTS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {overwriteStore} from '../../../redux/ActionCreator';
import {API_BASE_URL} from '../../config/apiConfig';

const MyCarScreen = () => {
  const navigation = useNavigation();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  const [isActivating, setIsActivating] = useState(false);
  // const [activeCarId, setActiveCarId] = useState(car?.active ? car?._id : null);
  const driverCar = useSelector(state => state?.store?.GetCarDetails) || [];
  const [activeCarId, setActiveCarId] = useState(null);

  useEffect(() => {
    fetchCarDetails();
  }, []);

  useEffect(() => {
    if (driverCar.length > 0) {
      const activeCar = driverCar.find(car => car.active);
      setActiveCarId(activeCar ? activeCar._id : null);
    }
  }, [driverCar]);

  const fetchCarDetails = async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/driver/car/getCarDetails`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      console.log(data?.message?.cars, 'response in getCarDetails');
      if (response.ok) {
        dispatch(
          overwriteStore({name: 'GetCarDetails', value: data?.message?.cars}),
        );
      }
    } catch (error) {
      console.error('Error fetching car details:', error);
    }
  };

  const handleDeleteCar = async carId => {
    setLoading(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/car/deleteCarDetails`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({carId}),
        },
      );

      const data = await response.json();
      console.log(data, 'response from deleteCar');

      if (response.ok) {
        // Update the Redux store or state to reflect the deleted car
        dispatch(
          overwriteStore({
            name: 'GetCarDetails',
            value: driverCar.filter(car => car._id !== carId),
          }),
        );
      }
    } catch (error) {
      console.error('Error deleting car:', error);
    }
    setLoading(false);
  };

  const handleNextForRentCar = () => {
    // Show the modal instead of navigating
    setModalVisible(true);
  };

  const handleActivateCar = async carId => {
    setIsActivating(true);
    try {
      const response = await fetch(
        `${API_BASE_URL}/driver/car/activateCar`,
        {
          method: 'POST',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({_id: carId}),
        },
      );

      const data = await response.json();
      console.log(data, 'response from activateCar');

      if (response.ok) {
        setActiveCarId(carId); // Set active car
        dispatch(
          overwriteStore({name: 'GetCarDetails', value: data?.message?.cars}),
        );
        fetchCarDetails(); // Fetch updated car details
      }
    } catch (error) {
      console.error('Error activating car:', error);
    }
    setIsActivating(false);
  };

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      {loading && (
        <View style={styles.overlay}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      )}

      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => {
          setModalVisible(!modalVisible);
        }}>
        <View style={styles.overlayy}>
          <View style={styles.modalView}>
            <Text style={styles.modalText}>Coming Soon!</Text>
            <TouchableOpacity
              style={[styles.button, styles.buttonClose]}
              onPress={() => setModalVisible(false)}>
              <Text style={styles.textStyle}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
      <View style={{paddingHorizontal: 15}}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Image source={images.goback} style={styles.goBackIcon} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>My Car</Text>
          <Text></Text>
        </View>

        {/* Car List */}
        {driverCar.length > 0 ? (
          driverCar.map((car, index) => (
            <View key={index} style={styles.carCard}>
              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}>
                <View>
                  <Text style={styles.carYear}>{car.year}</Text>
                  <Text style={styles.carModel}>{car.model}</Text>
                  <Text style={styles.carPlate}>{car.registrationNumber}</Text>
                </View>
                <Image source={{uri: car?.carImgUrl}} style={styles.carImage} />
                {/* Delete Icon */}
                <TouchableOpacity onPress={() => handleDeleteCar(car._id)}>
                  <Image source={images.delete} style={styles.deleteIcon} />
                </TouchableOpacity>
              </View>
              <TouchableOpacity
                onPress={() => navigation.navigate('CarDetailsScreen', {car})}
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  borderTopWidth: 0.5,
                  borderTopColor: COLORS.border,
                  paddingTop: 7,
                  marginTop: 10,
                }}>
                <Text style={styles.seeDetails}>See details</Text>
                // Button to activate the car
                <TouchableOpacity
                  style={styles.activateButton}
                  onPress={() => handleActivateCar(car._id)}
                  disabled={isActivating}>
                  {isActivating ? (
                    <ActivityIndicator size="small" color={COLORS.primary} />
                  ) : (
                    <Text style={styles.activateButtonText}>
                      {activeCarId === car._id ? '✅' : '❌'}
                    </Text>
                  )}
                </TouchableOpacity>
                <Image source={images.next} style={{width: 10, height: 10}} />
              </TouchableOpacity>
            </View>
          ))
        ) : (
          <Text style={styles.noCarText}>No cars added yet.</Text>
        )}

        {/* Add Car Button */}
        <TouchableOpacity
          style={styles.addCarButton}
          onPress={() => navigation.navigate('AddCarScreen')}>
          <Text style={styles.addCarText}>Add car</Text>
        </TouchableOpacity>

        {/* Rent a Car Section */}
        <View style={styles.rentCarCard}>
          <Image source={images.CarMultiple} style={styles.rentCarImage} />
          <Text style={styles.rentTitle}>Rent a car</Text>
          <Text style={styles.rentDescription}>
            Get an affordable rental you can use to drive on RideFuze starting
            from <Text style={styles.rentPrice}>$20.5 - $40.5</Text>
          </Text>
          <TouchableOpacity>
            <Text style={styles.terms}>Terms and conditions apply</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.rentButton}
            onPress={handleNextForRentCar}>
            <Text style={styles.rentButtonText}>Rent a car</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    // padding: SIZES.padding,
  },
  activateButton: {
    backgroundColor: COLORS.success,
    borderRadius: 7,
    paddingVertical: 10,
    alignItems: 'center',
    marginTop: 15,
  },
  activateButtonText: {
    ...FONTS.h4,
    color: COLORS.primary,
  },

  overlayy: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
  },
  modalView: {
    width: '90%',
    margin: 20,
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 35,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },

  button: {
    borderRadius: 20,
    padding: 10,
    elevation: 2,
  },
  buttonClose: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
  },
  textStyle: {
    color: 'white',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalText: {
    marginBottom: 15,
    textAlign: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  deleteIcon: {
    width: 20,
    height: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  goBackIcon: {
    width: 25,
    height: 25,
    marginRight: 10,
  },
  headerTitle: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  carCard: {
    flexDirection: 'column',
    // alignItems: 'center',
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    justifyContent: 'space-between',
    elevation: 5,
    shadowColor: COLORS.black,
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
    marginBottom: 20,
  },
  carYear: {
    ...FONTS.h3,
    color: COLORS.primary,
  },
  carModel: {
    ...FONTS.h4,
    color: COLORS.black,
    marginTop: 5,
  },
  carPlate: {
    ...FONTS.body4,
    color: COLORS.grey,
    marginTop: 5,
  },
  carImage: {
    width: 100,
    height: 60,
    resizeMode: 'contain',
  },
  seeDetails: {
    ...FONTS.body4,
    color: COLORS.primary,
    textAlign: 'right',
    marginTop: 5,
  },
  addCarButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 7,
    paddingVertical: 15,
    alignItems: 'center',
    marginBottom: 20,
  },
  addCarText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  rentCarCard: {
    backgroundColor: COLORS.white,
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    elevation: 5,
    shadowColor: COLORS.black,
    shadowOpacity: 0.1,
    shadowOffset: {width: 0, height: 2},
  },
  rentCarImage: {
    width: '100%',
    height: 100,
    resizeMode: 'contain',
    marginBottom: 10,
  },
  rentTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    marginBottom: 10,
  },
  rentDescription: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginBottom: 10,
  },
  rentPrice: {
    color: COLORS.primary,
  },
  activeIcon: {
    width: 20,
    height: 20,
    tintColor: 'green', // Green for active cars
  },
  inactiveIcon: {
    width: 20,
    height: 20,
    tintColor: COLORS.grey, // Grey for inactive cars
  },
  terms: {
    ...FONTS.body4,
    color: COLORS.primary,
    textDecorationLine: 'underline',
    marginBottom: 10,
  },
  rentButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 7,
    paddingVertical: 15,
    paddingHorizontal: 30,
    alignItems: 'center',
  },
  rentButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default MyCarScreen;
