import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Image,
  FlatList,
  Linking,
  ScrollView,
  Platform,
} from 'react-native';
import {COLORS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import {useNavigation} from '@react-navigation/native';
import {useDispatch, useSelector} from 'react-redux';
import {overwriteStore} from '../../../redux/ActionCreator';
import {APP_SETTINGS_ENDPOINTS} from '../../config/apiConfig';

// Define interfaces for type safety
interface FAQItem {
  _id: string;
  question: string;
  answer: string;
}

interface SocialMediaLink {
  id: number;
  icon: any;
  link: string;
}

// Define Redux state interface
interface RootState {
  store?: {
    GetFaqs?: FAQItem[];
  };
}

const FAQ = () => {
  const [selectedFAQ, setSelectedFAQ] = useState<FAQItem | null>(null);
  const [loading, setLoading] = useState<boolean>(true); // Loader state
  const faqs = useSelector((state: RootState) => state?.store?.GetFaqs) || [];
  const dispatch = useDispatch();
  const navigation = useNavigation();

  const fetchFaqs = async () => {
    setLoading(true); // Show loader
    try {
      const response = await fetch(APP_SETTINGS_ENDPOINTS.GET_FAQS, {
        method: 'GET',
        credentials: 'include',
        headers: {'Content-Type': 'application/json'},
      });
      const data = await response.json();
      console.log(data);

      if (response.ok) {
        dispatch(overwriteStore({name: 'GetFaqs', value: data?.message || []}));
      }
    } catch (error) {
      console.error('Error fetching FAQs:', error);
    } finally {
      setLoading(false); // Hide loader when done
    }
  };

  useEffect(() => {
    fetchFaqs();
  }, []);

  const socialMediaLinks: SocialMediaLink[] = [
    {
      id: 1,
      icon: images.facebook,
      link: 'https://web.facebook.com/profile.php?id=61573934830288',
    },
    {
      id: 2,
      icon: images.twitter,
      link: 'https://x.com/RideFuze?t=gLnc4-LCrYwxQcQo2VLELw&s=08',
    },
    {
      id: 3,
      icon: images.instagram,
      link: 'https://www.instagram.com/ridefuze?utm_source=qr&igsh=NmJ5cWhtMnVxZjJh',
    },
    // { id: 4, icon: images.telegram, link: 'https://www.telegram.org' },
    {id: 5, icon: images.in, link: 'https://www.linkedin.com/company/ridefuze'},
    // { id: 6, icon: images.whatsapp, link: 'https://www.whatsapp.com' },
  ];

  const renderSocialIcons = () => (
    <View style={styles.socialIconsContainer}>
      {socialMediaLinks.map(icon => (
        <TouchableOpacity
          key={icon.id}
          onPress={() => Linking.openURL(icon.link)}>
          <Image source={icon.icon} style={styles.socialIcon} />
        </TouchableOpacity>
      ))}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Header */}
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}> Help/FAQ</Text>
      </View>

      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color={COLORS.primary} />
        </View>
      ) : null}

      {selectedFAQ ? (
        <ScrollView style={styles.faqDetails}>
          <TouchableOpacity onPress={() => setSelectedFAQ(null)}>
            <Image source={images.goback} style={styles.backButton} />
          </TouchableOpacity>
          <Text style={styles.faqTitle}>{selectedFAQ.question}</Text>
          <Text style={styles.faqContent}>{selectedFAQ.answer}</Text>
        </ScrollView>
      ) : (
        <FlatList
          data={faqs}
          renderItem={({item}) => (
            <TouchableOpacity
              style={styles.faqItem}
              onPress={() => setSelectedFAQ(item)}>
              <Text style={styles.faqQuestion}>{item.question}</Text>
              <Image source={images.next} style={styles.arrowIcon} />
            </TouchableOpacity>
          )}
          keyExtractor={item => item._id}
          contentContainerStyle={styles.faqList}
        />
      )}

      {renderSocialIcons()}
    </View>
  );
};
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  backButton: {
    width: 25,
    height: 25,
    resizeMode: 'contain',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.black,
    marginLeft: 10,
  },
  faqList: {
    flexGrow: 1,
    marginBottom: 20,
  },
  faqItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  faqQuestion: {
    fontSize: 14,
    fontWeight: '400',
    color: COLORS.black,
  },
  arrowIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
  },
  faqDetails: {
    backgroundColor: COLORS.white,
    borderRadius: 5,
    padding: SIZES.padding,
    marginBottom: 20,
  },
  faqTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.black,
    marginBottom: 10,
  },
  faqContent: {
    fontSize: 14,
    fontWeight: '400',
    color: COLORS.black,
    lineHeight: 20,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  socialIconsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 20,
  },
  socialIcon: {
    width: 30,
    height: 30,
    resizeMode: 'contain',
  },
});

export default FAQ;
