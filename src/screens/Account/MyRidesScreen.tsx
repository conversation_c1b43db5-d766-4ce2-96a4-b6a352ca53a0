
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  FlatList,
  Modal,
  Platform,
} from 'react-native';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';
import { API_BASE_URL } from '../../config/apiConfig';
const recentRides = [
  {
    title: "Delivery to California",
    date: "Nov 14th, 2024 21:42:39",
    amount: 200,
    status: "Successful",
  },
  {
    title: "Ride to Washington DC",
    date: "Nov 14th, 2024 21:42:39",
    amount: 20,
    status: "Canceled",
  },
  {
    title: "Group ride to Washington DC",
    date: "Nov 14th, 2024 21:42:39",
    amount: 20,
    status: "Pending",
  },
  {
    title: "Ride to Washington DC",
    date: "Nov 14th, 2024 21:42:39",
    amount: 20,
    status: "Canceled",
  },
];

const MyRidesScreen = ({ rides = recentRides }) => {
  const [driverRides, setDriverRides] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedRide, setSelectedRide] = useState(null);
  const [funds, setFunds] = useState(0);

  const openModal = (ride) => {
    setSelectedRide(ride);
    setModalVisible(true);
  };

  useEffect(() => {
    fetchDriverRides();
  }, []);

  const fetchDriverRides = async () => {
    setLoading(true);
    try {
      const response = await fetch(`${API_BASE_URL}/rides/getDriverRides`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const data = await response.json();
      console.log('Driver Rides Response:', JSON.stringify(data, null, 2));

      if (response.ok) {
        setDriverRides(data?.message?.rides || []);
        setFunds(data?.message?.earningsBalance || 0);
      }
    } catch (error) {
      console.error('Error fetching driver rides:', error);
    }
    setLoading(false);
  };

  const navigation = useNavigation();

  // const renderRideItem = ({ item }) => (
  //   <View style={styles.rideItem}>
  //     <View>
  //       <Text style={styles.rideTitle}>{item.title}</Text>
  //       <Text style={styles.rideDate}>{item.date}</Text>
  //     </View>
  //     <View style={styles.rideStatusContainer}>
  //       <Text style={styles.rideAmount}>${item.amount}</Text>
  //       <Text
  //         style={[
  //           styles.rideStatus,
  //           item.status === 'Successful' && { color: 'green' },
  //           item.status === 'Canceled' && { color: COLORS.red },
  //           item.status === 'Pending' && { color: 'orange' },
  //         ]}
  //       >
  //         {item.status}
  //       </Text>
  //     </View>
  //   </View>
  // );

  // const renderRideItem = ({ item }) => (
  //   <View style={styles.rideItem}>
  //     <View style={styles.rideDetails}>
  //       <Text style={styles.rideTitle}>{item.rideType.toUpperCase()} Ride</Text>
  //       <Text style={styles.rideDate}>Ride ID: {item.rideId}</Text>
  //       <Text style={styles.rideDate}>{item.from} → {item.to[0]?.place}</Text>
  //       <Text style={styles.rideDate}>Pickup: {item.pickupPoint}</Text>
  //       <Text style={styles.rideDate}>Distance: {item.kmDistance} km</Text>
  //       <Text style={styles.rideDate}>Payment: {item.paymentMethod}</Text>
  //     </View>

  //     <View style={styles.rideStatusContainer}>
  //       <Text style={styles.rideAmount}>${item.charge}</Text>
  //       <Text
  //         style={[
  //           styles.rideStatus,
  //           item.status === 'Successful' && { color: 'green' },
  //           item.status === 'Requested' && { color: 'orange' },
  //           item.status === 'Active' && { color: 'blue' },
  //           item.status === 'Canceled' && { color: COLORS.red },
  //         ]}
  //       >
  //         {item.status}
  //       </Text>
  //     </View>
  //   </View>
  // );


  const renderRideItem = ({ item }) => (
    // <TouchableOpacity onPress={() => openModal(item)} style={styles.rideItem}>
    <TouchableOpacity   style={styles.rideItem}
       onPress={() => navigation.navigate('RidesDetail', { rideId: item?.rideId })}>
      <View style={styles.rideDetails}>
        <Text style={styles.rideTitle}>{item.rideType.toUpperCase()} Ride</Text>
        <Text style={styles.rideDate}>
          {item.from.length > 20 ? item.from.substring(0, 20) + '...' : item.from} →
          {item.to[0]?.place.length > 10 ? item.to[0]?.place.substring(0, 20) + '...' : item.to[0]?.place}
        </Text>
        <Text style={styles.rideDate}>Distance: {item.kmDistance} km</Text>
      </View>

      <View style={styles.rideStatusContainer}>
        <Text style={styles.rideAmount}>${item?.charge}</Text>
        <Text
          style={[
            styles.rideStatus,
            item.status === 'Successful' && { color: 'green' },
            item.status === 'Requested' && { color: 'orange' },
            item.status === 'Active' && { color: 'blue' },
            item.status === 'Canceled' && { color: COLORS.red },
          ]}
        >
          {item.status}
        </Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
       {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                      {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>RIDEFUZE Funds</Text>
        <View style={{ width: 25 }} />
      </View>

      {/* Funds Card */}
      <View style={styles.fundsCard}>
        <View>
          <Text style={styles.fundsLabel}>RIDEFUZE Funds</Text>
          <Text style={styles.fundsValue}>${funds.toFixed(2)}</Text>
          <Text style={styles.fundsSubtitle}>Top balance</Text>
        </View>
        <TouchableOpacity>
          <Image source={images.next} style={styles.nextIcon} />
        </TouchableOpacity>
      </View>
      <Modal
        visible={modalVisible} transparent animationType="slide">
        <View style={styles.modalContainer}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Ride Details</Text>
            {selectedRide && (
              <>
                <Text style={styles.modalText}>Ride Type: {selectedRide.rideType}</Text>
                <Text style={styles.modalText}>Ride ID: {selectedRide.rideId}</Text>
                <Text style={styles.modalText}>From: {selectedRide.from}</Text>
                <Text style={styles.modalText}>To: {selectedRide.to[0]?.place}</Text>
                <Text style={styles.modalText}>Pickup Point: {selectedRide.pickupPoint}</Text>
                <Text style={styles.modalText}>Distance: {selectedRide.kmDistance} km</Text>
                <Text style={styles.modalText}>Payment Method: {selectedRide.paymentMethod}</Text>
                <Text style={styles.modalText}>Charge: ${selectedRide.charge}</Text>
                <Text style={styles.modalText}>Status: {selectedRide.status}</Text>
              </>
            )}

            <TouchableOpacity style={styles.closeButton} onPress={() => setModalVisible(false)}>
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>


      {rides.length > 0 ? (
        <>
          {/* Recent Rides */}
          <Text style={styles.sectionTitle}>Recent Rides</Text>
          <FlatList
            data={driverRides}
            renderItem={renderRideItem}
            keyExtractor={(item, index) => index.toString()}
            contentContainerStyle={styles.rideList}
            ListEmptyComponent={() => (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateText}>No Recent Ride</Text>

                <Image source={images.emptyride} style={styles.emptyStateImage} />
                <TouchableOpacity style={styles.getRideButton} onPress={() => navigation.navigate('TabStack')}>
                  <Text style={styles.getRideButtonText}>Get a ride</Text>
                </TouchableOpacity>
              </View>
            )}
          />

        </>
      ) : (
        // Empty State
        <View style={styles.emptyStateContainer}>
          <Text style={styles.emptyStateText}>No Recent Ride</Text>

          <Image source={images.emptyride} style={styles.emptyStateImage} />
          <TouchableOpacity style={styles.getRideButton} onPress={() => navigation.navigate('TakeABreak')}>
            <Text style={styles.getRideButtonText}>Get a ride</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  header: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  fundsCard: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    padding: SIZES.padding,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 20,
    elevation: 5,
    shadowColor: COLORS.grey,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

  },
  fundsLabel: {
    ...FONTS.body3,
    color: COLORS.black,
  },
  fundsValue: {
    ...FONTS.h2,
    color: COLORS.primary,
    marginVertical: 15,
  },
  fundsSubtitle: {
    ...FONTS.body4,
    color: COLORS.black,
  },
  nextIcon: {
    width: 12,
    height: 12,
    resizeMode: 'contain',
  },
  sectionTitle: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 10,
    marginVertical: 20,
  },
  rideList: {
    paddingBottom: 20,
  },
  rideItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderColor: COLORS.border,
  },
  rideTitle: {
    ...FONTS.body3,
    fontSize: 12,

    color: COLORS.black,
  },
  rideDate: {
    ...FONTS.body4,
    fontSize: 11,
    color: COLORS.grey,
  },
  rideAmount: {
    ...FONTS.body4,
    textAlign: 'right',
    color: COLORS.black,
  },
  rideStatus: {
    ...FONTS.body4,
    fontSize: 11,
  },
  emptyStateContainer: {
    flex: 1,
    marginTop: 50,
    // justifyContent: 'center',
    // alignItems: 'center',
  },
  emptyStateImage: {
    width: 100,
    height: 100,
    marginBottom: 20,
    alignSelf: 'center',
    marginVertical: 30,
  },
  emptyStateText: {
    ...FONTS.body3,
    color: COLORS.black,
    marginBottom: 20,
    textAlign: 'center',
  },
  getRideButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    paddingVertical: 15,
    paddingHorizontal: 40,
    alignItems: 'center',
  },
  getRideButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Semi-transparent background
  },
  modalContent: {
    width: '85%',
    backgroundColor: COLORS.white,
    padding: 20,
    borderRadius: 10,
    alignItems: 'center',
    elevation: 5, // Shadow for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  modalTitle: {
    ...FONTS.h2,
    color: COLORS.black,
    marginBottom: 10,
  },
  modalText: {
    ...FONTS.body3,
    color: COLORS.darkGray,
    marginBottom: 5,
  },
  closeButton: {
    marginTop: 15,
    backgroundColor: COLORS.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  closeButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },


});

export default MyRidesScreen;
