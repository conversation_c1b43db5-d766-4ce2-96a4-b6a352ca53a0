import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TextInput,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  Platform,
  Alert, // Import ActivityIndicator for the loading spinner
} from 'react-native';
import {COLORS, FONTS, SIZES} from '../../constants/theme';
import {images} from '../../constants';
import {useNavigation} from '@react-navigation/native';
import {API_BASE_URL} from '../../config/apiConfig';

interface MessageItem {
  id: string;
  content: string;
  rideId?: string;
  passengerName?: string;
  driverName?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
}

const Message = () => {
  const [search, setSearch] = useState('');
  const [messages, setMessages] = useState<MessageItem[]>([]); // State to hold messages
  const [isLoading, setIsLoading] = useState(false); // State to handle loading

  const navigation = useNavigation();

  // Fetch messages from the API
  useEffect(() => {
    const fetchMessages = async () => {
      setIsLoading(true); // Start loading
      try {
        const response = await fetch(
          `${API_BASE_URL}/rideChat/getCustomerChat`,
          {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );

        // Check if response is JSON before parsing
        const contentType = response.headers.get('content-type');
        const isJson = contentType && contentType.includes('application/json');

        if (!isJson) {
          const textResponse = await response.text();
          console.error('Non-JSON response received:', textResponse);
          throw new Error('Server returned non-JSON response');
        }

        const data = await response.json();
        console.log(data.message, 'response in getCustomerChat');

        if (response.ok) {
          // The API returns ride data, not chat messages
          // Extract rides array and convert to message format for display
          const ridesData = data?.message?.rides || data?.rides || [];

          if (Array.isArray(ridesData)) {
            // Convert rides to message-like format for display
            const messagesList = ridesData.map((ride, index) => ({
              id: ride.rideId || index,
              content: `Ride with ${ride.passengerName} - ${ride.status}`,
              rideId: ride.rideId,
              passengerName: ride.passengerName,
              driverName: ride.driverName,
              status: ride.status,
              startDate: ride.startDate,
              endDate: ride.endDate
            }));

            setMessages(messagesList);
          } else {
            console.warn('Rides data is not an array:', ridesData);
            setMessages([]); // Set empty array as fallback
          }
        } else {
          throw new Error('Failed to fetch messages');
        }
      } catch (error) {
        console.error('Error fetching messages:', error);
        setMessages([]); // Set empty array on error to prevent filter issues
      } finally {
        setIsLoading(false); // End loading
      }
    };

    fetchMessages();
  }, []);

  const renderMessage = ({item}: {item: MessageItem}) => {
    if (!item) return null;

    const handleMessagePress = () => {
      console.log('💬 [Messages] Message item clicked:', JSON.stringify(item, null, 2));
      console.log('💬 [Messages] Opening chat for:', {
        rideId: item.rideId,
        passengerName: item.passengerName,
        status: item.status
      });

      if (!item.rideId) {
        Alert.alert('Error', 'Cannot open chat: Ride ID not found');
        return;
      }

      // Navigate to ChatScreen with ride and passenger information
      navigation.navigate('ChatScreen', {
        rideId: item.rideId,
        passengerName: item.passengerName || 'Passenger',
        passengerImg: '', // No image available from messages list
        driverId: '', // Will be handled by ChatScreen
        passengerId: '', // Will be handled by ChatScreen
        fromMessages: true // Flag to indicate navigation source
      });
    };

    return (
      <TouchableOpacity style={styles.messageItem} onPress={handleMessagePress}>
        <View style={styles.messageHeader}>
          <Text style={styles.messageTitle}>
            {item.passengerName ? `${item.passengerName}` : 'Passenger'}
          </Text>
          <Text style={styles.messageTime}>
            {item.startDate ? new Date(item.startDate).toLocaleDateString() : ''}
          </Text>
        </View>
        {item.rideId && (
          <Text style={styles.rideId}>
            Ride ID: {item.rideId}
          </Text>
        )}
        <View style={styles.messageFooter}>
          <Text style={styles.tapToOpen}>Tap to open chat</Text>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {Platform.OS === 'ios' && <View style={{marginTop: 60}} />}
      {Platform.OS === 'android' && <View style={{marginTop: 50}} />}
      {/* Header */}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.title}>Message</Text>
        <Text></Text>
      </View>

      {/* Loading Indicator */}
      {isLoading ? (
        <ActivityIndicator size="large" color={COLORS.primary} />
      ) : (
        <FlatList
          data={
            Array.isArray(messages)
              ? messages.filter(
                  msg =>
                    msg?.content
                      ?.toLowerCase()
                      ?.includes(search.toLowerCase()) || false,
                )
              : []
          }
          renderItem={renderMessage}
          keyExtractor={(item, index) =>
            item?.id?.toString() || index.toString()
          }
          contentContainerStyle={styles.messageList}
          ListEmptyComponent={() => (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateTitle}>No Messages</Text>
              <Text style={styles.emptyStateText}>
                Your ride conversations will appear here.{'\n'}
                Start a ride to begin chatting with passengers.
              </Text>
            </View>
          )}
          ListHeaderComponent={
            <View style={styles.searchContainer}>
              <TextInput
                style={styles.searchInput}
                placeholder="Search messages"
                placeholderTextColor={COLORS.grey}
                value={search}
                onChangeText={setSearch}
              />
            </View>
          }
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
    padding: SIZES.padding,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    justifyContent: 'space-between',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1,
  },
  shareModal: {
    position: 'absolute',
    bottom: 250,
    top: 250,
    left: 20,
    right: 20,
    backgroundColor: COLORS.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    padding: SIZES.padding,
    alignItems: 'center',
    zIndex: 2,
  },
  closeButton: {
    position: 'absolute',
    top: 20,
    right: 20,
  },
  closeButtonText: {
    fontSize: 24,
    color: COLORS.black,
  },
  shareModalTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    textAlign: 'center',
    marginHorizontal: 50,
    marginTop: 20,
  },
  shareModalSubtitle: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    marginHorizontal: 50,

    marginVertical: 30,
  },
  okayButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    width: '80%',
    alignItems: 'center',
  },
  okayButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },

  backButton: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
    ...FONTS.body3,
    color: COLORS.primary,
    marginRight: 10,
  },
  title: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  searchContainer: {
    backgroundColor: COLORS.white,
    borderRadius: SIZES.radius,
    paddingHorizontal: 15,
    paddingVertical: 10,
    marginBottom: 20,
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    ...FONTS.body3,
    color: COLORS.black,
  },
  friendList: {
    marginBottom: 20,
  },
  friendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.border,
  },
  friendImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 10,
  },
  friendDetails: {
    flex: 1,
  },
  friendName: {
    ...FONTS.body3,
    fontSize: 14,
    color: COLORS.black,
  },
  friendPhone: {
    ...FONTS.body4,
    fontSize: 12,

    color: COLORS.grey,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderWidth: 1,
    borderColor: COLORS.grey,
    borderRadius: 4,
  },
  checkboxSelected: {
    backgroundColor: COLORS.primary,
  },
  shareButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  shareButtonText: {
    ...FONTS.body3,
    color: COLORS.white,
  },

  messageItem: {
    padding: SIZES.padding,
    marginHorizontal: 8,
    marginVertical: 6,
    backgroundColor: COLORS.white,
    borderRadius: 8,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  messageTitle: {
    ...FONTS.h4,
    color: COLORS.black,
    fontWeight: '600',
    flex: 1,
  },
  messageTime: {
    ...FONTS.body5,
    color: COLORS.grey,
    fontSize: 12,
  },

  rideId: {
    ...FONTS.body5,
    color: COLORS.grey,
    fontSize: 11,
    marginBottom: 4,
  },
  messageFooter: {
    paddingTop: 4,
    alignItems: 'flex-end',
  },
  tapToOpen: {
    ...FONTS.body5,
    color: COLORS.primary,
    fontSize: 11,
    fontWeight: '500',
  },
  messageContent: {
    ...FONTS.body3,
  },
  messageList: {
    padding: SIZES.padding,
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyStateTitle: {
    ...FONTS.h3,
    color: COLORS.black,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptyStateText: {
    ...FONTS.body4,
    color: COLORS.grey,
    textAlign: 'center',
    lineHeight: 22,
  },
});

export default Message;
