 

import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import MapView, { Marker } from 'react-native-maps';
import { COLORS, FONTS, SIZES } from '../../constants/theme';
import { images } from '../../constants';
import { useNavigation } from '@react-navigation/native';

const ConfirmLocation = () => {
  const [location, setLocation] = useState('AMLI 7th Street Station...');

  const handleConfirm = () => {
    console.log('Location confirmed:', location);
  };
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* Header */}
           {Platform.OS === 'ios' && <View style={{ marginTop: 60 }} />}     
                {Platform.OS === 'android' && <View style={{ marginTop: 50 }} />}
      <View style={styles.headerContainer}>
        <TouchableOpacity onPress={()=> navigation.goBack()}>
          <Image source={images.goback} style={styles.backButton} />
        </TouchableOpacity>
        <Text style={styles.header}>Confirm location</Text>
        <Text></Text>
      </View>

      {/* Map */}
      <MapView
        style={styles.map}
        initialRegion={{
          latitude: 33.5186, // Example latitude
          longitude: -86.8104, // Example longitude
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        }}
      >
        <Marker
          coordinate={{ latitude: 33.5186, longitude: -86.8104 }}
          title="Pickup Location"
        />
      </MapView>

      {/* Confirm Location Section */}
      <View style={styles.confirmContainer}>
        <Text style={styles.confirmHeader}>Confirm location</Text>

        <View style={styles.inputRow}>
          <TextInput
            style={styles.input}
            placeholder="Enter location"
            
            placeholderTextColor={COLORS.grey}
            value={location}
            onChangeText={setLocation}
          />
          <TouchableOpacity>
            <Image source={images.searchIcon} style={styles.searchIcon} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
          <Text style={styles.confirmButtonText}>Confirm location</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.light_blue,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: SIZES.padding,
  },
  header: {
    ...FONTS.h3,
    color: COLORS.black,
  },
  backButton: {
    width: 25,
    height: 25,
  },
  map: {
    flex: 1,
  },
  confirmContainer: {
    backgroundColor: COLORS.white,
    padding: SIZES.padding,
    borderTopLeftRadius: SIZES.radius,
    borderTopRightRadius: SIZES.radius,
  },
  confirmHeader: {
    ...FONTS.body3,
    textAlign:'center',
    color: COLORS.black,
    marginBottom: 15,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  input: {
    flex: 1,
    borderRadius: SIZES.radius,
    padding: 15,
    ...FONTS.body3,
    color: COLORS.black,
    marginRight: 10,
  },
  searchIcon: {
    width: 25,
    height: 25,
  },
  confirmButton: {
    backgroundColor: COLORS.primary,
    borderRadius: SIZES.radius,
    padding: 15,
    alignItems: 'center',
  },
  confirmButtonText: {
    ...FONTS.h3,
    color: COLORS.white,
  },
});

export default ConfirmLocation;
