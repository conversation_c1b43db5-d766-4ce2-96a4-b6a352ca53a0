import React, { createContext, useState, useEffect, useContext } from "react";
import { AppState } from "react-native";
import socketService from "./socketService";
import callNotificationService from "./CallNotificationService";
import InCallManager from 'react-native-incall-manager';
import PushNotification from 'react-native-push-notification';

// Create Call Context
const CallContext = createContext();

export const CallProvider = ({ children }) => {
  const [incomingCall, setIncomingCall] = useState(null);
  const [callStatus, setCallStatus] = useState("idle"); // idle, ringing, in-call, calling
  const [showGlobalCallScreen, setShowGlobalCallScreen] = useState(false);
  const [currentCall, setCurrentCall] = useState(null);
  const [appState, setAppState] = useState(AppState.currentState);
  const [pendingCallData, setPendingCallData] = useState(null);

  // Handle app state changes for background call management
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      setAppState(nextAppState);

      if (nextAppState === 'background' && callStatus === 'ringing' && incomingCall) {
        // Show enhanced notification for incoming call when app is in background
        callNotificationService.showIncomingCallNotification(incomingCall);
      } else if (nextAppState === 'active' && callStatus === 'ringing') {
        // Clear notifications when app comes to foreground during incoming call
        callNotificationService.clearIncomingCallNotification();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [callStatus, incomingCall]);

  useEffect(() => {
    console.log('🔄 [CallContext] Setting up unified socket event handlers');

    // Unified incoming call handlers
    socketService.setIncomingCallHandler((data) => {
      console.log('📞 [CallContext] Incoming audio call received:', JSON.stringify(data, null, 2));
      handleIncomingCall(data, false); // false = audio call
    });

    socketService.setIncomingVideoCallHandler((data) => {
      console.log('📹 [CallContext] Incoming video call received:', JSON.stringify(data, null, 2));
      // Store the initial call data for video calls that need tokens
      setPendingCallData(data);
      handleIncomingCall(data, true); // true = video call
    });

    // Stream.io token handlers
    socketService.setCallerTokenHandler((data) => {
      console.log('📞 [CallContext] Caller token received:', JSON.stringify(data, null, 2));

      // Update current call with caller token
      if (currentCall) {
        setCurrentCall(prev => ({
          ...prev,
          callId: data.callId,
          token: data.token,
          success: true
        }));
      }
    });

    socketService.setReceiverTokenHandler((data) => {
      console.log('📞 [CallContext] Receiver token received:', JSON.stringify(data, null, 2));

      // If we have pending call data, combine it with the token
      if (pendingCallData) {
        const enhancedCallData = {
          ...pendingCallData,
          callId: data.callId,
          token: data.token,
          success: true // Now we have all required data
        };

        console.log('📞 [CallContext] Enhanced call data with token:', JSON.stringify(enhancedCallData, null, 2));

        // Update the current call with the enhanced data
        setCurrentCall(enhancedCallData);
        setPendingCallData(null);
      } else if (currentCall) {
        // If we already have a current call, just update it with the token
        setCurrentCall(prev => ({
          ...prev,
          callId: data.callId,
          token: data.token,
          success: true
        }));
      }
    });

    // Unified call status handlers
    socketService.setCallAcceptedHandler((data) => {
      console.log('✅ [CallContext] Audio call accepted:', JSON.stringify(data, null, 2));
      setCallStatus("in-call");
      setShowGlobalCallScreen(false);
    });

    socketService.setVideoCallAcceptedHandler((data) => {
      console.log('✅ [CallContext] Video call accepted:', JSON.stringify(data, null, 2));
      setCallStatus("in-call");
      setShowGlobalCallScreen(false);
    });

    socketService.setCallRejectedHandler(() => {
      console.log('❌ [CallContext] Audio call rejected');
      handleCallEnd();
    });

    socketService.setVideoCallRejectedHandler(() => {
      console.log('❌ [CallContext] Video call rejected');
      handleCallEnd();
    });

    socketService.setCallEndedHandler(() => {
      console.log('🔚 [CallContext] Audio call ended');
      handleCallEnd();
    });

    socketService.setVideoCallEndedHandler(() => {
      console.log('🔚 [CallContext] Video call ended');
      handleCallEnd();
    });

    socketService.setVideoCallErrorHandler((data) => {
      console.log('⚠️ [CallContext] Video call error:', data);
      if (data.error) {
        console.error('Video call error details:', data.error);
        handleCallEnd();
      }
    });

    socketService.setVideoCallFailedHandler((data) => {
      console.log('❌ [CallContext] Video call failed:', data);
      handleCallEnd();
    });

    return () => {
      console.log('🧹 [CallContext] Cleaning up unified socket handlers');
      // Clean up all unified socket handlers
      socketService.setIncomingCallHandler(null);
      socketService.setIncomingVideoCallHandler(null);
      socketService.setCallAcceptedHandler(null);
      socketService.setVideoCallAcceptedHandler(null);
      socketService.setCallRejectedHandler(null);
      socketService.setVideoCallRejectedHandler(null);
      socketService.setCallEndedHandler(null);
      socketService.setVideoCallEndedHandler(null);
      socketService.setCallerTokenHandler(null);
      socketService.setReceiverTokenHandler(null);
      socketService.setVideoCallErrorHandler(null);
      socketService.setVideoCallFailedHandler(null);
    };
  }, [currentCall, pendingCallData]);

  // Enhanced call handling functions
  const handleIncomingCall = (data, isVideoCall = false) => {
    console.log(`📞 [CallContext] Processing incoming ${isVideoCall ? 'video' : 'audio'} call:`, JSON.stringify(data, null, 2));

    const callData = {
      rideId: data.rideId,
      callId: data.callId,
      callerName: data.caller?.name || data.callerName || 'Unknown Caller',
      callerImage: data.caller?.profileImg || data.callerImage,
      callerPhone: data.caller?.phone || data.callerPhone,
      isVideoCall,
      isIncoming: true,
      timestamp: Date.now(),
      success: data.success,
      token: data.token
    };

    console.log('📞 [CallContext] Setting call states with data:', JSON.stringify(callData, null, 2));
    
    // Set all states immediately to ensure UI updates
    setIncomingCall(callData);
    setCurrentCall(callData);
    setCallStatus("ringing");
    setShowGlobalCallScreen(true);

    // Start call manager for audio
    console.log('🔊 [CallContext] Starting InCallManager');
    InCallManager.start({ media: 'audio', auto: true, ringback: '_DTMF_' });
    InCallManager.setKeepScreenOn(true);
    InCallManager.setSpeakerphoneOn(false);

    // Clear any existing notifications
    console.log('🔔 [CallContext] Clearing existing notifications');
    PushNotification.cancelAllLocalNotifications();

    // Show incoming call notification if app is in background
    if (appState !== 'active') {
      console.log('🔔 [CallContext] App in background, showing notification');
      callNotificationService.showIncomingCallNotification(callData);
    }
  };

  const handleCallEnd = () => {
    console.log('🔚 [CallContext] Handling call end');
    const callId = currentCall?.callId;

    // Reset all states
    setIncomingCall(null);
    setCurrentCall(null);
    setCallStatus("idle");
    setShowGlobalCallScreen(false);

    // Clear notifications and stop call manager
    callNotificationService.clearAllCallNotifications();
    if (callId) {
      callNotificationService.clearOngoingCallNotification(callId);
    }

    try {
      InCallManager.stop();
      InCallManager.setKeepScreenOn(false);
      console.log('✅ [CallContext] InCallManager cleaned up');
    } catch (error) {
      console.error('❌ [CallContext] Error cleaning up InCallManager:', error);
    }
  };

  const callUser = (rideId, receiverId, isVideoCall = false) => {
    console.log(`📞 Initiating ${isVideoCall ? 'video' : 'audio'} call`);

    const callData = {
      rideId,
      callId: Date.now().toString(), // Generate a temporary call ID
      callerName: 'You', // This will be updated with actual caller info
      isVideoCall,
      isIncoming: false,
      timestamp: Date.now(),
    };

    // Set outgoing call states
    setCurrentCall(callData);
    setCallStatus("calling");
    setShowGlobalCallScreen(true);

    // Use unified socket service for all calls
    socketService.callUser(rideId, receiverId, isVideoCall);
  };

  const acceptCall = (rideId, callId) => {
    console.log('✅ [CallContext] Accepting call:', rideId, callId, 'isVideoCall:', currentCall?.isVideoCall);

    if (!rideId || !callId) {
      console.error('❌ [CallContext] Cannot accept call - missing rideId or callId');
      return;
    }

    // Use unified socket service for all call acceptances
    const success = socketService.acceptCall(rideId, callId, currentCall?.isVideoCall);

    if (success) {
      console.log('✅ [CallContext] Call acceptance sent to server successfully');
      setCallStatus("in-call");
      setShowGlobalCallScreen(false);

      // Clear incoming call notifications and show ongoing call notification
      callNotificationService.clearIncomingCallNotification();
      if (currentCall) {
        callNotificationService.showOngoingCallNotification(currentCall);
      }

      // Navigate to CallScreen after accepting the call
      try {
        // Use global navigation reference
        const { navigate } = require('../../redux/shared');
        console.log('🔄 [CallContext] Navigating to CallScreen after call acceptance');
        navigate('CallScreen', {
          rideId,
          callId,
          isIncoming: true,
          isVideoCall: currentCall?.isVideoCall,
          callerName: currentCall?.callerName,
          callerImage: currentCall?.callerImage,
          callerPhone: currentCall?.callerPhone,
        });
      } catch (error) {
        console.error('❌ [CallContext] Error navigating to CallScreen:', error);
      }
    } else {
      console.error('❌ [CallContext] Failed to send call acceptance to server');
      // Keep the call screen open if acceptance failed
    }
  };

  const rejectCall = (rideId, callId) => {
    console.log('❌ [CallContext] Rejecting call:', rideId, callId, 'isVideoCall:', currentCall?.isVideoCall);

    if (!rideId || !callId) {
      console.error('❌ [CallContext] Cannot reject call - missing rideId or callId');
      handleCallEnd(); // Still end the call locally
      return;
    }

    // Use unified socket service for all call rejections
    const success = socketService.rejectCall(rideId, callId, currentCall?.isVideoCall);

    if (success) {
      console.log('✅ [CallContext] Call rejection sent to server successfully');
    } else {
      console.error('❌ [CallContext] Failed to send call rejection to server');
    }

    // Always end the call locally regardless of server response
    handleCallEnd();
  };

  const endCall = (rideId, callId) => {
    console.log('🔚 Ending call:', rideId, callId);

    // Use unified socket service for all call endings
    socketService.endCall(rideId, callId, currentCall?.isVideoCall);

    handleCallEnd();
  };

  const dismissGlobalCallScreen = () => {
    setShowGlobalCallScreen(false);
  };

  return (
    <CallContext.Provider value={{
      incomingCall,
      callStatus,
      showGlobalCallScreen,
      currentCall,
      appState,
      callUser,
      acceptCall,
      rejectCall,
      endCall,
      dismissGlobalCallScreen,
      handleCallEnd
    }}>
      {children}
    </CallContext.Provider>
  );
};

// Hook to use Call Context
export const useCall = () => useContext(CallContext);
