import Geolocation from '@react-native-community/geolocation';
import { Platform, Alert, Linking } from 'react-native';
import { request, PERMISSIONS, RESULTS } from 'react-native-permissions';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socketService from './socketService';

class LocationService {
  constructor() {
    this.currentLocation = null;
    this.isTracking = false;
    this.locationInterval = null;
    this.updateInterval = 5000; // Update every 5 seconds
    this.permissionGranted = false;
    this.listeners = [];
    this.isInitialized = false;

    // Statistics tracking
    this.locationUpdateCount = 0;
    this.lastUpdateTime = null;
    this.trackingStartTime = null;
  }

  /**
   * Initialize location service - request permissions and get initial location
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('📍 [LocationService] Already initialized');
      return true;
    }

    console.log('📍 [LocationService] Initializing location service...');

    try {
      // Request location permissions
      const hasPermission = await this.requestLocationPermission();
      if (!hasPermission) {
        console.warn('⚠️ [LocationService] Location permission denied');
        return false;
      }

      // Get initial location
      const location = await this.getCurrentLocation();
      if (location) {
        console.log('✅ [LocationService] Initial location obtained:', location);
        await this.saveLocationToStorage(location);
        this.isInitialized = true;
        return true;
      }

      return false;
    } catch (error) {
      console.error('❌ [LocationService] Error initializing location service:', error);
      return false;
    }
  }

  /**
   * Request location permissions
   */
  async requestLocationPermission() {
    const permission = Platform.select({
      ios: PERMISSIONS.IOS.LOCATION_WHEN_IN_USE,
      android: PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION,
    });

    try {
      const result = await request(permission);
      console.log('🛰️ [LocationService] Permission result:', result);
      
      if (result === RESULTS.GRANTED) {
        this.permissionGranted = true;
        return true;
      } else {
        console.log('❌ [LocationService] Location permission denied');
        Alert.alert(
          'Permission Required',
          'Location access is required for the driver app to function properly. Please enable location permissions in Settings.',
          [
            {
              text: 'Open Settings',
              onPress: () => Linking.openSettings(),
            },
            {
              text: 'Cancel',
              style: 'cancel',
            },
          ]
        );
        return false;
      }
    } catch (error) {
      console.error('⚠️ [LocationService] Permission error:', error);
      return false;
    }
  }

  /**
   * Get current location
   */
  getCurrentLocation() {
    return new Promise((resolve, reject) => {
      if (!this.permissionGranted) {
        console.warn('⚠️ [LocationService] Location permission not granted');
        reject(new Error('Location permission not granted'));
        return;
      }

      console.log('📍 [LocationService] Getting current location...');
      
      Geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;

          // Standardized location format for consistency
          const location = {
            type: 'Point',
            coordinates: [longitude, latitude], // [lng, lat] - GeoJSON standard
            timestamp: Date.now(),
            accuracy: position.coords.accuracy || null
          };

          console.log('✅ [LocationService] Location retrieved:', location);
          this.currentLocation = location;
          this.lastUpdateTime = Date.now();
          this.locationUpdateCount++;
          this.notifyListeners(location);
          resolve(location);
        },
        (error) => {
          console.error('❌ [LocationService] Location error:', error);
          
          // Handle specific error codes
          if (error.code === 2) {
            Alert.alert(
              'Location Services Disabled',
              'Please enable location services in your device settings.',
              [
                { text: 'Open Settings', onPress: () => Linking.openSettings() },
                { text: 'Cancel', style: 'cancel' }
              ]
            );
          }
          
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000, // Reduced timeout for faster response
          maximumAge: 5000, // Reduced cache age for more frequent updates
        }
      );
    });
  }

  /**
   * Start continuous location tracking
   */
  async startTracking() {
    if (this.isTracking) {
      console.log('🔄 [LocationService] Location tracking already active');
      return;
    }

    if (!this.isInitialized) {
      const initialized = await this.initialize();
      if (!initialized) {
        console.error('❌ [LocationService] Failed to initialize, cannot start tracking');
        return;
      }
    }

    console.log(`🟢 [LocationService] Starting location tracking with ${this.updateInterval/1000}s intervals...`);
    this.isTracking = true;
    this.trackingStartTime = Date.now();
    this.locationUpdateCount = 0;

    // Get initial location and send to server
    try {
      await this.getCurrentLocation();
      this.sendLocationToServer();
    } catch (error) {
      console.error('❌ [LocationService] Error getting initial location:', error);
    }

    // Set up interval for continuous tracking every 5 seconds
    this.locationInterval = setInterval(async () => {
      try {
        console.log('🔄 [LocationService] Periodic location update (5s interval)');
        await this.getCurrentLocation();
        this.sendLocationToServer();

        // Log statistics every 30 seconds (every 6 updates)
        if (this.locationUpdateCount % 6 === 0) {
          this.logTrackingStats();
        }
      } catch (error) {
        console.error('❌ [LocationService] Error in location tracking interval:', error);
      }
    }, this.updateInterval);
  }

  /**
   * Stop location tracking
   */
  stopTracking() {
    if (!this.isTracking) {
      console.log('⚠️ [LocationService] Location tracking not active');
      return;
    }

    console.log('🔴 [LocationService] Stopping location tracking...');
    this.isTracking = false;

    if (this.locationInterval) {
      clearInterval(this.locationInterval);
      this.locationInterval = null;
    }
  }

  /**
   * Send current location to server via socket
   */
  sendLocationToServer() {
    if (!this.currentLocation) {
      console.warn('⚠️ [LocationService] No current location to send');
      return;
    }

    const timestamp = new Date().toLocaleTimeString();
    console.log(`📡 [LocationService] [${timestamp}] Sending location update:`, {
      coordinates: this.currentLocation.coordinates,
      accuracy: this.currentLocation.accuracy
    });

    let locationSent = false;

    // Send to driver socket (primary)
    if (socketService.driverSocket && socketService.isDriverConnected) {
      socketService.driverSocket.emit('updateLocation', this.currentLocation);
      console.log(`✅ [LocationService] [${timestamp}] Location sent via driver socket`);
      locationSent = true;
    }
    // Fallback to call socket
    else if (socketService.callSocket && socketService.isCallConnected) {
      socketService.callSocket.emit('updateLocation', this.currentLocation);
      console.log(`✅ [LocationService] [${timestamp}] Location sent via call socket (fallback)`);
      locationSent = true;
    }

    if (!locationSent) {
      console.warn(`⚠️ [LocationService] [${timestamp}] No socket connection available to send location`);
    }
  }

  /**
   * Send location immediately (for login/goOnline events)
   */
  async sendLocationImmediately() {
    try {
      console.log('🚀 [LocationService] Getting and sending location immediately...');
      
      const location = await this.getCurrentLocation();
      if (location) {
        this.sendLocationToServer();
        return location;
      }
    } catch (error) {
      console.error('❌ [LocationService] Error sending location immediately:', error);
      
      // Try to use cached location if available
      if (this.currentLocation) {
        console.log('🔄 [LocationService] Using cached location');
        this.sendLocationToServer();
        return this.currentLocation;
      }
    }
    
    return null;
  }

  /**
   * Save location to AsyncStorage for offline access
   */
  async saveLocationToStorage(location) {
    try {
      await AsyncStorage.setItem('lastKnownLocation', JSON.stringify(location));
      console.log('💾 [LocationService] Location saved to storage');
    } catch (error) {
      console.error('❌ [LocationService] Error saving location to storage:', error);
    }
  }

  /**
   * Get location from AsyncStorage
   */
  async getLocationFromStorage() {
    try {
      const locationString = await AsyncStorage.getItem('lastKnownLocation');
      if (locationString) {
        const location = JSON.parse(locationString);
        console.log('📱 [LocationService] Location retrieved from storage:', location);
        return location;
      }
    } catch (error) {
      console.error('❌ [LocationService] Error getting location from storage:', error);
    }
    return null;
  }

  /**
   * Add location update listener
   */
  addLocationListener(callback) {
    this.listeners.push(callback);
    console.log('👂 [LocationService] Location listener added');
  }

  /**
   * Remove location update listener
   */
  removeLocationListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
    console.log('🔇 [LocationService] Location listener removed');
  }

  /**
   * Notify all listeners of location updates
   */
  notifyListeners(location) {
    this.listeners.forEach(callback => {
      try {
        callback(location);
      } catch (error) {
        console.error('❌ [LocationService] Error in location listener:', error);
      }
    });
  }

  /**
   * Get current location (synchronous)
   */
  getLastKnownLocation() {
    return this.currentLocation;
  }

  /**
   * Check if location services are available
   */
  isLocationAvailable() {
    return this.permissionGranted && this.currentLocation !== null;
  }

  /**
   * Set location update interval (in milliseconds)
   */
  setUpdateInterval(intervalMs) {
    console.log(`🔧 [LocationService] Setting update interval to ${intervalMs/1000}s`);
    this.updateInterval = intervalMs;

    // If tracking is active, restart with new interval
    if (this.isTracking) {
      console.log('🔄 [LocationService] Restarting tracking with new interval');
      this.stopTracking();
      setTimeout(() => {
        this.startTracking();
      }, 100);
    }
  }

  /**
   * Get current update interval
   */
  getUpdateInterval() {
    return this.updateInterval;
  }

  /**
   * Get location tracking statistics
   */
  getTrackingStats() {
    const now = Date.now();
    const trackingDuration = this.trackingStartTime ? (now - this.trackingStartTime) / 1000 : 0;
    const expectedUpdates = trackingDuration > 0 ? Math.floor(trackingDuration / (this.updateInterval / 1000)) : 0;

    return {
      isTracking: this.isTracking,
      updateInterval: this.updateInterval,
      updateCount: this.locationUpdateCount,
      expectedUpdates,
      trackingDuration: Math.round(trackingDuration),
      lastUpdateTime: this.lastUpdateTime,
      successRate: expectedUpdates > 0 ? Math.round((this.locationUpdateCount / expectedUpdates) * 100) : 0
    };
  }

  /**
   * Log tracking statistics
   */
  logTrackingStats() {
    const stats = this.getTrackingStats();
    console.log('📊 [LocationService] Tracking Statistics:', stats);
  }

  /**
   * Force an immediate location update (for testing)
   */
  async forceLocationUpdate() {
    console.log('🔧 [LocationService] Forcing immediate location update...');
    try {
      await this.getCurrentLocation();
      this.sendLocationToServer();
      console.log('✅ [LocationService] Forced location update completed');
    } catch (error) {
      console.error('❌ [LocationService] Error in forced location update:', error);
    }
  }

  /**
   * Cleanup - stop tracking and clear intervals
   */
  cleanup() {
    console.log('🧹 [LocationService] Cleaning up location service...');

    // Log final statistics before cleanup
    if (this.isTracking) {
      this.logTrackingStats();
    }

    this.stopTracking();
    this.listeners = [];
    this.currentLocation = null;
    this.permissionGranted = false;
    this.isInitialized = false;

    // Reset statistics
    this.locationUpdateCount = 0;
    this.lastUpdateTime = null;
    this.trackingStartTime = null;
  }
}

// Export singleton instance
const locationService = new LocationService();
export default locationService;
