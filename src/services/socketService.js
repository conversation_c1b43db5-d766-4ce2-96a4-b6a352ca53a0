import io from 'socket.io-client';
import { SOCKET_URL, VIDEO_CALL_SOCKET_URL } from '../config/apiConfig';
import { AppState } from 'react-native';

class SocketService {
  constructor() {
    // Driver namespace socket (for general driver functionality)
    this.driverSocket = null;
    this.isDriverConnected = false;

    // General namespace socket (for call functionality)
    this.callSocket = null;
    this.isCallConnected = false;

    // General connection status
    this.isOnline = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;

    // General event handlers
    this.onIncomingCall = null;
    this.onCallAccepted = null;
    this.onCallRejected = null;
    this.onCallEnded = null;

    // Video call specific handlers
    this.onIncomingVideoCall = null;
    this.onVideoCallAccepted = null;
    this.onVideoCallRejected = null;
    this.onVideoCallEnded = null;
    this.onVideoCallError = null;
    this.onVideoCallFailed = null;
    this.onCallInitiated = null;

    // Stream.io token handlers
    this.onCallerToken = null;
    this.onReceiverToken = null;

    // WebRTC signaling handlers
    this.onOffer = null;
    this.onAnswer = null;
    this.onIceCandidate = null;

    console.log("Unified SocketService initialized");
  }

  // Handle app state changes for both sockets
  setupAppStateHandling() {
    AppState.addEventListener('change', (nextAppState) => {
      if (nextAppState === 'active') {
        console.log('🔄 [SocketService] App came to foreground, checking socket connections...');
        if (!this.isDriverConnected) {
          this.connectDriverSocket();
        }
        if (!this.isCallConnected) {
          this.connectCallSocket();
        }
      }
    });
  }

  // Setup driver-specific event handlers on the driver socket
  setupDriverEventHandlers() {
    if (!this.driverSocket) {
      console.error('❌ [SocketService] Cannot setup driver handlers - driver socket not initialized');
      return;
    }

    // Driver-specific events
    this.driverSocket.on('rideUpdated', (data) => {
      console.log('📍 [SocketService] Ride updated:', data);
    });

    this.driverSocket.on('rideAccepted', (data) => {
      console.log('✅ [SocketService] Ride accepted:', data);
    });

    this.driverSocket.on('rideRejected', (data) => {
      console.log('❌ [SocketService] Ride rejected:', data);
    });

    this.driverSocket.on('error', (error) => {
      console.error('⚠️ [SocketService] Driver socket error:', error);
    });

    this.driverSocket.on('message', (data) => {
      console.log('💬 [SocketService] Message received:', data);
    });

    this.driverSocket.on('locationUpdate', (data) => {
      console.log('📍 [SocketService] Location update:', data);
    });

    this.driverSocket.on('driverStatusUpdate', (data) => {
      console.log('🚗 [SocketService] Driver status update:', data);
    });

    this.driverSocket.on('rideRequest', (data) => {
      console.log('🚖 [SocketService] Ride request:', data);
    });

    this.driverSocket.on('paymentUpdate', (data) => {
      console.log('💰 [SocketService] Payment update:', data);
    });

    // Listen for online status confirmation on driver socket
    this.driverSocket.on('statusUpdated', (response) => {
      console.log('✅ [SocketService] Driver online status updated:', response);
    });

    // Chat-specific events on driver socket
    this.driverSocket.on('getRideChat', (data) => {
      console.log('💬 [SocketService] Chat history received:', data);
    });

    this.driverSocket.on('chatWithPassenger', (data) => {
      console.log('💬 [SocketService] New chat message from passenger:', data);
    });

    this.driverSocket.on('getChatHistoryResponse', (data) => {
      console.log('💬 [SocketService] Legacy chat history response:', data);
    });
  }

  connect() {
    console.log("🔄 [SocketService] Initializing dual namespace socket connections");

    // Connect to driver namespace for general functionality
    this.connectDriverSocket();

    // Connect to general namespace for call functionality
    this.connectCallSocket();
  }

  connectDriverSocket() {
    if (this.driverSocket && this.isDriverConnected) {
      console.log("🔄 [SocketService] Driver socket already connected");
      return;
    }

    console.log("🔄 [SocketService] Connecting to driver namespace:", SOCKET_URL);

    this.driverSocket = io(SOCKET_URL, {
      transports: ['websocket'],
      withCredentials: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.driverSocket.on('connect', () => {
      console.log('✅ [SocketService] Driver socket connected successfully');
      console.log('🔌 [SocketService] Driver Socket ID:', this.driverSocket.id);
      this.isDriverConnected = true;

      // Set up driver-specific event listeners
      this.setupDriverEventHandlers();

      // Set up app state handling
      this.setupAppStateHandling();
    });

    this.driverSocket.on('disconnect', () => {
      console.log('❌ [SocketService] Driver socket disconnected');
      this.isDriverConnected = false;
    });

    this.driverSocket.on('connect_error', (err) => {
      console.error('❌ [SocketService] Driver socket connection error:', err);
      this.isDriverConnected = false;
    });
  }

  connectCallSocket() {
    if (this.callSocket && this.isCallConnected) {
      console.log("🔄 [SocketService] Call socket already connected");
      return;
    }

    console.log("🔄 [SocketService] Connecting to general namespace for calls:", VIDEO_CALL_SOCKET_URL);

    this.callSocket = io(VIDEO_CALL_SOCKET_URL, {
      transports: ['websocket'],
      withCredentials: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
    });

    this.callSocket.on('connect', () => {
      console.log('✅ [SocketService] Call socket connected successfully to /general namespace');
      console.log('🔌 [SocketService] Call Socket ID:', this.callSocket.id);
      this.isCallConnected = true;

      // Set up call-specific event listeners
      this.setupCallEventHandlers();
    });

    this.callSocket.on('disconnect', () => {
      console.log('❌ [SocketService] Call socket disconnected');
      this.isCallConnected = false;
    });

    this.callSocket.on('connect_error', (err) => {
      console.error('❌ [SocketService] Call socket connection error:', err);
      this.isCallConnected = false;
    });
  }

  // Setup call-specific event handlers on the call socket
  setupCallEventHandlers() {
    if (!this.callSocket) {
      console.error('❌ [SocketService] Cannot setup call handlers - call socket not initialized');
      return;
    }

    // Handle incoming calls from other users (not your own outgoing calls)
    this.callSocket.on('i', (data) => {
      console.log('📞 [SocketService] Incoming call received:', JSON.stringify(data, null, 2));

      // Check if this is your own call being echoed back
      if (data.callerId === this.callSocket?.id || data.message === "Call initiated") {
        console.log('🔄 [SocketService] Ignoring own call initiation echo');
        return;
      }

      const isVideoCall = data.isVideoCall || data.callType === 'video';

      if (isVideoCall) {
        console.log('📹 [SocketService] Processing as incoming video call');
        if (this.onIncomingVideoCall) {
          this.onIncomingVideoCall(data);
        } else {
          console.warn('⚠️ [SocketService] No video call handler registered');
        }
      } else {
        console.log('📞 [SocketService] Processing as incoming audio call');
        if (this.onIncomingCall) {
          this.onIncomingCall(data);
        } else {
          console.warn('⚠️ [SocketService] No audio call handler registered');
        }
      }
    });

    // Handle call initiation confirmation (when you start a call)
    this.callSocket.on('callInitiated', (data) => {
      console.log('📞 [SocketService] Call initiation confirmed:', data);
      if (this.onCallInitiated) {
        this.onCallInitiated(data);
      }
    });

    // Handle proper incoming calls (this should be the main incoming call event)
    this.callSocket.on('incomingCall', (data) => {
      console.log('📞 [SocketService] [incomingCall] Received:', JSON.stringify(data, null, 2));

      // This is a legitimate incoming call from another user
      const enhancedData = {
        callId: data.callId,
        rideId: data.rideId,
        caller: {
          name: data.message || data.callerName || 'Unknown',
          profileImg: data.profileImg || data.callerImg
        },
        timestamp: Date.now(),
        socketId: this.callSocket?.id,
        isVideoCall: data.isVideoCall || false
      };

      if (data.isVideoCall && this.onIncomingVideoCall) {
        console.log('📹 [SocketService] Processing incoming video call');
        this.onIncomingVideoCall(enhancedData);
      } else if (!data.isVideoCall && this.onIncomingCall) {
        console.log('📞 [SocketService] Processing incoming audio call');
        this.onIncomingCall(enhancedData);
      } else {
        console.warn('⚠️ [SocketService] No appropriate call handler registered');
      }
    });

    // Handle incoming video calls specifically
    this.callSocket.on('incomingVideoCall', (data) => {
      console.log('📹 [SocketService] [incomingVideoCall] Received:', JSON.stringify(data, null, 2));
      if (this.onIncomingVideoCall) {
        this.onIncomingVideoCall(data);
      } else {
        console.warn('⚠️ [SocketService] No video call handler registered');
      }
    });
    // Stream.io token events
    this.callSocket.on('callerToken', (data) => {
      console.log('📞 [SocketService] Caller token received:', data);
      if (this.onCallerToken) {
        this.onCallerToken(data);
      }
    });
    this.callSocket.on('receiverToken', (data) => {
      console.log('📞 [SocketService] Receiver token received:', data);
      if (this.onReceiverToken) {
        this.onReceiverToken(data);
      }
    });

    // Call status events - Multiple event name variations for server compatibility
    const handleCallAccepted = (data) => {
      console.log('✅ [SocketService] Call accepted:', data);
      const isVideoCall = data.isVideoCall || data.callType === 'video';

      if (isVideoCall && this.onVideoCallAccepted) {
        this.onVideoCallAccepted(data);
      } else if (!isVideoCall && this.onCallAccepted) {
        this.onCallAccepted(data);
      }
    };

    const handleCallRejected = (data) => {
      console.log('❌ [SocketService] Call rejected:', data);
      const isVideoCall = data.isVideoCall || data.callType === 'video';

      if (isVideoCall && this.onVideoCallRejected) {
        this.onVideoCallRejected(data);
      } else if (!isVideoCall && this.onCallRejected) {
        this.onCallRejected(data);
      }
    };

    // Listen for multiple possible event names
    this.callSocket.on('acceptCall', handleCallAccepted);
    this.callSocket.on('callAccepted', handleCallAccepted);
    this.callSocket.on('call-accepted', handleCallAccepted);
    this.callSocket.on('call_accepted', handleCallAccepted);

    this.callSocket.on('rejectCall', handleCallRejected);
    this.callSocket.on('callRejected', handleCallRejected);
    this.callSocket.on('call-rejected', handleCallRejected);
    this.callSocket.on('call_rejected', handleCallRejected);

    this.callSocket.on('endCall', (data) => {
      console.log('🔚 [SocketService] Call ended:', data);
      const isVideoCall = data.isVideoCall || data.callType === 'video';

      if (isVideoCall && this.onVideoCallEnded) {
        this.onVideoCallEnded(data);
      } else if (!isVideoCall && this.onCallEnded) {
        this.onCallEnded(data);
      }
    });
  }

  disconnect() {
    console.log('🔌 [SocketService] Disconnecting both sockets...');
    this.isOnline = false;

    if (this.driverSocket) {
      this.driverSocket.disconnect();
      this.isDriverConnected = false;
    }

    if (this.callSocket) {
      this.callSocket.disconnect();
      this.isCallConnected = false;
    }
  }

  async goOnline() {
    console.log('🟢 [SocketService] Going online...');
    this.isOnline = true;

    // Import location service dynamically to avoid circular dependency
    const locationService = require('./locationService').default;

    // Get current location and send with goOnline event
    try {
      const location = await locationService.sendLocationImmediately();
      console.log('📍 [SocketService] Location obtained for goOnline:', location);

      // Emit goOnline with location to call socket for proper online status
      if (this.callSocket && this.isCallConnected) {
        this.callSocket.emit('goOnline', location);
        console.log('✅ [SocketService] goOnline emitted with location via call socket');
      } else {
        console.log('⚠️ Call socket not connected, connecting...');
        this.connectCallSocket();
        // Retry after connection
        setTimeout(() => {
          if (this.callSocket && this.isCallConnected) {
            this.callSocket.emit('goOnline', location);
          }
        }, 1000);
      }

      // Also emit to driver socket if available
      if (this.driverSocket && this.isDriverConnected) {
        this.driverSocket.emit('goOnline', location);
        console.log('✅ [SocketService] goOnline emitted with location via driver socket');
      }
    } catch (error) {
      console.error('❌ [SocketService] Error getting location for goOnline:', error);

      // Fallback: emit goOnline without location
      if (this.callSocket && this.isCallConnected) {
        this.callSocket.emit('goOnline');
        console.log('⚠️ [SocketService] goOnline emitted without location (fallback)');
      }
    }
  }

  async goOffline() {
    console.log('🔴 [SocketService] Going offline...');
    this.isOnline = false;

    // Import location service dynamically to avoid circular dependency
    const locationService = require('./locationService').default;

    // Get current location and send with goOffline event
    try {
      const location = locationService.getLastKnownLocation();

      // Emit goOffline with location to call socket
      if (this.callSocket && this.isCallConnected) {
        this.callSocket.emit('goOffline', location);
        console.log('✅ [SocketService] goOffline emitted with location via call socket');
      }

      // Also emit to driver socket if available
      if (this.driverSocket && this.isDriverConnected) {
        this.driverSocket.emit('goOffline', location);
        console.log('✅ [SocketService] goOffline emitted with location via driver socket');
      }
    } catch (error) {
      console.error('❌ [SocketService] Error getting location for goOffline:', error);

      // Fallback: emit goOffline without location
      if (this.callSocket && this.isCallConnected) {
        this.callSocket.emit('goOffline');
        console.log('⚠️ [SocketService] goOffline emitted without location (fallback)');
      }
    }
  }

  // Getter for overall connection status (for backward compatibility)
  get isConnected() {
    return this.isCallConnected; // Call functionality is primary concern
  }

  // Getter for socket (for backward compatibility) - returns call socket
  get socket() {
    return this.callSocket;
  }

  onEvent(event, func) {
    // Use call socket for call-related events
    if (this.callSocket) {
      this.callSocket.on(event, (data) => {
        console.log(`Event ${event} received with data:`, data);
        func(data);
      });
    }
  }

  emitEvent(event, data) {
    // Use call socket for call-related events
    if (this.callSocket) {
      this.callSocket.emit(event, data);
      console.log(`Event ${event} emitted with data:`, data);
    }
  }

  // Chat-specific methods using driver socket
  onChatEvent(event, func) {
    // Use driver socket for chat-related events
    if (this.driverSocket) {
      this.driverSocket.on(event, (data) => {
        console.log(`💬 Chat Event ${event} received with data:`, data);
        func(data);
      });
    } else {
      console.error('❌ [SocketService] Driver socket not available for chat events');
    }
  }

  emitChatEvent(event, data) {
    // Use driver socket for chat-related events
    if (this.driverSocket && this.isDriverConnected) {
      this.driverSocket.emit(event, data);
      console.log(`💬 Chat Event ${event} emitted with data:`, data);
    } else {
      console.error('❌ [SocketService] Driver socket not connected for chat events');
      // Fallback to call socket if driver socket is not available
      if (this.callSocket && this.isCallConnected) {
        console.log('🔄 [SocketService] Falling back to call socket for chat');
        this.callSocket.emit(event, data);
      }
    }
  }

  // Reconnection handling
  handleReconnection() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 [SocketService] Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);

      setTimeout(() => {
        if (!this.isConnected) {
          this.connect();
        }
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('❌ [SocketService] Max reconnection attempts reached');
    }
  }

  // Call initiation methods - Use call socket for all call operations
  callUser(rideId, receiverId, isVideoCall = false) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot make call - call socket not connected');
      console.log('🔄 [SocketService] Attempting to connect call socket...');
      this.connectCallSocket();
      return false;
    }

    console.log(`📞 [SocketService] Initiating ${isVideoCall ? 'video' : 'audio'} call via /general namespace`);
    console.log('📞 [SocketService] Call details:', {
      rideId,
      receiverId,
      isVideoCall,
      callType: isVideoCall ? 'video' : 'voice',
      mySocketId: this.callSocket.id
    });

    try {
      this.callSocket.emit('callUser', {
        rideId,
        receiverId,
        isVideoCall,
        callType: isVideoCall ? 'video' : 'voice',
        callerId: this.callSocket.id // Add caller ID to identify our own calls
      });
      console.log('✅ [SocketService] Call initiation event emitted successfully');
      return true;
    } catch (error) {
      console.error('❌ [SocketService] Error initiating call:', error);
      return false;
    }
  }

  acceptCall(rideId, callId, isVideoCall = false) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot accept call - call socket not connected');
      return false;
    }

    try {
      const acceptData = {
        rideId,
        callId,
        isVideoCall,
        callType: isVideoCall ? 'video' : 'voice',
        status: 'accepted',
        timestamp: new Date().toISOString()
      };

      console.log(`✅ [SocketService] Accepting ${isVideoCall ? 'video' : 'audio'} call via /general namespace`, acceptData);

      // Emit with acknowledgment to detect server response
      this.callSocket.emit('acceptCall', acceptData, (ack) => {
        if (ack) {
          console.log('✅ [SocketService] Server acknowledged call acceptance:', ack);
        } else {
          console.warn('⚠️ [SocketService] No acknowledgment received for call acceptance');
        }
      });

      // Also try alternative event names in case server expects different ones
      this.callSocket.emit('callAccepted', acceptData);
      this.callSocket.emit('call-accepted', acceptData);

      // Set a timeout to detect if server doesn't respond
      setTimeout(() => {
        console.log('⏰ [SocketService] Call acceptance timeout - checking if call was processed');
      }, 5000);

      return true;
    } catch (error) {
      console.error('❌ [SocketService] Error accepting call:', error);
      return false;
    }
  }

  rejectCall(rideId, callId, isVideoCall = false) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot reject call - call socket not connected');
      return false;
    }

    try {
      const rejectData = {
        rideId,
        callId,
        isVideoCall,
        callType: isVideoCall ? 'video' : 'voice',
        status: 'rejected',
        timestamp: new Date().toISOString()
      };

      console.log(`❌ [SocketService] Rejecting ${isVideoCall ? 'video' : 'audio'} call via /general namespace`, rejectData);

      // Emit with acknowledgment to detect server response
      this.callSocket.emit('rejectCall', rejectData, (ack) => {
        if (ack) {
          console.log('✅ [SocketService] Server acknowledged call rejection:', ack);
        } else {
          console.warn('⚠️ [SocketService] No acknowledgment received for call rejection');
        }
      });

      // Also try alternative event names in case server expects different ones
      this.callSocket.emit('callRejected', rejectData);
      this.callSocket.emit('call-rejected', rejectData);

      // Set a timeout to detect if server doesn't respond
      setTimeout(() => {
        console.log('⏰ [SocketService] Call rejection timeout - checking if call was processed');
      }, 5000);

      return true;
    } catch (error) {
      console.error('❌ [SocketService] Error rejecting call:', error);
      return false;
    }
  }

  endCall(rideId, callId, isVideoCall = false) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot end call - call socket not connected');
      return;
    }

    console.log(`🔚 [SocketService] Ending ${isVideoCall ? 'video' : 'audio'} call via /general namespace`);
    this.callSocket.emit('endCall', { rideId, callId, isVideoCall });
  }

  // WebRTC signaling methods - Use call socket
  sendOffer(offer, rideId, receiverId) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot send offer - call socket not connected');
      return;
    }

    console.log('📡 [SocketService] Sending WebRTC offer via /general namespace');
    this.callSocket.emit('offer', { offer, rideId, receiverId });
  }

  sendAnswer(answer, rideId, receiverId) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot send answer - call socket not connected');
      return;
    }

    console.log('📡 [SocketService] Sending WebRTC answer via /general namespace');
    this.callSocket.emit('answer', { answer, rideId, receiverId });
  }

  sendIceCandidate(candidate, rideId, receiverId) {
    if (!this.callSocket || !this.isCallConnected) {
      console.error('❌ [SocketService] Cannot send ICE candidate - call socket not connected');
      return;
    }

    console.log('🧊 [SocketService] Sending ICE candidate via /general namespace');
    this.callSocket.emit('ice-candidate', { candidate, rideId, receiverId });
  }

  // Event binding methods - Audio calls
  setIncomingCallHandler(handler) {
    this.onIncomingCall = handler;
  }

  setCallAcceptedHandler(handler) {
    this.onCallAccepted = handler;
  }

  setCallRejectedHandler(handler) {
    this.onCallRejected = handler;
  }

  setCallEndedHandler(handler) {
    this.onCallEnded = handler;
  }

  // Event binding methods - Video calls
  setIncomingVideoCallHandler(handler) {
    this.onIncomingVideoCall = handler;
  }

  setVideoCallAcceptedHandler(handler) {
    this.onVideoCallAccepted = handler;
  }

  setVideoCallRejectedHandler(handler) {
    this.onVideoCallRejected = handler;
  }

  setVideoCallEndedHandler(handler) {
    this.onVideoCallEnded = handler;
  }

  setVideoCallErrorHandler(handler) {
    this.onVideoCallError = handler;
  }

  setVideoCallFailedHandler(handler) {
    this.onVideoCallFailed = handler;
  }

  setCallInitiatedHandler(handler) {
    this.onCallInitiated = handler;
  }

  // Stream.io token handlers
  setCallerTokenHandler(handler) {
    this.onCallerToken = handler;
  }

  setReceiverTokenHandler(handler) {
    this.onReceiverToken = handler;
  }

  // WebRTC signaling handlers
  setOfferHandler(handler) {
    this.onOffer = handler;
  }

  setAnswerHandler(handler) {
    this.onAnswer = handler;
  }

  setIceCandidateHandler(handler) {
    this.onIceCandidate = handler;
  }
}

const socketService = new SocketService();
export default socketService;
