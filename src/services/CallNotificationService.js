import PushNotification from 'react-native-push-notification';
import { Platform } from 'react-native';
import InCallManager from 'react-native-incall-manager';

class CallNotificationService {
  constructor() {
    this.activeCallNotificationId = null;
    this.setupNotificationChannels();
  }

  setupNotificationChannels() {
    if (Platform.OS === 'android') {
      // Create notification channel for incoming calls
      PushNotification.createChannel(
        {
          channelId: 'incoming-calls',
          channelName: 'Incoming Calls',
          channelDescription: 'Notifications for incoming calls',
          playSound: true,
          soundName: 'default',
          importance: 4, // High importance
          vibrate: true,
        },
        (created) => console.log(`Incoming calls channel created: ${created}`)
      );

      // Create notification channel for ongoing calls
      PushNotification.createChannel(
        {
          channelId: 'ongoing-calls',
          channelName: 'Ongoing Calls',
          channelDescription: 'Notifications for ongoing calls',
          playSound: false,
          importance: 3, // Default importance
          vibrate: false,
        },
        (created) => console.log(`Ongoing calls channel created: ${created}`)
      );
    }
  }

  showIncomingCallNotification(callData) {
    const { callerName, rideId, callId, isVideoCall } = callData;
    
    this.activeCallNotificationId = `incoming_call_${callId}`;
    
    const notificationConfig = {
      id: this.activeCallNotificationId,
      channelId: 'incoming-calls',
      title: isVideoCall ? 'Incoming Video Call' : 'Incoming Call',
      message: `${callerName || 'Unknown'} is calling`,
      playSound: true,
      soundName: 'default',
      importance: 'high',
      priority: 'high',
      ongoing: true,
      autoCancel: false,
      vibrate: true,
      vibration: 1000,
      data: {
        type: 'incoming_call',
        rideId,
        callId,
        isVideoCall,
        callerName,
      },
    };

    // Add action buttons for Android
    if (Platform.OS === 'android') {
      notificationConfig.actions = [
        {
          id: 'reject',
          title: 'Reject',
          icon: 'ic_menu_close_clear_cancel',
        },
        {
          id: 'accept',
          title: isVideoCall ? 'Accept Video' : 'Accept',
          icon: 'ic_menu_call',
        },
      ];
    }

    PushNotification.localNotification(notificationConfig);
    
    console.log('📱 Incoming call notification shown:', this.activeCallNotificationId);
  }

  showOngoingCallNotification(callData) {
    const { callerName, rideId, callId, isVideoCall } = callData;
    
    const ongoingNotificationId = `ongoing_call_${callId}`;
    
    PushNotification.localNotification({
      id: ongoingNotificationId,
      channelId: 'ongoing-calls',
      title: isVideoCall ? 'Video Call in Progress' : 'Call in Progress',
      message: `Connected with ${callerName || 'Unknown'}`,
      playSound: false,
      ongoing: true,
      autoCancel: false,
      data: {
        type: 'ongoing_call',
        rideId,
        callId,
        isVideoCall,
        callerName,
      },
    });

    console.log('📱 Ongoing call notification shown:', ongoingNotificationId);
  }

  clearIncomingCallNotification() {
    if (this.activeCallNotificationId) {
      PushNotification.cancelLocalNotification(this.activeCallNotificationId);
      console.log('📱 Incoming call notification cleared:', this.activeCallNotificationId);
      this.activeCallNotificationId = null;
    }
  }

  clearOngoingCallNotification(callId) {
    const ongoingNotificationId = `ongoing_call_${callId}`;
    PushNotification.cancelLocalNotification(ongoingNotificationId);
    console.log('📱 Ongoing call notification cleared:', ongoingNotificationId);
  }

  clearAllCallNotifications() {
    PushNotification.cancelAllLocalNotifications();
    this.activeCallNotificationId = null;
    console.log('📱 All call notifications cleared');
  }

  handleNotificationAction(notification) {
    const { action, data } = notification;
    
    if (!data || data.type !== 'incoming_call') {
      return false;
    }

    const { rideId, callId, isVideoCall } = data;

    switch (action) {
      case 'accept':
        console.log('📱 Call accepted via notification');
        this.clearIncomingCallNotification();
        // The global call manager will handle the actual acceptance
        return { action: 'accept', rideId, callId, isVideoCall };
        
      case 'reject':
        console.log('📱 Call rejected via notification');
        this.clearIncomingCallNotification();
        // The global call manager will handle the actual rejection
        return { action: 'reject', rideId, callId, isVideoCall };
        
      default:
        return false;
    }
  }

  // Handle when user taps on the notification (not action buttons)
  handleNotificationTap(notification) {
    const { data } = notification;
    
    if (!data) {
      return false;
    }

    if (data.type === 'incoming_call') {
      console.log('📱 Incoming call notification tapped - opening call screen');
      // The app should bring the global call screen to foreground
      return { action: 'open_call_screen', ...data };
    }

    if (data.type === 'ongoing_call') {
      console.log('📱 Ongoing call notification tapped - opening call screen');
      // Navigate to the active call screen
      return { action: 'open_active_call', ...data };
    }

    return false;
  }

  // Start call-related services
  startCallServices() {
    InCallManager.start({ media: 'audio', auto: true });
    InCallManager.setKeepScreenOn(true);
  }

  // Stop call-related services
  stopCallServices() {
    InCallManager.stop();
    InCallManager.setKeepScreenOn(false);
  }
}

// Export singleton instance
const callNotificationService = new CallNotificationService();
export default callNotificationService;
