declare module 'react-native-push-notification' {
  interface Notification {
    title?: string;
    message: string;
    userInfo?: any;
    playSound?: boolean;
    soundName?: string;
    importance?: string;
    priority?: string;
    smallIcon?: string;
    largeIcon?: string;
    bigText?: string;
    subText?: string;
    color?: string;
    vibrate?: boolean;
    vibration?: number;
    tag?: string;
    group?: string;
    ongoing?: boolean;
    autoCancel?: boolean;
    channelId?: string;
    data?: any;
  }

  interface PushNotification {
    configure(options: {
      onRegister?: (token: { os: string; token: string }) => void;
      onNotification?: (notification: Notification) => void;
      onAction?: (notification: Notification) => void;
      onRegistrationError?: (error: Error) => void;
      permissions?: {
        alert?: boolean;
        badge?: boolean;
        sound?: boolean;
      };
      popInitialNotification?: boolean;
      requestPermissions?: boolean;
    }): void;
    localNotification(details: Notification): void;
    scheduleNotification(details: Notification): void;
    cancelNotification(id: string): void;
    cancelAllNotifications(): void;
    removeAllDeliveredNotifications(): void;
    getDeliveredNotifications(callback: (notifications: Notification[]) => void): void;
    getScheduledLocalNotifications(callback: (notifications: Notification[]) => void): void;
    requestPermissions(permissions?: string[]): void;
    createChannel(
      channel: {
        channelId: string;
        channelName: string;
        channelDescription?: string;
        playSound?: boolean;
        soundName?: string;
        importance?: number;
        vibrate?: boolean;
      },
      callback?: (created: boolean) => void
    ): void;
  }

  const PushNotification: PushNotification;
  export default PushNotification;
} 