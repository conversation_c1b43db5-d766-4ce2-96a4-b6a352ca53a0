import React, { useEffect, useState, useRef } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Platform, Image, Dimensions, Animated, Vibration } from 'react-native';

const { width, height } = Dimensions.get('window');
import { useCall } from '../services/CallContext';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import { StreamVideo, CallControls, CallContent } from '@stream-io/video-react-native-sdk';
import InCallManager from 'react-native-incall-manager';
import socketService from '../services/socketService';
import Toast from 'react-native-toast-message';

const GlobalCallScreen = () => {
  const { currentCall, acceptCall, rejectCall, endCall } = useCall();
  const [isMuted, setIsMuted] = useState(false);
  const [isSpeakerOn, setIsSpeakerOn] = useState(true);
  const shakeAnimation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (currentCall?.isIncoming) {
      // Start ringing sound and vibration
      InCallManager.start({ media: 'audio', auto: true, ringback: '_DTMF_' });
      InCallManager.setKeepScreenOn(true);
      InCallManager.setSpeakerphoneOn(false);

      // Start vibration pattern for incoming call
      const vibrationPattern = [1000, 1000, 1000, 1000]; // Vibrate for 1s, pause 1s, repeat
      Vibration.vibrate(vibrationPattern, true); // true = repeat indefinitely

      // Start shake animation for accept button
      startShakeAnimation();

      console.log('🔊 [GlobalCallScreen] Started ringing and vibration for incoming call');
    }
    return () => {
      // Stop all ringing effects
      InCallManager.stop();
      InCallManager.setKeepScreenOn(false);
      Vibration.cancel(); // Stop vibration

      // Stop shake animation
      shakeAnimation.stopAnimation();

      console.log('🔇 [GlobalCallScreen] Stopped ringing and vibration');
    };
  }, [currentCall?.isIncoming]);

  const startShakeAnimation = () => {
    const shakeSequence = Animated.sequence([
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: -10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 10,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(shakeAnimation, {
        toValue: 0,
        duration: 100,
        useNativeDriver: true,
      }),
    ]);

    const loopAnimation = Animated.loop(shakeSequence, { iterations: -1 });
    loopAnimation.start();
  };

  const handleAcceptCall = async () => {
    console.log('✅ [GlobalCallScreen] Accepting call:', currentCall);

    // Stop ringing immediately when call is accepted
    InCallManager.stop();
    Vibration.cancel();
    shakeAnimation.stopAnimation();

    try {
      if (currentCall) {
        if (currentCall.error) {
          console.log('⚠️ [GlobalCallScreen] Cannot accept call with error:', currentCall.errorMessage);
          // Show error message to user
          Toast.show({
            type: 'error',
            text1: 'Cannot Accept Call',
            text2: currentCall.errorMessage || 'Invalid call data',
          });
          return;
        }
        acceptCall(currentCall.rideId, currentCall.callId);
      }
    } catch (error) {
      console.error('❌ [GlobalCallScreen] Error accepting call:', error);
    }
  };

  const handleRejectCall = async () => {
    console.log('❌ [GlobalCallScreen] Rejecting call:', currentCall);

    // Stop ringing immediately when call is rejected
    InCallManager.stop();
    Vibration.cancel();
    shakeAnimation.stopAnimation();

    try {
      if (currentCall) {
        if (currentCall.error) {
          // For calls with errors, just end the call without sending to server
          handleCallEnd();
          return;
        }
        rejectCall(currentCall.rideId, currentCall.callId);
      }
    } catch (error) {
      console.error('❌ [GlobalCallScreen] Error rejecting call:', error);
    }
  };

  const handleEndCall = async () => {
    console.log('🔚 [GlobalCallScreen] Ending call:', currentCall);
    try {
      InCallManager.stop();
      if (currentCall) {
        if (currentCall.error) {
          // For calls with errors, just end the call without sending to server
          handleCallEnd();
          return;
        }
        // Ensure socket is connected before ending call
        if (!socketService.isConnected) {
          console.log('🔄 [GlobalCallScreen] Reconnecting socket before ending call');
          socketService.connect();
          // Wait for connection
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        if (currentCall.isVideoCall && !socketService.isConnected) {
          console.log('🔄 [GlobalCallScreen] Ensuring unified socket connection before ending call');
          socketService.connect();
          // Wait for connection
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        endCall(currentCall.rideId, currentCall.callId);
      }
    } catch (error) {
      console.error('❌ [GlobalCallScreen] Error ending call:', error);
    }
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
    InCallManager.setForceSpeakerphoneOn(!isMuted);
  };

  const toggleSpeaker = () => {
    setIsSpeakerOn(!isSpeakerOn);
    InCallManager.setForceSpeakerphoneOn(!isSpeakerOn);
  };

  if (!currentCall) return null;

  return (
    <View style={styles.container}>
      {currentCall.isVideoCall && currentCall.token ? (
        <StreamVideo client={global.streamClient}>
          <CallContent
            callId={currentCall.callId}
            CallControls={CallControls}
          />
        </StreamVideo>
      ) : (
        <View style={styles.modernContainer}>
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerText}>
              {currentCall.isVideoCall ? 'Incoming Video Call' : 'Incoming Call'}
            </Text>
          </View>

          {/* Caller Info */}
          <View style={styles.callerContainer}>
            <View style={styles.profileImageContainer}>
              <Image
                source={
                  currentCall.callerImage
                    ? { uri: currentCall.callerImage }
                    : require('../assets/images/profile.png')
                }
                style={styles.profileImage}
              />
              <View style={styles.pulseRing1} />
              <View style={styles.pulseRing2} />
              <View style={styles.pulseRing3} />
              <View style={styles.profileBorder} />
            </View>

            <Text style={styles.callerName}>
              {(currentCall.callerName || 'Unknown Caller').replace(/^Incoming call from\s*/i, '')}
            </Text>
            {currentCall.callerPhone && (
              <Text style={styles.callerPhone}>{currentCall.callerPhone}</Text>
            )}
            <Text style={styles.callStatus}>
              {currentCall.isVideoCall ? '📹 Video Call' : '📞 Voice Call'}
            </Text>
            {currentCall.error && (
              <Text style={styles.errorText}>{currentCall.errorMessage}</Text>
            )}
          </View>

          {/* Call Actions */}
          {currentCall.isIncoming ? (
            <View style={styles.actionsContainer}>
              {/* Reject Button */}
              <TouchableOpacity
                style={styles.actionButton}
                onPress={handleRejectCall}
                activeOpacity={0.8}
              >
                <View style={[styles.buttonContent, { backgroundColor: '#FF4444' }]}>
                  <MaterialIcons name="call-end" size={32} color="white" />
                </View>
                <Text style={styles.buttonLabel}>Decline</Text>
              </TouchableOpacity>



              {/* Accept Button with Shake Animation */}
              <Animated.View
                style={[
                  styles.actionButton,
                  {
                    transform: [{ translateX: shakeAnimation }],
                  },
                ]}
              >
                <TouchableOpacity
                  onPress={handleAcceptCall}
                  activeOpacity={0.8}
                  style={styles.touchableButton}
                >
                  <View style={[styles.buttonContent, { backgroundColor: '#4CAF50' }]}>
                    <MaterialIcons
                      name={currentCall.isVideoCall ? "videocam" : "call"}
                      size={32}
                      color="white"
                    />
                  </View>
                  <Text style={styles.buttonLabel}>Accept</Text>
                </TouchableOpacity>
              </Animated.View>
            </View>
          ) : (
            <View style={styles.controls}>
              <TouchableOpacity
                style={[styles.button, styles.muteButton]}
                onPress={toggleMute}
              >
                <MaterialIcons
                  name={isMuted ? 'mic-off' : 'mic'}
                  size={30}
                  color="white"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.speakerButton]}
                onPress={toggleSpeaker}
              >
                <MaterialIcons
                  name={isSpeakerOn ? 'volume-up' : 'volume-off'}
                  size={30}
                  color="white"
                />
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.endCallButton]}
                onPress={handleEndCall}
              >
                <MaterialIcons name="call-end" size={30} color="white" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#1a1a1a',
    zIndex: 1000,
  },
  audioContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a1a',
  },
  modernContainer: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 50,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerText: {
    fontSize: 20,
    fontWeight: '600',
    color: 'white',
    marginBottom: 5,
  },
  rideText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  callerContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImage: {
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 5,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  profileBorder: {
    position: 'absolute',
    width: 170,
    height: 170,
    borderRadius: 85,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  pulseRing1: {
    position: 'absolute',
    width: 180,
    height: 180,
    top: -10,
    left: -10,
    borderRadius: 90,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    opacity: 1,
  },
  pulseRing2: {
    position: 'absolute',
    width: 200,
    height: 200,
    top: -20,
    left: -20,
    borderRadius: 100,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    opacity: 0.7,
  },
  pulseRing3: {
    position: 'absolute',
    width: 220,
    height: 220,
    top: -30,
    left: -30,
    borderRadius: 110,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    opacity: 0.4,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 80,
    marginBottom: 30,
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  touchableButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContent: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    marginBottom: 8,
  },
  buttonLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
    textAlign: 'center',
  },
  quickActions: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickActionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  callStatus: {
    fontSize: 24,
    color: '#fff',
    marginTop: 20,
  },
  callerName: {
    fontSize: 28,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  callerPhone: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 8,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  button: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  rejectButton: {
    backgroundColor: '#f44336',
  },
  endCallButton: {
    backgroundColor: '#f44336',
  },
  muteButton: {
    backgroundColor: '#666',
  },
  speakerButton: {
    backgroundColor: '#666',
  },
  errorText: {
    color: '#f44336',
    fontSize: 14,
    marginTop: 10,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
});

export default GlobalCallScreen; 