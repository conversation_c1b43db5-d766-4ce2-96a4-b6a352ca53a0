import React, { ReactNode } from 'react';
import {
  KeyboardAvoidingView,
  ScrollView,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
  StyleSheet,
  View,
} from 'react-native';

interface KeyboardAvoidingWrapperProps {
  children: ReactNode;
  style?: any;
  contentContainerStyle?: any;
}

/**
 * A wrapper component that handles keyboard avoiding behavior
 * Works on both iOS and Android
 */
const KeyboardAvoidingWrapper = ({
  children,
  style,
  contentContainerStyle,
}: KeyboardAvoidingWrapperProps) => {
  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 64 : 0}
    >
      <ScrollView
        style={[styles.container, style]}
        contentContainerStyle={[
          styles.contentContainer,
          contentContainerStyle,
          Platform.OS === 'android' ? { paddingBottom: 150 } : {},
        ]}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={true}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.inner}>{children}</View>
        </TouchableWithoutFeedback>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    flexGrow: 1,
  },
  inner: {
    flex: 1,
  },
});

export default KeyboardAvoidingWrapper;
