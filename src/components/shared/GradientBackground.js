 // GradientBackground.js
import React from 'react';
import { StyleSheet } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

const GradientBackground = ({ children, style }) => {
  return (
    <LinearGradient
      colors={['#D0E6FF', '#F2FCFC']}
      style={[styles.gradient, style]}
    >
      {children}
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  gradient: {
    flex: 1, 
  },
});

export default GradientBackground;
