 
import React, { useState } from "react";
import {
  View,
  TextInput,
  ActivityIndicator,
  FlatList,
  TouchableOpacity,
  Text,
  StyleSheet,
} from "react-native";
import { COLORS } from "../../constants"; // Ensure this path is correct

const GOOGLE_API_KEY = "AIzaSyC0VJu9ttMNPOWP-vxTuXtzAaR932hdKUc"; // Replace with your actual API Key

const GooglePlacesAutocompleteComponent = ({ placeholder, onSelect }) => {
  const [query, setQuery] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);

  /** 📍 Fetch Place Predictions */
  const handleInputChange = async (text) => {
    setQuery(text);
    if (text.length > 2) {
      setLoading(true);
      try {
        const response = await fetch(
          `https://maps.googleapis.com/maps/api/place/autocomplete/json?input=${text}&key=${GOOGLE_API_KEY}`
        );
        const data = await response.json();
        console.log("🔍 Google Places API Response:", JSON.stringify(data, null, 2));

        if (data.status === "OK") {
          const predictions = data.predictions.map((prediction) => ({
            id: prediction.place_id,
            description: prediction.description,
            place_id: prediction.place_id, // Store place_id for fetching lat/lng
          }));
          setSuggestions(predictions);
        } else {
          console.error("❌ Google Places API Error:", data.status);
          setSuggestions([]);
        }
      } catch (error) {
        console.error("❌ Google Places API Error:", error);
      } finally {
        setLoading(false);
      }
    } else {
      setSuggestions([]);
    }
  };

  /** 📌 Fetch Place Details (Lat/Lng) */
  const fetchPlaceDetails = async (placeId) => {
    const url = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&key=${GOOGLE_API_KEY}`;
    try {
      const response = await fetch(url);
      const data = await response.json();

      if (data.status === "OK") {
        const location = data.result.geometry.location; // Extract lat/lng
        return { lat: location.lat, lng: location.lng };
      } else {
        console.error("❌ Google Place Details API Error:", data.status);
        return null;
      }
    } catch (error) {
      console.error("❌ Error fetching place details:", error);
      return null;
    }
  };

  /** ✅ Handle Place Selection */
  const handleSelect = async (item) => {
    setQuery(item.description);
    setSuggestions([]);
  
    const coordinates = await fetchPlaceDetails(item.place_id);
  
    if (coordinates) {
      const selectedLocation = {
        description: item.description,
        place_id: item.id, // Ensure place_id is stored correctly
        location: coordinates,
      };
  
      console.log("📍 Selected Location:", selectedLocation);
      onSelect(selectedLocation); // Pass selected location back to ArrangeYourRide.js
    }
  };
  

  return (
    <View style={styles.container}>
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholderTextColor={COLORS.grey}
          placeholder={placeholder || "Enter location"}
          value={query}
          onChangeText={handleInputChange}
        />
        {loading && <ActivityIndicator size="small" color={COLORS.primary} />}
      </View>

      {suggestions.length > 0 && (
        <FlatList
          data={suggestions}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <TouchableOpacity style={styles.suggestionItem} onPress={() => handleSelect(item)}>
              <Text style={styles.suggestionText}>{item.description}</Text>
            </TouchableOpacity>
          )}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 10,
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: COLORS.white,
    paddingHorizontal: 10,
    borderRadius: 8,
    elevation: 2,
  },
  searchInput: {
    paddingVertical: 15,
    marginLeft: 10,
    color: COLORS.black,
    width: "75%",
  },
  suggestionItem: {
    padding: 10,
    borderBottomWidth: 0.5,
    borderBottomColor: COLORS.grey,
  },
  suggestionText: {
    color: COLORS.black,
  },
});

export default GooglePlacesAutocompleteComponent;
