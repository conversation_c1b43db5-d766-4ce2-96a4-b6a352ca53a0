import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Dimensions,
  Platform,
  StatusBar,
  Vibration,
  AppState,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import InCallManager from 'react-native-incall-manager';
import { COLORS, FONTS } from '../constants';

const { width, height } = Dimensions.get('window');

const GlobalIncomingCallScreen = ({
  visible,
  callerName,
  callerImage,
  callerPhone,
  rideId,
  callId,
  isVideoCall = false,
  onAccept,
  onReject,
  onEnd,
  callStatus,
}) => {
  const navigation = useNavigation();
  const [isRinging, setIsRinging] = useState(true);

  useEffect(() => {
    console.log('🔄 [GlobalIncomingCallScreen] Effect triggered - visible:', visible);
    console.log('🔄 [GlobalIncomingCallScreen] Effect triggered - callStatus:', callStatus);
    console.log('🔄 [GlobalIncomingCallScreen] Effect triggered - isRinging:', isRinging);

    if (visible) {
      console.log('🔊 [GlobalIncomingCallScreen] Starting call UI effects');
      // Start ringing sound and vibration
      InCallManager.start({ media: 'audio', auto: true, ringback: '_DTMF_' });
      InCallManager.setKeepScreenOn(true);
      InCallManager.setSpeakerphoneOn(false);
      
      // Start vibration pattern for incoming call
      const vibrationPattern = [1000, 1000, 1000, 1000];
      Vibration.vibrate(vibrationPattern, true);

      // Set status bar to light content for better visibility
      StatusBar.setBarStyle('light-content', true);
      if (Platform.OS === 'android') {
        StatusBar.setBackgroundColor('rgba(0,0,0,0.8)', true);
      }

      return () => {
        console.log('🧹 [GlobalIncomingCallScreen] Cleaning up call UI effects');
        // Cleanup when call screen is hidden
        Vibration.cancel();
        InCallManager.stop();
        InCallManager.setKeepScreenOn(false);
        
        // Restore status bar
        StatusBar.setBarStyle('dark-content', true);
        if (Platform.OS === 'android') {
          StatusBar.setBackgroundColor(COLORS.primary, true);
        }
      };
    }
  }, [visible, callStatus, isRinging]);

  // Handle app state changes to ensure call screen stays visible
  useEffect(() => {
    const handleAppStateChange = (nextAppState) => {
      if (visible && nextAppState === 'background') {
        // Keep the call screen active even when app goes to background
        InCallManager.setKeepScreenOn(true);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [visible]);

  const handleAccept = () => {
    console.log('✅ [GlobalIncomingCallScreen] Call accepted - rideId:', rideId, 'callId:', callId, 'isVideoCall:', isVideoCall);
    setIsRinging(false);
    Vibration.cancel();
    InCallManager.stop();

    if (onAccept) {
      console.log('✅ [GlobalIncomingCallScreen] Calling onAccept handler with params:', { rideId, callId });
      try {
        onAccept(rideId, callId);
        console.log('✅ [GlobalIncomingCallScreen] onAccept handler called successfully');
      } catch (error) {
        console.error('❌ [GlobalIncomingCallScreen] Error calling onAccept handler:', error);
      }
    } else {
      console.error('❌ [GlobalIncomingCallScreen] No onAccept handler provided');
    }

    // Navigate to call screen with error handling
    try {
      if (navigation && navigation.navigate) {
        console.log('🔄 [GlobalIncomingCallScreen] Navigating to CallScreen with params:', {
          rideId, callId, isIncoming: true, isVideoCall, callerName, callerImage, callerPhone
        });
        navigation.navigate('CallScreen', {
          rideId,
          callId,
          isIncoming: true,
          isVideoCall,
          callerName,
          callerImage,
          callerPhone,
        });
      } else {
        console.error('❌ [GlobalIncomingCallScreen] Navigation object is null or invalid');
      }
    } catch (error) {
      console.error('❌ [GlobalIncomingCallScreen] Navigation error:', error);
    }
  };

  const handleReject = () => {
    console.log('❌ [GlobalIncomingCallScreen] Call rejected - rideId:', rideId, 'callId:', callId, 'isVideoCall:', isVideoCall);
    setIsRinging(false);
    Vibration.cancel();
    InCallManager.stop();

    if (onReject) {
      console.log('❌ [GlobalIncomingCallScreen] Calling onReject handler with params:', { rideId, callId });
      try {
        onReject(rideId, callId);
        console.log('✅ [GlobalIncomingCallScreen] onReject handler called successfully');
      } catch (error) {
        console.error('❌ [GlobalIncomingCallScreen] Error calling onReject handler:', error);
      }
    } else {
      console.error('❌ [GlobalIncomingCallScreen] No onReject handler provided');
    }
  };

  const handleEnd = () => {
    console.log('🔚 [GlobalIncomingCallScreen] Call ended');
    setIsRinging(false);
    Vibration.cancel();
    InCallManager.stop();
    
    if (onEnd) {
      console.log('🔚 [GlobalIncomingCallScreen] Calling onEnd handler');
      onEnd(rideId, callId);
    }
  };

  console.log('🎯 [GlobalIncomingCallScreen] Render - visible:', visible);
  console.log('🎯 [GlobalIncomingCallScreen] Render - callStatus:', callStatus);
  console.log('🎯 [GlobalIncomingCallScreen] Render - isRinging:', isRinging);

  if (!visible) {
    console.log('❌ [GlobalIncomingCallScreen] Not rendering - not visible');
    return null;
  }

  console.log('✅ [GlobalIncomingCallScreen] Rendering call screen UI');
  return (
    <View style={styles.overlay}>
      <StatusBar barStyle="light-content" backgroundColor="rgba(0,0,0,0.8)" translucent />
      
      {/* Background blur effect */}
      <View style={styles.backgroundBlur} />
      
      {/* Main content */}
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerText}>
            {isVideoCall ? 'Incoming Video Call' : 'Incoming Call'}
          </Text>
          <Text style={styles.rideText}>Ride #{rideId}</Text>
        </View>

        {/* Caller Info */}
        <View style={styles.callerContainer}>
          <View style={styles.profileImageContainer}>
            <Image
              source={
                callerImage
                  ? { uri: callerImage }
                  : require('../assets/images/profile.png')
              }
              style={styles.profileImage}
            />
            {isRinging && (
              <>
                <View style={[styles.pulseRing, styles.pulseRing1]} />
                <View style={[styles.pulseRing, styles.pulseRing2]} />
                <View style={[styles.pulseRing, styles.pulseRing3]} />
              </>
            )}
            <View style={styles.profileBorder} />
          </View>

          <Text style={styles.callerName}>{callerName || 'Unknown Caller'}</Text>
          {callerPhone && <Text style={styles.callerPhone}>{callerPhone}</Text>}
          <Text style={styles.callStatus}>
            {isVideoCall ? '📹 Video Call' : '📞 Voice Call'}
          </Text>
          <Text style={styles.rideInfo}>Ride #{rideId}</Text>
        </View>

        {/* Call Actions */}
        <View style={styles.actionsContainer}>
          {/* Reject Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.rejectButton]}
            onPress={handleReject}
            activeOpacity={0.8}
          >
            <View style={[styles.buttonContent, { backgroundColor: '#FF4444' }]}>
              <Ionicons name="call" size={32} color="white" style={styles.rejectIcon} />
            </View>
            <Text style={styles.buttonLabel}>Decline</Text>
          </TouchableOpacity>

          {/* Quick Actions */}
          <View style={styles.quickActions}>
            <TouchableOpacity style={styles.quickActionButton}>
              <Ionicons name="chatbubble" size={24} color="white" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.quickActionButton}>
              <Ionicons name="time" size={24} color="white" />
            </TouchableOpacity>
          </View>

          {/* Accept Button */}
          <TouchableOpacity
            style={[styles.actionButton, styles.acceptButton]}
            onPress={handleAccept}
            activeOpacity={0.8}
          >
            <View style={[styles.buttonContent, { backgroundColor: '#4CAF50' }]}>
              <Ionicons
                name={isVideoCall ? "videocam" : "call"}
                size={32}
                color="white"
              />
            </View>
            <Text style={styles.buttonLabel}>Accept</Text>
          </TouchableOpacity>
        </View>


      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    elevation: 9999,
  },
  backgroundBlur: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.85)',
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 50,
    paddingHorizontal: 20,
  },
  header: {
    alignItems: 'center',
    marginTop: 20,
  },
  headerText: {
    ...FONTS.h3,
    color: 'white',
    marginBottom: 5,
  },
  rideText: {
    ...FONTS.body4,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  callerContainer: {
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileImage: {
    width: 160,
    height: 160,
    borderRadius: 80,
    borderWidth: 5,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  profileBorder: {
    position: 'absolute',
    width: 170,
    height: 170,
    borderRadius: 85,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  pulseRing: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    borderRadius: 100,
  },
  pulseRing1: {
    width: 180,
    height: 180,
    top: -10,
    left: -10,
    opacity: 1,
  },
  pulseRing2: {
    width: 200,
    height: 200,
    top: -20,
    left: -20,
    opacity: 0.7,
  },
  pulseRing3: {
    width: 220,
    height: 220,
    top: -30,
    left: -30,
    opacity: 0.4,
  },
  callerName: {
    fontSize: 28,
    fontWeight: '600',
    color: 'white',
    textAlign: 'center',
    marginBottom: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  callerPhone: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    marginBottom: 8,
  },
  callStatus: {
    fontSize: 18,
    fontWeight: '500',
    color: '#4CAF50',
    textAlign: 'center',
    marginBottom: 5,
  },
  rideInfo: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 8,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    width: '100%',
    paddingHorizontal: 30,
    marginBottom: 30,
  },
  actionButton: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonContent: {
    width: 75,
    height: 75,
    borderRadius: 37.5,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    marginBottom: 8,
  },
  buttonLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: 'white',
    textAlign: 'center',
  },
  quickActions: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  quickActionButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 8,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  acceptButton: {
    backgroundColor: COLORS.success || '#4CAF50',
  },
  rejectButton: {
    backgroundColor: COLORS.error || '#F44336',
  },
  rejectIcon: {
    transform: [{ rotate: '135deg' }],
  },
  additionalActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    paddingHorizontal: 60,
  },
  messageButton: {
    alignItems: 'center',
  },
  reminderButton: {
    alignItems: 'center',
  },
  messageText: {
    ...FONTS.body4,
    color: 'white',
    marginTop: 5,
  },
  reminderText: {
    ...FONTS.body4,
    color: 'white',
    marginTop: 5,
  },
  callStatus: {
    ...FONTS.body4,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
});

export default GlobalIncomingCallScreen;
