import React, { useEffect } from 'react';
import { useCall } from '../services/CallContext';
import GlobalCallScreen from './GlobalCallScreen';
import InCallManager from 'react-native-incall-manager';

const GlobalCallManager = () => {
  const {
    showGlobalCallScreen,
    currentCall,
    acceptCall,
    rejectCall,
    endCall,
    callStatus,
  } = useCall();

  useEffect(() => {
    console.log('🔄 [GlobalCallManager] Effect triggered - showGlobalCallScreen:', showGlobalCallScreen);
    console.log('🔄 [GlobalCallManager] Effect triggered - currentCall:', JSON.stringify(currentCall, null, 2));
    console.log('🔄 [GlobalCallManager] Effect triggered - callStatus:', callStatus);

    if (showGlobalCallScreen && currentCall) {
      console.log('🔊 [GlobalCallManager] Starting InCallManager for call');
      // Start call manager when showing call screen
      InCallManager.start({ media: 'audio', auto: true, ringback: '_DTMF_' });
      InCallManager.setKeepScreenOn(true);
      InCallManager.setSpeakerphoneOn(false);

      return () => {
        console.log('🧹 [GlobalCallManager] Cleaning up InCallManager');
        // Cleanup when call screen is hidden
        InCallManager.stop();
        InCallManager.setKeepScreenOn(false);
      };
    }
  }, [showGlobalCallScreen, currentCall, callStatus]);

  console.log('🎯 [GlobalCallManager] Render - showGlobalCallScreen:', showGlobalCallScreen);
  console.log('🎯 [GlobalCallManager] Render - currentCall:', JSON.stringify(currentCall, null, 2));
  console.log('🎯 [GlobalCallManager] Render - callStatus:', callStatus);

  if (!showGlobalCallScreen || !currentCall) {
    console.log('❌ [GlobalCallManager] Not rendering - conditions not met');
    return null;
  }

  console.log('✅ [GlobalCallManager] Rendering GlobalCallScreen');
  return (
    <GlobalCallScreen
      visible={showGlobalCallScreen}
      callerName={currentCall.callerName}
      callerImage={currentCall.callerImage}
      callerPhone={currentCall.callerPhone}
      rideId={currentCall.rideId}
      callId={currentCall.callId}
      isVideoCall={currentCall.isVideoCall}
      isIncoming={currentCall.isIncoming}
      onAccept={acceptCall}
      onReject={rejectCall}
      onEnd={endCall}
      callStatus={callStatus}
    />
  );
};

export default GlobalCallManager;
