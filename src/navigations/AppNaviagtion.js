import React, {useState, useEffect} from 'react';
import {View, Text, TouchableOpacity, Image, StyleSheet} from 'react-native';
import {DefaultTheme, NavigationContainer} from '@react-navigation/native';
import {createStackNavigator} from '@react-navigation/stack';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {COLORS} from '../constants';
import SplashScreen from '../screens/SplashScreen';
import Toast from 'react-native-toast-message';
import Ionicons from 'react-native-vector-icons/Ionicons';
// import GetStarted from '../screens/AuthScreens/GetStarted';
import AddProfilePicture from '../screens/AuthScreens/Onboarding/AddProfilePicture';
import MobileNumber from '../screens/AuthScreens/Onboarding/MobileNumber';
import AddSocialSecurity from '../screens/AuthScreens/Onboarding/AddSocialSecurity';
import AddSocialSecurityNumber from '../screens/AuthScreens/Onboarding/AddSocialSecurityNumber';
import AddYourCar from '../screens/AuthScreens/Onboarding/AddYourCar';
import CheckConsent from '../screens/AuthScreens/Onboarding/CheckConsent';
import DriverLicense from '../screens/AuthScreens/Onboarding/DriverLicense';
import IdentificationSubmitted from '../screens/AuthScreens/Onboarding/IdentificationSubmitted';
import Notification from '../screens/AuthScreens/Onboarding/Notification';
import TermOfService from '../screens/AuthScreens/Onboarding/TermOfService';
import WhereToDrive from '../screens/AuthScreens/Onboarding/WhereToDrive';
import WelcomeBack from '../screens/AuthScreens/SignIn/WelcomeBack';
import SecretCode from '../screens/AuthScreens/SignIn/SecretCode';
import AddNewBank from '../screens/Earnings/AddNewBank';
import CashOut from '../screens/Earnings/CashOut';
import EarningHistory from '../screens/Earnings/EarningHistory';
import EarningSummary from '../screens/Earnings/EarningSummary';
import PayOutHistory from '../screens/Earnings/PayOutHistory';
import PayOutMethods from '../screens/Earnings/PayOutMethods';
import RecentRides from '../screens/Earnings/RecentRides';
import RideDetails from '../screens/Earnings/RideDetails';
import WeeklyBreakDown from '../screens/Earnings/WeeklyBreakDown';
import AddACarDetails from '../screens/AuthScreens/Onboarding/AddACarDetails';
import AddCarDetails from '../screens/AuthScreens/Onboarding/AddCarDetails';
import CCamera from '../screens/AuthScreens/Onboarding/Camera';
import {getToken} from '../../redux/shared';
import GetStarted from '../screens/AuthScreens/Onboarding/GetStarted';
import RSecretCode from '../screens/AuthScreens/Onboarding/RSecretCode';
import AddName from '../screens/AuthScreens/Onboarding/AddName';
import AddEmail from '../screens/AuthScreens/Onboarding/AddEmail';
import DriverLicenseImage from '../screens/AuthScreens/Onboarding/DriverLicenseImage';
import CarFormDetails from '../screens/AuthScreens/Onboarding/CarFormDetails';
import SNotification from '../screens/AuthScreens/SignIn/SNotification';
import HomeStack from './HomeStack';
import PAddEmail from '../screens/AuthScreens/PassengerAccount/PAddEmail';
import PAddName from '../screens/AuthScreens/PassengerAccount/PAddName';
import PSecretCode from '../screens/AuthScreens/PassengerAccount/PSecretCode';
import PMobileNumber from '../screens/AuthScreens/PassengerAccount/PMobileNumber';
import PWhereToDrive from '../screens/AuthScreens/PassengerAccount/PWhereToDrive';
import PTermsOfService from '../screens/AuthScreens/PassengerAccount/PTermOfService';
import PNotification from '../screens/AuthScreens/PassengerAccount/PNotification';
import PIdentificationSubmitted from '../screens/AuthScreens/PassengerAccount/PIdentificationSubmitted';
import PDriverLicense from '../screens/AuthScreens/PassengerAccount/PDriverLicense';
import PCheckConsent from '../screens/AuthScreens/PassengerAccount/PCheckConsent';
import PCCamera from '../screens/AuthScreens/PassengerAccount/PCamera';
import PAddYourCar from '../screens/AuthScreens/PassengerAccount/PAddYourCar';
import PAddSocialSecurityNumber from '../screens/AuthScreens/PassengerAccount/PAddSocialSecurityNumber';
import PAddProfilePicture from '../screens/AuthScreens/PassengerAccount/PAddProfilePicture';
import PAddCarDetails from '../screens/AuthScreens/PassengerAccount/PAddCarDetails';
import PCarFormDetails from '../screens/AuthScreens/PassengerAccount/PCarFormDetails';
import PAddACarDetails from '../screens/AuthScreens/PassengerAccount/PAddACarDetails';
import PDriverLicenseImage from '../screens/AuthScreens/PassengerAccount/PDriverLicenseImage';
import PGetStarted from '../screens/AuthScreens/PassengerAccount/PGetStarted';
import PAddSocialSecurity from '../screens/AuthScreens/PassengerAccount/PAddSocialSecurity';
import AboutUs from '../screens/Account/AboutUs';
import AddHomeAddress from '../screens/Account/AddHomeAddress';
import AddWorkAddress from '../screens/Account/AddWorkAddress';
import ConfirmLocation from '../screens/Account/ConfirmLocation';
import EditProfileScreen from '../screens/Account/EditProfileScreen';
import FAQ from '../screens/Account/FAQ';
import LanguageScreen from '../screens/Account/LanguageScreen';
import NotificationScreen from '../screens/Account/NotificationScreen';
import NotificationSettings from '../screens/Account/NotificationSettings';
import Payment from '../screens/Account/Payment';
import RideFuzeFunds from '../screens/Account/RideFuzeFunds';
import VerifyEmailScreen from '../screens/Account/VerifyEmailScreen';
import ViewProfileScreen from '../screens/Account/ViewProfileScreen';
import MyRidesScreen from '../screens/Account/MyRidesScreen';
import ProfileScreen from '../screens/Account/ProfileScreen';
import MyCarScreen from '../screens/Account/MyCarScreen';
import CarDetailsScreen from '../screens/Account/CarDetailsScreen';
import RidesScreen from '../screens/Ride/RidesScreen';
import RidesDetail from '../screens/Ride/RidesDetail';
import LiveRideTracking from '../screens/Ride/LiveRideTracking';
import Review from '../screens/GettingAPassenger/Review';
import GotAnotherDriver from '../screens/GettingAPassenger/GotAnotherDriver';
import MadePayment from '../screens/GettingAPassenger/MadePayment';
import MeetPassenger from '../screens/GettingAPassenger/MeetPassenger';
import PassengerWaiting from '../screens/GettingAPassenger/PassengerWaiting';
import RatePassenger from '../screens/GettingAPassenger/RatePassenger';
import SetPrice from '../screens/GettingAPassenger/SetPrice';
import SetRate from '../screens/GettingAPassenger/SetRate';
import StartRide from '../screens/GettingAPassenger/StartRide';
import TakeABreak from '../screens/GettingAPassenger/TakeABreak';
import HeadToDestination1 from '../screens/HomeBreak/HeadToDestination1';
import HeadToDestination2 from '../screens/HomeBreak/HeadToDestination2';
import QueuedRide from '../screens/HomeBreak/QueuedRide';
import RideType from '../screens/HomeBreak/RideType';
import StayInArea from '../screens/HomeBreak/StayInArea';
import RateAdded from '../screens/GettingAPassenger/RateAdded';
import AddCarScreen from '../screens/Account/AddCarScreen';
import ChatScreen from '../screens/Ride/ChatScreen';
import AddPriceForEdit from '../screens/Ride/AddPriceForEdit';
import EditPaymentMade from '../screens/Ride/EditPaymentMade';
import RideRequestEdit from '../screens/Ride/RideRequestEdit';
import CallScreen from '../screens/Ride/CallScreen';
import Message from '../screens/Account/Message';
import AddPersonalInformation from '../screens/AuthScreens/Onboarding/AddPersonalInformation';
import AddDigitalSignature from '../screens/AuthScreens/Onboarding/AddDigitalSignature';
import TAC from '../screens/AuthScreens/Onboarding/TAC';
//  import { getToken } from './redux/shared';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

const AppNavigation = () => {
  const [initialRouteName, setInitialRouteName] = useState('');

  // const fetchToken = async () => {
  //   try {
  //     const storedToken = await getToken();
  //    console.log('stored stokennn.....................', storedToken)
  //     if (storedToken) {
  //       setInitialRouteName(URL_WELCOME)}
  //     else {
  //       setInitialRouteName(URL_INTRO_SLIDER)
  //     }
  //    } catch (error) {

  //   }
  // }
  const fetchToken = async () => {
    try {
      const storedToken = await getToken();
      console.log('Stored token for auto-login:', storedToken);

      if (
        storedToken &&
        storedToken !== 'null' &&
        storedToken !== 'undefined'
      ) {
        console.log('Valid token found, navigating to TabStack');
        setInitialRouteName('TabStack');
      } else {
        console.log('No valid token found, navigating to login');
        setInitialRouteName('WelcomeBack');
      }
    } catch (error) {
      console.error('Error fetching token:', error);
      setInitialRouteName('WelcomeBack');
    }
  };
  useEffect(() => {
    setTimeout(() => {
      fetchToken();
    }, 1000);
  }, []);
  return (
    <>
      {!initialRouteName ? (
        <SplashScreen />
      ) : (
        <Stack.Navigator
          initialRouteName={initialRouteName}
          screenOptions={() => ({
            headerShown: false,
          })}>
          <Stack.Screen name={'TabStack'} component={TabStack} />

          {/* Onboarding */}
          <Stack.Screen name={'GetStarted'} component={GetStarted} />
          <Stack.Screen
            name={'DriverLicenseImage'}
            component={DriverLicenseImage}
          />
          <Stack.Screen name={'AddACarDetails'} component={AddACarDetails} />
          <Stack.Screen name={'CarFormDetails'} component={CarFormDetails} />
          <Stack.Screen name={'AddCarDetails'} component={AddCarDetails} />
          <Stack.Screen
            name={'AddProfilePicture'}
            component={AddProfilePicture}
          />
          <Stack.Screen
            name={'AddSocialSecurity'}
            component={AddSocialSecurity}
          />
          <Stack.Screen
            name={'AddSocialSecurityNumber'}
            component={AddSocialSecurityNumber}
          />
          <Stack.Screen name={'AddYourCar'} component={AddYourCar} />
          <Stack.Screen name={'CCamera'} component={CCamera} />
          <Stack.Screen name={'CheckConsent'} component={CheckConsent} />
          <Stack.Screen name={'DriverLicense'} component={DriverLicense} />
          <Stack.Screen
            name={'IdentificationSubmitted'}
            component={IdentificationSubmitted}
          />
          <Stack.Screen name={'Notification'} component={Notification} />
          <Stack.Screen name={'TermOfService'} component={TermOfService} />
          <Stack.Screen name={'WhereToDrive'} component={WhereToDrive} />
          <Stack.Screen
            name={'AddPersonalInformation'}
            component={AddPersonalInformation}
          />
          <Stack.Screen name={'MobileNumber'} component={MobileNumber} />
          <Stack.Screen name={'RSecretCode'} component={RSecretCode} />
          <Stack.Screen name={'ChatScreen'} component={ChatScreen} />
          <Stack.Screen name={'CallScreen'} component={CallScreen} />
          <Stack.Screen name={'Message'} component={Message} />
          <Stack.Screen
            name={'AddDigitalSignature'}
            component={AddDigitalSignature}
          />
          <Stack.Screen name={'TAC'} component={TAC} />

          {/* Onboarding for passenger -> driver */}
          <Stack.Screen name={'PGetStarted'} component={PGetStarted} />
          <Stack.Screen
            name={'PDriverLicenseImage'}
            component={PDriverLicenseImage}
          />
          <Stack.Screen name={'PAddACarDetails'} component={PAddACarDetails} />
          <Stack.Screen name={'PCarFormDetails'} component={PCarFormDetails} />
          <Stack.Screen name={'PAddCarDetails'} component={PAddCarDetails} />
          <Stack.Screen
            name={'PAddProfilePicture'}
            component={PAddProfilePicture}
          />
          <Stack.Screen
            name={'PAddSocialSecurity'}
            component={PAddSocialSecurity}
          />
          <Stack.Screen
            name={'PAddSocialSecurityNumber'}
            component={PAddSocialSecurityNumber}
          />
          <Stack.Screen name={'PAddYourCar'} component={PAddYourCar} />
          <Stack.Screen name={'PCCamera'} component={PCCamera} />
          <Stack.Screen name={'PCheckConsent'} component={PCheckConsent} />
          <Stack.Screen name={'PDriverLicense'} component={PDriverLicense} />
          <Stack.Screen
            name={'PIdentificationSubmitted'}
            component={PIdentificationSubmitted}
          />
          <Stack.Screen name={'PNotification'} component={PNotification} />
          <Stack.Screen name={'PTermOfService'} component={PTermsOfService} />
          <Stack.Screen name={'PWhereToDrive'} component={PWhereToDrive} />
          <Stack.Screen name={'PMobileNumber'} component={PMobileNumber} />
          <Stack.Screen name={'PSecretCode'} component={PSecretCode} />
          <Stack.Screen name={'PAddName'} component={PAddName} />
          <Stack.Screen name={'PAddEmail'} component={PAddEmail} />

          {/* SignIn */}
          <Stack.Screen name={'WelcomeBack'} component={WelcomeBack} />
          <Stack.Screen name={'SecretCode'} component={SecretCode} />
          <Stack.Screen name={'SNotification'} component={SNotification} />
          <Stack.Screen name={'AddName'} component={AddName} />
          <Stack.Screen name={'AddEmail'} component={AddEmail} />

          <Stack.Screen
            name="HeadToDestination1"
            component={HeadToDestination1}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="HeadToDestination2"
            component={HeadToDestination2}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="QueuedRide"
            component={QueuedRide}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="RideType"
            component={RideType}
            options={{headerShown: false}}
          />
          <Stack.Screen
            name="StayInArea"
            component={StayInArea}
            options={{headerShown: false}}
          />

          {/* Earning */}
          <Stack.Screen name={'AddNewBank'} component={AddNewBank} />
          <Stack.Screen name={'CashOut'} component={CashOut} />
          <Stack.Screen name={'EarningHistory'} component={EarningHistory} />
          {/* <Stack.Screen name={'SecretCode'} component={SecretCode} /> */}
          <Stack.Screen name={'EarningSummary'} component={EarningSummary} />
          <Stack.Screen name={'PayOutHistory'} component={PayOutHistory} />
          <Stack.Screen name={'PayOutMethods'} component={PayOutMethods} />
          <Stack.Screen name={'RecentRides'} component={RecentRides} />
          <Stack.Screen name={'RideDetails'} component={RideDetails} />
          <Stack.Screen name={'WeeklyBreakDown'} component={WeeklyBreakDown} />
          <Stack.Screen name={'AddPriceForEdit'} component={AddPriceForEdit} />
          <Stack.Screen name={'EditPaymentMade'} component={EditPaymentMade} />
          <Stack.Screen name={'RideRequestEdit'} component={RideRequestEdit} />

          {/* //Account */}

          <Stack.Screen name={'AboutUs'} component={AboutUs} />
          <Stack.Screen name={'AddHomeAddress'} component={AddHomeAddress} />
          <Stack.Screen name={'AddWorkAddress'} component={AddWorkAddress} />
          <Stack.Screen name={'ConfirmLocation'} component={ConfirmLocation} />
          <Stack.Screen
            name={'EditProfileScreen'}
            component={EditProfileScreen}
          />

          <Stack.Screen name={'FAQ'} component={FAQ} />
          <Stack.Screen name={'LanguageScreen'} component={LanguageScreen} />
          <Stack.Screen
            name={'NotificationScreen'}
            component={NotificationScreen}
          />
          <Stack.Screen
            name={'NotificationSettings'}
            component={NotificationSettings}
          />
          <Stack.Screen name={'Payment'} component={Payment} />
          <Stack.Screen name={'ProfileScreen'} component={ProfileScreen} />
          <Stack.Screen name={'RideFuzeFunds'} component={RideFuzeFunds} />
          <Stack.Screen
            name={'VerifyEmailScreen'}
            component={VerifyEmailScreen}
          />
          <Stack.Screen
            name={'ViewProfileScreen'}
            component={ViewProfileScreen}
          />
          <Stack.Screen name={'MyRidesScreen'} component={MyRidesScreen} />
          <Stack.Screen name={'MyCarScreen'} component={MyCarScreen} />
          <Stack.Screen
            name={'CarDetailsScreen'}
            component={CarDetailsScreen}
          />
          <Stack.Screen name={'RidesDetail'} component={RidesDetail} />
          <Stack.Screen name={'LiveRideTracking'} component={LiveRideTracking} />

          <Stack.Screen name={'Review'} component={Review} />
          <Stack.Screen
            name={'GotAnotherDriver'}
            component={GotAnotherDriver}
          />
          <Stack.Screen name={'MadePayment'} component={MadePayment} />
          <Stack.Screen name={'MeetPassenger'} component={MeetPassenger} />
          <Stack.Screen
            name={'PassengerWaiting'}
            component={PassengerWaiting}
          />
          <Stack.Screen name={'RatePassenger'} component={RatePassenger} />
          <Stack.Screen name={'SetPrice'} component={SetPrice} />
          <Stack.Screen name={'SetRate'} component={SetRate} />
          <Stack.Screen name={'StartRide'} component={StartRide} />
          <Stack.Screen name={'TakeABreak'} component={TakeABreak} />
          <Stack.Screen name={'RateAdded'} component={RateAdded} />
          <Stack.Screen name={'AddCarScreen'} component={AddCarScreen} />
        </Stack.Navigator>
      )}
      <Toast
        refs={ref => {
          Toast.setRef(ref);
        }}
      />
    </>
  );
};

export default AppNavigation;

const TabStack = () => {
  return (
    <View style={{flex: 1, backgroundColor: COLORS.white}}>
      <Tab.Navigator
        screenOptions={({route}) => ({
          headerShown: 'false',
          tabBarIcon: ({focused, color, size}) => {
            let iconName;

            if (route.name === 'Home') {
              iconName = focused ? 'home' : 'home-outline';
            } else if (route.name === 'Information') {
              iconName = focused ? 'layers' : 'layers-outline';
            } else if (route.name === 'Rides') {
              iconName = focused ? 'car' : 'car-outline';
            } else if (route.name === 'Profile') {
              iconName = focused ? 'person' : 'person-outline';
            }

            // Return the icon
            return <Ionicons name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: COLORS.primary,
          tabBarInactiveTintColor: 'gray',
        })}>
        <Tab.Screen
          name="Home"
          component={HomeStack}
          options={{headerShown: false}}
        />
        <Tab.Screen
          name="Information"
          component={FAQ}
          options={{headerShown: false}}
        />
        <Tab.Screen
          name="Rides"
          component={RidesScreen}
          options={{headerShown: false}}
        />
        <Tab.Screen
          name="Profile"
          component={ProfileScreen}
          options={{headerShown: false}}
        />
      </Tab.Navigator>
    </View>
  );
};

function InformationScreen() {
  return (
    <View style={styles.container}>
      <Text>Information Screen</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
