/**
 * Stream.io Configuration
 * 
 * Centralized configuration for Stream.io video calling service.
 * Change the API key here to update it across the entire app.
 */

// Stream.io API Key - Change this value to update everywhere
export const STREAM_API_KEY = 'vjw8jjkqz6z8';

// Stream.io Configuration Options
export const STREAM_CONFIG = {
  // API Key
  apiKey: STREAM_API_KEY,
  
  // Connection options
  options: {
    // Disable location hint to prevent warnings
    locationHint: false,
    
    // Retry options for better connection reliability
    retryOptions: {
      maxRetries: 3,
      retryInterval: 1000,
    },
    
    // Timeout settings
    timeout: 10000,
  },
  
  // Call settings
  callSettings: {
    audio: true,
    video: true,
    // Add more default call settings here
  }
};

// Helper function to create Stream client configuration
export const createStreamClientConfig = (token, user) => ({
  apiKey: STREAM_API_KEY,
  token,
  user,
  ...STREAM_CONFIG.options
});

export default {
  STREAM_API_KEY,
  STREAM_CONFIG,
  createStreamClientConfig,
};
