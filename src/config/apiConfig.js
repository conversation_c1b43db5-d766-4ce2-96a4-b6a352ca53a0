/**
 * API Configuration
 * This file contains all API endpoints and configuration settings
 * Centralized to make it easier to change URLs across the application
 */



// Base URLs
// let Base_url = "https://ridefuze-dev-server.onrender.com"
let Base_url = "https://ridefuze-server.onrender.com"
export const INRIDE_URL = `${Base_url}/driver`
export const API_BASE_URL = `${Base_url}/api`;
export const COOKIE_URL = `${Base_url}`;
export const DRIVER_API_URL = `${Base_url}`;
export const SOCKET_URL = `${INRIDE_URL}`;
export const Call_URL = Base_url
export const VIDEO_CALL_SOCKET_URL = `${Base_url}/general`;
// export const INRIDE_URL = "https://ridefuze-dev-server.onrender.com/driver"
// export const API_BASE_URL = 'https://ridefuze-dev-server.onrender.com/api';
// export const COOKIE_URL = "https://ridefuze-dev-server.onrender.com";
// export const DRIVER_API_URL = 'https://ridefuze-dev-server.onrender.com/api/driver';
// export const SOCKET_URL = `${INRIDE_URL}`;
// export const VIDEO_CALL_SOCKET_URL = "https://ridefuze-dev-server.onrender.com/general";

// Auth endpoints
export const AUTH_ENDPOINTS = {
  SIGN_IN: `${API_BASE_URL}/driver/auth/signin`,
  SIGN_UP: `${API_BASE_URL}/auth/registerNewDriver`,
  SIGN_OUT: `${API_BASE_URL}/auth/signout`,
  VERIFY_OTP: `${API_BASE_URL}/auth/verifyOtp`,
  RESEND_OTP: `${API_BASE_URL}/auth/resendOtp`,
};

// Ride endpoints
export const RIDE_ENDPOINTS = {
  GET_DRIVER_RIDES: `${INRIDE_URL}api/rides/getDriverRides`,
  HOME_BREAK: `${API_BASE_URL}/driver/homeBreak`,
};

// App settings endpoints
export const APP_SETTINGS_ENDPOINTS = {
  GET_FAQS: `${API_BASE_URL}/appSettings/getFaqs`,
  GET_ABOUT: `${API_BASE_URL}/appSettings/getAbout`,
};

// External APIs
export const EXTERNAL_APIS = {
  GOOGLE_PLACES_AUTOCOMPLETE: 'https://maps.googleapis.com/maps/api/place/autocomplete/json',
  NOMINATIM_SEARCH: 'https://nominatim.openstreetmap.org/search',
};

// Default headers for API requests
export const DEFAULT_HEADERS = {
  'Content-Type': 'application/json',
  'frontend-source': 'app',
};

export default {
  API_BASE_URL,
  DRIVER_API_URL,
  SOCKET_URL,
  VIDEO_CALL_SOCKET_URL,
  AUTH_ENDPOINTS,
  RIDE_ENDPOINTS,
  APP_SETTINGS_ENDPOINTS,
  EXTERNAL_APIS,
  DEFAULT_HEADERS,
};
