#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1753936 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=14396, tid=25240
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Sat Jun 28 15:15:05 2025 W. Central Africa Standard Time elapsed time: 245.512888 seconds (0d 0h 4m 5s)

---------------  T H R E A D  ---------------

Current thread (0x00000255cdc8fc50):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25240, stack(0x0000006012f00000,0x0000006013000000)]


Current CompileTask:
C2: 245513 7010       4       org.gradle.internal.service.DefaultServiceRegistry$ClassInspector$ClassDetails::collectTypes (63 bytes)

Stack: [0x0000006012f00000,0x0000006013000000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad0a4]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0x36a3ff]
V  [jvm.dll+0x334b83]
V  [jvm.dll+0x33401a]
V  [jvm.dll+0x21a538]
V  [jvm.dll+0x21996f]
V  [jvm.dll+0x1a53e6]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000025609897400, length=51, elements={
0x00000255a689f380, 0x00000255cdc79a00, 0x00000255cdc7a790, 0x00000255cdc8a510,
0x00000255cdc8de00, 0x00000255cdc8e6d0, 0x00000255cdc8efa0, 0x00000255cdc8fc50,
0x0000025607008120, 0x00000255cdcee960, 0x00000256070aa3f0, 0x0000025607161610,
0x0000025607d04df0, 0x0000025607c916a0, 0x0000025607b68ab0, 0x0000025607c95610,
0x00000256079e5330, 0x0000025608505380, 0x00000256085062b0, 0x00000256085067c0,
0x0000025608505890, 0x0000025608505da0, 0x0000025608503010, 0x0000025608503520,
0x0000025608503a30, 0x0000025608504450, 0x0000025608504960, 0x00000256089a52f0,
0x00000256089a01f0, 0x00000256089a3eb0, 0x00000256089a2a70, 0x00000256089a6220,
0x00000256089a2050, 0x00000256089a4de0, 0x00000256089a5800, 0x000002560899f7d0,
0x00000256089a7150, 0x00000256089a43c0, 0x00000256089a48d0, 0x000002560899fce0,
0x00000256089a3490, 0x00000256089a0c10, 0x00000256089a1b40, 0x00000256089a2560,
0x00000256089a39a0, 0x00000256089a2f80, 0x0000025608503f40, 0x0000025609a2f5a0,
0x0000025609a2c810, 0x0000025609a304d0, 0x0000025609a2d230
}

Java Threads: ( => current thread )
  0x00000255a689f380 JavaThread "main" [_thread_blocked, id=23988, stack(0x0000006012100000,0x0000006012200000)]
  0x00000255cdc79a00 JavaThread "Reference Handler" daemon [_thread_blocked, id=16656, stack(0x0000006012900000,0x0000006012a00000)]
  0x00000255cdc7a790 JavaThread "Finalizer" daemon [_thread_blocked, id=15464, stack(0x0000006012a00000,0x0000006012b00000)]
  0x00000255cdc8a510 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=16092, stack(0x0000006012b00000,0x0000006012c00000)]
  0x00000255cdc8de00 JavaThread "Attach Listener" daemon [_thread_blocked, id=25360, stack(0x0000006012c00000,0x0000006012d00000)]
  0x00000255cdc8e6d0 JavaThread "Service Thread" daemon [_thread_blocked, id=3152, stack(0x0000006012d00000,0x0000006012e00000)]
  0x00000255cdc8efa0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=25368, stack(0x0000006012e00000,0x0000006012f00000)]
=>0x00000255cdc8fc50 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=25240, stack(0x0000006012f00000,0x0000006013000000)]
  0x0000025607008120 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=22768, stack(0x0000006013100000,0x0000006013200000)]
  0x00000255cdcee960 JavaThread "Sweeper thread" daemon [_thread_blocked, id=15092, stack(0x0000006013200000,0x0000006013300000)]
  0x00000256070aa3f0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=17036, stack(0x0000006013300000,0x0000006013400000)]
  0x0000025607161610 JavaThread "Notification Thread" daemon [_thread_blocked, id=21416, stack(0x0000006013400000,0x0000006013500000)]
  0x0000025607d04df0 JavaThread "Daemon health stats" [_thread_blocked, id=17648, stack(0x0000006013900000,0x0000006013a00000)]
  0x0000025607c916a0 JavaThread "Incoming local TCP Connector on port 56941" [_thread_in_native, id=17904, stack(0x0000006012200000,0x0000006012300000)]
  0x0000025607b68ab0 JavaThread "Daemon periodic checks" [_thread_blocked, id=16664, stack(0x0000006013a00000,0x0000006013b00000)]
  0x0000025607c95610 JavaThread "Daemon" [_thread_blocked, id=3284, stack(0x0000006013b00000,0x0000006013c00000)]
  0x00000256079e5330 JavaThread "Handler for socket connection from /127.0.0.1:56941 to /127.0.0.1:56945" [_thread_in_native, id=23516, stack(0x0000006013c00000,0x0000006013d00000)]
  0x0000025608505380 JavaThread "Cancel handler" [_thread_blocked, id=10856, stack(0x0000006013d00000,0x0000006013e00000)]
  0x00000256085062b0 JavaThread "Daemon worker" [_thread_in_vm, id=17376, stack(0x0000006013e00000,0x0000006013f00000)]
  0x00000256085067c0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56941 to /127.0.0.1:56945" [_thread_blocked, id=24284, stack(0x0000006013f00000,0x0000006014000000)]
  0x0000025608505890 JavaThread "Stdin handler" [_thread_blocked, id=17740, stack(0x0000006014000000,0x0000006014100000)]
  0x0000025608505da0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=24780, stack(0x0000006014100000,0x0000006014200000)]
  0x0000025608503010 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=19200, stack(0x0000006014200000,0x0000006014300000)]
  0x0000025608503520 JavaThread "File lock request listener" [_thread_in_native, id=19412, stack(0x0000006014300000,0x0000006014400000)]
  0x0000025608503a30 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=18672, stack(0x0000006014400000,0x0000006014500000)]
  0x0000025608504450 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=25420, stack(0x0000006014800000,0x0000006014900000)]
  0x0000025608504960 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=18816, stack(0x0000006014900000,0x0000006014a00000)]
  0x00000256089a52f0 JavaThread "File watcher server" daemon [_thread_in_native, id=19216, stack(0x0000006014a00000,0x0000006014b00000)]
  0x00000256089a01f0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=5220, stack(0x0000006014b00000,0x0000006014c00000)]
  0x00000256089a3eb0 JavaThread "jar transforms" [_thread_blocked, id=14584, stack(0x0000006013000000,0x0000006013100000)]
  0x00000256089a2a70 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=22540, stack(0x0000006014c00000,0x0000006014d00000)]
  0x00000256089a6220 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=6320, stack(0x0000006014d00000,0x0000006014e00000)]
  0x00000256089a2050 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=19056, stack(0x0000006014e00000,0x0000006014f00000)]
  0x00000256089a4de0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=15364, stack(0x0000006014f00000,0x0000006015000000)]
  0x00000256089a5800 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=21084, stack(0x0000006015000000,0x0000006015100000)]
  0x000002560899f7d0 JavaThread "Unconstrained build operations" [_thread_blocked, id=2344, stack(0x0000006015100000,0x0000006015200000)]
  0x00000256089a7150 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=16972, stack(0x0000006015200000,0x0000006015300000)]
  0x00000256089a43c0 JavaThread "File lock release action executor Thread 2" [_thread_blocked, id=20164, stack(0x0000006015300000,0x0000006015400000)]
  0x00000256089a48d0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=21436, stack(0x0000006015400000,0x0000006015500000)]
  0x000002560899fce0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=22244, stack(0x0000006015500000,0x0000006015600000)]
  0x00000256089a3490 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=23940, stack(0x0000006015600000,0x0000006015700000)]
  0x00000256089a0c10 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=24064, stack(0x0000006015700000,0x0000006015800000)]
  0x00000256089a1b40 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=15612, stack(0x0000006014600000,0x0000006014700000)]
  0x00000256089a2560 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=25092, stack(0x0000006014700000,0x0000006014800000)]
  0x00000256089a39a0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=18196, stack(0x0000006015800000,0x0000006015900000)]
  0x00000256089a2f80 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=11356, stack(0x0000006014500000,0x0000006014600000)]
  0x0000025608503f40 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=23316, stack(0x0000006015900000,0x0000006015a00000)]
  0x0000025609a2f5a0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=11732, stack(0x0000006015a00000,0x0000006015b00000)]
  0x0000025609a2c810 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=7620, stack(0x0000006015b00000,0x0000006015c00000)]
  0x0000025609a304d0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=18216, stack(0x0000006015c00000,0x0000006015d00000)]
  0x0000025609a2d230 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=1488, stack(0x0000006015d00000,0x0000006015e00000)]

Other Threads:
  0x00000255cdc72ef0 VMThread "VM Thread" [stack: 0x0000006012800000,0x0000006012900000] [id=17912]
  0x0000025607135ea0 WatcherThread [stack: 0x0000006013500000,0x0000006013600000] [id=24296]
  0x00000255a6928460 GCTaskThread "GC Thread#0" [stack: 0x0000006012300000,0x0000006012400000] [id=1672]
  0x00000256071f6770 GCTaskThread "GC Thread#1" [stack: 0x0000006013600000,0x0000006013700000] [id=23292]
  0x00000256071f6a30 GCTaskThread "GC Thread#2" [stack: 0x0000006013700000,0x0000006013800000] [id=25236]
  0x00000256076c57b0 GCTaskThread "GC Thread#3" [stack: 0x0000006013800000,0x0000006013900000] [id=22548]
  0x00000255a69352c0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000006012400000,0x0000006012500000] [id=19304]
  0x00000255a6935bf0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000006012500000,0x0000006012600000] [id=25300]
  0x00000255cdb2f560 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000006012600000,0x0000006012700000] [id=15476]
  0x00000255cdced880 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000006012000000,0x0000006012100000] [id=15540]
  0x00000255cdb2fda0 ConcurrentGCThread "G1 Service" [stack: 0x0000006012700000,0x0000006012800000] [id=19532]

Threads with active compile tasks:
C2 CompilerThread0   245594 7010       4       org.gradle.internal.service.DefaultServiceRegistry$ClassInspector$ClassDetails::collectTypes (63 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x00000255ce000000-0x00000255cebb0000-0x00000255cebb0000), size 12255232, SharedBaseAddress: 0x00000255ce000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x00000255cf000000-0x0000025603000000, reserved size: 872415232
Narrow klass base: 0x00000255ce000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 98304K, used 40089K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 2 survivors (8192K)
 Metaspace       used 53873K, committed 54464K, reserved 917504K
  class space    used 7552K, committed 7808K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HS|  |TAMS 0x0000000680400000, 0x0000000680000000| Complete 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%| O|  |TAMS 0x0000000680800000, 0x0000000680400000| Untracked 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000681e6c400, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682269200, 0x0000000682400000| 60%| O|  |TAMS 0x0000000682000000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682400000, 0x0000000682800000|  0%| F|  |TAMS 0x0000000682400000, 0x0000000682400000| Untracked 
|  10|0x0000000682800000, 0x0000000682800000, 0x0000000682c00000|  0%| F|  |TAMS 0x0000000682800000, 0x0000000682800000| Untracked 
|  11|0x0000000682c00000, 0x0000000682c00000, 0x0000000683000000|  0%| F|  |TAMS 0x0000000682c00000, 0x0000000682c00000| Untracked 
|  12|0x0000000683000000, 0x0000000683000000, 0x0000000683400000|  0%| F|  |TAMS 0x0000000683000000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x00000006834bd2a0, 0x0000000683800000| 18%| S|CS|TAMS 0x0000000683400000, 0x0000000683400000| Complete 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| S|CS|TAMS 0x0000000683800000, 0x0000000683800000| Complete 
|  15|0x0000000683c00000, 0x0000000683c00000, 0x0000000684000000|  0%| F|  |TAMS 0x0000000683c00000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684000000, 0x0000000684400000|  0%| F|  |TAMS 0x0000000684000000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684400000, 0x0000000684800000|  0%| F|  |TAMS 0x0000000684400000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684800000, 0x0000000684c00000|  0%| F|  |TAMS 0x0000000684800000, 0x0000000684800000| Untracked 
|  19|0x0000000684c00000, 0x0000000684c00000, 0x0000000685000000|  0%| F|  |TAMS 0x0000000684c00000, 0x0000000684c00000| Untracked 
|  20|0x0000000685000000, 0x0000000685000000, 0x0000000685400000|  0%| F|  |TAMS 0x0000000685000000, 0x0000000685000000| Untracked 
|  21|0x0000000685400000, 0x0000000685400000, 0x0000000685800000|  0%| F|  |TAMS 0x0000000685400000, 0x0000000685400000| Untracked 
|  22|0x0000000685800000, 0x0000000685800000, 0x0000000685c00000|  0%| F|  |TAMS 0x0000000685800000, 0x0000000685800000| Untracked 
|  31|0x0000000687c00000, 0x0000000687d5aee8, 0x0000000688000000| 33%| E|  |TAMS 0x0000000687c00000, 0x0000000687c00000| Complete 

Card table byte_map: [0x00000255bdfc0000,0x00000255bebc0000] _byte_map_base: 0x00000255babc0000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000255a6928990, (CMBitMap*) 0x00000255a69289d0
 Prev Bits: [0x00000255bf7c0000, 0x00000255c57c0000)
 Next Bits: [0x00000255c57c0000, 0x00000255cb7c0000)

Polling page: 0x00000255a4720000

Metaspace:

Usage:
  Non-class:     45.24 MB used.
      Class:      7.38 MB used.
       Both:     52.61 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      45.56 MB ( 71%) committed,  1 nodes.
      Class space:      832.00 MB reserved,       7.62 MB ( <1%) committed,  1 nodes.
             Both:      896.00 MB reserved,      53.19 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  1.72 MB
       Class:  8.27 MB
        Both:  9.98 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 59.06 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 720.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 851.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 2915.
num_chunk_merges: 6.
num_chunk_splits: 1935.
num_chunks_enlarged: 1323.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3409Kb max_used=3409Kb free=116590Kb
 bounds [0x00000255b5e90000, 0x00000255b61f0000, 0x00000255bd3c0000]
CodeHeap 'profiled nmethods': size=120000Kb used=12196Kb max_used=12196Kb free=107803Kb
 bounds [0x00000255ae3c0000, 0x00000255aefb0000, 0x00000255b58f0000]
CodeHeap 'non-nmethods': size=5760Kb used=2346Kb max_used=2416Kb free=3413Kb
 bounds [0x00000255b58f0000, 0x00000255b5b60000, 0x00000255b5e90000]
 total_blobs=7102 nmethods=6233 adapters=781
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 245.244 Thread 0x0000025607008120 nmethod 7001 0x00000255aef9aa90 code [0x00000255aef9ac20, 0x00000255aef9adf8]
Event: 245.283 Thread 0x0000025607008120 7002       1       org.gradle.model.internal.asm.AsmClassGenerator::getGeneratedType (5 bytes)
Event: 245.283 Thread 0x0000025607008120 nmethod 7002 0x00000255b61e3b10 code [0x00000255b61e3ca0, 0x00000255b61e3d78]
Event: 245.284 Thread 0x0000025607008120 7003       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::addSetter (9 bytes)
Event: 245.285 Thread 0x0000025607008120 nmethod 7003 0x00000255aef9ae90 code [0x00000255aef9b040, 0x00000255aef9b188]
Event: 245.285 Thread 0x0000025607008120 7004       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$157/0x00000255cf1cf288::<init> (15 bytes)
Event: 245.285 Thread 0x0000025607008120 nmethod 7004 0x00000255aef9b290 code [0x00000255aef9b440, 0x00000255aef9b678]
Event: 245.285 Thread 0x0000025607008120 7005       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$157/0x00000255cf1cf288::emit (13 bytes)
Event: 245.285 Thread 0x0000025607008120 nmethod 7005 0x00000255aef9b710 code [0x00000255aef9b8c0, 0x00000255aef9ba28]
Event: 245.285 Thread 0x0000025607008120 7006       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$14::<init> (28 bytes)
Event: 245.286 Thread 0x0000025607008120 nmethod 7006 0x00000255aef9bb10 code [0x00000255aef9bd00, 0x00000255aef9c388]
Event: 245.286 Thread 0x0000025607008120 7007       3       jdk.internal.org.objectweb.asm.ClassReader::getFirstAttributeOffset (136 bytes)
Event: 245.286 Thread 0x0000025607008120 nmethod 7007 0x00000255aef9c590 code [0x00000255aef9c7c0, 0x00000255aef9ce48]
Event: 245.293 Thread 0x0000025607008120 7008   !   3       org.gradle.internal.instantiation.generator.AbstractClassGenerator::generateUnderLock (704 bytes)
Event: 245.303 Thread 0x0000025607008120 nmethod 7008 0x00000255aef9d090 code [0x00000255aef9db20, 0x00000255aefa56c8]
Event: 245.310 Thread 0x00000255cdc8fc50 7009   !   4       org.gradle.internal.service.DefaultServiceRegistry$SingletonService::prepare (111 bytes)
Event: 245.315 Thread 0x00000255cdc8fc50 nmethod 7009 0x00000255b61e3e10 code [0x00000255b61e3fc0, 0x00000255b61e4598]
Event: 245.315 Thread 0x00000255cdc8fc50 7010       4       org.gradle.internal.service.DefaultServiceRegistry$ClassInspector$ClassDetails::collectTypes (63 bytes)
Event: 245.503 Thread 0x0000025607008120 7011       3       java.lang.Class::getMethods (26 bytes)
Event: 245.504 Thread 0x0000025607008120 nmethod 7011 0x00000255aefa8d10 code [0x00000255aefa8f00, 0x00000255aefa9218]

GC Heap History (20 events):
Event: 121.888 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 98304K, used 68786K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 29394K, committed 29760K, reserved 917504K
  class space    used 4085K, committed 4288K, reserved 851968K
}
Event: 121.907 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 98304K, used 28133K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 29394K, committed 29760K, reserved 917504K
  class space    used 4085K, committed 4288K, reserved 851968K
}
Event: 122.351 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 98304K, used 32229K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 2 survivors (8192K)
 Metaspace       used 29439K, committed 29824K, reserved 917504K
  class space    used 4085K, committed 4288K, reserved 851968K
}
Event: 122.373 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 98304K, used 28348K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 29439K, committed 29824K, reserved 917504K
  class space    used 4085K, committed 4288K, reserved 851968K
}
Event: 134.047 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 98304K, used 65212K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 32877K, committed 33280K, reserved 917504K
  class space    used 4543K, committed 4736K, reserved 851968K
}
Event: 134.057 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 98304K, used 30976K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 32877K, committed 33280K, reserved 917504K
  class space    used 4543K, committed 4736K, reserved 851968K
}
Event: 139.663 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 98304K, used 51456K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 1 survivors (4096K)
 Metaspace       used 35560K, committed 35968K, reserved 917504K
  class space    used 4936K, committed 5120K, reserved 851968K
}
Event: 139.671 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total 98304K, used 32125K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 35560K, committed 35968K, reserved 917504K
  class space    used 4936K, committed 5120K, reserved 851968K
}
Event: 145.508 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 98304K, used 68989K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 38138K, committed 38656K, reserved 917504K
  class space    used 5227K, committed 5440K, reserved 851968K
}
Event: 145.519 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 98304K, used 33207K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 38138K, committed 38656K, reserved 917504K
  class space    used 5227K, committed 5440K, reserved 851968K
}
Event: 166.883 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 98304K, used 74167K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 41380K, committed 41920K, reserved 917504K
  class space    used 5750K, committed 5952K, reserved 851968K
}
Event: 166.891 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 98304K, used 34062K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 41380K, committed 41920K, reserved 917504K
  class space    used 5750K, committed 5952K, reserved 851968K
}
Event: 194.549 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 98304K, used 75022K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 45191K, committed 45696K, reserved 917504K
  class space    used 6323K, committed 6528K, reserved 851968K
}
Event: 194.559 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 98304K, used 35325K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 45191K, committed 45696K, reserved 917504K
  class space    used 6323K, committed 6528K, reserved 851968K
}
Event: 207.720 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 98304K, used 72189K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 49566K, committed 50176K, reserved 917504K
  class space    used 6929K, committed 7232K, reserved 851968K
}
Event: 207.733 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 98304K, used 36562K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 49566K, committed 50176K, reserved 917504K
  class space    used 6929K, committed 7232K, reserved 851968K
}
Event: 232.574 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 98304K, used 73426K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 52743K, committed 53312K, reserved 917504K
  class space    used 7428K, committed 7680K, reserved 851968K
}
Event: 232.603 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 98304K, used 38667K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 52743K, committed 53312K, reserved 917504K
  class space    used 7428K, committed 7680K, reserved 851968K
}
Event: 245.245 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 98304K, used 75531K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 53767K, committed 54400K, reserved 917504K
  class space    used 7537K, committed 7808K, reserved 851968K
}
Event: 245.272 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 98304K, used 40089K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 53767K, committed 54400K, reserved 917504K
  class space    used 7537K, committed 7808K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.803 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 1.014 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 2.296 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 2.300 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 2.309 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 2.311 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 2.316 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 10.722 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 17.421 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 18.730 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 18.749 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 53.543 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 53.689 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 59.335 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 68.925 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 80.813 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform10793321259402690119dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 242.825 Thread 0x0000025608503010 DEOPT PACKING pc=0x00000255aed0fe43 sp=0x00000060142fea00
Event: 242.825 Thread 0x0000025608503010 DEOPT UNPACKING pc=0x00000255b5947143 sp=0x00000060142fde88 mode 0
Event: 242.861 Thread 0x00000256085062b0 DEOPT PACKING pc=0x00000255ae8260f4 sp=0x0000006013ef3b70
Event: 242.861 Thread 0x00000256085062b0 DEOPT UNPACKING pc=0x00000255b5947143 sp=0x0000006013ef30f0 mode 0
Event: 243.850 Thread 0x00000256085062b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000255b5f09e18 relative=0x0000000000002038
Event: 243.850 Thread 0x00000256085062b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000255b5f09e18 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 91 c2
Event: 243.850 Thread 0x00000256085062b0 DEOPT PACKING pc=0x00000255b5f09e18 sp=0x0000006013ef5dc0
Event: 243.850 Thread 0x00000256085062b0 DEOPT UNPACKING pc=0x00000255b59469a3 sp=0x0000006013ef5c88 mode 2
Event: 243.850 Thread 0x00000256085062b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x00000255b5f09e18 relative=0x0000000000002038
Event: 243.850 Thread 0x00000256085062b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x00000255b5f09e18 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 91 c2
Event: 243.850 Thread 0x00000256085062b0 DEOPT PACKING pc=0x00000255b5f09e18 sp=0x0000006013ef5dc0
Event: 243.850 Thread 0x00000256085062b0 DEOPT UNPACKING pc=0x00000255b59469a3 sp=0x0000006013ef5c88 mode 2
Event: 243.870 Thread 0x00000256085062b0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000255b5ffc5e8 relative=0x00000000000001c8
Event: 243.870 Thread 0x00000256085062b0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000255b5ffc5e8 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 12 c2
Event: 243.870 Thread 0x00000256085062b0 DEOPT PACKING pc=0x00000255b5ffc5e8 sp=0x0000006013ef4a00
Event: 243.870 Thread 0x00000256085062b0 DEOPT UNPACKING pc=0x00000255b59469a3 sp=0x0000006013ef49b8 mode 2
Event: 245.079 Thread 0x00000256085062b0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x00000255b5ffc5e8 relative=0x00000000000001c8
Event: 245.079 Thread 0x00000256085062b0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x00000255b5ffc5e8 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 12 c2
Event: 245.079 Thread 0x00000256085062b0 DEOPT PACKING pc=0x00000255b5ffc5e8 sp=0x0000006013ef2300
Event: 245.079 Thread 0x00000256085062b0 DEOPT UNPACKING pc=0x00000255b59469a3 sp=0x0000006013ef22b8 mode 2

Classes loaded (20 events):
Event: 232.055 Loading class jdk/internal/logger/LoggerFinderLoader$TemporaryLoggerFinder
Event: 232.055 Loading class jdk/internal/logger/LoggerFinderLoader$TemporaryLoggerFinder done
Event: 232.057 Loading class sun/util/logging/internal/LoggingProviderImpl$JULWrapper
Event: 232.057 Loading class sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
Event: 232.057 Loading class sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration done
Event: 232.057 Loading class sun/util/logging/internal/LoggingProviderImpl$JULWrapper done
Event: 232.058 Loading class java/io/ObjectInputFilter
Event: 232.059 Loading class java/io/ObjectInputFilter done
Event: 232.061 Loading class java/io/ObjectInputFilter$Config$BuiltinFilterFactory
Event: 232.061 Loading class java/io/ObjectInputFilter$Config$BuiltinFilterFactory done
Event: 232.061 Loading class jdk/internal/access/JavaObjectInputFilterAccess
Event: 232.061 Loading class jdk/internal/access/JavaObjectInputFilterAccess done
Event: 232.062 Loading class java/lang/System$Logger$Level
Event: 232.062 Loading class java/lang/System$Logger$Level done
Event: 232.063 Loading class jdk/internal/event/DeserializationEvent
Event: 232.063 Loading class jdk/internal/event/DeserializationEvent done
Event: 232.063 Loading class java/io/ObjectInputStream$FieldValues
Event: 232.064 Loading class java/io/ObjectInputStream$GetField
Event: 232.064 Loading class java/io/ObjectInputStream$GetField done
Event: 232.064 Loading class java/io/ObjectInputStream$FieldValues done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 179.107 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000068448e6b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000068448e6b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 202.751 Thread 0x00000256085062b0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000684aca5c8}: sun/misc/SharedSecrets> (0x0000000684aca5c8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 205.059 Thread 0x00000256085062b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006846a4638}> (0x00000006846a4638) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 205.059 Thread 0x00000256085062b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006846a4968}> (0x00000006846a4968) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 205.060 Thread 0x00000256085062b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006846a7b50}> (0x00000006846a7b50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 205.060 Thread 0x00000256085062b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006846a7f70}> (0x00000006846a7f70) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 205.066 Thread 0x00000256085062b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006846b8d60}> (0x00000006846b8d60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 205.067 Thread 0x00000256085062b0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006846b9180}> (0x00000006846b9180) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 207.616 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683f55918}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000683f55918) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 207.743 Thread 0x00000256085062b0 Implicit null exception at 0x00000255b5f86c53 to 0x00000255b5f876f0
Event: 213.895 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684e62c38}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000684e62c38) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 214.713 Thread 0x00000256085062b0 Implicit null exception at 0x00000255b5f30b06 to 0x00000255b5f30ebc
Event: 228.015 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c4e508}: static Lorg/gradle/api/internal/catalog/DefaultVersionCatalog;.<clinit>()V> (0x0000000683c4e508) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.452 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683cad280}: static [Ljava/lang/Object;.<clinit>()V> (0x0000000683cad280) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.463 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683cefca8}: static Lorg/gradle/api/internal/catalog/DependencyModel;.<clinit>()V> (0x0000000683cefca8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.465 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683cf3bf0}: static Lorg/gradle/api/internal/catalog/AbstractContextAwareModel;.<clinit>()V> (0x0000000683cf3bf0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.480 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683d11420}: static Lorg/gradle/api/internal/artifacts/dependencies/AbstractVersionConstraint;.<clinit>()V> (0x0000000683d11420) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.886 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683de09a8}: static Lorg/gradle/api/internal/catalog/PluginModel;.<clinit>()V> (0x0000000683de09a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.889 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683dfbc80}: static Lorg/gradle/api/internal/catalog/VersionModel;.<clinit>()V> (0x0000000683dfbc80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 228.922 Thread 0x00000256085062b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683e17600}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000683e17600) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 236.171 Executing VM operation: Cleanup
Event: 236.185 Executing VM operation: Cleanup done
Event: 237.199 Executing VM operation: Cleanup
Event: 237.200 Executing VM operation: Cleanup done
Event: 238.211 Executing VM operation: Cleanup
Event: 238.211 Executing VM operation: Cleanup done
Event: 240.248 Executing VM operation: Cleanup
Event: 240.553 Executing VM operation: Cleanup done
Event: 241.103 Executing VM operation: HandshakeAllThreads
Event: 241.103 Executing VM operation: HandshakeAllThreads done
Event: 242.119 Executing VM operation: Cleanup
Event: 242.119 Executing VM operation: Cleanup done
Event: 243.129 Executing VM operation: Cleanup
Event: 243.129 Executing VM operation: Cleanup done
Event: 244.139 Executing VM operation: Cleanup
Event: 244.170 Executing VM operation: Cleanup done
Event: 245.180 Executing VM operation: Cleanup
Event: 245.230 Executing VM operation: Cleanup done
Event: 245.245 Executing VM operation: G1CollectForAllocation
Event: 245.272 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 151.196 Thread 0x00000255cdcee960 flushing nmethod 0x00000255aeb8fb90
Event: 151.196 Thread 0x00000255cdcee960 flushing nmethod 0x00000255aebc0390
Event: 151.197 Thread 0x00000255cdcee960 flushing nmethod 0x00000255aece5490
Event: 177.628 Thread 0x000002560899f7d0 Thread added: 0x000002560899f7d0
Event: 177.628 Thread 0x00000256089a7150 Thread added: 0x00000256089a7150
Event: 178.062 Thread 0x00000256089a43c0 Thread added: 0x00000256089a43c0
Event: 178.504 Thread 0x00000256089a48d0 Thread added: 0x00000256089a48d0
Event: 178.506 Thread 0x000002560899fce0 Thread added: 0x000002560899fce0
Event: 179.113 Thread 0x00000256089a3490 Thread added: 0x00000256089a3490
Event: 179.114 Thread 0x00000256089a0c10 Thread added: 0x00000256089a0c10
Event: 236.274 Thread 0x00000256089a1b40 Thread added: 0x00000256089a1b40
Event: 236.278 Thread 0x00000256089a2560 Thread added: 0x00000256089a2560
Event: 236.279 Thread 0x00000256089a39a0 Thread added: 0x00000256089a39a0
Event: 238.326 Thread 0x0000025608503f40 Thread exited: 0x0000025608503f40
Event: 240.090 Thread 0x00000256089a2f80 Thread added: 0x00000256089a2f80
Event: 240.106 Thread 0x0000025608503f40 Thread added: 0x0000025608503f40
Event: 240.140 Thread 0x0000025609a2f5a0 Thread added: 0x0000025609a2f5a0
Event: 242.263 Thread 0x0000025609a2c810 Thread added: 0x0000025609a2c810
Event: 242.264 Thread 0x0000025609a304d0 Thread added: 0x0000025609a304d0
Event: 242.267 Thread 0x0000025609a2d230 Thread added: 0x0000025609a2d230


Dynamic libraries:
0x00007ff6cc7f0000 - 0x00007ff6cc7fe000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffddafb0000 - 0x00007ffddb1c7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffdd9760000 - 0x00007ffdd9824000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffdd88a0000 - 0x00007ffdd8c71000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffdd86c0000 - 0x00007ffdd87d1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffdbddb0000 - 0x00007ffdbddc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffdda8b0000 - 0x00007ffddaa61000 	C:\Windows\System32\USER32.dll
0x00007ffdd8520000 - 0x00007ffdd8546000 	C:\Windows\System32\win32u.dll
0x00007ffdd8c80000 - 0x00007ffdd8ca9000 	C:\Windows\System32\GDI32.dll
0x00007ffdd8400000 - 0x00007ffdd851b000 	C:\Windows\System32\gdi32full.dll
0x00007ffdd80f0000 - 0x00007ffdd818a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffdbd740000 - 0x00007ffdbd75d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffdc0e00000 - 0x00007ffdc1092000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffdd9de0000 - 0x00007ffdd9e87000 	C:\Windows\System32\msvcrt.dll
0x00007ffdda7a0000 - 0x00007ffdda7d1000 	C:\Windows\System32\IMM32.DLL
0x00007ffdc1410000 - 0x00007ffdc141c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffd1cfd0000 - 0x00007ffd1d05d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffd16270000 - 0x00007ffd16ee0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffdd9900000 - 0x00007ffdd99b1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffdd9830000 - 0x00007ffdd98d7000 	C:\Windows\System32\sechost.dll
0x00007ffdd8290000 - 0x00007ffdd82b8000 	C:\Windows\System32\bcrypt.dll
0x00007ffddac20000 - 0x00007ffddad34000 	C:\Windows\System32\RPCRT4.dll
0x00007ffdda580000 - 0x00007ffdda5f1000 	C:\Windows\System32\WS2_32.dll
0x00007ffdd7fc0000 - 0x00007ffdd800d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffdcdc20000 - 0x00007ffdcdc54000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffdc96e0000 - 0x00007ffdc96ea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffdd7fa0000 - 0x00007ffdd7fb3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffdd7100000 - 0x00007ffdd7118000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffdc0230000 - 0x00007ffdc023a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffdd20e0000 - 0x00007ffdd2312000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffdd99c0000 - 0x00007ffdd9d50000 	C:\Windows\System32\combase.dll
0x00007ffdda600000 - 0x00007ffdda6d7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdb8520000 - 0x00007ffdb8552000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffdd8190000 - 0x00007ffdd820b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffdb4de0000 - 0x00007ffdb4dee000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffdb0030000 - 0x00007ffdb0055000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffd1cef0000 - 0x00007ffd1cfc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffdd8d10000 - 0x00007ffdd9598000 	C:\Windows\System32\SHELL32.dll
0x00007ffdd82c0000 - 0x00007ffdd83ff000 	C:\Windows\System32\wintypes.dll
0x00007ffdd6000000 - 0x00007ffdd690d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffddad80000 - 0x00007ffddae8a000 	C:\Windows\System32\SHCORE.dll
0x00007ffddaf10000 - 0x00007ffddaf6e000 	C:\Windows\System32\shlwapi.dll
0x00007ffdd8020000 - 0x00007ffdd804b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdab940000 - 0x00007ffdab958000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffdbce60000 - 0x00007ffdbce7a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffdcd8e0000 - 0x00007ffdcda0c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffdd7560000 - 0x00007ffdd75ca000 	C:\Windows\system32\mswsock.dll
0x00007ffdac240000 - 0x00007ffdac256000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffdbd730000 - 0x00007ffdbd740000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffda9480000 - 0x00007ffda94a7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000059e60000 - 0x0000000059ed3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdbc7c0000 - 0x00007ffdbc7ca000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffdb9b10000 - 0x00007ffdb9b1b000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffddad60000 - 0x00007ffddad68000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdd78b0000 - 0x00007ffdd78cb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffdd7060000 - 0x00007ffdd7097000 	C:\Windows\system32\rsaenh.dll
0x00007ffdd7600000 - 0x00007ffdd7628000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffdd7890000 - 0x00007ffdd789c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffdd6b70000 - 0x00007ffdd6b9d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffdda6e0000 - 0x00007ffdda6e9000 	C:\Windows\System32\NSI.dll
0x00007ffdcd7e0000 - 0x00007ffdcd7f9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdcd7a0000 - 0x00007ffdcd7bf000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffdd6be0000 - 0x00007ffdd6ce2000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffdb5dc0000 - 0x00007ffdb5dc9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffdb4630000 - 0x00007ffdb463e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffdd8550000 - 0x00007ffdd86b6000 	C:\Windows\System32\CRYPT32.dll
0x00007ffdd79c0000 - 0x00007ffdd79ed000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffdd7980000 - 0x00007ffdd79b7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffdbec10000 - 0x00007ffdbec18000 	C:\Windows\system32\wshunix.dll
0x0000000059b60000 - 0x0000000059bd3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform10793321259402690119dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform10793321259402690119dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 7:52 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (251M free)
TotalPageFile size 25066M (AvailPageFile size 4M)
current process WorkingSet (physical memory assigned to process): 190M, peak: 208M
current process commit charge ("private bytes"): 287M, peak: 295M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
