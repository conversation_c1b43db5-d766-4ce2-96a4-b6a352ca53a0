#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 366976 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=10056, tid=1232
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew org.gradle.wrapper.GradleWrapperMain app:installDebug -PreactNativeDevServerPort=8081

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Mon May  5 19:41:56 2025 W. Central Africa Standard Time elapsed time: 796.532661 seconds (0d 0h 13m 16s)

---------------  T H R E A D  ---------------

Current thread (0x00000216e31264f0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=1232, stack(0x000000e0c6b00000,0x000000e0c6c00000)]


Current CompileTask:
C2: 796532 2044       4       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput::doAppend (43 bytes)

Stack: [0x000000e0c6b00000,0x000000e0c6c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad0a4]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0x2b09af]
V  [jvm.dll+0x58d829]
V  [jvm.dll+0x223592]
V  [jvm.dll+0x21c289]
V  [jvm.dll+0x21986b]
V  [jvm.dll+0x1a53e6]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00000216fc5c3850, length=13, elements={
0x00000216e307aa60, 0x00000216e310a4f0, 0x00000216e310b370, 0x00000216e31208f0,
0x00000216e31243d0, 0x00000216e3124d90, 0x00000216e3125750, 0x00000216e31264f0,
0x00000216e31273b0, 0x00000216fc26de30, 0x00000216fc2c3130, 0x00000216fc2c8ce0,
0x00000216fcba98d0
}

Java Threads: ( => current thread )
  0x00000216e307aa60 JavaThread "main" [_thread_in_native, id=2684, stack(0x000000e0c5e00000,0x000000e0c5f00000)]
  0x00000216e310a4f0 JavaThread "Reference Handler" daemon [_thread_blocked, id=1140, stack(0x000000e0c6500000,0x000000e0c6600000)]
  0x00000216e310b370 JavaThread "Finalizer" daemon [_thread_blocked, id=3320, stack(0x000000e0c6600000,0x000000e0c6700000)]
  0x00000216e31208f0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=9288, stack(0x000000e0c6700000,0x000000e0c6800000)]
  0x00000216e31243d0 JavaThread "Attach Listener" daemon [_thread_blocked, id=17604, stack(0x000000e0c6800000,0x000000e0c6900000)]
  0x00000216e3124d90 JavaThread "Service Thread" daemon [_thread_blocked, id=7444, stack(0x000000e0c6900000,0x000000e0c6a00000)]
  0x00000216e3125750 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=10156, stack(0x000000e0c6a00000,0x000000e0c6b00000)]
=>0x00000216e31264f0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=1232, stack(0x000000e0c6b00000,0x000000e0c6c00000)]
  0x00000216e31273b0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=13096, stack(0x000000e0c6c00000,0x000000e0c6d00000)]
  0x00000216fc26de30 JavaThread "Sweeper thread" daemon [_thread_blocked, id=17980, stack(0x000000e0c6d00000,0x000000e0c6e00000)]
  0x00000216fc2c3130 JavaThread "Notification Thread" daemon [_thread_blocked, id=16848, stack(0x000000e0c6e00000,0x000000e0c6f00000)]
  0x00000216fc2c8ce0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=16480, stack(0x000000e0c7000000,0x000000e0c7100000)]
  0x00000216fcba98d0 JavaThread "pool-1-thread-1" [_thread_blocked, id=18700, stack(0x000000e0c7400000,0x000000e0c7500000)]

Other Threads:
  0x00000216e3105200 VMThread "VM Thread" [stack: 0x000000e0c6400000,0x000000e0c6500000] [id=17620]
  0x00000216fc2c7a30 WatcherThread [stack: 0x000000e0c6f00000,0x000000e0c7000000] [id=17596]
  0x00000216e3098df0 GCTaskThread "GC Thread#0" [stack: 0x000000e0c5f00000,0x000000e0c6000000] [id=11384]
  0x00000216fc62eb80 GCTaskThread "GC Thread#1" [stack: 0x000000e0c7100000,0x000000e0c7200000] [id=18548]
  0x00000216fc636600 GCTaskThread "GC Thread#2" [stack: 0x000000e0c7200000,0x000000e0c7300000] [id=18552]
  0x00000216fc6368c0 GCTaskThread "GC Thread#3" [stack: 0x000000e0c7300000,0x000000e0c7400000] [id=18556]
  0x00000216e309a540 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000e0c6000000,0x000000e0c6100000] [id=16996]
  0x00000216e309af60 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000e0c6100000,0x000000e0c6200000] [id=17460]
  0x00000216e30e8cf0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000e0c6200000,0x000000e0c6300000] [id=5484]
  0x00000216e30e9720 ConcurrentGCThread "G1 Service" [stack: 0x000000e0c6300000,0x000000e0c6400000] [id=2128]

Threads with active compile tasks:
C2 CompilerThread0   796586 2044       4       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput::doAppend (43 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000fc000000, size: 64 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000021680000000-0x0000021680bb0000-0x0000021680bb0000), size 12255232, SharedBaseAddress: 0x0000021680000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000021681000000-0x00000216c1000000, reserved size: 1073741824
Narrow klass base: 0x0000021680000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 64M
 Heap Initial Capacity: 64M
 Heap Max Capacity: 64M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 65536K, used 42458K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 36 young (36864K), 3 survivors (3072K)
 Metaspace       used 10242K, committed 10432K, reserved 1114112K
  class space    used 1317K, committed 1408K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000fc000000, 0x00000000fc100000, 0x00000000fc100000|100%|HS|  |TAMS 0x00000000fc000000, 0x00000000fc000000| Complete 
|   1|0x00000000fc100000, 0x00000000fc200000, 0x00000000fc200000|100%|HC|  |TAMS 0x00000000fc100000, 0x00000000fc100000| Complete 
|   2|0x00000000fc200000, 0x00000000fc300000, 0x00000000fc300000|100%|HC|  |TAMS 0x00000000fc200000, 0x00000000fc200000| Complete 
|   3|0x00000000fc300000, 0x00000000fc400000, 0x00000000fc400000|100%| O|  |TAMS 0x00000000fc300000, 0x00000000fc300000| Untracked 
|   4|0x00000000fc400000, 0x00000000fc500000, 0x00000000fc500000|100%| O|  |TAMS 0x00000000fc400000, 0x00000000fc400000| Untracked 
|   5|0x00000000fc500000, 0x00000000fc600000, 0x00000000fc600000|100%| O|  |TAMS 0x00000000fc500000, 0x00000000fc500000| Untracked 
|   6|0x00000000fc600000, 0x00000000fc676800, 0x00000000fc700000| 46%| O|  |TAMS 0x00000000fc600000, 0x00000000fc600000| Untracked 
|   7|0x00000000fc700000, 0x00000000fc700000, 0x00000000fc800000|  0%| F|  |TAMS 0x00000000fc700000, 0x00000000fc700000| Untracked 
|   8|0x00000000fc800000, 0x00000000fc800000, 0x00000000fc900000|  0%| F|  |TAMS 0x00000000fc800000, 0x00000000fc800000| Untracked 
|   9|0x00000000fc900000, 0x00000000fc900000, 0x00000000fca00000|  0%| F|  |TAMS 0x00000000fc900000, 0x00000000fc900000| Untracked 
|  10|0x00000000fca00000, 0x00000000fca00000, 0x00000000fcb00000|  0%| F|  |TAMS 0x00000000fca00000, 0x00000000fca00000| Untracked 
|  11|0x00000000fcb00000, 0x00000000fcb00000, 0x00000000fcc00000|  0%| F|  |TAMS 0x00000000fcb00000, 0x00000000fcb00000| Untracked 
|  12|0x00000000fcc00000, 0x00000000fcc00000, 0x00000000fcd00000|  0%| F|  |TAMS 0x00000000fcc00000, 0x00000000fcc00000| Untracked 
|  13|0x00000000fcd00000, 0x00000000fcd00000, 0x00000000fce00000|  0%| F|  |TAMS 0x00000000fcd00000, 0x00000000fcd00000| Untracked 
|  14|0x00000000fce00000, 0x00000000fce00000, 0x00000000fcf00000|  0%| F|  |TAMS 0x00000000fce00000, 0x00000000fce00000| Untracked 
|  15|0x00000000fcf00000, 0x00000000fcf00000, 0x00000000fd000000|  0%| F|  |TAMS 0x00000000fcf00000, 0x00000000fcf00000| Untracked 
|  16|0x00000000fd000000, 0x00000000fd000000, 0x00000000fd100000|  0%| F|  |TAMS 0x00000000fd000000, 0x00000000fd000000| Untracked 
|  17|0x00000000fd100000, 0x00000000fd100000, 0x00000000fd200000|  0%| F|  |TAMS 0x00000000fd100000, 0x00000000fd100000| Untracked 
|  18|0x00000000fd200000, 0x00000000fd200000, 0x00000000fd300000|  0%| F|  |TAMS 0x00000000fd200000, 0x00000000fd200000| Untracked 
|  19|0x00000000fd300000, 0x00000000fd300000, 0x00000000fd400000|  0%| F|  |TAMS 0x00000000fd300000, 0x00000000fd300000| Untracked 
|  20|0x00000000fd400000, 0x00000000fd400000, 0x00000000fd500000|  0%| F|  |TAMS 0x00000000fd400000, 0x00000000fd400000| Untracked 
|  21|0x00000000fd500000, 0x00000000fd500000, 0x00000000fd600000|  0%| F|  |TAMS 0x00000000fd500000, 0x00000000fd500000| Untracked 
|  22|0x00000000fd600000, 0x00000000fd600000, 0x00000000fd700000|  0%| F|  |TAMS 0x00000000fd600000, 0x00000000fd600000| Untracked 
|  23|0x00000000fd700000, 0x00000000fd700000, 0x00000000fd800000|  0%| F|  |TAMS 0x00000000fd700000, 0x00000000fd700000| Untracked 
|  24|0x00000000fd800000, 0x00000000fd800000, 0x00000000fd900000|  0%| F|  |TAMS 0x00000000fd800000, 0x00000000fd800000| Untracked 
|  25|0x00000000fd900000, 0x00000000fd900000, 0x00000000fda00000|  0%| F|  |TAMS 0x00000000fd900000, 0x00000000fd900000| Untracked 
|  26|0x00000000fda00000, 0x00000000fda00000, 0x00000000fdb00000|  0%| F|  |TAMS 0x00000000fda00000, 0x00000000fda00000| Untracked 
|  27|0x00000000fdb00000, 0x00000000fdb00000, 0x00000000fdc00000|  0%| F|  |TAMS 0x00000000fdb00000, 0x00000000fdb00000| Untracked 
|  28|0x00000000fdc00000, 0x00000000fdc5a1e8, 0x00000000fdd00000| 35%| E|  |TAMS 0x00000000fdc00000, 0x00000000fdc00000| Complete 
|  29|0x00000000fdd00000, 0x00000000fde00000, 0x00000000fde00000|100%| E|CS|TAMS 0x00000000fdd00000, 0x00000000fdd00000| Complete 
|  30|0x00000000fde00000, 0x00000000fdf00000, 0x00000000fdf00000|100%| E|CS|TAMS 0x00000000fde00000, 0x00000000fde00000| Complete 
|  31|0x00000000fdf00000, 0x00000000fe000000, 0x00000000fe000000|100%| E|CS|TAMS 0x00000000fdf00000, 0x00000000fdf00000| Complete 
|  32|0x00000000fe000000, 0x00000000fe100000, 0x00000000fe100000|100%| E|CS|TAMS 0x00000000fe000000, 0x00000000fe000000| Complete 
|  33|0x00000000fe100000, 0x00000000fe200000, 0x00000000fe200000|100%| E|CS|TAMS 0x00000000fe100000, 0x00000000fe100000| Complete 
|  34|0x00000000fe200000, 0x00000000fe300000, 0x00000000fe300000|100%| E|CS|TAMS 0x00000000fe200000, 0x00000000fe200000| Complete 
|  35|0x00000000fe300000, 0x00000000fe400000, 0x00000000fe400000|100%| E|CS|TAMS 0x00000000fe300000, 0x00000000fe300000| Complete 
|  36|0x00000000fe400000, 0x00000000fe500000, 0x00000000fe500000|100%| E|CS|TAMS 0x00000000fe400000, 0x00000000fe400000| Complete 
|  37|0x00000000fe500000, 0x00000000fe600000, 0x00000000fe600000|100%| E|CS|TAMS 0x00000000fe500000, 0x00000000fe500000| Complete 
|  38|0x00000000fe600000, 0x00000000fe700000, 0x00000000fe700000|100%| E|CS|TAMS 0x00000000fe600000, 0x00000000fe600000| Complete 
|  39|0x00000000fe700000, 0x00000000fe800000, 0x00000000fe800000|100%| E|CS|TAMS 0x00000000fe700000, 0x00000000fe700000| Complete 
|  40|0x00000000fe800000, 0x00000000fe900000, 0x00000000fe900000|100%| E|CS|TAMS 0x00000000fe800000, 0x00000000fe800000| Complete 
|  41|0x00000000fe900000, 0x00000000fea00000, 0x00000000fea00000|100%| E|CS|TAMS 0x00000000fe900000, 0x00000000fe900000| Complete 
|  42|0x00000000fea00000, 0x00000000feb00000, 0x00000000feb00000|100%| S|CS|TAMS 0x00000000fea00000, 0x00000000fea00000| Complete 
|  43|0x00000000feb00000, 0x00000000fec00000, 0x00000000fec00000|100%| S|CS|TAMS 0x00000000feb00000, 0x00000000feb00000| Complete 
|  44|0x00000000fec00000, 0x00000000fed00000, 0x00000000fed00000|100%| S|CS|TAMS 0x00000000fec00000, 0x00000000fec00000| Complete 
|  45|0x00000000fed00000, 0x00000000fee00000, 0x00000000fee00000|100%| E|CS|TAMS 0x00000000fed00000, 0x00000000fed00000| Complete 
|  46|0x00000000fee00000, 0x00000000fef00000, 0x00000000fef00000|100%| E|CS|TAMS 0x00000000fee00000, 0x00000000fee00000| Complete 
|  47|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| E|CS|TAMS 0x00000000fef00000, 0x00000000fef00000| Complete 
|  48|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| E|CS|TAMS 0x00000000ff000000, 0x00000000ff000000| Complete 
|  49|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| E|CS|TAMS 0x00000000ff100000, 0x00000000ff100000| Complete 
|  50|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| E|CS|TAMS 0x00000000ff200000, 0x00000000ff200000| Complete 
|  51|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| E|CS|TAMS 0x00000000ff300000, 0x00000000ff300000| Complete 
|  52|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000, 0x00000000ff400000| Complete 
|  53|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| E|CS|TAMS 0x00000000ff500000, 0x00000000ff500000| Complete 
|  54|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| E|CS|TAMS 0x00000000ff600000, 0x00000000ff600000| Complete 
|  55|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| E|CS|TAMS 0x00000000ff700000, 0x00000000ff700000| Complete 
|  56|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| E|CS|TAMS 0x00000000ff800000, 0x00000000ff800000| Complete 
|  57|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| E|CS|TAMS 0x00000000ff900000, 0x00000000ff900000| Complete 
|  58|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| E|CS|TAMS 0x00000000ffa00000, 0x00000000ffa00000| Complete 
|  59|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| E|CS|TAMS 0x00000000ffb00000, 0x00000000ffb00000| Complete 
|  60|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000, 0x00000000ffc00000| Complete 
|  61|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000, 0x00000000ffd00000| Complete 
|  62|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000, 0x00000000ffe00000| Complete 
|  63|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000, 0x00000000fff00000| Complete 

Card table byte_map: [0x00000216e2e50000,0x00000216e2e70000] _byte_map_base: 0x00000216e2670000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000216e3099410, (CMBitMap*) 0x00000216e3099450
 Prev Bits: [0x00000216e2e90000, 0x00000216e2f90000)
 Next Bits: [0x00000216f9ba0000, 0x00000216f9ca0000)

Polling page: 0x00000216e0e20000

Metaspace:

Usage:
  Non-class:      8.72 MB used.
      Class:      1.29 MB used.
       Both:     10.00 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       8.81 MB ( 14%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.38 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,      10.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  6.64 MB
       Class:  14.48 MB
        Both:  21.12 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 84.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 163.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 351.
num_chunk_merges: 0.
num_chunk_splits: 230.
num_chunks_enlarged: 185.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=862Kb max_used=862Kb free=119137Kb
 bounds [0x00000216f2670000, 0x00000216f28e0000, 0x00000216f9ba0000]
CodeHeap 'profiled nmethods': size=120000Kb used=4008Kb max_used=4008Kb free=115992Kb
 bounds [0x00000216eaba0000, 0x00000216eaf90000, 0x00000216f20d0000]
CodeHeap 'non-nmethods': size=5760Kb used=1180Kb max_used=1207Kb free=4579Kb
 bounds [0x00000216f20d0000, 0x00000216f2340000, 0x00000216f2670000]
 total_blobs=2523 nmethods=2042 adapters=394
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 777.398 Thread 0x00000216e31273b0 nmethod 2035 0x00000216eaf84010 code [0x00000216eaf841e0, 0x00000216eaf84618]
Event: 777.398 Thread 0x00000216e31273b0 2036       3       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$StateContext::next (11 bytes)
Event: 777.399 Thread 0x00000216e31273b0 nmethod 2036 0x00000216eaf84790 code [0x00000216eaf84920, 0x00000216eaf84a38]
Event: 787.784 Thread 0x00000216e31273b0 2037       3       org.gradle.internal.time.TimeSource$1::currentTimeMillis (4 bytes)
Event: 787.785 Thread 0x00000216e31273b0 nmethod 2037 0x00000216eaf84b10 code [0x00000216eaf84ca0, 0x00000216eaf84dd8]
Event: 790.699 Thread 0x00000216e31273b0 2038       3       org.gradle.internal.logging.events.StyledTextOutputEvent::<init> (24 bytes)
Event: 790.699 Thread 0x00000216e31273b0 nmethod 2038 0x00000216eaf84e90 code [0x00000216eaf85040, 0x00000216eaf85278]
Event: 790.699 Thread 0x00000216e31273b0 2039       3       org.gradle.internal.logging.events.StyledTextOutputEvent::render (55 bytes)
Event: 790.700 Thread 0x00000216e31273b0 nmethod 2039 0x00000216eaf85390 code [0x00000216eaf855c0, 0x00000216eaf86028]
Event: 790.703 Thread 0x00000216e31273b0 2040       3       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput$SeenFromEol::add (18 bytes)
Event: 790.704 Thread 0x00000216e31273b0 nmethod 2040 0x00000216eaf86390 code [0x00000216eaf86520, 0x00000216eaf86658]
Event: 795.186 Thread 0x00000216e31273b0 2041       3       java.util.ArrayList::clear (43 bytes)
Event: 795.187 Thread 0x00000216e31273b0 nmethod 2041 0x00000216eaf86710 code [0x00000216eaf868c0, 0x00000216eaf86c38]
Event: 796.484 Thread 0x00000216e31273b0 2042       3       org.gradle.internal.logging.format.PrettyPrefixedLogHeaderFormatter::format (62 bytes)
Event: 796.484 Thread 0x00000216e31264f0 2044       4       org.gradle.internal.logging.text.AbstractLineChoppingStyledTextOutput::doAppend (43 bytes)
Event: 796.485 Thread 0x00000216e31273b0 nmethod 2042 0x00000216eaf86d90 code [0x00000216eaf87040, 0x00000216eaf882a8]
Event: 796.485 Thread 0x00000216e31273b0 2043       3       org.gradle.internal.logging.format.PrettyPrefixedLogHeaderFormatter::header (42 bytes)
Event: 796.486 Thread 0x00000216e31273b0 nmethod 2043 0x00000216eaf88710 code [0x00000216eaf88940, 0x00000216eaf890e8]
Event: 796.486 Thread 0x00000216e31273b0 2045       3       org.gradle.internal.logging.format.PrettyPrefixedLogHeaderFormatter::status (42 bytes)
Event: 796.487 Thread 0x00000216e31273b0 nmethod 2045 0x00000216eaf89390 code [0x00000216eaf895c0, 0x00000216eaf89d68]

GC Heap History (4 events):
Event: 2.454 GC heap before
{Heap before GC invocations=0 (full 0):
 garbage-first heap   total 65536K, used 13312K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 13 young (13312K), 0 survivors (0K)
 Metaspace       used 1646K, committed 1728K, reserved 1114112K
  class space    used 153K, committed 192K, reserved 1048576K
}
Event: 2.605 GC heap after
{Heap after GC invocations=1 (full 0):
 garbage-first heap   total 65536K, used 1496K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 1646K, committed 1728K, reserved 1114112K
  class space    used 153K, committed 192K, reserved 1048576K
}
Event: 7.284 GC heap before
{Heap before GC invocations=1 (full 0):
 garbage-first heap   total 65536K, used 20952K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 19 young (19456K), 2 survivors (2048K)
 Metaspace       used 2712K, committed 2880K, reserved 1114112K
  class space    used 284K, committed 384K, reserved 1048576K
}
Event: 7.293 GC heap after
{Heap after GC invocations=2 (full 0):
 garbage-first heap   total 65536K, used 9690K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 2712K, committed 2880K, reserved 1114112K
  class space    used 284K, committed 384K, reserved 1048576K
}

Dll operation events (12 events):
Event: 0.147 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.338 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 0.786 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 0.788 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 0.794 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 0.800 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 2.900 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 7.760 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 7.763 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 8.189 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 12.643 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 45.521 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll

Deoptimization events (20 events):
Event: 45.539 Thread 0x00000216e307aa60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000216f26b0258 relative=0x0000000000000ab8
Event: 45.539 Thread 0x00000216e307aa60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000216f26b0258 method=java.util.HashMap.putVal(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/lang/Object; @ 162 c2
Event: 45.539 Thread 0x00000216e307aa60 DEOPT PACKING pc=0x00000216f26b0258 sp=0x000000e0c5efaee0
Event: 45.539 Thread 0x00000216e307aa60 DEOPT UNPACKING pc=0x00000216f21269a3 sp=0x000000e0c5efae18 mode 2
Event: 45.906 Thread 0x00000216e307aa60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000216f26f78a0 relative=0x00000000000004c0
Event: 45.906 Thread 0x00000216e307aa60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000216f26f78a0 method=java.lang.invoke.VarForm.getMemberName(I)Ljava/lang/invoke/MemberName; @ 8 c2
Event: 45.906 Thread 0x00000216e307aa60 DEOPT PACKING pc=0x00000216f26f78a0 sp=0x000000e0c5efce50
Event: 45.906 Thread 0x00000216e307aa60 DEOPT UNPACKING pc=0x00000216f21269a3 sp=0x000000e0c5efcdb0 mode 2
Event: 655.970 Thread 0x00000216e307aa60 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000216f268f314 relative=0x00000000000003f4
Event: 655.975 Thread 0x00000216e307aa60 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000216f268f314 method=java.lang.StringUTF16.compress([CII)[B @ 13 c2
Event: 655.975 Thread 0x00000216e307aa60 DEOPT PACKING pc=0x00000216f268f314 sp=0x000000e0c5efcaf0
Event: 656.002 Thread 0x00000216e307aa60 DEOPT UNPACKING pc=0x00000216f21269a3 sp=0x000000e0c5efca30 mode 2
Event: 658.981 Thread 0x00000216fcba98d0 Uncommon trap: trap_request=0xffffffde fr.pc=0x00000216f27303c8 relative=0x0000000000001708
Event: 658.981 Thread 0x00000216fcba98d0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x00000216f27303c8 method=org.gradle.internal.logging.sink.GroupingProgressLogEventGenerator.onOutput(Lorg/gradle/internal/logging/events/OutputEvent;)V @ 27 c2
Event: 658.981 Thread 0x00000216fcba98d0 DEOPT PACKING pc=0x00000216f27303c8 sp=0x000000e0c74fefc0
Event: 658.982 Thread 0x00000216fcba98d0 DEOPT UNPACKING pc=0x00000216f21269a3 sp=0x000000e0c74fede0 mode 2
Event: 658.990 Thread 0x00000216fcba98d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000216f268c084 relative=0x0000000000000264
Event: 658.990 Thread 0x00000216fcba98d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000216f268c084 method=java.lang.String.getChars(II[CI)V @ 23 c2
Event: 658.990 Thread 0x00000216fcba98d0 DEOPT PACKING pc=0x00000216f268c084 sp=0x000000e0c74fe700
Event: 658.990 Thread 0x00000216fcba98d0 DEOPT UNPACKING pc=0x00000216f21269a3 sp=0x000000e0c74fe6c0 mode 2

Classes loaded (20 events):
Event: 658.761 Loading class jdk/internal/logger/LazyLoggers$LazyLoggerFactories
Event: 658.765 Loading class jdk/internal/logger/LazyLoggers$LazyLoggerFactories done
Event: 658.850 Loading class jdk/internal/logger/LoggerFinderLoader
Event: 658.852 Loading class jdk/internal/logger/LoggerFinderLoader done
Event: 658.870 Loading class jdk/internal/logger/LoggerFinderLoader$TemporaryLoggerFinder
Event: 658.870 Loading class jdk/internal/logger/LoggerFinderLoader$TemporaryLoggerFinder done
Event: 658.877 Loading class sun/util/logging/internal/LoggingProviderImpl$JULWrapper
Event: 658.878 Loading class sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
Event: 658.878 Loading class sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration done
Event: 658.878 Loading class sun/util/logging/internal/LoggingProviderImpl$JULWrapper done
Event: 658.881 Loading class java/io/ObjectInputFilter
Event: 658.881 Loading class java/io/ObjectInputFilter done
Event: 658.883 Loading class java/io/ObjectInputFilter$Config$BuiltinFilterFactory
Event: 658.884 Loading class java/io/ObjectInputFilter$Config$BuiltinFilterFactory done
Event: 658.884 Loading class jdk/internal/access/JavaObjectInputFilterAccess
Event: 658.884 Loading class jdk/internal/access/JavaObjectInputFilterAccess done
Event: 658.887 Loading class java/lang/System$Logger$Level
Event: 658.887 Loading class java/lang/System$Logger$Level done
Event: 658.890 Loading class java/io/Bits
Event: 658.892 Loading class java/io/Bits done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 7.831 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffb5f9b8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000000ffb5f9b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.833 Thread 0x00000216e307aa60 Implicit null exception at 0x00000216f26cf60f to 0x00000216f26cfb80
Event: 7.834 Thread 0x00000216e307aa60 Implicit null exception at 0x00000216f26bd88f to 0x00000216f26bde14
Event: 7.889 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoClassDefFoundError'{0x00000000ffbde518}: org/slf4j/impl/StaticMarkerBinder> (0x00000000ffbde518) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 245]
Event: 7.985 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffa8cc70}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invoker(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ffa8cc70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.987 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffa95dc8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000000ffa95dc8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 7.995 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffaa1ab8}: 'void java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x00000000ffaa1ab8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 8.004 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffab5100}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ffab5100) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 8.024 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffadf6a0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ffadf6a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 8.456 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ff697d68}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ff697d68) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 8.529 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ff5c0468}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ff5c0468) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 9.079 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ff086d20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000ff086d20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 9.419 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fed1e850}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int, int, int)'> (0x00000000fed1e850) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 10.738 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fedffcb0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, int, int, int, int)'> (0x00000000fedffcb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 12.646 Thread 0x00000216e307aa60 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe92c1a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000000fe92c1a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.136 Thread 0x00000216fce7ab00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe190200}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000000fe190200) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.137 Thread 0x00000216fce7ab00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe195b58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, long)'> (0x00000000fe195b58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.141 Thread 0x00000216fce7ab00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe1a06f0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, long, java.lang.Object)'> (0x00000000fe1a06f0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.143 Thread 0x00000216fce7ab00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe1a6d78}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, long)'> (0x00000000fe1a6d78) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 14.144 Thread 0x00000216fce7ab00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000fe1ab068}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, long, java.lang.Object)'> (0x00000000fe1ab068) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 714.065 Executing VM operation: Cleanup
Event: 714.065 Executing VM operation: Cleanup done
Event: 722.151 Executing VM operation: Cleanup
Event: 722.151 Executing VM operation: Cleanup done
Event: 730.227 Executing VM operation: Cleanup
Event: 730.227 Executing VM operation: Cleanup done
Event: 749.385 Executing VM operation: Cleanup
Event: 749.385 Executing VM operation: Cleanup done
Event: 750.386 Executing VM operation: Cleanup
Event: 750.386 Executing VM operation: Cleanup done
Event: 762.486 Executing VM operation: Cleanup
Event: 762.599 Executing VM operation: Cleanup done
Event: 767.642 Executing VM operation: Cleanup
Event: 767.642 Executing VM operation: Cleanup done
Event: 773.695 Executing VM operation: Cleanup
Event: 773.695 Executing VM operation: Cleanup done
Event: 781.707 Executing VM operation: HandshakeAllThreads
Event: 781.707 Executing VM operation: HandshakeAllThreads done
Event: 793.808 Executing VM operation: Cleanup
Event: 793.808 Executing VM operation: Cleanup done

Events (20 events):
Event: 0.479 Thread 0x00000216e31208f0 Thread added: 0x00000216e31208f0
Event: 0.479 Thread 0x00000216e31243d0 Thread added: 0x00000216e31243d0
Event: 0.479 Thread 0x00000216e3124d90 Thread added: 0x00000216e3124d90
Event: 0.479 Thread 0x00000216e3125750 Thread added: 0x00000216e3125750
Event: 0.479 Thread 0x00000216e31264f0 Thread added: 0x00000216e31264f0
Event: 0.549 Thread 0x00000216e31273b0 Thread added: 0x00000216e31273b0
Event: 0.632 Thread 0x00000216fc26de30 Thread added: 0x00000216fc26de30
Event: 0.741 Thread 0x00000216fc2c3130 Thread added: 0x00000216fc2c3130
Event: 0.748 Thread 0x00000216fc2c8ce0 Thread added: 0x00000216fc2c8ce0
Event: 2.904 Thread 0x00000216fc636dc0 Thread added: 0x00000216fc636dc0
Event: 3.305 Thread 0x00000216fc636dc0 Thread exited: 0x00000216fc636dc0
Event: 7.890 Thread 0x00000216fc5f6360 Thread added: 0x00000216fc5f6360
Event: 8.214 Thread 0x00000216fc5f6360 Thread exited: 0x00000216fc5f6360
Event: 8.280 Thread 0x00000216fcba98d0 Thread added: 0x00000216fcba98d0
Event: 13.777 Thread 0x00000216fce7ab00 Thread added: 0x00000216fce7ab00
Event: 14.147 Thread 0x00000216fcf07950 Thread added: 0x00000216fcf07950
Event: 14.150 Thread 0x00000216fd27e560 Thread added: 0x00000216fd27e560
Event: 14.261 Thread 0x00000216fcf07950 Thread exited: 0x00000216fcf07950
Event: 44.504 Thread 0x00000216fce7ab00 Thread exited: 0x00000216fce7ab00
Event: 44.504 Thread 0x00000216fd27e560 Thread exited: 0x00000216fd27e560


Dynamic libraries:
0x00007ff603630000 - 0x00007ff60363e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffacb2f0000 - 0x00007ffacb507000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffac9a70000 - 0x00007ffac9b34000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffac8be0000 - 0x00007ffac8fb1000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffac84d0000 - 0x00007ffac85e1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffaaeea0000 - 0x00007ffaaeeb7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffac9b60000 - 0x00007ffac9d11000 	C:\Windows\System32\USER32.dll
0x00007ffac8ac0000 - 0x00007ffac8ae6000 	C:\Windows\System32\win32u.dll
0x00007ffacb280000 - 0x00007ffacb2a9000 	C:\Windows\System32\GDI32.dll
0x00007ffac85f0000 - 0x00007ffac870b000 	C:\Windows\System32\gdi32full.dll
0x00007ffac8430000 - 0x00007ffac84ca000 	C:\Windows\System32\msvcp_win.dll
0x00007ffaaed30000 - 0x00007ffaaed4d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffab0c00000 - 0x00007ffab0e92000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffacb170000 - 0x00007ffacb217000 	C:\Windows\System32\msvcrt.dll
0x00007ffacb220000 - 0x00007ffacb251000 	C:\Windows\System32\IMM32.DLL
0x00007ffabf040000 - 0x00007ffabf04c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffabdda0000 - 0x00007ffabde2d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffa17d70000 - 0x00007ffa189e0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffac96e0000 - 0x00007ffac9791000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffac9d20000 - 0x00007ffac9dc7000 	C:\Windows\System32\sechost.dll
0x00007ffac8af0000 - 0x00007ffac8b18000 	C:\Windows\System32\bcrypt.dll
0x00007ffacb050000 - 0x00007ffacb164000 	C:\Windows\System32\RPCRT4.dll
0x00007ffac9130000 - 0x00007ffac91a1000 	C:\Windows\System32\WS2_32.dll
0x00007ffac8300000 - 0x00007ffac834d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffabdc00000 - 0x00007ffabdc34000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffabc100000 - 0x00007ffabc10a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffac82e0000 - 0x00007ffac82f3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffac7440000 - 0x00007ffac7458000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffabeaa0000 - 0x00007ffabeaaa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffac2e00000 - 0x00007ffac3032000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffac92c0000 - 0x00007ffac9650000 	C:\Windows\System32\combase.dll
0x00007ffac9990000 - 0x00007ffac9a67000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffaa95d0000 - 0x00007ffaa9602000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffac8a40000 - 0x00007ffac8abb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffaaded0000 - 0x00007ffaadef5000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffaad6b0000 - 0x00007ffaad787000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffaca760000 - 0x00007ffacafe8000 	C:\Windows\System32\SHELL32.dll
0x00007ffac8880000 - 0x00007ffac89bf000 	C:\Windows\System32\wintypes.dll
0x00007ffac6340000 - 0x00007ffac6c4d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffac9020000 - 0x00007ffac912a000 	C:\Windows\System32\SHCORE.dll
0x00007ffacaff0000 - 0x00007ffacb04e000 	C:\Windows\System32\shlwapi.dll
0x00007ffac8360000 - 0x00007ffac838b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffaaeb80000 - 0x00007ffaaeb9a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffabe3f0000 - 0x00007ffabe51c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffac78a0000 - 0x00007ffac790a000 	C:\Windows\system32\mswsock.dll
0x00007ffaae4d0000 - 0x00007ffaae4e6000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffaad690000 - 0x00007ffaad6a8000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffabdd90000 - 0x00007ffabdda0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffab9cf0000 - 0x00007ffab9cfa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffab0ed0000 - 0x00007ffab0edb000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffac97a0000 - 0x00007ffac97a8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffaad1e0000 - 0x00007ffaad207000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ffac7bf0000 - 0x00007ffac7c0b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffac73a0000 - 0x00007ffac73d7000 	C:\Windows\system32\rsaenh.dll
0x00007ffac7940000 - 0x00007ffac7968000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffac7bd0000 - 0x00007ffac7bdc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffac6eb0000 - 0x00007ffac6edd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffac9dd0000 - 0x00007ffac9dd9000 	C:\Windows\System32\NSI.dll
0x00007ffabe730000 - 0x00007ffabe749000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffabe010000 - 0x00007ffabe02f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffac6f20000 - 0x00007ffac7022000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffac11a0000 - 0x00007ffac11a9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffac1180000 - 0x00007ffac118e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffac8710000 - 0x00007ffac8876000 	C:\Windows\System32\CRYPT32.dll
0x00007ffac7d00000 - 0x00007ffac7d2d000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffac7cc0000 - 0x00007ffac7cf7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffabf6d0000 - 0x00007ffabf6d8000 	C:\Windows\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64

VM Arguments:
jvm_args: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew 
java_command: org.gradle.wrapper.GradleWrapperMain app:installDebug -PreactNativeDevServerPort=8081
java_class_path (initial): C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 67108864                                  {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 67108864                                  {product} {command line}
   size_t MaxNewSize                               = 39845888                                  {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 67108864                                  {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 67108864                               {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 0:36 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (467M free)
TotalPageFile size 26992M (AvailPageFile size 2M)
current process WorkingSet (physical memory assigned to process): 32M, peak: 100M
current process commit charge ("private bytes"): 161M, peak: 161M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
