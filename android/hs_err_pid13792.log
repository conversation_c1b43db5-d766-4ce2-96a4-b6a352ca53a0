#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: ChunkPool::allocate
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:79), pid=13792, tid=12616
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew org.gradle.wrapper.GradleWrapperMain app:installDebug -PreactNativeDevServerPort=8081

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Wed May 28 16:17:01 2025 W. Central Africa Standard Time elapsed time: 6.671742 seconds (0d 0h 0m 6s)

---------------  T H R E A D  ---------------

Current thread (0x000001b86ecce090):  JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=12616, stack(0x0000002752200000,0x0000002752300000)]


Current CompileTask:
C1:   6672  299       3       java.util.regex.Pattern::escape (1327 bytes)

Stack: [0x0000002752200000,0x0000002752300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad2ac]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0x11d855]
V  [jvm.dll+0x17b3dd]
V  [jvm.dll+0x17be33]
V  [jvm.dll+0x1366f3]
V  [jvm.dll+0x135fbf]
V  [jvm.dll+0x136208]
V  [jvm.dll+0x135671]
V  [jvm.dll+0x13736d]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001b878e41440, length=12, elements={
0x000001b86ec198a0, 0x000001b86ecaa7f0, 0x000001b86ecab670, 0x000001b86ecc1710,
0x000001b86ecc51f0, 0x000001b86ecc5bb0, 0x000001b86ecc6570, 0x000001b86ecc7310,
0x000001b86ecce090, 0x000001b86ecce6a0, 0x000001b878e5ee80, 0x000001b878e686d0
}

Java Threads: ( => current thread )
  0x000001b86ec198a0 JavaThread "main" [_thread_in_native, id=21680, stack(0x0000002751300000,0x0000002751400000)]
  0x000001b86ecaa7f0 JavaThread "Reference Handler" daemon [_thread_blocked, id=22176, stack(0x0000002751b00000,0x0000002751c00000)]
  0x000001b86ecab670 JavaThread "Finalizer" daemon [_thread_blocked, id=22432, stack(0x0000002751c00000,0x0000002751d00000)]
  0x000001b86ecc1710 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=1548, stack(0x0000002751d00000,0x0000002751e00000)]
  0x000001b86ecc51f0 JavaThread "Attach Listener" daemon [_thread_blocked, id=17672, stack(0x0000002751e00000,0x0000002751f00000)]
  0x000001b86ecc5bb0 JavaThread "Service Thread" daemon [_thread_blocked, id=3952, stack(0x0000002751f00000,0x0000002752000000)]
  0x000001b86ecc6570 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=20096, stack(0x0000002752000000,0x0000002752100000)]
  0x000001b86ecc7310 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=21992, stack(0x0000002752100000,0x0000002752200000)]
=>0x000001b86ecce090 JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=12616, stack(0x0000002752200000,0x0000002752300000)]
  0x000001b86ecce6a0 JavaThread "Sweeper thread" daemon [_thread_blocked, id=9276, stack(0x0000002752300000,0x0000002752400000)]
  0x000001b878e5ee80 JavaThread "Notification Thread" daemon [_thread_blocked, id=13748, stack(0x0000002752400000,0x0000002752500000)]
  0x000001b878e686d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=17788, stack(0x0000002752600000,0x0000002752700000)]

Other Threads:
  0x000001b86eca7450 VMThread "VM Thread" [stack: 0x0000002751a00000,0x0000002751b00000] [id=20604]
  0x000001b878e61770 WatcherThread [stack: 0x0000002752500000,0x0000002752600000] [id=16336]
  0x000001b86ec380c0 GCTaskThread "GC Thread#0" [stack: 0x0000002751500000,0x0000002751600000] [id=19452]
  0x000001b86ec39810 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000002751600000,0x0000002751700000] [id=16792]
  0x000001b86ec3a230 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000002751700000,0x0000002751800000] [id=21380]
  0x000001b86ec87bc0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000002751800000,0x0000002751900000] [id=12576]
  0x000001b86ec885f0 ConcurrentGCThread "G1 Service" [stack: 0x0000002751900000,0x0000002751a00000] [id=18920]

Threads with active compile tasks:
C1 CompilerThread0     6740  299       3       java.util.regex.Pattern::escape (1327 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x00000000fc000000, size: 64 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x000001b80f000000-0x000001b80fbb0000-0x000001b80fbb0000), size 12255232, SharedBaseAddress: 0x000001b80f000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001b810000000-0x000001b850000000, reserved size: 1073741824
Narrow klass base: 0x000001b80f000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 64M
 Heap Initial Capacity: 64M
 Heap Max Capacity: 64M
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 65536K, used 4096K [0x00000000fc000000, 0x0000000100000000)
  region size 1024K, 5 young (5120K), 0 survivors (0K)
 Metaspace       used 1545K, committed 1600K, reserved 1114112K
  class space    used 153K, committed 192K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x00000000fc000000, 0x00000000fc000000, 0x00000000fc100000|  0%| F|  |TAMS 0x00000000fc000000, 0x00000000fc000000| Untracked 
|   1|0x00000000fc100000, 0x00000000fc100000, 0x00000000fc200000|  0%| F|  |TAMS 0x00000000fc100000, 0x00000000fc100000| Untracked 
|   2|0x00000000fc200000, 0x00000000fc200000, 0x00000000fc300000|  0%| F|  |TAMS 0x00000000fc200000, 0x00000000fc200000| Untracked 
|   3|0x00000000fc300000, 0x00000000fc300000, 0x00000000fc400000|  0%| F|  |TAMS 0x00000000fc300000, 0x00000000fc300000| Untracked 
|   4|0x00000000fc400000, 0x00000000fc400000, 0x00000000fc500000|  0%| F|  |TAMS 0x00000000fc400000, 0x00000000fc400000| Untracked 
|   5|0x00000000fc500000, 0x00000000fc500000, 0x00000000fc600000|  0%| F|  |TAMS 0x00000000fc500000, 0x00000000fc500000| Untracked 
|   6|0x00000000fc600000, 0x00000000fc600000, 0x00000000fc700000|  0%| F|  |TAMS 0x00000000fc600000, 0x00000000fc600000| Untracked 
|   7|0x00000000fc700000, 0x00000000fc700000, 0x00000000fc800000|  0%| F|  |TAMS 0x00000000fc700000, 0x00000000fc700000| Untracked 
|   8|0x00000000fc800000, 0x00000000fc800000, 0x00000000fc900000|  0%| F|  |TAMS 0x00000000fc800000, 0x00000000fc800000| Untracked 
|   9|0x00000000fc900000, 0x00000000fc900000, 0x00000000fca00000|  0%| F|  |TAMS 0x00000000fc900000, 0x00000000fc900000| Untracked 
|  10|0x00000000fca00000, 0x00000000fca00000, 0x00000000fcb00000|  0%| F|  |TAMS 0x00000000fca00000, 0x00000000fca00000| Untracked 
|  11|0x00000000fcb00000, 0x00000000fcb00000, 0x00000000fcc00000|  0%| F|  |TAMS 0x00000000fcb00000, 0x00000000fcb00000| Untracked 
|  12|0x00000000fcc00000, 0x00000000fcc00000, 0x00000000fcd00000|  0%| F|  |TAMS 0x00000000fcc00000, 0x00000000fcc00000| Untracked 
|  13|0x00000000fcd00000, 0x00000000fcd00000, 0x00000000fce00000|  0%| F|  |TAMS 0x00000000fcd00000, 0x00000000fcd00000| Untracked 
|  14|0x00000000fce00000, 0x00000000fce00000, 0x00000000fcf00000|  0%| F|  |TAMS 0x00000000fce00000, 0x00000000fce00000| Untracked 
|  15|0x00000000fcf00000, 0x00000000fcf00000, 0x00000000fd000000|  0%| F|  |TAMS 0x00000000fcf00000, 0x00000000fcf00000| Untracked 
|  16|0x00000000fd000000, 0x00000000fd000000, 0x00000000fd100000|  0%| F|  |TAMS 0x00000000fd000000, 0x00000000fd000000| Untracked 
|  17|0x00000000fd100000, 0x00000000fd100000, 0x00000000fd200000|  0%| F|  |TAMS 0x00000000fd100000, 0x00000000fd100000| Untracked 
|  18|0x00000000fd200000, 0x00000000fd200000, 0x00000000fd300000|  0%| F|  |TAMS 0x00000000fd200000, 0x00000000fd200000| Untracked 
|  19|0x00000000fd300000, 0x00000000fd300000, 0x00000000fd400000|  0%| F|  |TAMS 0x00000000fd300000, 0x00000000fd300000| Untracked 
|  20|0x00000000fd400000, 0x00000000fd400000, 0x00000000fd500000|  0%| F|  |TAMS 0x00000000fd400000, 0x00000000fd400000| Untracked 
|  21|0x00000000fd500000, 0x00000000fd500000, 0x00000000fd600000|  0%| F|  |TAMS 0x00000000fd500000, 0x00000000fd500000| Untracked 
|  22|0x00000000fd600000, 0x00000000fd600000, 0x00000000fd700000|  0%| F|  |TAMS 0x00000000fd600000, 0x00000000fd600000| Untracked 
|  23|0x00000000fd700000, 0x00000000fd700000, 0x00000000fd800000|  0%| F|  |TAMS 0x00000000fd700000, 0x00000000fd700000| Untracked 
|  24|0x00000000fd800000, 0x00000000fd800000, 0x00000000fd900000|  0%| F|  |TAMS 0x00000000fd800000, 0x00000000fd800000| Untracked 
|  25|0x00000000fd900000, 0x00000000fd900000, 0x00000000fda00000|  0%| F|  |TAMS 0x00000000fd900000, 0x00000000fd900000| Untracked 
|  26|0x00000000fda00000, 0x00000000fda00000, 0x00000000fdb00000|  0%| F|  |TAMS 0x00000000fda00000, 0x00000000fda00000| Untracked 
|  27|0x00000000fdb00000, 0x00000000fdb00000, 0x00000000fdc00000|  0%| F|  |TAMS 0x00000000fdb00000, 0x00000000fdb00000| Untracked 
|  28|0x00000000fdc00000, 0x00000000fdc00000, 0x00000000fdd00000|  0%| F|  |TAMS 0x00000000fdc00000, 0x00000000fdc00000| Untracked 
|  29|0x00000000fdd00000, 0x00000000fdd00000, 0x00000000fde00000|  0%| F|  |TAMS 0x00000000fdd00000, 0x00000000fdd00000| Untracked 
|  30|0x00000000fde00000, 0x00000000fde00000, 0x00000000fdf00000|  0%| F|  |TAMS 0x00000000fde00000, 0x00000000fde00000| Untracked 
|  31|0x00000000fdf00000, 0x00000000fdf00000, 0x00000000fe000000|  0%| F|  |TAMS 0x00000000fdf00000, 0x00000000fdf00000| Untracked 
|  32|0x00000000fe000000, 0x00000000fe000000, 0x00000000fe100000|  0%| F|  |TAMS 0x00000000fe000000, 0x00000000fe000000| Untracked 
|  33|0x00000000fe100000, 0x00000000fe100000, 0x00000000fe200000|  0%| F|  |TAMS 0x00000000fe100000, 0x00000000fe100000| Untracked 
|  34|0x00000000fe200000, 0x00000000fe200000, 0x00000000fe300000|  0%| F|  |TAMS 0x00000000fe200000, 0x00000000fe200000| Untracked 
|  35|0x00000000fe300000, 0x00000000fe300000, 0x00000000fe400000|  0%| F|  |TAMS 0x00000000fe300000, 0x00000000fe300000| Untracked 
|  36|0x00000000fe400000, 0x00000000fe400000, 0x00000000fe500000|  0%| F|  |TAMS 0x00000000fe400000, 0x00000000fe400000| Untracked 
|  37|0x00000000fe500000, 0x00000000fe500000, 0x00000000fe600000|  0%| F|  |TAMS 0x00000000fe500000, 0x00000000fe500000| Untracked 
|  38|0x00000000fe600000, 0x00000000fe600000, 0x00000000fe700000|  0%| F|  |TAMS 0x00000000fe600000, 0x00000000fe600000| Untracked 
|  39|0x00000000fe700000, 0x00000000fe700000, 0x00000000fe800000|  0%| F|  |TAMS 0x00000000fe700000, 0x00000000fe700000| Untracked 
|  40|0x00000000fe800000, 0x00000000fe800000, 0x00000000fe900000|  0%| F|  |TAMS 0x00000000fe800000, 0x00000000fe800000| Untracked 
|  41|0x00000000fe900000, 0x00000000fe900000, 0x00000000fea00000|  0%| F|  |TAMS 0x00000000fe900000, 0x00000000fe900000| Untracked 
|  42|0x00000000fea00000, 0x00000000fea00000, 0x00000000feb00000|  0%| F|  |TAMS 0x00000000fea00000, 0x00000000fea00000| Untracked 
|  43|0x00000000feb00000, 0x00000000feb00000, 0x00000000fec00000|  0%| F|  |TAMS 0x00000000feb00000, 0x00000000feb00000| Untracked 
|  44|0x00000000fec00000, 0x00000000fec00000, 0x00000000fed00000|  0%| F|  |TAMS 0x00000000fec00000, 0x00000000fec00000| Untracked 
|  45|0x00000000fed00000, 0x00000000fed00000, 0x00000000fee00000|  0%| F|  |TAMS 0x00000000fed00000, 0x00000000fed00000| Untracked 
|  46|0x00000000fee00000, 0x00000000fee00000, 0x00000000fef00000|  0%| F|  |TAMS 0x00000000fee00000, 0x00000000fee00000| Untracked 
|  47|0x00000000fef00000, 0x00000000fef00000, 0x00000000ff000000|  0%| F|  |TAMS 0x00000000fef00000, 0x00000000fef00000| Untracked 
|  48|0x00000000ff000000, 0x00000000ff000000, 0x00000000ff100000|  0%| F|  |TAMS 0x00000000ff000000, 0x00000000ff000000| Untracked 
|  49|0x00000000ff100000, 0x00000000ff100000, 0x00000000ff200000|  0%| F|  |TAMS 0x00000000ff100000, 0x00000000ff100000| Untracked 
|  50|0x00000000ff200000, 0x00000000ff200000, 0x00000000ff300000|  0%| F|  |TAMS 0x00000000ff200000, 0x00000000ff200000| Untracked 
|  51|0x00000000ff300000, 0x00000000ff300000, 0x00000000ff400000|  0%| F|  |TAMS 0x00000000ff300000, 0x00000000ff300000| Untracked 
|  52|0x00000000ff400000, 0x00000000ff400000, 0x00000000ff500000|  0%| F|  |TAMS 0x00000000ff400000, 0x00000000ff400000| Untracked 
|  53|0x00000000ff500000, 0x00000000ff500000, 0x00000000ff600000|  0%| F|  |TAMS 0x00000000ff500000, 0x00000000ff500000| Untracked 
|  54|0x00000000ff600000, 0x00000000ff600000, 0x00000000ff700000|  0%| F|  |TAMS 0x00000000ff600000, 0x00000000ff600000| Untracked 
|  55|0x00000000ff700000, 0x00000000ff700000, 0x00000000ff800000|  0%| F|  |TAMS 0x00000000ff700000, 0x00000000ff700000| Untracked 
|  56|0x00000000ff800000, 0x00000000ff800000, 0x00000000ff900000|  0%| F|  |TAMS 0x00000000ff800000, 0x00000000ff800000| Untracked 
|  57|0x00000000ff900000, 0x00000000ff900000, 0x00000000ffa00000|  0%| F|  |TAMS 0x00000000ff900000, 0x00000000ff900000| Untracked 
|  58|0x00000000ffa00000, 0x00000000ffa00000, 0x00000000ffb00000|  0%| F|  |TAMS 0x00000000ffa00000, 0x00000000ffa00000| Untracked 
|  59|0x00000000ffb00000, 0x00000000ffb43140, 0x00000000ffc00000| 26%| E|  |TAMS 0x00000000ffb00000, 0x00000000ffb00000| Complete 
|  60|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000, 0x00000000ffc00000| Complete 
|  61|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| E|CS|TAMS 0x00000000ffd00000, 0x00000000ffd00000| Complete 
|  62|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000, 0x00000000ffe00000| Complete 
|  63|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| E|CS|TAMS 0x00000000fff00000, 0x00000000fff00000| Complete 

Card table byte_map: [0x000001b86ea40000,0x000001b86ea60000] _byte_map_base: 0x000001b86e260000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001b86ec386e0, (CMBitMap*) 0x000001b86ec38720
 Prev Bits: [0x000001b86ea80000, 0x000001b86eb80000)
 Next Bits: [0x000001b876740000, 0x000001b876840000)

Polling page: 0x000001b86e3d0000

Metaspace:

Usage:
  Non-class:      1.36 MB used.
      Class:    153.64 KB used.
       Both:      1.51 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       1.38 MB (  2%) committed,  1 nodes.
      Class space:        1.00 GB reserved,     192.00 KB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       1.56 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  11.75 MB
       Class:  15.69 MB
        Both:  27.45 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 12.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 25.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 39.
num_chunk_merges: 0.
num_chunk_splits: 18.
num_chunks_enlarged: 13.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=89Kb max_used=89Kb free=119911Kb
 bounds [0x000001b807ad0000, 0x000001b807d40000, 0x000001b80f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=429Kb max_used=429Kb free=119570Kb
 bounds [0x000001b800000000, 0x000001b800270000, 0x000001b807530000]
CodeHeap 'non-nmethods': size=5760Kb used=1118Kb max_used=1133Kb free=4641Kb
 bounds [0x000001b807530000, 0x000001b8077a0000, 0x000001b807ad0000]
 total_blobs=708 nmethods=308 adapters=313
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 4.333 Thread 0x000001b86ecce090 nmethod 290 0x000001b800069a10 code [0x000001b800069be0, 0x000001b800069f18]
Event: 4.334 Thread 0x000001b86ecce090  291       3       java.util.zip.ZipUtils::CENLEN (9 bytes)
Event: 4.334 Thread 0x000001b86ecce090 nmethod 291 0x000001b80006a010 code [0x000001b80006a1c0, 0x000001b80006a498]
Event: 4.334 Thread 0x000001b86ecc7310  292       4       java.lang.AbstractStringBuilder::ensureCapacityInternal (39 bytes)
Event: 4.335 Thread 0x000001b86ecce090  293       3       jdk.internal.util.Preconditions::checkIndex (18 bytes)
Event: 4.335 Thread 0x000001b86ecce090 nmethod 293 0x000001b80006a610 code [0x000001b80006a7c0, 0x000001b80006a968]
Event: 4.336 Thread 0x000001b86ecce090  294       3       java.lang.Number::<init> (5 bytes)
Event: 4.336 Thread 0x000001b86ecce090 nmethod 294 0x000001b80006aa10 code [0x000001b80006aba0, 0x000001b80006acf8]
Event: 4.338 Thread 0x000001b86ecce090  297       3       java.io.File::<init> (78 bytes)
Event: 4.339 Thread 0x000001b86ecce090 nmethod 297 0x000001b80006ad90 code [0x000001b80006af40, 0x000001b80006b1c8]
Event: 4.339 Thread 0x000001b86ecce090  298       3       java.nio.DirectLongBufferU::ix (10 bytes)
Event: 4.340 Thread 0x000001b86ecce090 nmethod 298 0x000001b80006b310 code [0x000001b80006b4a0, 0x000001b80006b5b8]
Event: 4.340 Thread 0x000001b86ecc7310 nmethod 292 0x000001b807ae2e10 code [0x000001b807ae2fa0, 0x000001b807ae3448]
Event: 4.340 Thread 0x000001b86ecc7310  296       4       java.lang.StringUTF16::compress (50 bytes)
Event: 4.340 Thread 0x000001b86ecce090  299       3       java.util.regex.Pattern::escape (1327 bytes)
Event: 4.349 Thread 0x000001b86ecc7310 nmethod 296 0x000001b807ae3d90 code [0x000001b807ae3f00, 0x000001b807ae42d8]
Event: 4.349 Thread 0x000001b86ecc7310  295 %     4       java.lang.StringLatin1::inflate @ 3 (34 bytes)
Event: 4.356 Thread 0x000001b86ecc7310 nmethod 295% 0x000001b807ae4410 code [0x000001b807ae45a0, 0x000001b807ae4918]
Event: 4.356 Thread 0x000001b86ecc7310  307       4       java.lang.StringLatin1::lastIndexOf (40 bytes)
Event: 4.359 Thread 0x000001b86ecc7310 nmethod 307 0x000001b807ae4a90 code [0x000001b807ae4c00, 0x000001b807ae4df8]

GC Heap History (0 events):
No events

Dll operation events (6 events):
Event: 0.091 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.161 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 1.038 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 1.042 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 1.052 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 1.061 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll

Deoptimization events (12 events):
Event: 2.055 Thread 0x000001b86ec198a0 DEOPT PACKING pc=0x000001b80000db67 sp=0x00000027513fd9d0
Event: 2.055 Thread 0x000001b86ec198a0 DEOPT UNPACKING pc=0x000001b807587143 sp=0x00000027513fcdf0 mode 0
Event: 4.338 Thread 0x000001b86ec198a0 DEOPT PACKING pc=0x000001b80002407e sp=0x00000027513fe560
Event: 4.338 Thread 0x000001b86ec198a0 DEOPT UNPACKING pc=0x000001b807587143 sp=0x00000027513fd9d0 mode 0
Event: 4.338 Thread 0x000001b86ec198a0 DEOPT PACKING pc=0x000001b80002407e sp=0x00000027513fe560
Event: 4.338 Thread 0x000001b86ec198a0 DEOPT UNPACKING pc=0x000001b807587143 sp=0x00000027513fd9d0 mode 0
Event: 4.342 Thread 0x000001b86ec198a0 DEOPT PACKING pc=0x000001b80002407e sp=0x00000027513fe570
Event: 4.342 Thread 0x000001b86ec198a0 DEOPT UNPACKING pc=0x000001b807587143 sp=0x00000027513fd9e0 mode 0
Event: 4.342 Thread 0x000001b86ec198a0 DEOPT PACKING pc=0x000001b80002407e sp=0x00000027513fe570
Event: 4.342 Thread 0x000001b86ec198a0 DEOPT UNPACKING pc=0x000001b807587143 sp=0x00000027513fd9e0 mode 0
Event: 4.342 Thread 0x000001b86ec198a0 DEOPT PACKING pc=0x000001b80002407e sp=0x00000027513fe570
Event: 4.342 Thread 0x000001b86ec198a0 DEOPT UNPACKING pc=0x000001b807587143 sp=0x00000027513fd9e0 mode 0

Classes loaded (20 events):
Event: 2.031 Loading class sun/net/www/MessageHeader
Event: 2.031 Loading class sun/net/www/MessageHeader done
Event: 2.031 Loading class java/io/FilePermission$1
Event: 2.031 Loading class jdk/internal/access/JavaIOFilePermissionAccess
Event: 2.031 Loading class jdk/internal/access/JavaIOFilePermissionAccess done
Event: 2.031 Loading class java/io/FilePermission$1 done
Event: 2.032 Loading class java/io/FilePermissionCollection
Event: 2.032 Loading class java/io/FilePermissionCollection done
Event: 2.032 Loading class java/util/Vector
Event: 2.032 Loading class java/util/Vector done
Event: 2.032 Loading class java/io/FilePermissionCollection$1
Event: 2.033 Loading class java/io/FilePermissionCollection$1 done
Event: 2.056 Loading class java/net/MalformedURLException
Event: 2.056 Loading class java/net/MalformedURLException done
Event: 2.059 Loading class java/io/FileFilter
Event: 2.059 Loading class java/io/FileFilter done
Event: 4.315 Loading class java/util/AbstractList$Itr
Event: 4.315 Loading class java/util/AbstractList$Itr done
Event: 4.347 Loading class java/lang/invoke/DirectMethodHandle$Special
Event: 4.347 Loading class java/lang/invoke/DirectMethodHandle$Special done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (1 events):
Event: 2.049 Thread 0x000001b86ec198a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000000ffdeacb8}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object)'> (0x00000000ffdeacb8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (8 events):
Event: 1.290 Executing VM operation: Cleanup
Event: 1.290 Executing VM operation: Cleanup done
Event: 2.293 Executing VM operation: Cleanup
Event: 2.294 Executing VM operation: Cleanup done
Event: 4.318 Executing VM operation: Cleanup
Event: 4.318 Executing VM operation: Cleanup done
Event: 5.333 Executing VM operation: Cleanup
Event: 5.333 Executing VM operation: Cleanup done

Events (12 events):
Event: 0.162 Thread 0x000001b86ec198a0 Thread added: 0x000001b86ec198a0
Event: 0.282 Thread 0x000001b86ecaa7f0 Thread added: 0x000001b86ecaa7f0
Event: 0.282 Thread 0x000001b86ecab670 Thread added: 0x000001b86ecab670
Event: 0.420 Thread 0x000001b86ecc1710 Thread added: 0x000001b86ecc1710
Event: 0.420 Thread 0x000001b86ecc51f0 Thread added: 0x000001b86ecc51f0
Event: 0.420 Thread 0x000001b86ecc5bb0 Thread added: 0x000001b86ecc5bb0
Event: 0.420 Thread 0x000001b86ecc6570 Thread added: 0x000001b86ecc6570
Event: 0.421 Thread 0x000001b86ecc7310 Thread added: 0x000001b86ecc7310
Event: 0.536 Thread 0x000001b86ecce090 Thread added: 0x000001b86ecce090
Event: 0.598 Thread 0x000001b86ecce6a0 Thread added: 0x000001b86ecce6a0
Event: 0.686 Thread 0x000001b878e5ee80 Thread added: 0x000001b878e5ee80
Event: 0.768 Thread 0x000001b878e686d0 Thread added: 0x000001b878e686d0


Dynamic libraries:
0x00007ff6b42c0000 - 0x00007ff6b42ce000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007fff8b530000 - 0x00007fff8b747000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007fff89430000 - 0x00007fff894f4000 	C:\Windows\System32\KERNEL32.DLL
0x00007fff88df0000 - 0x00007fff891c1000 	C:\Windows\System32\KERNELBASE.dll
0x00007fff88cd0000 - 0x00007fff88de1000 	C:\Windows\System32\ucrtbase.dll
0x00007fff6c8d0000 - 0x00007fff6c8e7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007fff89fe0000 - 0x00007fff8a191000 	C:\Windows\System32\USER32.dll
0x00007fff891d0000 - 0x00007fff891f6000 	C:\Windows\System32\win32u.dll
0x00007fff89510000 - 0x00007fff89539000 	C:\Windows\System32\GDI32.dll
0x00007fff88bb0000 - 0x00007fff88ccb000 	C:\Windows\System32\gdi32full.dll
0x00007fff88670000 - 0x00007fff8870a000 	C:\Windows\System32\msvcp_win.dll
0x00007fff6c8b0000 - 0x00007fff6c8cd000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007fff716a0000 - 0x00007fff71932000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007fff8a300000 - 0x00007fff8a3a7000 	C:\Windows\System32\msvcrt.dll
0x00007fff8a2c0000 - 0x00007fff8a2f1000 	C:\Windows\System32\IMM32.DLL
0x00007fff7f6c0000 - 0x00007fff7f6cc000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffef9c50000 - 0x00007ffef9cdd000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffeb6fd0000 - 0x00007ffeb7c40000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007fff89750000 - 0x00007fff89801000 	C:\Windows\System32\ADVAPI32.dll
0x00007fff89540000 - 0x00007fff895e7000 	C:\Windows\System32\sechost.dll
0x00007fff888d0000 - 0x00007fff888f8000 	C:\Windows\System32\bcrypt.dll
0x00007fff8a640000 - 0x00007fff8a754000 	C:\Windows\System32\RPCRT4.dll
0x00007fff89200000 - 0x00007fff89271000 	C:\Windows\System32\WS2_32.dll
0x00007fff88540000 - 0x00007fff8858d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007fff7e3f0000 - 0x00007fff7e424000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007fff75030000 - 0x00007fff7503a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007fff88520000 - 0x00007fff88533000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007fff87680000 - 0x00007fff87698000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fff6e840000 - 0x00007fff6e84a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007fff83040000 - 0x00007fff83272000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fff8a8d0000 - 0x00007fff8ac60000 	C:\Windows\System32\combase.dll
0x00007fff8a1e0000 - 0x00007fff8a2b7000 	C:\Windows\System32\OLEAUT32.dll
0x00007fff69820000 - 0x00007fff69852000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007fff887d0000 - 0x00007fff8884b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fff5e430000 - 0x00007fff5e455000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffef7f50000 - 0x00007ffef8027000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007fff8ac60000 - 0x00007fff8b4e8000 	C:\Windows\System32\SHELL32.dll
0x00007fff88900000 - 0x00007fff88a3f000 	C:\Windows\System32\wintypes.dll
0x00007fff86580000 - 0x00007fff86e8d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007fff8a7c0000 - 0x00007fff8a8ca000 	C:\Windows\System32\SHCORE.dll
0x00007fff8a5e0000 - 0x00007fff8a63e000 	C:\Windows\System32\shlwapi.dll
0x00007fff885a0000 - 0x00007fff885cb000 	C:\Windows\SYSTEM32\profapi.dll
0x00007fff67f80000 - 0x00007fff67f9a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007fff7d290000 - 0x00007fff7d3bc000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007fff87ae0000 - 0x00007fff87b4a000 	C:\Windows\system32\mswsock.dll
0x00007fff59820000 - 0x00007fff59836000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007fff59570000 - 0x00007fff59588000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007fff6e7b0000 - 0x00007fff6e7c0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server

VM Arguments:
jvm_args: -Xmx64m -Xms64m -Dorg.gradle.appname=gradlew 
java_command: org.gradle.wrapper.GradleWrapperMain app:installDebug -PreactNativeDevServerPort=8081
java_class_path (initial): C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 67108864                                  {product} {command line}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 67108864                                  {product} {command line}
   size_t MaxNewSize                               = 39845888                                  {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 67108864                                  {product} {command line}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 67108864                               {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 4:29 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (533M free)
TotalPageFile size 29072M (AvailPageFile size 14M)
current process WorkingSet (physical memory assigned to process): 41M, peak: 41M
current process commit charge ("private bytes"): 137M, peak: 137M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
