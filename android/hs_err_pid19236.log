#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1114272 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=19236, tid=17864
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Mon May  5 19:41:56 2025 W. Central Africa Standard Time elapsed time: 781.836113 seconds (0d 0h 13m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000002376d49f6a0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=17864, stack(0x000000eb3f000000,0x000000eb3f100000)]


Current CompileTask:
C2: 781837 22674       4       org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ArtifactBackedResolvedVariant$SingleArtifactSet::visit (8 bytes)

Stack: [0x000000eb3f000000,0x000000eb3f100000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad0a4]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0x36a44e]
V  [jvm.dll+0x334b83]
V  [jvm.dll+0x33401a]
V  [jvm.dll+0x21a538]
V  [jvm.dll+0x21996f]
V  [jvm.dll+0x1a53e6]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002377fe27a90, length=90, elements={
0x00000237460ad3d0, 0x000002376d486160, 0x000002376d486fe0, 0x000002376d497830,
0x000002376d499560, 0x000002376d49bf30, 0x000002376d49c8f0, 0x000002376d49f6a0,
0x000002376d4a18e0, 0x000002376d4fea30, 0x000002376d59f9d0, 0x000002376d606390,
0x000002377210f530, 0x00000237722d06b0, 0x000002376d748800, 0x0000023772a863d0,
0x000002377273e440, 0x000002376d50b380, 0x000002376d509010, 0x000002376d508b00,
0x000002376d5080e0, 0x000002376d509f40, 0x000002376d509520, 0x000002376d50b890,
0x000002376d50a450, 0x000002376d509a30, 0x000002376d50ae70, 0x00000237740a18a0,
0x00000237740a1db0, 0x00000237740a4b40, 0x00000237740a4630, 0x00000237740a31f0,
0x0000023774d4d9b0, 0x0000023774d534d0, 0x0000023774d52ab0, 0x0000023774d4d4a0,
0x0000023774d4dec0, 0x0000023774d51160, 0x0000023774d4c570, 0x0000023774d4e8e0,
0x0000023774d4ca80, 0x0000023774d4edf0, 0x0000023774d4f300, 0x0000023774d50740,
0x0000023774d4f810, 0x0000023774d51b80, 0x0000023774d4fd20, 0x0000023774d4cf90,
0x0000023774d50230, 0x0000023774d50c50, 0x0000023774d52090, 0x0000023774d525a0,
0x0000023774f26f90, 0x0000023774f26a80, 0x0000023774f279b0, 0x0000023774f288e0,
0x0000023774f28df0, 0x0000023774f26060, 0x000002377563d0b0, 0x0000023775638ee0,
0x000002377563a830, 0x000002377563ad40, 0x00000237756389d0, 0x000002377563ea00,
0x000002377563dad0, 0x000002377563c690, 0x000002377563e4f0, 0x0000023775639e10,
0x000002377563ef10, 0x000002377563cba0, 0x000002377563dfe0, 0x000002377563f420,
0x000002377563b250, 0x000002377563f930, 0x000002377563b760, 0x000002377563bc70,
0x00000237756384c0, 0x000002377563a320, 0x0000023774e67fe0, 0x0000023774e670b0,
0x0000023774e65c70, 0x0000023774e65760, 0x0000023774e62ee0, 0x0000023774e64d40,
0x0000023774e675c0, 0x0000023774e633f0, 0x00000237766a4230, 0x00000237766a3d20,
0x000002377eedb0c0, 0x000002377eedc500
}

Java Threads: ( => current thread )
  0x00000237460ad3d0 JavaThread "main" [_thread_blocked, id=18640, stack(0x000000eb3e200000,0x000000eb3e300000)]
  0x000002376d486160 JavaThread "Reference Handler" daemon [_thread_blocked, id=18844, stack(0x000000eb3ea00000,0x000000eb3eb00000)]
  0x000002376d486fe0 JavaThread "Finalizer" daemon [_thread_blocked, id=18864, stack(0x000000eb3eb00000,0x000000eb3ec00000)]
  0x000002376d497830 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=18784, stack(0x000000eb3ec00000,0x000000eb3ed00000)]
  0x000002376d499560 JavaThread "Attach Listener" daemon [_thread_blocked, id=4624, stack(0x000000eb3ed00000,0x000000eb3ee00000)]
  0x000002376d49bf30 JavaThread "Service Thread" daemon [_thread_blocked, id=18812, stack(0x000000eb3ee00000,0x000000eb3ef00000)]
  0x000002376d49c8f0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=18592, stack(0x000000eb3ef00000,0x000000eb3f000000)]
=>0x000002376d49f6a0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=17864, stack(0x000000eb3f000000,0x000000eb3f100000)]
  0x000002376d4a18e0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=3792, stack(0x000000eb3f100000,0x000000eb3f200000)]
  0x000002376d4fea30 JavaThread "Sweeper thread" daemon [_thread_blocked, id=11824, stack(0x000000eb3f200000,0x000000eb3f300000)]
  0x000002376d59f9d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=17944, stack(0x000000eb3f300000,0x000000eb3f400000)]
  0x000002376d606390 JavaThread "Notification Thread" daemon [_thread_blocked, id=19352, stack(0x000000eb3f400000,0x000000eb3f500000)]
  0x000002377210f530 JavaThread "Daemon health stats" [_thread_blocked, id=20340, stack(0x000000eb3f900000,0x000000eb3fa00000)]
  0x00000237722d06b0 JavaThread "Incoming local TCP Connector on port 50743" [_thread_in_native, id=17756, stack(0x000000eb3fa00000,0x000000eb3fb00000)]
  0x000002376d748800 JavaThread "Daemon periodic checks" [_thread_blocked, id=19600, stack(0x000000eb3fb00000,0x000000eb3fc00000)]
  0x0000023772a863d0 JavaThread "Daemon" [_thread_blocked, id=19592, stack(0x000000eb3fc00000,0x000000eb3fd00000)]
  0x000002377273e440 JavaThread "Handler for socket connection from /127.0.0.1:50743 to /127.0.0.1:50744" [_thread_in_native, id=19552, stack(0x000000eb3fd00000,0x000000eb3fe00000)]
  0x000002376d50b380 JavaThread "Cancel handler" [_thread_blocked, id=19612, stack(0x000000eb3fe00000,0x000000eb3ff00000)]
  0x000002376d509010 JavaThread "Daemon worker" [_thread_blocked, id=19724, stack(0x000000eb3ff00000,0x000000eb40000000)]
  0x000002376d508b00 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50743 to /127.0.0.1:50744" [_thread_blocked, id=19788, stack(0x000000eb40000000,0x000000eb40100000)]
  0x000002376d5080e0 JavaThread "Stdin handler" [_thread_blocked, id=17652, stack(0x000000eb40100000,0x000000eb40200000)]
  0x000002376d509f40 JavaThread "Daemon client event forwarder" [_thread_blocked, id=19768, stack(0x000000eb40200000,0x000000eb40300000)]
  0x000002376d509520 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=19976, stack(0x000000eb40300000,0x000000eb40400000)]
  0x000002376d50b890 JavaThread "File lock request listener" [_thread_in_native, id=19984, stack(0x000000eb40400000,0x000000eb40500000)]
  0x000002376d50a450 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=19932, stack(0x000000eb40500000,0x000000eb40600000)]
  0x000002376d509a30 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=19216, stack(0x000000eb40700000,0x000000eb40800000)]
  0x000002376d50ae70 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=18960, stack(0x000000eb40800000,0x000000eb40900000)]
  0x00000237740a18a0 JavaThread "File watcher server" daemon [_thread_in_native, id=19140, stack(0x000000eb40900000,0x000000eb40a00000)]
  0x00000237740a1db0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=11988, stack(0x000000eb40a00000,0x000000eb40b00000)]
  0x00000237740a4b40 JavaThread "jar transforms" [_thread_blocked, id=6712, stack(0x000000eb40b00000,0x000000eb40c00000)]
  0x00000237740a4630 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=4432, stack(0x000000eb40e00000,0x000000eb40f00000)]
  0x00000237740a31f0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=4652, stack(0x000000eb40f00000,0x000000eb41000000)]
  0x0000023774d4d9b0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=13160, stack(0x000000eb41000000,0x000000eb41100000)]
  0x0000023774d534d0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=9208, stack(0x000000eb41100000,0x000000eb41200000)]
  0x0000023774d52ab0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=6920, stack(0x000000eb3e100000,0x000000eb3e200000)]
  0x0000023774d4d4a0 JavaThread "Unconstrained build operations" [_thread_blocked, id=20348, stack(0x000000eb3e300000,0x000000eb3e400000)]
  0x0000023774d4dec0 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=1392, stack(0x000000eb41200000,0x000000eb41300000)]
  0x0000023774d51160 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=3144, stack(0x000000eb41300000,0x000000eb41400000)]
  0x0000023774d4c570 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=18448, stack(0x000000eb41400000,0x000000eb41500000)]
  0x0000023774d4e8e0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=19784, stack(0x000000eb41500000,0x000000eb41600000)]
  0x0000023774d4ca80 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=19540, stack(0x000000eb41600000,0x000000eb41700000)]
  0x0000023774d4edf0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=9440, stack(0x000000eb41800000,0x000000eb41900000)]
  0x0000023774d4f300 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=16296, stack(0x000000eb41900000,0x000000eb41a00000)]
  0x0000023774d50740 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=14172, stack(0x000000eb41a00000,0x000000eb41b00000)]
  0x0000023774d4f810 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=11340, stack(0x000000eb41700000,0x000000eb41800000)]
  0x0000023774d51b80 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=10220, stack(0x000000eb41c00000,0x000000eb41d00000)]
  0x0000023774d4fd20 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=14360, stack(0x000000eb41d00000,0x000000eb41e00000)]
  0x0000023774d4cf90 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=5816, stack(0x000000eb41e00000,0x000000eb41f00000)]
  0x0000023774d50230 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=3252, stack(0x000000eb41f00000,0x000000eb42000000)]
  0x0000023774d50c50 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=17344, stack(0x000000eb42000000,0x000000eb42100000)]
  0x0000023774d52090 JavaThread "Memory manager" [_thread_blocked, id=12944, stack(0x000000eb42100000,0x000000eb42200000)]
  0x0000023774d525a0 JavaThread "build event listener" [_thread_blocked, id=18280, stack(0x000000eb42200000,0x000000eb42300000)]
  0x0000023774f26f90 JavaThread "Execution worker" [_thread_blocked, id=20544, stack(0x000000eb42500000,0x000000eb42600000)]
  0x0000023774f26a80 JavaThread "Execution worker Thread 2" [_thread_blocked, id=20632, stack(0x000000eb42600000,0x000000eb42700000)]
  0x0000023774f279b0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=20608, stack(0x000000eb42700000,0x000000eb42800000)]
  0x0000023774f288e0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=20676, stack(0x000000eb42800000,0x000000eb42900000)]
  0x0000023774f28df0 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=20612, stack(0x000000eb42900000,0x000000eb42a00000)]
  0x0000023774f26060 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=20628, stack(0x000000eb42a00000,0x000000eb42b00000)]
  0x000002377563d0b0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=20624, stack(0x000000eb42b00000,0x000000eb42c00000)]
  0x0000023775638ee0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=20588, stack(0x000000eb42c00000,0x000000eb42d00000)]
  0x000002377563a830 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=20620, stack(0x000000eb42d00000,0x000000eb42e00000)]
  0x000002377563ad40 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=20540, stack(0x000000eb42e00000,0x000000eb42f00000)]
  0x00000237756389d0 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=20592, stack(0x000000eb42f00000,0x000000eb43000000)]
  0x000002377563ea00 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=20600, stack(0x000000eb43000000,0x000000eb43100000)]
  0x000002377563dad0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=20564, stack(0x000000eb43100000,0x000000eb43200000)]
  0x000002377563c690 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=1956, stack(0x000000eb43200000,0x000000eb43300000)]
  0x000002377563e4f0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=20680, stack(0x000000eb43300000,0x000000eb43400000)]
  0x0000023775639e10 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=20664, stack(0x000000eb43400000,0x000000eb43500000)]
  0x000002377563ef10 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=8468, stack(0x000000eb43500000,0x000000eb43600000)]
  0x000002377563cba0 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=20720, stack(0x000000eb43600000,0x000000eb43700000)]
  0x000002377563dfe0 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=20640, stack(0x000000eb43700000,0x000000eb43800000)]
  0x000002377563f420 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=20688, stack(0x000000eb43800000,0x000000eb43900000)]
  0x000002377563b250 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=3376, stack(0x000000eb43900000,0x000000eb43a00000)]
  0x000002377563f930 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=20692, stack(0x000000eb43a00000,0x000000eb43b00000)]
  0x000002377563b760 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=20684, stack(0x000000eb43b00000,0x000000eb43c00000)]
  0x000002377563bc70 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=20696, stack(0x000000eb43c00000,0x000000eb43d00000)]
  0x00000237756384c0 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=20672, stack(0x000000eb43d00000,0x000000eb43e00000)]
  0x000002377563a320 JavaThread "jar transforms Thread 2" [_thread_blocked, id=20728, stack(0x000000eb43e00000,0x000000eb43f00000)]
  0x0000023774e67fe0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=7172, stack(0x000000eb43f00000,0x000000eb44000000)]
  0x0000023774e670b0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=7768, stack(0x000000eb44000000,0x000000eb44100000)]
  0x0000023774e65c70 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=17348, stack(0x000000eb44100000,0x000000eb44200000)]
  0x0000023774e65760 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=20072, stack(0x000000eb44200000,0x000000eb44300000)]
  0x0000023774e62ee0 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=20324, stack(0x000000eb44300000,0x000000eb44400000)]
  0x0000023774e64d40 JavaThread "jar transforms Thread 4" [_thread_blocked, id=9092, stack(0x000000eb44500000,0x000000eb44600000)]
  0x0000023774e675c0 JavaThread "build event listener" [_thread_blocked, id=21684, stack(0x000000eb44700000,0x000000eb44800000)]
  0x0000023774e633f0 JavaThread "Problems report writer" [_thread_blocked, id=22084, stack(0x000000eb40c00000,0x000000eb40d00000)]
  0x00000237766a4230 JavaThread "File lock release action executor Thread 3" [_thread_blocked, id=3184, stack(0x000000eb40600000,0x000000eb40700000)]
  0x00000237766a3d20 JavaThread "ForkJoinPool.commonPool-worker-3" daemon [_thread_blocked, id=17544, stack(0x000000eb42300000,0x000000eb42400000)]
  0x000002377eedb0c0 JavaThread "included builds Thread 2" [_thread_blocked, id=22508, stack(0x000000eb40d00000,0x000000eb40e00000)]
  0x000002377eedc500 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\executionHistory)" [_thread_blocked, id=22292, stack(0x000000eb44400000,0x000000eb44500000)]

Other Threads:
  0x000002376d47e2b0 VMThread "VM Thread" [stack: 0x000000eb3e900000,0x000000eb3ea00000] [id=9388]
  0x000002376d607080 WatcherThread [stack: 0x000000eb3f500000,0x000000eb3f600000] [id=19408]
  0x0000023746136a50 GCTaskThread "GC Thread#0" [stack: 0x000000eb3e400000,0x000000eb3e500000] [id=18236]
  0x000002376d706570 GCTaskThread "GC Thread#1" [stack: 0x000000eb3f600000,0x000000eb3f700000] [id=19604]
  0x000002376d706830 GCTaskThread "GC Thread#2" [stack: 0x000000eb3f700000,0x000000eb3f800000] [id=19608]
  0x000002377221ba80 GCTaskThread "GC Thread#3" [stack: 0x000000eb3f800000,0x000000eb3f900000] [id=20292]
  0x00000237461439a0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000eb3e500000,0x000000eb3e600000] [id=18600]
  0x00000237461443c0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000eb3e600000,0x000000eb3e700000] [id=18676]
  0x000002374615e6e0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000eb3e700000,0x000000eb3e800000] [id=7572]
  0x00000237732e9450 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000eb41b00000,0x000000eb41c00000] [id=2012]
  0x000002376d33e2b0 ConcurrentGCThread "G1 Service" [stack: 0x000000eb3e800000,0x000000eb3e900000] [id=2244]

Threads with active compile tasks:
C2 CompilerThread0   781994 22674       4       org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ArtifactBackedResolvedVariant$SingleArtifactSet::visit (8 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000023700000000-0x0000023700bb0000-0x0000023700bb0000), size 12255232, SharedBaseAddress: 0x0000023700000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000023701000000-0x0000023735000000, reserved size: 872415232
Narrow klass base: 0x0000023700000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 1105920K, used 533245K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 44 young (180224K), 3 survivors (12288K)
 Metaspace       used 119152K, committed 121408K, reserved 983040K
  class space    used 16459K, committed 17472K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%| O|  |TAMS 0x0000000680400000, 0x0000000680000000| Untracked 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%|HS|  |TAMS 0x0000000680800000, 0x0000000680400000| Complete 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%| O|  |TAMS 0x0000000681000000, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000682000000, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682400000, 0x0000000682400000|100%| O|  |TAMS 0x0000000682400000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682800000, 0x0000000682800000|100%| O|  |TAMS 0x0000000682800000, 0x0000000682400000| Untracked 
|  10|0x0000000682800000, 0x0000000682c00000, 0x0000000682c00000|100%|HS|  |TAMS 0x0000000682c00000, 0x0000000682800000| Complete 
|  11|0x0000000682c00000, 0x0000000683000000, 0x0000000683000000|100%|HS|  |TAMS 0x0000000683000000, 0x0000000682c00000| Complete 
|  12|0x0000000683000000, 0x0000000683400000, 0x0000000683400000|100%| O|  |TAMS 0x0000000683400000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x0000000683800000, 0x0000000683800000|100%| O|  |TAMS 0x0000000683800000, 0x0000000683400000| Untracked 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| O|  |TAMS 0x0000000683c00000, 0x0000000683800000| Untracked 
|  15|0x0000000683c00000, 0x0000000684000000, 0x0000000684000000|100%| O|  |TAMS 0x0000000684000000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684400000, 0x0000000684400000|100%| O|  |TAMS 0x0000000684400000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684800000, 0x0000000684800000|100%| O|  |TAMS 0x0000000684800000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684c00000, 0x0000000684c00000|100%| O|  |TAMS 0x0000000684c00000, 0x0000000684800000| Untracked 
|  19|0x0000000684c00000, 0x0000000685000000, 0x0000000685000000|100%|HS|  |TAMS 0x0000000685000000, 0x0000000684c00000| Complete 
|  20|0x0000000685000000, 0x0000000685400000, 0x0000000685400000|100%| O|  |TAMS 0x0000000685400000, 0x0000000685000000| Untracked 
|  21|0x0000000685400000, 0x0000000685800000, 0x0000000685800000|100%|HS|  |TAMS 0x0000000685800000, 0x0000000685400000| Complete 
|  22|0x0000000685800000, 0x0000000685c00000, 0x0000000685c00000|100%| O|  |TAMS 0x0000000685c00000, 0x0000000685800000| Untracked 
|  23|0x0000000685c00000, 0x0000000686000000, 0x0000000686000000|100%| O|  |TAMS 0x0000000686000000, 0x0000000685c00000| Untracked 
|  24|0x0000000686000000, 0x0000000686400000, 0x0000000686400000|100%| O|  |TAMS 0x0000000686400000, 0x0000000686000000| Untracked 
|  25|0x0000000686400000, 0x0000000686800000, 0x0000000686800000|100%| O|  |TAMS 0x0000000686800000, 0x0000000686400000| Untracked 
|  26|0x0000000686800000, 0x0000000686c00000, 0x0000000686c00000|100%| O|  |TAMS 0x0000000686c00000, 0x0000000686800000| Untracked 
|  27|0x0000000686c00000, 0x0000000687000000, 0x0000000687000000|100%| O|  |TAMS 0x0000000687000000, 0x0000000686c00000| Untracked 
|  28|0x0000000687000000, 0x0000000687400000, 0x0000000687400000|100%| O|  |TAMS 0x0000000687400000, 0x0000000687000000| Untracked 
|  29|0x0000000687400000, 0x0000000687800000, 0x0000000687800000|100%| O|  |TAMS 0x0000000687800000, 0x0000000687400000| Untracked 
|  30|0x0000000687800000, 0x0000000687c00000, 0x0000000687c00000|100%| O|  |TAMS 0x0000000687c00000, 0x0000000687800000| Untracked 
|  31|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%| O|  |TAMS 0x0000000688000000, 0x0000000687c00000| Untracked 
|  32|0x0000000688000000, 0x0000000688400000, 0x0000000688400000|100%| O|  |TAMS 0x0000000688400000, 0x0000000688000000| Untracked 
|  33|0x0000000688400000, 0x0000000688800000, 0x0000000688800000|100%| O|  |TAMS 0x0000000688800000, 0x0000000688400000| Untracked 
|  34|0x0000000688800000, 0x0000000688c00000, 0x0000000688c00000|100%| O|  |TAMS 0x0000000688c00000, 0x0000000688800000| Untracked 
|  35|0x0000000688c00000, 0x0000000689000000, 0x0000000689000000|100%| O|  |TAMS 0x0000000689000000, 0x0000000688c00000| Untracked 
|  36|0x0000000689000000, 0x0000000689400000, 0x0000000689400000|100%| O|  |TAMS 0x0000000689400000, 0x0000000689000000| Untracked 
|  37|0x0000000689400000, 0x0000000689800000, 0x0000000689800000|100%| O|  |TAMS 0x0000000689800000, 0x0000000689400000| Untracked 
|  38|0x0000000689800000, 0x0000000689c00000, 0x0000000689c00000|100%| O|  |TAMS 0x0000000689c00000, 0x0000000689800000| Untracked 
|  39|0x0000000689c00000, 0x000000068a000000, 0x000000068a000000|100%| O|  |TAMS 0x000000068a000000, 0x0000000689c00000| Untracked 
|  40|0x000000068a000000, 0x000000068a400000, 0x000000068a400000|100%| O|  |TAMS 0x000000068a400000, 0x000000068a000000| Untracked 
|  41|0x000000068a400000, 0x000000068a800000, 0x000000068a800000|100%| O|  |TAMS 0x000000068a800000, 0x000000068a400000| Untracked 
|  42|0x000000068a800000, 0x000000068ac00000, 0x000000068ac00000|100%| O|  |TAMS 0x000000068a800000, 0x000000068a800000| Untracked 
|  43|0x000000068ac00000, 0x000000068b000000, 0x000000068b000000|100%| O|  |TAMS 0x000000068b000000, 0x000000068ac00000| Untracked 
|  44|0x000000068b000000, 0x000000068b400000, 0x000000068b400000|100%| O|  |TAMS 0x000000068b400000, 0x000000068b000000| Untracked 
|  45|0x000000068b400000, 0x000000068b800000, 0x000000068b800000|100%| O|  |TAMS 0x000000068b400000, 0x000000068b400000| Untracked 
|  46|0x000000068b800000, 0x000000068bc00000, 0x000000068bc00000|100%| O|  |TAMS 0x000000068bc00000, 0x000000068b800000| Untracked 
|  47|0x000000068bc00000, 0x000000068c000000, 0x000000068c000000|100%| O|  |TAMS 0x000000068c000000, 0x000000068bc00000| Untracked 
|  48|0x000000068c000000, 0x000000068c400000, 0x000000068c400000|100%| O|  |TAMS 0x000000068c400000, 0x000000068c000000| Untracked 
|  49|0x000000068c400000, 0x000000068c800000, 0x000000068c800000|100%| O|  |TAMS 0x000000068c800000, 0x000000068c400000| Untracked 
|  50|0x000000068c800000, 0x000000068cc00000, 0x000000068cc00000|100%| O|  |TAMS 0x000000068c882000, 0x000000068c800000| Untracked 
|  51|0x000000068cc00000, 0x000000068d000000, 0x000000068d000000|100%| O|  |TAMS 0x000000068cc00000, 0x000000068cc00000| Untracked 
|  52|0x000000068d000000, 0x000000068d400000, 0x000000068d400000|100%| O|  |TAMS 0x000000068d000000, 0x000000068d000000| Untracked 
|  53|0x000000068d400000, 0x000000068d800000, 0x000000068d800000|100%| O|  |TAMS 0x000000068d400000, 0x000000068d400000| Untracked 
|  54|0x000000068d800000, 0x000000068dc00000, 0x000000068dc00000|100%| O|  |TAMS 0x000000068d800000, 0x000000068d800000| Untracked 
|  55|0x000000068dc00000, 0x000000068e000000, 0x000000068e000000|100%| O|  |TAMS 0x000000068dc00000, 0x000000068dc00000| Untracked 
|  56|0x000000068e000000, 0x000000068e400000, 0x000000068e400000|100%| O|  |TAMS 0x000000068e000000, 0x000000068e000000| Untracked 
|  57|0x000000068e400000, 0x000000068e800000, 0x000000068e800000|100%| O|  |TAMS 0x000000068e400000, 0x000000068e400000| Untracked 
|  58|0x000000068e800000, 0x000000068ec00000, 0x000000068ec00000|100%| O|  |TAMS 0x000000068e800000, 0x000000068e800000| Untracked 
|  59|0x000000068ec00000, 0x000000068f000000, 0x000000068f000000|100%| O|  |TAMS 0x000000068ec00000, 0x000000068ec00000| Untracked 
|  60|0x000000068f000000, 0x000000068f400000, 0x000000068f400000|100%| O|  |TAMS 0x000000068f000000, 0x000000068f000000| Untracked 
|  61|0x000000068f400000, 0x000000068f800000, 0x000000068f800000|100%| O|  |TAMS 0x000000068f400000, 0x000000068f400000| Untracked 
|  62|0x000000068f800000, 0x000000068fc00000, 0x000000068fc00000|100%| O|  |TAMS 0x000000068f800000, 0x000000068f800000| Untracked 
|  63|0x000000068fc00000, 0x0000000690000000, 0x0000000690000000|100%| O|  |TAMS 0x000000068fc00000, 0x000000068fc00000| Untracked 
|  64|0x0000000690000000, 0x0000000690400000, 0x0000000690400000|100%| O|  |TAMS 0x0000000690000000, 0x0000000690000000| Untracked 
|  65|0x0000000690400000, 0x0000000690800000, 0x0000000690800000|100%| O|  |TAMS 0x0000000690400000, 0x0000000690400000| Untracked 
|  66|0x0000000690800000, 0x0000000690c00000, 0x0000000690c00000|100%| O|  |TAMS 0x0000000690800000, 0x0000000690800000| Untracked 
|  67|0x0000000690c00000, 0x0000000691000000, 0x0000000691000000|100%| O|  |TAMS 0x0000000690c00000, 0x0000000690c00000| Untracked 
|  68|0x0000000691000000, 0x0000000691400000, 0x0000000691400000|100%| O|  |TAMS 0x0000000691000000, 0x0000000691000000| Untracked 
|  69|0x0000000691400000, 0x0000000691800000, 0x0000000691800000|100%| O|  |TAMS 0x0000000691400000, 0x0000000691400000| Untracked 
|  70|0x0000000691800000, 0x0000000691c00000, 0x0000000691c00000|100%| O|  |TAMS 0x0000000691800000, 0x0000000691800000| Untracked 
|  71|0x0000000691c00000, 0x0000000692000000, 0x0000000692000000|100%| O|  |TAMS 0x0000000691c00000, 0x0000000691c00000| Untracked 
|  72|0x0000000692000000, 0x0000000692400000, 0x0000000692400000|100%| O|  |TAMS 0x0000000692000000, 0x0000000692000000| Untracked 
|  73|0x0000000692400000, 0x0000000692800000, 0x0000000692800000|100%| O|  |TAMS 0x0000000692400000, 0x0000000692400000| Untracked 
|  74|0x0000000692800000, 0x0000000692c00000, 0x0000000692c00000|100%| O|  |TAMS 0x0000000692800000, 0x0000000692800000| Untracked 
|  75|0x0000000692c00000, 0x0000000693000000, 0x0000000693000000|100%| O|  |TAMS 0x0000000692c00000, 0x0000000692c00000| Untracked 
|  76|0x0000000693000000, 0x0000000693400000, 0x0000000693400000|100%| O|  |TAMS 0x0000000693000000, 0x0000000693000000| Untracked 
|  77|0x0000000693400000, 0x0000000693800000, 0x0000000693800000|100%| O|  |TAMS 0x0000000693400000, 0x0000000693400000| Untracked 
|  78|0x0000000693800000, 0x0000000693c00000, 0x0000000693c00000|100%| O|  |TAMS 0x0000000693800000, 0x0000000693800000| Untracked 
|  79|0x0000000693c00000, 0x0000000694000000, 0x0000000694000000|100%| O|  |TAMS 0x0000000693c00000, 0x0000000693c00000| Untracked 
|  80|0x0000000694000000, 0x0000000694400000, 0x0000000694400000|100%| O|  |TAMS 0x0000000694000000, 0x0000000694000000| Untracked 
|  81|0x0000000694400000, 0x0000000694800000, 0x0000000694800000|100%| O|  |TAMS 0x0000000694400000, 0x0000000694400000| Untracked 
|  82|0x0000000694800000, 0x0000000694c00000, 0x0000000694c00000|100%| O|  |TAMS 0x0000000694800000, 0x0000000694800000| Untracked 
|  83|0x0000000694c00000, 0x0000000695000000, 0x0000000695000000|100%| O|  |TAMS 0x0000000694c00000, 0x0000000694c00000| Untracked 
|  84|0x0000000695000000, 0x0000000695400000, 0x0000000695400000|100%| O|  |TAMS 0x0000000695000000, 0x0000000695000000| Untracked 
|  85|0x0000000695400000, 0x0000000695800000, 0x0000000695800000|100%| O|  |TAMS 0x0000000695400000, 0x0000000695400000| Untracked 
|  86|0x0000000695800000, 0x0000000695c00000, 0x0000000695c00000|100%| O|  |TAMS 0x0000000695800000, 0x0000000695800000| Untracked 
|  87|0x0000000695c00000, 0x0000000696000000, 0x0000000696000000|100%| O|  |TAMS 0x0000000695c00000, 0x0000000695c00000| Untracked 
|  88|0x0000000696000000, 0x00000006962dce00, 0x0000000696400000| 71%| O|  |TAMS 0x0000000696000000, 0x0000000696000000| Untracked 
|  89|0x0000000696400000, 0x0000000696400000, 0x0000000696800000|  0%| F|  |TAMS 0x0000000696400000, 0x0000000696400000| Untracked 
|  90|0x0000000696800000, 0x0000000696800000, 0x0000000696c00000|  0%| F|  |TAMS 0x0000000696800000, 0x0000000696800000| Untracked 
|  91|0x0000000696c00000, 0x0000000696c00000, 0x0000000697000000|  0%| F|  |TAMS 0x0000000696c00000, 0x0000000696c00000| Untracked 
|  92|0x0000000697000000, 0x0000000697000000, 0x0000000697400000|  0%| F|  |TAMS 0x0000000697000000, 0x0000000697000000| Untracked 
|  93|0x0000000697400000, 0x0000000697400000, 0x0000000697800000|  0%| F|  |TAMS 0x0000000697400000, 0x0000000697400000| Untracked 
|  94|0x0000000697800000, 0x0000000697800000, 0x0000000697c00000|  0%| F|  |TAMS 0x0000000697800000, 0x0000000697800000| Untracked 
|  95|0x0000000697c00000, 0x0000000697c00000, 0x0000000698000000|  0%| F|  |TAMS 0x0000000697c00000, 0x0000000697c00000| Untracked 
|  96|0x0000000698000000, 0x0000000698000000, 0x0000000698400000|  0%| F|  |TAMS 0x0000000698000000, 0x0000000698000000| Untracked 
|  97|0x0000000698400000, 0x0000000698400000, 0x0000000698800000|  0%| F|  |TAMS 0x0000000698400000, 0x0000000698400000| Untracked 
|  98|0x0000000698800000, 0x0000000698800000, 0x0000000698c00000|  0%| F|  |TAMS 0x0000000698800000, 0x0000000698800000| Untracked 
|  99|0x0000000698c00000, 0x0000000698c00000, 0x0000000699000000|  0%| F|  |TAMS 0x0000000698c00000, 0x0000000698c00000| Untracked 
| 100|0x0000000699000000, 0x0000000699000000, 0x0000000699400000|  0%| F|  |TAMS 0x0000000699000000, 0x0000000699000000| Untracked 
| 101|0x0000000699400000, 0x0000000699400000, 0x0000000699800000|  0%| F|  |TAMS 0x0000000699400000, 0x0000000699400000| Untracked 
| 102|0x0000000699800000, 0x0000000699800000, 0x0000000699c00000|  0%| F|  |TAMS 0x0000000699800000, 0x0000000699800000| Untracked 
| 103|0x0000000699c00000, 0x0000000699c00000, 0x000000069a000000|  0%| F|  |TAMS 0x0000000699c00000, 0x0000000699c00000| Untracked 
| 104|0x000000069a000000, 0x000000069a000000, 0x000000069a400000|  0%| F|  |TAMS 0x000000069a000000, 0x000000069a000000| Untracked 
| 105|0x000000069a400000, 0x000000069a400000, 0x000000069a800000|  0%| F|  |TAMS 0x000000069a400000, 0x000000069a400000| Untracked 
| 106|0x000000069a800000, 0x000000069a800000, 0x000000069ac00000|  0%| F|  |TAMS 0x000000069a800000, 0x000000069a800000| Untracked 
| 107|0x000000069ac00000, 0x000000069ac00000, 0x000000069b000000|  0%| F|  |TAMS 0x000000069ac00000, 0x000000069ac00000| Untracked 
| 108|0x000000069b000000, 0x000000069b000000, 0x000000069b400000|  0%| F|  |TAMS 0x000000069b000000, 0x000000069b000000| Untracked 
| 109|0x000000069b400000, 0x000000069b400000, 0x000000069b800000|  0%| F|  |TAMS 0x000000069b400000, 0x000000069b400000| Untracked 
| 110|0x000000069b800000, 0x000000069b800000, 0x000000069bc00000|  0%| F|  |TAMS 0x000000069b800000, 0x000000069b800000| Untracked 
| 111|0x000000069bc00000, 0x000000069bc00000, 0x000000069c000000|  0%| F|  |TAMS 0x000000069bc00000, 0x000000069bc00000| Untracked 
| 112|0x000000069c000000, 0x000000069c000000, 0x000000069c400000|  0%| F|  |TAMS 0x000000069c000000, 0x000000069c000000| Untracked 
| 113|0x000000069c400000, 0x000000069c400000, 0x000000069c800000|  0%| F|  |TAMS 0x000000069c400000, 0x000000069c400000| Untracked 
| 114|0x000000069c800000, 0x000000069c800000, 0x000000069cc00000|  0%| F|  |TAMS 0x000000069c800000, 0x000000069c800000| Untracked 
| 115|0x000000069cc00000, 0x000000069cc00000, 0x000000069d000000|  0%| F|  |TAMS 0x000000069cc00000, 0x000000069cc00000| Untracked 
| 116|0x000000069d000000, 0x000000069d000000, 0x000000069d400000|  0%| F|  |TAMS 0x000000069d000000, 0x000000069d000000| Untracked 
| 117|0x000000069d400000, 0x000000069d400000, 0x000000069d800000|  0%| F|  |TAMS 0x000000069d400000, 0x000000069d400000| Untracked 
| 118|0x000000069d800000, 0x000000069d800000, 0x000000069dc00000|  0%| F|  |TAMS 0x000000069d800000, 0x000000069d800000| Untracked 
| 119|0x000000069dc00000, 0x000000069dc00000, 0x000000069e000000|  0%| F|  |TAMS 0x000000069dc00000, 0x000000069dc00000| Untracked 
| 120|0x000000069e000000, 0x000000069e000000, 0x000000069e400000|  0%| F|  |TAMS 0x000000069e000000, 0x000000069e000000| Untracked 
| 121|0x000000069e400000, 0x000000069e400000, 0x000000069e800000|  0%| F|  |TAMS 0x000000069e400000, 0x000000069e400000| Untracked 
| 122|0x000000069e800000, 0x000000069e800000, 0x000000069ec00000|  0%| F|  |TAMS 0x000000069e800000, 0x000000069e800000| Untracked 
| 123|0x000000069ec00000, 0x000000069ec00000, 0x000000069f000000|  0%| F|  |TAMS 0x000000069ec00000, 0x000000069ec00000| Untracked 
| 124|0x000000069f000000, 0x000000069f000000, 0x000000069f400000|  0%| F|  |TAMS 0x000000069f000000, 0x000000069f000000| Untracked 
| 125|0x000000069f400000, 0x000000069f400000, 0x000000069f800000|  0%| F|  |TAMS 0x000000069f400000, 0x000000069f400000| Untracked 
| 126|0x000000069f800000, 0x000000069f800000, 0x000000069fc00000|  0%| F|  |TAMS 0x000000069f800000, 0x000000069f800000| Untracked 
| 127|0x000000069fc00000, 0x000000069fc00000, 0x00000006a0000000|  0%| F|  |TAMS 0x000000069fc00000, 0x000000069fc00000| Untracked 
| 128|0x00000006a0000000, 0x00000006a0000000, 0x00000006a0400000|  0%| F|  |TAMS 0x00000006a0000000, 0x00000006a0000000| Untracked 
| 129|0x00000006a0400000, 0x00000006a0400000, 0x00000006a0800000|  0%| F|  |TAMS 0x00000006a0400000, 0x00000006a0400000| Untracked 
| 130|0x00000006a0800000, 0x00000006a0800000, 0x00000006a0c00000|  0%| F|  |TAMS 0x00000006a0800000, 0x00000006a0800000| Untracked 
| 131|0x00000006a0c00000, 0x00000006a0c00000, 0x00000006a1000000|  0%| F|  |TAMS 0x00000006a0c00000, 0x00000006a0c00000| Untracked 
| 132|0x00000006a1000000, 0x00000006a1000000, 0x00000006a1400000|  0%| F|  |TAMS 0x00000006a1000000, 0x00000006a1000000| Untracked 
| 133|0x00000006a1400000, 0x00000006a1400000, 0x00000006a1800000|  0%| F|  |TAMS 0x00000006a1400000, 0x00000006a1400000| Untracked 
| 134|0x00000006a1800000, 0x00000006a1800000, 0x00000006a1c00000|  0%| F|  |TAMS 0x00000006a1800000, 0x00000006a1800000| Untracked 
| 135|0x00000006a1c00000, 0x00000006a1c00000, 0x00000006a2000000|  0%| F|  |TAMS 0x00000006a1c00000, 0x00000006a1c00000| Untracked 
| 136|0x00000006a2000000, 0x00000006a2000000, 0x00000006a2400000|  0%| F|  |TAMS 0x00000006a2000000, 0x00000006a2000000| Untracked 
| 137|0x00000006a2400000, 0x00000006a2400000, 0x00000006a2800000|  0%| F|  |TAMS 0x00000006a2400000, 0x00000006a2400000| Untracked 
| 138|0x00000006a2800000, 0x00000006a2800000, 0x00000006a2c00000|  0%| F|  |TAMS 0x00000006a2800000, 0x00000006a2800000| Untracked 
| 139|0x00000006a2c00000, 0x00000006a2c00000, 0x00000006a3000000|  0%| F|  |TAMS 0x00000006a2c00000, 0x00000006a2c00000| Untracked 
| 140|0x00000006a3000000, 0x00000006a3000000, 0x00000006a3400000|  0%| F|  |TAMS 0x00000006a3000000, 0x00000006a3000000| Untracked 
| 141|0x00000006a3400000, 0x00000006a3400000, 0x00000006a3800000|  0%| F|  |TAMS 0x00000006a3400000, 0x00000006a3400000| Untracked 
| 142|0x00000006a3800000, 0x00000006a3800000, 0x00000006a3c00000|  0%| F|  |TAMS 0x00000006a3800000, 0x00000006a3800000| Untracked 
| 143|0x00000006a3c00000, 0x00000006a3c00000, 0x00000006a4000000|  0%| F|  |TAMS 0x00000006a3c00000, 0x00000006a3c00000| Untracked 
| 144|0x00000006a4000000, 0x00000006a4000000, 0x00000006a4400000|  0%| F|  |TAMS 0x00000006a4000000, 0x00000006a4000000| Untracked 
| 145|0x00000006a4400000, 0x00000006a4400000, 0x00000006a4800000|  0%| F|  |TAMS 0x00000006a4400000, 0x00000006a4400000| Untracked 
| 146|0x00000006a4800000, 0x00000006a4800000, 0x00000006a4c00000|  0%| F|  |TAMS 0x00000006a4800000, 0x00000006a4800000| Untracked 
| 147|0x00000006a4c00000, 0x00000006a4c00000, 0x00000006a5000000|  0%| F|  |TAMS 0x00000006a4c00000, 0x00000006a4c00000| Untracked 
| 148|0x00000006a5000000, 0x00000006a5000000, 0x00000006a5400000|  0%| F|  |TAMS 0x00000006a5000000, 0x00000006a5000000| Untracked 
| 149|0x00000006a5400000, 0x00000006a5400000, 0x00000006a5800000|  0%| F|  |TAMS 0x00000006a5400000, 0x00000006a5400000| Untracked 
| 150|0x00000006a5800000, 0x00000006a5800000, 0x00000006a5c00000|  0%| F|  |TAMS 0x00000006a5800000, 0x00000006a5800000| Untracked 
| 151|0x00000006a5c00000, 0x00000006a5c00000, 0x00000006a6000000|  0%| F|  |TAMS 0x00000006a5c00000, 0x00000006a5c00000| Untracked 
| 152|0x00000006a6000000, 0x00000006a6000000, 0x00000006a6400000|  0%| F|  |TAMS 0x00000006a6000000, 0x00000006a6000000| Untracked 
| 153|0x00000006a6400000, 0x00000006a6400000, 0x00000006a6800000|  0%| F|  |TAMS 0x00000006a6400000, 0x00000006a6400000| Untracked 
| 154|0x00000006a6800000, 0x00000006a6800000, 0x00000006a6c00000|  0%| F|  |TAMS 0x00000006a6800000, 0x00000006a6800000| Untracked 
| 155|0x00000006a6c00000, 0x00000006a6c00000, 0x00000006a7000000|  0%| F|  |TAMS 0x00000006a6c00000, 0x00000006a6c00000| Untracked 
| 156|0x00000006a7000000, 0x00000006a7000000, 0x00000006a7400000|  0%| F|  |TAMS 0x00000006a7000000, 0x00000006a7000000| Untracked 
| 157|0x00000006a7400000, 0x00000006a7400000, 0x00000006a7800000|  0%| F|  |TAMS 0x00000006a7400000, 0x00000006a7400000| Untracked 
| 158|0x00000006a7800000, 0x00000006a7800000, 0x00000006a7c00000|  0%| F|  |TAMS 0x00000006a7800000, 0x00000006a7800000| Untracked 
| 159|0x00000006a7c00000, 0x00000006a7c00000, 0x00000006a8000000|  0%| F|  |TAMS 0x00000006a7c00000, 0x00000006a7c00000| Untracked 
| 160|0x00000006a8000000, 0x00000006a8000000, 0x00000006a8400000|  0%| F|  |TAMS 0x00000006a8000000, 0x00000006a8000000| Untracked 
| 161|0x00000006a8400000, 0x00000006a8400000, 0x00000006a8800000|  0%| F|  |TAMS 0x00000006a8400000, 0x00000006a8400000| Untracked 
| 162|0x00000006a8800000, 0x00000006a8800000, 0x00000006a8c00000|  0%| F|  |TAMS 0x00000006a8800000, 0x00000006a8800000| Untracked 
| 163|0x00000006a8c00000, 0x00000006a8c00000, 0x00000006a9000000|  0%| F|  |TAMS 0x00000006a8c00000, 0x00000006a8c00000| Untracked 
| 164|0x00000006a9000000, 0x00000006a9000000, 0x00000006a9400000|  0%| F|  |TAMS 0x00000006a9000000, 0x00000006a9000000| Untracked 
| 165|0x00000006a9400000, 0x00000006a9400000, 0x00000006a9800000|  0%| F|  |TAMS 0x00000006a9400000, 0x00000006a9400000| Untracked 
| 166|0x00000006a9800000, 0x00000006a9800000, 0x00000006a9c00000|  0%| F|  |TAMS 0x00000006a9800000, 0x00000006a9800000| Untracked 
| 167|0x00000006a9c00000, 0x00000006a9c00000, 0x00000006aa000000|  0%| F|  |TAMS 0x00000006a9c00000, 0x00000006a9c00000| Untracked 
| 168|0x00000006aa000000, 0x00000006aa000000, 0x00000006aa400000|  0%| F|  |TAMS 0x00000006aa000000, 0x00000006aa000000| Untracked 
| 169|0x00000006aa400000, 0x00000006aa400000, 0x00000006aa800000|  0%| F|  |TAMS 0x00000006aa400000, 0x00000006aa400000| Untracked 
| 170|0x00000006aa800000, 0x00000006aa800000, 0x00000006aac00000|  0%| F|  |TAMS 0x00000006aa800000, 0x00000006aa800000| Untracked 
| 171|0x00000006aac00000, 0x00000006aac00000, 0x00000006ab000000|  0%| F|  |TAMS 0x00000006aac00000, 0x00000006aac00000| Untracked 
| 172|0x00000006ab000000, 0x00000006ab000000, 0x00000006ab400000|  0%| F|  |TAMS 0x00000006ab000000, 0x00000006ab000000| Untracked 
| 173|0x00000006ab400000, 0x00000006ab400000, 0x00000006ab800000|  0%| F|  |TAMS 0x00000006ab400000, 0x00000006ab400000| Untracked 
| 174|0x00000006ab800000, 0x00000006ab800000, 0x00000006abc00000|  0%| F|  |TAMS 0x00000006ab800000, 0x00000006ab800000| Untracked 
| 175|0x00000006abc00000, 0x00000006abc00000, 0x00000006ac000000|  0%| F|  |TAMS 0x00000006abc00000, 0x00000006abc00000| Untracked 
| 176|0x00000006ac000000, 0x00000006ac000000, 0x00000006ac400000|  0%| F|  |TAMS 0x00000006ac000000, 0x00000006ac000000| Untracked 
| 177|0x00000006ac400000, 0x00000006ac400000, 0x00000006ac800000|  0%| F|  |TAMS 0x00000006ac400000, 0x00000006ac400000| Untracked 
| 178|0x00000006ac800000, 0x00000006ac800000, 0x00000006acc00000|  0%| F|  |TAMS 0x00000006ac800000, 0x00000006ac800000| Untracked 
| 179|0x00000006acc00000, 0x00000006acc00000, 0x00000006ad000000|  0%| F|  |TAMS 0x00000006acc00000, 0x00000006acc00000| Untracked 
| 180|0x00000006ad000000, 0x00000006ad000000, 0x00000006ad400000|  0%| F|  |TAMS 0x00000006ad000000, 0x00000006ad000000| Untracked 
| 181|0x00000006ad400000, 0x00000006ad400000, 0x00000006ad800000|  0%| F|  |TAMS 0x00000006ad400000, 0x00000006ad400000| Untracked 
| 182|0x00000006ad800000, 0x00000006ad800000, 0x00000006adc00000|  0%| F|  |TAMS 0x00000006ad800000, 0x00000006ad800000| Untracked 
| 183|0x00000006adc00000, 0x00000006adc00000, 0x00000006ae000000|  0%| F|  |TAMS 0x00000006adc00000, 0x00000006adc00000| Untracked 
| 184|0x00000006ae000000, 0x00000006ae000000, 0x00000006ae400000|  0%| F|  |TAMS 0x00000006ae000000, 0x00000006ae000000| Untracked 
| 185|0x00000006ae400000, 0x00000006ae400000, 0x00000006ae800000|  0%| F|  |TAMS 0x00000006ae400000, 0x00000006ae400000| Untracked 
| 186|0x00000006ae800000, 0x00000006ae800000, 0x00000006aec00000|  0%| F|  |TAMS 0x00000006ae800000, 0x00000006ae800000| Untracked 
| 187|0x00000006aec00000, 0x00000006aec00000, 0x00000006af000000|  0%| F|  |TAMS 0x00000006aec00000, 0x00000006aec00000| Untracked 
| 188|0x00000006af000000, 0x00000006af000000, 0x00000006af400000|  0%| F|  |TAMS 0x00000006af000000, 0x00000006af000000| Untracked 
| 189|0x00000006af400000, 0x00000006af400000, 0x00000006af800000|  0%| F|  |TAMS 0x00000006af400000, 0x00000006af400000| Untracked 
| 190|0x00000006af800000, 0x00000006af800000, 0x00000006afc00000|  0%| F|  |TAMS 0x00000006af800000, 0x00000006af800000| Untracked 
| 191|0x00000006afc00000, 0x00000006afc00000, 0x00000006b0000000|  0%| F|  |TAMS 0x00000006afc00000, 0x00000006afc00000| Untracked 
| 192|0x00000006b0000000, 0x00000006b0000000, 0x00000006b0400000|  0%| F|  |TAMS 0x00000006b0000000, 0x00000006b0000000| Untracked 
| 193|0x00000006b0400000, 0x00000006b0400000, 0x00000006b0800000|  0%| F|  |TAMS 0x00000006b0400000, 0x00000006b0400000| Untracked 
| 194|0x00000006b0800000, 0x00000006b0800000, 0x00000006b0c00000|  0%| F|  |TAMS 0x00000006b0800000, 0x00000006b0800000| Untracked 
| 195|0x00000006b0c00000, 0x00000006b0c00000, 0x00000006b1000000|  0%| F|  |TAMS 0x00000006b0c00000, 0x00000006b0c00000| Untracked 
| 196|0x00000006b1000000, 0x00000006b1000000, 0x00000006b1400000|  0%| F|  |TAMS 0x00000006b1000000, 0x00000006b1000000| Untracked 
| 197|0x00000006b1400000, 0x00000006b1400000, 0x00000006b1800000|  0%| F|  |TAMS 0x00000006b1400000, 0x00000006b1400000| Untracked 
| 198|0x00000006b1800000, 0x00000006b1800000, 0x00000006b1c00000|  0%| F|  |TAMS 0x00000006b1800000, 0x00000006b1800000| Untracked 
| 199|0x00000006b1c00000, 0x00000006b1c00000, 0x00000006b2000000|  0%| F|  |TAMS 0x00000006b1c00000, 0x00000006b1c00000| Untracked 
| 200|0x00000006b2000000, 0x00000006b2000000, 0x00000006b2400000|  0%| F|  |TAMS 0x00000006b2000000, 0x00000006b2000000| Untracked 
| 201|0x00000006b2400000, 0x00000006b2400000, 0x00000006b2800000|  0%| F|  |TAMS 0x00000006b2400000, 0x00000006b2400000| Untracked 
| 202|0x00000006b2800000, 0x00000006b2800000, 0x00000006b2c00000|  0%| F|  |TAMS 0x00000006b2800000, 0x00000006b2800000| Untracked 
| 203|0x00000006b2c00000, 0x00000006b2c00000, 0x00000006b3000000|  0%| F|  |TAMS 0x00000006b2c00000, 0x00000006b2c00000| Untracked 
| 204|0x00000006b3000000, 0x00000006b3000000, 0x00000006b3400000|  0%| F|  |TAMS 0x00000006b3000000, 0x00000006b3000000| Untracked 
| 205|0x00000006b3400000, 0x00000006b35e2740, 0x00000006b3800000| 47%| S|CS|TAMS 0x00000006b3400000, 0x00000006b3400000| Complete 
| 206|0x00000006b3800000, 0x00000006b3c00000, 0x00000006b3c00000|100%| S|CS|TAMS 0x00000006b3800000, 0x00000006b3800000| Complete 
| 207|0x00000006b3c00000, 0x00000006b4000000, 0x00000006b4000000|100%| S|CS|TAMS 0x00000006b3c00000, 0x00000006b3c00000| Complete 
| 208|0x00000006b4000000, 0x00000006b4000000, 0x00000006b4400000|  0%| F|  |TAMS 0x00000006b4000000, 0x00000006b4000000| Untracked 
| 209|0x00000006b4400000, 0x00000006b4400000, 0x00000006b4800000|  0%| F|  |TAMS 0x00000006b4400000, 0x00000006b4400000| Untracked 
| 210|0x00000006b4800000, 0x00000006b4800000, 0x00000006b4c00000|  0%| F|  |TAMS 0x00000006b4800000, 0x00000006b4800000| Untracked 
| 211|0x00000006b4c00000, 0x00000006b4c00000, 0x00000006b5000000|  0%| F|  |TAMS 0x00000006b4c00000, 0x00000006b4c00000| Untracked 
| 212|0x00000006b5000000, 0x00000006b5000000, 0x00000006b5400000|  0%| F|  |TAMS 0x00000006b5000000, 0x00000006b5000000| Untracked 
| 213|0x00000006b5400000, 0x00000006b5400000, 0x00000006b5800000|  0%| F|  |TAMS 0x00000006b5400000, 0x00000006b5400000| Untracked 
| 214|0x00000006b5800000, 0x00000006b5800000, 0x00000006b5c00000|  0%| F|  |TAMS 0x00000006b5800000, 0x00000006b5800000| Untracked 
| 215|0x00000006b5c00000, 0x00000006b5c00000, 0x00000006b6000000|  0%| F|  |TAMS 0x00000006b5c00000, 0x00000006b5c00000| Untracked 
| 216|0x00000006b6000000, 0x00000006b6000000, 0x00000006b6400000|  0%| F|  |TAMS 0x00000006b6000000, 0x00000006b6000000| Untracked 
| 217|0x00000006b6400000, 0x00000006b6400000, 0x00000006b6800000|  0%| F|  |TAMS 0x00000006b6400000, 0x00000006b6400000| Untracked 
| 218|0x00000006b6800000, 0x00000006b6800000, 0x00000006b6c00000|  0%| F|  |TAMS 0x00000006b6800000, 0x00000006b6800000| Untracked 
| 219|0x00000006b6c00000, 0x00000006b6c00000, 0x00000006b7000000|  0%| F|  |TAMS 0x00000006b6c00000, 0x00000006b6c00000| Untracked 
| 220|0x00000006b7000000, 0x00000006b7000000, 0x00000006b7400000|  0%| F|  |TAMS 0x00000006b7000000, 0x00000006b7000000| Untracked 
| 221|0x00000006b7400000, 0x00000006b7400000, 0x00000006b7800000|  0%| F|  |TAMS 0x00000006b7400000, 0x00000006b7400000| Untracked 
| 222|0x00000006b7800000, 0x00000006b7800000, 0x00000006b7c00000|  0%| F|  |TAMS 0x00000006b7800000, 0x00000006b7800000| Untracked 
| 223|0x00000006b7c00000, 0x00000006b7c00000, 0x00000006b8000000|  0%| F|  |TAMS 0x00000006b7c00000, 0x00000006b7c00000| Untracked 
| 224|0x00000006b8000000, 0x00000006b8000000, 0x00000006b8400000|  0%| F|  |TAMS 0x00000006b8000000, 0x00000006b8000000| Untracked 
| 225|0x00000006b8400000, 0x00000006b8400000, 0x00000006b8800000|  0%| F|  |TAMS 0x00000006b8400000, 0x00000006b8400000| Untracked 
| 226|0x00000006b8800000, 0x00000006b8800000, 0x00000006b8c00000|  0%| F|  |TAMS 0x00000006b8800000, 0x00000006b8800000| Untracked 
| 227|0x00000006b8c00000, 0x00000006b8c00000, 0x00000006b9000000|  0%| F|  |TAMS 0x00000006b8c00000, 0x00000006b8c00000| Untracked 
| 228|0x00000006b9000000, 0x00000006b9000000, 0x00000006b9400000|  0%| F|  |TAMS 0x00000006b9000000, 0x00000006b9000000| Untracked 
| 229|0x00000006b9400000, 0x00000006b947ace0, 0x00000006b9800000| 11%| E|  |TAMS 0x00000006b9400000, 0x00000006b9400000| Complete 
| 230|0x00000006b9800000, 0x00000006b9c00000, 0x00000006b9c00000|100%| E|CS|TAMS 0x00000006b9800000, 0x00000006b9800000| Complete 
| 231|0x00000006b9c00000, 0x00000006ba000000, 0x00000006ba000000|100%| E|CS|TAMS 0x00000006b9c00000, 0x00000006b9c00000| Complete 
| 232|0x00000006ba000000, 0x00000006ba400000, 0x00000006ba400000|100%| E|  |TAMS 0x00000006ba000000, 0x00000006ba000000| Complete 
| 233|0x00000006ba400000, 0x00000006ba800000, 0x00000006ba800000|100%| E|CS|TAMS 0x00000006ba400000, 0x00000006ba400000| Complete 
| 234|0x00000006ba800000, 0x00000006bac00000, 0x00000006bac00000|100%| E|CS|TAMS 0x00000006ba800000, 0x00000006ba800000| Complete 
| 235|0x00000006bac00000, 0x00000006bb000000, 0x00000006bb000000|100%| E|CS|TAMS 0x00000006bac00000, 0x00000006bac00000| Complete 
| 236|0x00000006bb000000, 0x00000006bb400000, 0x00000006bb400000|100%| E|CS|TAMS 0x00000006bb000000, 0x00000006bb000000| Complete 
| 237|0x00000006bb400000, 0x00000006bb800000, 0x00000006bb800000|100%| E|CS|TAMS 0x00000006bb400000, 0x00000006bb400000| Complete 
| 238|0x00000006bb800000, 0x00000006bbc00000, 0x00000006bbc00000|100%| E|CS|TAMS 0x00000006bb800000, 0x00000006bb800000| Complete 
| 239|0x00000006bbc00000, 0x00000006bc000000, 0x00000006bc000000|100%| E|CS|TAMS 0x00000006bbc00000, 0x00000006bbc00000| Complete 
| 240|0x00000006bc000000, 0x00000006bc400000, 0x00000006bc400000|100%| E|CS|TAMS 0x00000006bc000000, 0x00000006bc000000| Complete 
| 241|0x00000006bc400000, 0x00000006bc800000, 0x00000006bc800000|100%| E|CS|TAMS 0x00000006bc400000, 0x00000006bc400000| Complete 
| 242|0x00000006bc800000, 0x00000006bcc00000, 0x00000006bcc00000|100%| E|CS|TAMS 0x00000006bc800000, 0x00000006bc800000| Complete 
| 243|0x00000006bcc00000, 0x00000006bd000000, 0x00000006bd000000|100%| E|CS|TAMS 0x00000006bcc00000, 0x00000006bcc00000| Complete 
| 244|0x00000006bd000000, 0x00000006bd400000, 0x00000006bd400000|100%| E|CS|TAMS 0x00000006bd000000, 0x00000006bd000000| Complete 
| 245|0x00000006bd400000, 0x00000006bd800000, 0x00000006bd800000|100%| E|CS|TAMS 0x00000006bd400000, 0x00000006bd400000| Complete 
| 246|0x00000006bd800000, 0x00000006bdc00000, 0x00000006bdc00000|100%| E|CS|TAMS 0x00000006bd800000, 0x00000006bd800000| Complete 
| 247|0x00000006bdc00000, 0x00000006be000000, 0x00000006be000000|100%| E|CS|TAMS 0x00000006bdc00000, 0x00000006bdc00000| Complete 
| 248|0x00000006be000000, 0x00000006be400000, 0x00000006be400000|100%| E|CS|TAMS 0x00000006be000000, 0x00000006be000000| Complete 
| 249|0x00000006be400000, 0x00000006be800000, 0x00000006be800000|100%| E|CS|TAMS 0x00000006be400000, 0x00000006be400000| Complete 
| 250|0x00000006be800000, 0x00000006bec00000, 0x00000006bec00000|100%| E|CS|TAMS 0x00000006be800000, 0x00000006be800000| Complete 
| 251|0x00000006bec00000, 0x00000006bf000000, 0x00000006bf000000|100%| E|CS|TAMS 0x00000006bec00000, 0x00000006bec00000| Complete 
| 252|0x00000006bf000000, 0x00000006bf400000, 0x00000006bf400000|100%| E|CS|TAMS 0x00000006bf000000, 0x00000006bf000000| Complete 
| 253|0x00000006bf400000, 0x00000006bf800000, 0x00000006bf800000|100%| E|CS|TAMS 0x00000006bf400000, 0x00000006bf400000| Complete 
| 254|0x00000006bf800000, 0x00000006bfc00000, 0x00000006bfc00000|100%| E|CS|TAMS 0x00000006bf800000, 0x00000006bf800000| Complete 
| 255|0x00000006bfc00000, 0x00000006c0000000, 0x00000006c0000000|100%| E|CS|TAMS 0x00000006bfc00000, 0x00000006bfc00000| Complete 
| 256|0x00000006c0000000, 0x00000006c0400000, 0x00000006c0400000|100%| E|CS|TAMS 0x00000006c0000000, 0x00000006c0000000| Complete 
| 257|0x00000006c0400000, 0x00000006c0800000, 0x00000006c0800000|100%| E|CS|TAMS 0x00000006c0400000, 0x00000006c0400000| Complete 
| 258|0x00000006c0800000, 0x00000006c0c00000, 0x00000006c0c00000|100%| E|CS|TAMS 0x00000006c0800000, 0x00000006c0800000| Complete 
| 259|0x00000006c0c00000, 0x00000006c1000000, 0x00000006c1000000|100%| E|CS|TAMS 0x00000006c0c00000, 0x00000006c0c00000| Complete 
| 260|0x00000006c1000000, 0x00000006c1400000, 0x00000006c1400000|100%| E|CS|TAMS 0x00000006c1000000, 0x00000006c1000000| Complete 
| 261|0x00000006c1400000, 0x00000006c1800000, 0x00000006c1800000|100%| E|CS|TAMS 0x00000006c1400000, 0x00000006c1400000| Complete 
| 262|0x00000006c1800000, 0x00000006c1c00000, 0x00000006c1c00000|100%| E|CS|TAMS 0x00000006c1800000, 0x00000006c1800000| Complete 
| 263|0x00000006c1c00000, 0x00000006c2000000, 0x00000006c2000000|100%| E|CS|TAMS 0x00000006c1c00000, 0x00000006c1c00000| Complete 
| 264|0x00000006c2000000, 0x00000006c2400000, 0x00000006c2400000|100%| E|CS|TAMS 0x00000006c2000000, 0x00000006c2000000| Complete 
| 265|0x00000006c2400000, 0x00000006c2800000, 0x00000006c2800000|100%| E|CS|TAMS 0x00000006c2400000, 0x00000006c2400000| Complete 
| 266|0x00000006c2800000, 0x00000006c2c00000, 0x00000006c2c00000|100%| E|CS|TAMS 0x00000006c2800000, 0x00000006c2800000| Complete 
| 267|0x00000006c2c00000, 0x00000006c3000000, 0x00000006c3000000|100%| E|CS|TAMS 0x00000006c2c00000, 0x00000006c2c00000| Complete 
| 268|0x00000006c3000000, 0x00000006c3400000, 0x00000006c3400000|100%| E|CS|TAMS 0x00000006c3000000, 0x00000006c3000000| Complete 
| 269|0x00000006c3400000, 0x00000006c3800000, 0x00000006c3800000|100%| E|CS|TAMS 0x00000006c3400000, 0x00000006c3400000| Complete 

Card table byte_map: [0x000002375d7d0000,0x000002375e3d0000] _byte_map_base: 0x000002375a3d0000

Marking Bits (Prev, Next): (CMBitMap*) 0x00000237461370b0, (CMBitMap*) 0x0000023746137070
 Prev Bits: [0x0000023764fd0000, 0x000002376afd0000)
 Next Bits: [0x000002375efd0000, 0x0000023764fd0000)

Polling page: 0x0000023744050000

Metaspace:

Usage:
  Non-class:    100.29 MB used.
      Class:     16.07 MB used.
       Both:    116.36 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     101.50 MB ( 79%) committed,  2 nodes.
      Class space:      832.00 MB reserved,      17.06 MB (  2%) committed,  1 nodes.
             Both:      960.00 MB reserved,     118.56 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  9.98 MB
       Class:  14.99 MB
        Both:  24.97 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 176.06 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 2670.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1897.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 8628.
num_chunk_merges: 9.
num_chunk_splits: 5239.
num_chunks_enlarged: 2902.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=17198Kb max_used=17198Kb free=102801Kb
 bounds [0x00000237556a0000, 0x0000023756770000, 0x000002375cbd0000]
CodeHeap 'profiled nmethods': size=120000Kb used=34214Kb max_used=34214Kb free=85785Kb
 bounds [0x000002374dbd0000, 0x000002374fd40000, 0x0000023755100000]
CodeHeap 'non-nmethods': size=5760Kb used=2457Kb max_used=2559Kb free=3302Kb
 bounds [0x0000023755100000, 0x0000023755390000, 0x00000237556a0000]
 total_blobs=19260 nmethods=18259 adapters=912
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 778.065 Thread 0x000002376d4a18e0 nmethod 22664 0x000002374fd3eb10 code [0x000002374fd3eca0, 0x000002374fd3edc8]
Event: 779.511 Thread 0x000002376d49f6a0 22665       4       java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode::isReleasable (23 bytes)
Event: 779.512 Thread 0x000002376d49f6a0 nmethod 22665 0x0000023756765410 code [0x0000023756765580, 0x0000023756765638]
Event: 779.815 Thread 0x000002376d49f6a0 22666       4       org.gradle.api.internal.GeneratedSubclasses::unpack (14 bytes)
Event: 779.889 Thread 0x000002376d49f6a0 nmethod 22666 0x0000023756765710 code [0x0000023756765a20, 0x0000023756767888]
Event: 779.889 Thread 0x000002376d49f6a0 22668       4       org.gradle.internal.snapshot.AbstractFileSystemLocationSnapshot::getSnapshot (7 bytes)
Event: 779.895 Thread 0x000002376d49f6a0 nmethod 22668 0x0000023756768d90 code [0x0000023756768f60, 0x0000023756769268]
Event: 779.895 Thread 0x000002376d49f6a0 22667       4       org.gradle.internal.Try::<init> (5 bytes)
Event: 779.895 Thread 0x000002376d49f6a0 nmethod 22667 0x0000023756769590 code [0x0000023756769700, 0x0000023756769778]
Event: 780.176 Thread 0x000002376d49f6a0 22669       4       java.util.LinkedList::pollFirst (19 bytes)
Event: 780.180 Thread 0x000002376d49f6a0 nmethod 22669 0x0000023756769890 code [0x0000023756769a00, 0x0000023756769d58]
Event: 780.200 Thread 0x000002376d49f6a0 22670   !   4       org.gradle.internal.operations.DefaultBuildOperationQueue$WorkerRunnable::getNextOperation (51 bytes)
Event: 780.218 Thread 0x000002376d49f6a0 nmethod 22670 0x0000023756769e10 code [0x0000023756769fc0, 0x000002375676a898]
Event: 780.546 Thread 0x000002376d49f6a0 22671       4       org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ArtifactBackedResolvedVariant$SingleArtifactSet::visit (93 bytes)
Event: 780.549 Thread 0x000002376d49f6a0 nmethod 22671 0x000002375676ac90 code [0x000002375676ae20, 0x000002375676aff8]
Event: 780.570 Thread 0x000002376d49f6a0 22672       4       org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.EndCollection::<init> (10 bytes)
Event: 780.572 Thread 0x000002376d49f6a0 nmethod 22672 0x000002375676b210 code [0x000002375676b380, 0x000002375676b4f8]
Event: 780.572 Thread 0x000002376d49f6a0 22673       4       org.gradle.internal.Deferrable$2::getCompleted (8 bytes)
Event: 780.574 Thread 0x000002376d49f6a0 nmethod 22673 0x000002375676b590 code [0x000002375676b720, 0x000002375676b898]
Event: 780.579 Thread 0x000002376d49f6a0 22674       4       org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ArtifactBackedResolvedVariant$SingleArtifactSet::visit (8 bytes)

GC Heap History (20 events):
Event: 558.363 GC heap before
{Heap before GC invocations=69 (full 0):
 garbage-first heap   total 1105920K, used 848647K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 149 young (610304K), 2 survivors (8192K)
 Metaspace       used 108950K, committed 110784K, reserved 983040K
  class space    used 15143K, committed 15936K, reserved 851968K
}
Event: 559.300 GC heap after
{Heap after GC invocations=70 (full 0):
 garbage-first heap   total 1105920K, used 286992K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 11 survivors (45056K)
 Metaspace       used 108950K, committed 110784K, reserved 983040K
  class space    used 15143K, committed 15936K, reserved 851968K
}
Event: 566.206 GC heap before
{Heap before GC invocations=70 (full 0):
 garbage-first heap   total 1105920K, used 295184K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 11 survivors (45056K)
 Metaspace       used 108997K, committed 110784K, reserved 983040K
  class space    used 15154K, committed 15936K, reserved 851968K
}
Event: 566.448 GC heap after
{Heap after GC invocations=71 (full 0):
 garbage-first heap   total 1105920K, used 292181K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 108997K, committed 110784K, reserved 983040K
  class space    used 15154K, committed 15936K, reserved 851968K
}
Event: 579.915 GC heap before
{Heap before GC invocations=71 (full 0):
 garbage-first heap   total 1105920K, used 390485K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 25 young (102400K), 1 survivors (4096K)
 Metaspace       used 109097K, committed 110976K, reserved 983040K
  class space    used 15168K, committed 16000K, reserved 851968K
}
Event: 579.954 GC heap after
{Heap after GC invocations=72 (full 0):
 garbage-first heap   total 1105920K, used 295764K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 109097K, committed 110976K, reserved 983040K
  class space    used 15168K, committed 16000K, reserved 851968K
}
Event: 618.107 GC heap before
{Heap before GC invocations=72 (full 0):
 garbage-first heap   total 1105920K, used 402260K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 29 young (118784K), 2 survivors (8192K)
 Metaspace       used 112861K, committed 114688K, reserved 983040K
  class space    used 15735K, committed 16576K, reserved 851968K
}
Event: 618.193 GC heap after
{Heap after GC invocations=73 (full 0):
 garbage-first heap   total 1105920K, used 302528K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 112861K, committed 114688K, reserved 983040K
  class space    used 15735K, committed 16576K, reserved 851968K
}
Event: 651.643 GC heap before
{Heap before GC invocations=73 (full 0):
 garbage-first heap   total 1105920K, used 400832K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 28 young (114688K), 4 survivors (16384K)
 Metaspace       used 115440K, committed 117376K, reserved 983040K
  class space    used 16013K, committed 16896K, reserved 851968K
}
Event: 651.760 GC heap after
{Heap after GC invocations=74 (full 0):
 garbage-first heap   total 1105920K, used 321515K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 115440K, committed 117376K, reserved 983040K
  class space    used 16013K, committed 16896K, reserved 851968K
}
Event: 659.619 GC heap before
{Heap before GC invocations=74 (full 0):
 garbage-first heap   total 1105920K, used 391147K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 21 young (86016K), 4 survivors (16384K)
 Metaspace       used 116683K, committed 118656K, reserved 983040K
  class space    used 16183K, committed 17088K, reserved 851968K
}
Event: 659.705 GC heap after
{Heap after GC invocations=75 (full 0):
 garbage-first heap   total 1105920K, used 334716K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 116683K, committed 118656K, reserved 983040K
  class space    used 16183K, committed 17088K, reserved 851968K
}
Event: 684.811 GC heap before
{Heap before GC invocations=75 (full 0):
 garbage-first heap   total 1105920K, used 465788K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 35 young (143360K), 3 survivors (12288K)
 Metaspace       used 117378K, committed 119488K, reserved 983040K
  class space    used 16267K, committed 17216K, reserved 851968K
}
Event: 684.936 GC heap after
{Heap after GC invocations=76 (full 0):
 garbage-first heap   total 1105920K, used 348059K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 117378K, committed 119488K, reserved 983040K
  class space    used 16267K, committed 17216K, reserved 851968K
}
Event: 694.194 GC heap before
{Heap before GC invocations=76 (full 0):
 garbage-first heap   total 1105920K, used 479131K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 36 young (147456K), 4 survivors (16384K)
 Metaspace       used 118424K, committed 120576K, reserved 983040K
  class space    used 16404K, committed 17408K, reserved 851968K
}
Event: 694.310 GC heap after
{Heap after GC invocations=77 (full 0):
 garbage-first heap   total 1105920K, used 364551K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 118424K, committed 120576K, reserved 983040K
  class space    used 16404K, committed 17408K, reserved 851968K
}
Event: 712.106 GC heap before
{Heap before GC invocations=77 (full 0):
 garbage-first heap   total 1105920K, used 491527K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 37 young (151552K), 5 survivors (20480K)
 Metaspace       used 118680K, committed 120832K, reserved 983040K
  class space    used 16428K, committed 17408K, reserved 851968K
}
Event: 712.189 GC heap after
{Heap after GC invocations=78 (full 0):
 garbage-first heap   total 1105920K, used 373091K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 118680K, committed 120832K, reserved 983040K
  class space    used 16428K, committed 17408K, reserved 851968K
}
Event: 768.150 GC heap before
{Heap before GC invocations=78 (full 0):
 garbage-first heap   total 1105920K, used 610659K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 62 young (253952K), 3 survivors (12288K)
 Metaspace       used 118960K, committed 121152K, reserved 983040K
  class space    used 16442K, committed 17408K, reserved 851968K
}
Event: 768.211 GC heap after
{Heap after GC invocations=79 (full 0):
 garbage-first heap   total 1105920K, used 373501K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 118960K, committed 121152K, reserved 983040K
  class space    used 16442K, committed 17408K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.125 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.174 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 1.262 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 1.265 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 1.336 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 1.339 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 1.347 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 3.481 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 8.757 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 9.731 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 9.744 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 21.121 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 21.127 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 23.034 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 24.941 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 31.187 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform8710997025584462282dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 768.507 Thread 0x0000023774f279b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023755c1a6a4 relative=0x0000000000000164
Event: 768.507 Thread 0x0000023774f279b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023755c1a6a4 method=org.gradle.internal.snapshot.impl.EnumValueSnapshot.equals(Ljava/lang/Object;)Z @ 25 c2
Event: 768.507 Thread 0x0000023774f279b0 DEOPT PACKING pc=0x0000023755c1a6a4 sp=0x000000eb427fcae0
Event: 768.507 Thread 0x0000023774f279b0 DEOPT UNPACKING pc=0x00000237551569a3 sp=0x000000eb427fca78 mode 2
Event: 768.507 Thread 0x0000023774f279b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000023755c1a6a4 relative=0x0000000000000164
Event: 768.507 Thread 0x0000023774f279b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000023755c1a6a4 method=org.gradle.internal.snapshot.impl.EnumValueSnapshot.equals(Ljava/lang/Object;)Z @ 25 c2
Event: 768.507 Thread 0x0000023774f279b0 DEOPT PACKING pc=0x0000023755c1a6a4 sp=0x000000eb427fcae0
Event: 768.508 Thread 0x0000023774f279b0 DEOPT UNPACKING pc=0x00000237551569a3 sp=0x000000eb427fca78 mode 2
Event: 777.139 Thread 0x0000023774f279b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002375655d190 relative=0x0000000000000750
Event: 777.139 Thread 0x0000023774f279b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002375655d190 method=org.gradle.api.internal.artifacts.transform.AbstractTransformedArtifactSet.lambda$new$0(Lcom/google/common/collect/ImmutableList$Builder;Lorg/gradle/api/internal/artifac
Event: 777.139 Thread 0x0000023774f279b0 DEOPT PACKING pc=0x000002375655d190 sp=0x000000eb427fbd20
Event: 777.139 Thread 0x0000023774f279b0 DEOPT UNPACKING pc=0x00000237551569a3 sp=0x000000eb427fbc68 mode 2
Event: 777.140 Thread 0x0000023774f279b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002375655d190 relative=0x0000000000000750
Event: 777.140 Thread 0x0000023774f279b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002375655d190 method=org.gradle.api.internal.artifacts.transform.AbstractTransformedArtifactSet.lambda$new$0(Lcom/google/common/collect/ImmutableList$Builder;Lorg/gradle/api/internal/artifac
Event: 777.140 Thread 0x0000023774f279b0 DEOPT PACKING pc=0x000002375655d190 sp=0x000000eb427fc0a0
Event: 777.140 Thread 0x0000023774f279b0 DEOPT UNPACKING pc=0x00000237551569a3 sp=0x000000eb427fbfe8 mode 2
Event: 777.147 Thread 0x0000023774f279b0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002375655c21c relative=0x00000000000006dc
Event: 777.147 Thread 0x0000023774f279b0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002375655c21c method=org.gradle.api.internal.artifacts.transform.AbstractTransformedArtifactSet.lambda$new$0(Lcom/google/common/collect/ImmutableList$Builder;Lorg/gradle/api/internal/artifac
Event: 777.147 Thread 0x0000023774f279b0 DEOPT PACKING pc=0x000002375655c21c sp=0x000000eb427fb900
Event: 777.147 Thread 0x0000023774f279b0 DEOPT UNPACKING pc=0x00000237551569a3 sp=0x000000eb427fb8c0 mode 2

Classes loaded (20 events):
Event: 634.663 Loading class java/io/CharArrayWriter
Event: 634.665 Loading class java/io/CharArrayWriter done
Event: 634.931 Loading class java/io/SequenceInputStream
Event: 634.931 Loading class java/io/SequenceInputStream done
Event: 634.967 Loading class java/util/function/IntUnaryOperator
Event: 634.968 Loading class java/util/function/IntUnaryOperator done
Event: 691.175 Loading class java/util/HashSetBeanInfo
Event: 691.175 Loading class java/util/HashSetBeanInfo done
Event: 691.175 Loading class java/util/HashSetBeanInfo
Event: 691.175 Loading class java/util/HashSetBeanInfo done
Event: 691.176 Loading class java/util/HashSetCustomizer
Event: 691.176 Loading class java/util/HashSetCustomizer done
Event: 691.176 Loading class java/util/HashSetCustomizer
Event: 691.176 Loading class java/util/HashSetCustomizer done
Event: 692.810 Loading class java/util/HashMap$UnsafeHolder
Event: 692.810 Loading class java/util/HashMap$UnsafeHolder done
Event: 697.260 Loading class java/nio/file/FileTreeWalker$1
Event: 697.260 Loading class java/nio/file/FileTreeWalker$1 done
Event: 722.862 Loading class java/time/Instant$1
Event: 722.864 Loading class java/time/Instant$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 726.792 Thread 0x0000023774f26f90 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b8af8040}> (0x00000006b8af8040) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 726.828 Thread 0x0000023774f26f90 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b8b98698}> (0x00000006b8b98698) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 726.866 Thread 0x0000023774f26f90 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b84629b0}> (0x00000006b84629b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 726.898 Thread 0x0000023774f26f90 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b85150c8}> (0x00000006b85150c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 726.928 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b858d6a8}> (0x00000006b858d6a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 726.966 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b8619a10}> (0x00000006b8619a10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 727.030 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b86ce3b8}> (0x00000006b86ce3b8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 727.083 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b879e770}> (0x00000006b879e770) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 727.164 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b80c3ca0}> (0x00000006b80c3ca0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 727.537 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b81cdbf8}> (0x00000006b81cdbf8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 732.752 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b839d5b0}> (0x00000006b839d5b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 737.159 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b7ed87c8}> (0x00000006b7ed87c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 740.781 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b7997aa0}> (0x00000006b7997aa0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 746.822 Thread 0x000002377eedb0c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b748a3f0}> (0x00000006b748a3f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 777.965 Thread 0x0000023774f26a80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006bdde1ed8}: static Lcom/android/build/gradle/internal/scope/InternalArtifactType;.<clinit>()V> (0x00000006bdde1ed8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 777.965 Thread 0x0000023774f26a80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006bdde3620}: static Lcom/android/build/gradle/internal/scope/InternalArtifactType;.<clinit>()V> (0x00000006bdde3620) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 777.965 Thread 0x0000023774f26a80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006bdde67b0}: static Lcom/android/build/api/artifact/Artifact$Single;.<clinit>()V> (0x00000006bdde67b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 777.965 Thread 0x0000023774f26a80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006bdde8d28}: static Ljava/lang/Object;.<clinit>()V> (0x00000006bdde8d28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 777.968 Thread 0x0000023774f26a80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006bde018d8}: static Lcom/android/build/api/artifact/ArtifactKind;.<clinit>()V> (0x00000006bde018d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 777.969 Thread 0x0000023774f26a80 Exception <a 'java/lang/NoSuchMethodError'{0x00000006bde02a88}: static Lcom/android/build/api/artifact/ArtifactKind;.<clinit>()V> (0x00000006bde02a88) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]

VM Operations (20 events):
Event: 768.150 Executing VM operation: G1CollectForAllocation
Event: 768.215 Executing VM operation: G1CollectForAllocation done
Event: 769.218 Executing VM operation: Cleanup
Event: 769.218 Executing VM operation: Cleanup done
Event: 771.238 Executing VM operation: Cleanup
Event: 771.625 Executing VM operation: Cleanup done
Event: 772.626 Executing VM operation: Cleanup
Event: 773.239 Executing VM operation: Cleanup done
Event: 774.250 Executing VM operation: Cleanup
Event: 774.730 Executing VM operation: Cleanup done
Event: 776.750 Executing VM operation: Cleanup
Event: 776.750 Executing VM operation: Cleanup done
Event: 777.758 Executing VM operation: Cleanup
Event: 777.758 Executing VM operation: Cleanup done
Event: 778.000 Executing VM operation: HandshakeAllThreads
Event: 778.000 Executing VM operation: HandshakeAllThreads done
Event: 779.005 Executing VM operation: Cleanup
Event: 779.005 Executing VM operation: Cleanup done
Event: 780.017 Executing VM operation: Cleanup
Event: 780.017 Executing VM operation: Cleanup done

Events (20 events):
Event: 694.366 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f54a710
Event: 694.367 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f5f3690
Event: 694.367 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f634f10
Event: 694.368 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f6b7090
Event: 694.369 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f86b910
Event: 694.369 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f870290
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f8e9790
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f8ea490
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f8ed390
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f8ef290
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f8fbe90
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f909010
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f91c790
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f942110
Event: 694.370 Thread 0x000002376d4fea30 flushing nmethod 0x000002374f991a90
Event: 694.372 Thread 0x000002376d4fea30 flushing nmethod 0x000002374fb53b90
Event: 694.372 Thread 0x000002376d4fea30 flushing nmethod 0x000002374fb54410
Event: 725.712 Thread 0x0000023774f29810 Thread exited: 0x0000023774f29810
Event: 767.924 Thread 0x00000237355375c0 Thread added: 0x00000237355375c0
Event: 768.224 Thread 0x00000237355375c0 Thread exited: 0x00000237355375c0


Dynamic libraries:
0x00007ff603630000 - 0x00007ff60363e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffacb2f0000 - 0x00007ffacb507000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffac9a70000 - 0x00007ffac9b34000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffac8be0000 - 0x00007ffac8fb1000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffac84d0000 - 0x00007ffac85e1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffaaeea0000 - 0x00007ffaaeeb7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffac9b60000 - 0x00007ffac9d11000 	C:\Windows\System32\USER32.dll
0x00007ffac8ac0000 - 0x00007ffac8ae6000 	C:\Windows\System32\win32u.dll
0x00007ffacb280000 - 0x00007ffacb2a9000 	C:\Windows\System32\GDI32.dll
0x00007ffac85f0000 - 0x00007ffac870b000 	C:\Windows\System32\gdi32full.dll
0x00007ffac8430000 - 0x00007ffac84ca000 	C:\Windows\System32\msvcp_win.dll
0x00007ffaaed30000 - 0x00007ffaaed4d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffab0c00000 - 0x00007ffab0e92000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffacb170000 - 0x00007ffacb217000 	C:\Windows\System32\msvcrt.dll
0x00007ffacb220000 - 0x00007ffacb251000 	C:\Windows\System32\IMM32.DLL
0x00007ffabf040000 - 0x00007ffabf04c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffabdda0000 - 0x00007ffabde2d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffa17d70000 - 0x00007ffa189e0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffac96e0000 - 0x00007ffac9791000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffac9d20000 - 0x00007ffac9dc7000 	C:\Windows\System32\sechost.dll
0x00007ffac8af0000 - 0x00007ffac8b18000 	C:\Windows\System32\bcrypt.dll
0x00007ffacb050000 - 0x00007ffacb164000 	C:\Windows\System32\RPCRT4.dll
0x00007ffac9130000 - 0x00007ffac91a1000 	C:\Windows\System32\WS2_32.dll
0x00007ffac8300000 - 0x00007ffac834d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffabdc00000 - 0x00007ffabdc34000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffabc100000 - 0x00007ffabc10a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffac82e0000 - 0x00007ffac82f3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffac7440000 - 0x00007ffac7458000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffabeaa0000 - 0x00007ffabeaaa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffac2e00000 - 0x00007ffac3032000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffac92c0000 - 0x00007ffac9650000 	C:\Windows\System32\combase.dll
0x00007ffac9990000 - 0x00007ffac9a67000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffaa95d0000 - 0x00007ffaa9602000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffac8a40000 - 0x00007ffac8abb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffac1190000 - 0x00007ffac119e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffaaded0000 - 0x00007ffaadef5000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffaad6b0000 - 0x00007ffaad787000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffaca760000 - 0x00007ffacafe8000 	C:\Windows\System32\SHELL32.dll
0x00007ffac8880000 - 0x00007ffac89bf000 	C:\Windows\System32\wintypes.dll
0x00007ffac6340000 - 0x00007ffac6c4d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffac9020000 - 0x00007ffac912a000 	C:\Windows\System32\SHCORE.dll
0x00007ffacaff0000 - 0x00007ffacb04e000 	C:\Windows\System32\shlwapi.dll
0x00007ffac8360000 - 0x00007ffac838b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffaad690000 - 0x00007ffaad6a8000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffaaeb80000 - 0x00007ffaaeb9a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffabe3f0000 - 0x00007ffabe51c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffac78a0000 - 0x00007ffac790a000 	C:\Windows\system32\mswsock.dll
0x00007ffaae4d0000 - 0x00007ffaae4e6000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffabdd90000 - 0x00007ffabdda0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffaad1e0000 - 0x00007ffaad207000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000005ace0000 - 0x000000005ad53000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffab9cf0000 - 0x00007ffab9cfa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffab0ed0000 - 0x00007ffab0edb000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffac97a0000 - 0x00007ffac97a8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffac7bf0000 - 0x00007ffac7c0b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffac73a0000 - 0x00007ffac73d7000 	C:\Windows\system32\rsaenh.dll
0x00007ffac7940000 - 0x00007ffac7968000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffac7bd0000 - 0x00007ffac7bdc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffac6eb0000 - 0x00007ffac6edd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffac9dd0000 - 0x00007ffac9dd9000 	C:\Windows\System32\NSI.dll
0x00007ffabe730000 - 0x00007ffabe749000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffabe010000 - 0x00007ffabe02f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffac6f20000 - 0x00007ffac7022000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffac11a0000 - 0x00007ffac11a9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffac1180000 - 0x00007ffac118e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffac8710000 - 0x00007ffac8876000 	C:\Windows\System32\CRYPT32.dll
0x00007ffac7d00000 - 0x00007ffac7d2d000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffac7cc0000 - 0x00007ffac7cf7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffabf6d0000 - 0x00007ffabf6d8000 	C:\Windows\system32\wshunix.dll
0x000000005aa60000 - 0x000000005aad3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform8710997025584462282dir\gradle-fileevents.dll
0x00007ffaaa3d0000 - 0x00007ffaaa3e7000 	C:\Windows\system32\napinsp.dll
0x00007ffaaa000000 - 0x00007ffaaa01b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffaa7ef0000 - 0x00007ffaa7f01000 	C:\Windows\System32\winrnr.dll
0x00007ffabd040000 - 0x00007ffabd055000 	C:\Windows\system32\wshbth.dll
0x00007ffaa1bc0000 - 0x00007ffaa1be7000 	C:\Windows\system32\nlansp_c.dll
0x00007ffab2af0000 - 0x00007ffab2afa000 	C:\Windows\System32\rasadhlp.dll
0x00007ffaba010000 - 0x00007ffaba093000 	C:\Windows\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform8710997025584462282dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 0:36 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (493M free)
TotalPageFile size 26992M (AvailPageFile size 10M)
current process WorkingSet (physical memory assigned to process): 503M, peak: 690M
current process commit charge ("private bytes"): 1523M, peak: 1540M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
