#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1097024 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=19424, tid=19480
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Mon May  5 19:41:56 2025 W. Central Africa Standard Time elapsed time: 782.401405 seconds (0d 0h 13m 2s)

---------------  T H R E A D  ---------------

Current thread (0x0000017f2db8f340):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=19480, stack(0x000000327d200000,0x000000327d300000)]


Current CompileTask:
C2: 782401 22318   !   4       com.google.common.cache.LocalCache$Segment::put (439 bytes)

Stack: [0x000000327d200000,0x000000327d300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad0a4]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0x36a44e]
V  [jvm.dll+0x334b83]
V  [jvm.dll+0x33401a]
V  [jvm.dll+0x21a538]
V  [jvm.dll+0x21996f]
V  [jvm.dll+0x1a53e6]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000017f74198f60, length=92, elements={
0x0000017f0679e380, 0x0000017f2db75f20, 0x0000017f2db76da0, 0x0000017f2db874d0,
0x0000017f2db89200, 0x0000017f2db8bbd0, 0x0000017f2db8c590, 0x0000017f2db8f340,
0x0000017f2db91e00, 0x0000017f2dc08050, 0x0000017f2dc8f140, 0x0000017f2dde3220,
0x0000017f67909ae0, 0x0000017f671336a0, 0x0000017f2df84910, 0x0000017f2df87dd0,
0x0000017f68409450, 0x0000017f2de4b590, 0x0000017f2de4baa0, 0x0000017f2de4c9d0,
0x0000017f2de49220, 0x0000017f2de49730, 0x0000017f2de4ab70, 0x0000017f2de4b080,
0x0000017f2de4bfb0, 0x0000017f2de4a660, 0x0000017f2de4c4c0, 0x0000017f6973ab50,
0x0000017f69739c20, 0x0000017f69736980, 0x0000017f6973c9b0, 0x0000017f697378b0,
0x0000017f6973a130, 0x0000017f69735a50, 0x0000017f69735f60, 0x0000017f697382d0,
0x0000017f6973d3d0, 0x0000017f6973a640, 0x0000017f6973b060, 0x0000017f697387e0,
0x0000017f69736470, 0x0000017f69736e90, 0x0000017f69739200, 0x0000017f697373a0,
0x0000017f6973ba80, 0x0000017f6973bf90, 0x0000017f69737dc0, 0x0000017f69738cf0,
0x0000017f69739710, 0x0000017f69deb720, 0x0000017f69de8ea0, 0x0000017f69def3e0,
0x0000017f69dec650, 0x0000017f69df0310, 0x0000017f69dea7f0, 0x0000017f69de93b0,
0x0000017f69de98c0, 0x0000017f69dead00, 0x0000017f69decb60, 0x0000017f69deb210,
0x0000017f69ded580, 0x0000017f69deda90, 0x0000017f69debc30, 0x0000017f69dec140,
0x0000017f69dedfa0, 0x0000017f69dee4b0, 0x0000017f69dee9c0, 0x0000017f69deeed0,
0x0000017f2de4a150, 0x0000017f6ac7f320, 0x0000017f6ac80c70, 0x0000017f6ac7f830,
0x0000017f6ac7bb70, 0x0000017f6ac7a220, 0x0000017f6ac7fd40, 0x0000017f6ac80250,
0x0000017f6ac7caa0, 0x0000017f6ac7ac40, 0x0000017f6ac7b150, 0x0000017f6ac7b660,
0x0000017f6ac7d4c0, 0x0000017f6ac7cfb0, 0x0000017f6ac80760, 0x0000017f6ac7c590,
0x0000017f6ac7c080, 0x0000017f6ac81690, 0x0000017f6ac7dee0, 0x0000017f70064db0,
0x0000017f70063970, 0x0000017f700652c0, 0x0000017f700657d0, 0x0000017f7407e2d0
}

Java Threads: ( => current thread )
  0x0000017f0679e380 JavaThread "main" [_thread_blocked, id=14468, stack(0x000000327c500000,0x000000327c600000)]
  0x0000017f2db75f20 JavaThread "Reference Handler" daemon [_thread_blocked, id=11496, stack(0x000000327cc00000,0x000000327cd00000)]
  0x0000017f2db76da0 JavaThread "Finalizer" daemon [_thread_blocked, id=19420, stack(0x000000327cd00000,0x000000327ce00000)]
  0x0000017f2db874d0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=19464, stack(0x000000327ce00000,0x000000327cf00000)]
  0x0000017f2db89200 JavaThread "Attach Listener" daemon [_thread_blocked, id=19468, stack(0x000000327cf00000,0x000000327d000000)]
  0x0000017f2db8bbd0 JavaThread "Service Thread" daemon [_thread_blocked, id=19472, stack(0x000000327d000000,0x000000327d100000)]
  0x0000017f2db8c590 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=19476, stack(0x000000327d100000,0x000000327d200000)]
=>0x0000017f2db8f340 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=19480, stack(0x000000327d200000,0x000000327d300000)]
  0x0000017f2db91e00 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=19492, stack(0x000000327d300000,0x000000327d400000)]
  0x0000017f2dc08050 JavaThread "Sweeper thread" daemon [_thread_blocked, id=19496, stack(0x000000327d400000,0x000000327d500000)]
  0x0000017f2dc8f140 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=19536, stack(0x000000327d500000,0x000000327d600000)]
  0x0000017f2dde3220 JavaThread "Notification Thread" daemon [_thread_blocked, id=19568, stack(0x000000327d600000,0x000000327d700000)]
  0x0000017f67909ae0 JavaThread "Daemon health stats" [_thread_blocked, id=20440, stack(0x000000327db00000,0x000000327dc00000)]
  0x0000017f671336a0 JavaThread "Incoming local TCP Connector on port 50745" [_thread_in_native, id=19972, stack(0x000000327dc00000,0x000000327dd00000)]
  0x0000017f2df84910 JavaThread "Daemon periodic checks" [_thread_blocked, id=19868, stack(0x000000327dd00000,0x000000327de00000)]
  0x0000017f2df87dd0 JavaThread "Daemon" [_thread_blocked, id=19888, stack(0x000000327df00000,0x000000327e000000)]
  0x0000017f68409450 JavaThread "Handler for socket connection from /127.0.0.1:50745 to /127.0.0.1:50746" [_thread_in_native, id=19900, stack(0x000000327e000000,0x000000327e100000)]
  0x0000017f2de4b590 JavaThread "Cancel handler" [_thread_blocked, id=19992, stack(0x000000327e100000,0x000000327e200000)]
  0x0000017f2de4baa0 JavaThread "Daemon worker" [_thread_blocked, id=7792, stack(0x000000327e200000,0x000000327e300000)]
  0x0000017f2de4c9d0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50745 to /127.0.0.1:50746" [_thread_blocked, id=12536, stack(0x000000327e300000,0x000000327e400000)]
  0x0000017f2de49220 JavaThread "Stdin handler" [_thread_blocked, id=2752, stack(0x000000327e400000,0x000000327e500000)]
  0x0000017f2de49730 JavaThread "Daemon client event forwarder" [_thread_blocked, id=20108, stack(0x000000327e500000,0x000000327e600000)]
  0x0000017f2de4ab70 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=19092, stack(0x000000327e600000,0x000000327e700000)]
  0x0000017f2de4b080 JavaThread "File lock request listener" [_thread_in_native, id=18360, stack(0x000000327e700000,0x000000327e800000)]
  0x0000017f2de4bfb0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=19220, stack(0x000000327e800000,0x000000327e900000)]
  0x0000017f2de4a660 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=5680, stack(0x000000327ea00000,0x000000327eb00000)]
  0x0000017f2de4c4c0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=10540, stack(0x000000327eb00000,0x000000327ec00000)]
  0x0000017f6973ab50 JavaThread "File watcher server" daemon [_thread_in_native, id=4600, stack(0x000000327ec00000,0x000000327ed00000)]
  0x0000017f69739c20 JavaThread "File watcher consumer" daemon [_thread_blocked, id=8368, stack(0x000000327ed00000,0x000000327ee00000)]
  0x0000017f69736980 JavaThread "jar transforms" [_thread_blocked, id=7428, stack(0x000000327f100000,0x000000327f200000)]
  0x0000017f6973c9b0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=18104, stack(0x000000327f200000,0x000000327f300000)]
  0x0000017f697378b0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=4240, stack(0x000000327f300000,0x000000327f400000)]
  0x0000017f6973a130 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=10104, stack(0x000000327f400000,0x000000327f500000)]
  0x0000017f69735a50 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=20216, stack(0x000000327f500000,0x000000327f600000)]
  0x0000017f69735f60 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=11884, stack(0x000000327f600000,0x000000327f700000)]
  0x0000017f697382d0 JavaThread "Unconstrained build operations" [_thread_blocked, id=20124, stack(0x000000327f700000,0x000000327f800000)]
  0x0000017f6973d3d0 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=19804, stack(0x000000327f800000,0x000000327f900000)]
  0x0000017f6973a640 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=14936, stack(0x000000327f900000,0x000000327fa00000)]
  0x0000017f6973b060 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=10772, stack(0x000000327fa00000,0x000000327fb00000)]
  0x0000017f697387e0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=4788, stack(0x000000327fb00000,0x000000327fc00000)]
  0x0000017f69736470 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=8880, stack(0x000000327fc00000,0x000000327fd00000)]
  0x0000017f69736e90 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=5568, stack(0x000000327c300000,0x000000327c400000)]
  0x0000017f69739200 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=6220, stack(0x000000327c400000,0x000000327c500000)]
  0x0000017f697373a0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=6032, stack(0x000000327de00000,0x000000327df00000)]
  0x0000017f6973ba80 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=1100, stack(0x000000327fd00000,0x000000327fe00000)]
  0x0000017f6973bf90 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=1200, stack(0x000000327fe00000,0x000000327ff00000)]
  0x0000017f69737dc0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=11516, stack(0x000000327ff00000,0x0000003280000000)]
  0x0000017f69738cf0 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=20364, stack(0x0000003200000000,0x0000003200100000)]
  0x0000017f69739710 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=2420, stack(0x0000003200100000,0x0000003200200000)]
  0x0000017f69deb720 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=17732, stack(0x0000003200200000,0x0000003200300000)]
  0x0000017f69de8ea0 JavaThread "Memory manager" [_thread_blocked, id=17812, stack(0x0000003200300000,0x0000003200400000)]
  0x0000017f69def3e0 JavaThread "build event listener" [_thread_blocked, id=2432, stack(0x0000003200400000,0x0000003200500000)]
  0x0000017f69dec650 JavaThread "Execution worker" [_thread_blocked, id=21956, stack(0x0000003200900000,0x0000003200a00000)]
  0x0000017f69df0310 JavaThread "Execution worker Thread 2" [_thread_blocked, id=21964, stack(0x0000003200a00000,0x0000003200b00000)]
  0x0000017f69dea7f0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=21968, stack(0x0000003200b00000,0x0000003200c00000)]
  0x0000017f69de93b0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=21972, stack(0x0000003200c00000,0x0000003200d00000)]
  0x0000017f69de98c0 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=21992, stack(0x0000003200d00000,0x0000003200e00000)]
  0x0000017f69dead00 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=21996, stack(0x0000003200e00000,0x0000003200f00000)]
  0x0000017f69decb60 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=22000, stack(0x0000003200f00000,0x0000003201000000)]
  0x0000017f69deb210 JavaThread "File lock release action executor Thread 2" [_thread_blocked, id=22024, stack(0x0000003201000000,0x0000003201100000)]
  0x0000017f69ded580 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=22028, stack(0x0000003201100000,0x0000003201200000)]
  0x0000017f69deda90 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=22032, stack(0x0000003201200000,0x0000003201300000)]
  0x0000017f69debc30 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=22036, stack(0x0000003201300000,0x0000003201400000)]
  0x0000017f69dec140 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=22040, stack(0x0000003201400000,0x0000003201500000)]
  0x0000017f69dedfa0 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=22044, stack(0x0000003201500000,0x0000003201600000)]
  0x0000017f69dee4b0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=22064, stack(0x0000003201600000,0x0000003201700000)]
  0x0000017f69dee9c0 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=22068, stack(0x0000003201700000,0x0000003201800000)]
  0x0000017f69deeed0 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=22072, stack(0x0000003201800000,0x0000003201900000)]
  0x0000017f2de4a150 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=22076, stack(0x0000003201900000,0x0000003201a00000)]
  0x0000017f6ac7f320 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=22124, stack(0x0000003201a00000,0x0000003201b00000)]
  0x0000017f6ac80c70 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=22128, stack(0x0000003201b00000,0x0000003201c00000)]
  0x0000017f6ac7f830 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=22132, stack(0x0000003201c00000,0x0000003201d00000)]
  0x0000017f6ac7bb70 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=22140, stack(0x0000003201d00000,0x0000003201e00000)]
  0x0000017f6ac7a220 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=22144, stack(0x0000003201e00000,0x0000003201f00000)]
  0x0000017f6ac7fd40 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=22148, stack(0x0000003201f00000,0x0000003202000000)]
  0x0000017f6ac80250 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=22156, stack(0x0000003202000000,0x0000003202100000)]
  0x0000017f6ac7caa0 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=22160, stack(0x0000003202100000,0x0000003202200000)]
  0x0000017f6ac7ac40 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=22164, stack(0x0000003202200000,0x0000003202300000)]
  0x0000017f6ac7b150 JavaThread "jar transforms Thread 2" [_thread_blocked, id=22184, stack(0x0000003202300000,0x0000003202400000)]
  0x0000017f6ac7b660 JavaThread "jar transforms Thread 3" [_thread_blocked, id=22448, stack(0x0000003202600000,0x0000003202700000)]
  0x0000017f6ac7d4c0 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=22524, stack(0x0000003202700000,0x0000003202800000)]
  0x0000017f6ac7cfb0 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=19956, stack(0x0000003202800000,0x0000003202900000)]
  0x0000017f6ac80760 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=5316, stack(0x0000003202900000,0x0000003202a00000)]
  0x0000017f6ac7c590 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=4564, stack(0x0000003202a00000,0x0000003202b00000)]
  0x0000017f6ac7c080 JavaThread "jar transforms Thread 4" [_thread_blocked, id=20960, stack(0x0000003202c00000,0x0000003202d00000)]
  0x0000017f6ac81690 JavaThread "build event listener" [_thread_blocked, id=19088, stack(0x0000003200500000,0x0000003200600000)]
  0x0000017f6ac7dee0 JavaThread "Problems report writer" [_thread_blocked, id=740, stack(0x0000003202b00000,0x0000003202c00000)]
  0x0000017f70064db0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=21508, stack(0x0000003200600000,0x0000003200700000)]
  0x0000017f70063970 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=6644, stack(0x0000003200800000,0x0000003200900000)]
  0x0000017f700652c0 JavaThread "included builds Thread 2" [_thread_blocked, id=17256, stack(0x0000003202d00000,0x0000003202e00000)]
  0x0000017f700657d0 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\executionHistory)" [_thread_blocked, id=13544, stack(0x0000003202e00000,0x0000003202f00000)]
  0x0000017f7407e2d0 JavaThread "C2 CompilerThread1" daemon [_thread_blocked, id=8904, stack(0x0000003200700000,0x0000003200800000)]

Other Threads:
  0x0000017f2db6e070 VMThread "VM Thread" [stack: 0x000000327cb00000,0x000000327cc00000] [id=18628]
  0x0000017f2dd4ffb0 WatcherThread [stack: 0x000000327d700000,0x000000327d800000] [id=19572]
  0x0000017f06826770 GCTaskThread "GC Thread#0" [stack: 0x000000327c600000,0x000000327c700000] [id=18952]
  0x0000017f2de60d80 GCTaskThread "GC Thread#1" [stack: 0x000000327d800000,0x000000327d900000] [id=19656]
  0x0000017f2de61040 GCTaskThread "GC Thread#2" [stack: 0x000000327d900000,0x000000327da00000] [id=19660]
  0x0000017f67e0bbb0 GCTaskThread "GC Thread#3" [stack: 0x000000327da00000,0x000000327db00000] [id=20384]
  0x0000017f068336c0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000327c700000,0x000000327c800000] [id=15456]
  0x0000017f068340e0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000327c800000,0x000000327c900000] [id=19152]
  0x0000017f0684e400 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000327c900000,0x000000327ca00000] [id=18976]
  0x0000017f69a11800 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000327f000000,0x000000327f100000] [id=10420]
  0x0000017f726a6e30 ConcurrentGCThread "G1 Refine#2" [stack: 0x0000003202400000,0x0000003202500000] [id=16816]
  0x0000017f2da2e150 ConcurrentGCThread "G1 Service" [stack: 0x000000327ca00000,0x000000327cb00000] [id=17192]

Threads with active compile tasks:
C2 CompilerThread0   782464 22318   !   4       com.google.common.cache.LocalCache$Segment::put (439 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000017f2e000000-0x0000017f2ebb0000-0x0000017f2ebb0000), size 12255232, SharedBaseAddress: 0x0000017f2e000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000017f2f000000-0x0000017f63000000, reserved size: 872415232
Narrow klass base: 0x0000017f2e000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 1024000K, used 572441K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 56 young (229376K), 3 survivors (12288K)
 Metaspace       used 119013K, committed 121216K, reserved 983040K
  class space    used 16450K, committed 17408K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HS|  |TAMS 0x0000000680400000, 0x0000000680000000| Complete 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%| O|  |TAMS 0x0000000680800000, 0x0000000680400000| Untracked 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%| O|  |TAMS 0x0000000681000000, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000682000000, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682400000, 0x0000000682400000|100%| O|  |TAMS 0x0000000682400000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682800000, 0x0000000682800000|100%| O|  |TAMS 0x0000000682800000, 0x0000000682400000| Untracked 
|  10|0x0000000682800000, 0x0000000682c00000, 0x0000000682c00000|100%|HS|  |TAMS 0x0000000682c00000, 0x0000000682800000| Complete 
|  11|0x0000000682c00000, 0x0000000683000000, 0x0000000683000000|100%| O|  |TAMS 0x0000000683000000, 0x0000000682c00000| Untracked 
|  12|0x0000000683000000, 0x0000000683400000, 0x0000000683400000|100%| O|  |TAMS 0x0000000683400000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x0000000683800000, 0x0000000683800000|100%|HS|  |TAMS 0x0000000683800000, 0x0000000683400000| Complete 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| O|  |TAMS 0x0000000683c00000, 0x0000000683800000| Untracked 
|  15|0x0000000683c00000, 0x0000000684000000, 0x0000000684000000|100%| O|  |TAMS 0x0000000684000000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684400000, 0x0000000684400000|100%| O|  |TAMS 0x0000000684400000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684800000, 0x0000000684800000|100%| O|  |TAMS 0x0000000684800000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684c00000, 0x0000000684c00000|100%|HS|  |TAMS 0x0000000684c00000, 0x0000000684800000| Complete 
|  19|0x0000000684c00000, 0x0000000685000000, 0x0000000685000000|100%| O|  |TAMS 0x0000000685000000, 0x0000000684c00000| Untracked 
|  20|0x0000000685000000, 0x0000000685400000, 0x0000000685400000|100%|HS|  |TAMS 0x0000000685400000, 0x0000000685000000| Complete 
|  21|0x0000000685400000, 0x0000000685800000, 0x0000000685800000|100%| O|  |TAMS 0x0000000685800000, 0x0000000685400000| Untracked 
|  22|0x0000000685800000, 0x0000000685c00000, 0x0000000685c00000|100%| O|  |TAMS 0x0000000685c00000, 0x0000000685800000| Untracked 
|  23|0x0000000685c00000, 0x0000000686000000, 0x0000000686000000|100%| O|  |TAMS 0x0000000686000000, 0x0000000685c00000| Untracked 
|  24|0x0000000686000000, 0x0000000686400000, 0x0000000686400000|100%| O|  |TAMS 0x0000000686400000, 0x0000000686000000| Untracked 
|  25|0x0000000686400000, 0x0000000686800000, 0x0000000686800000|100%| O|  |TAMS 0x0000000686800000, 0x0000000686400000| Untracked 
|  26|0x0000000686800000, 0x0000000686c00000, 0x0000000686c00000|100%| O|  |TAMS 0x0000000686c00000, 0x0000000686800000| Untracked 
|  27|0x0000000686c00000, 0x0000000687000000, 0x0000000687000000|100%| O|  |TAMS 0x0000000687000000, 0x0000000686c00000| Untracked 
|  28|0x0000000687000000, 0x0000000687400000, 0x0000000687400000|100%| O|  |TAMS 0x0000000687400000, 0x0000000687000000| Untracked 
|  29|0x0000000687400000, 0x0000000687800000, 0x0000000687800000|100%| O|  |TAMS 0x0000000687800000, 0x0000000687400000| Untracked 
|  30|0x0000000687800000, 0x0000000687c00000, 0x0000000687c00000|100%| O|  |TAMS 0x0000000687c00000, 0x0000000687800000| Untracked 
|  31|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%| O|  |TAMS 0x0000000688000000, 0x0000000687c00000| Untracked 
|  32|0x0000000688000000, 0x0000000688400000, 0x0000000688400000|100%| O|  |TAMS 0x0000000688400000, 0x0000000688000000| Untracked 
|  33|0x0000000688400000, 0x0000000688800000, 0x0000000688800000|100%| O|  |TAMS 0x0000000688800000, 0x0000000688400000| Untracked 
|  34|0x0000000688800000, 0x0000000688c00000, 0x0000000688c00000|100%| O|  |TAMS 0x0000000688c00000, 0x0000000688800000| Untracked 
|  35|0x0000000688c00000, 0x0000000689000000, 0x0000000689000000|100%| O|  |TAMS 0x0000000689000000, 0x0000000688c00000| Untracked 
|  36|0x0000000689000000, 0x0000000689400000, 0x0000000689400000|100%| O|  |TAMS 0x0000000689400000, 0x0000000689000000| Untracked 
|  37|0x0000000689400000, 0x0000000689800000, 0x0000000689800000|100%| O|  |TAMS 0x0000000689800000, 0x0000000689400000| Untracked 
|  38|0x0000000689800000, 0x0000000689c00000, 0x0000000689c00000|100%| O|  |TAMS 0x0000000689c00000, 0x0000000689800000| Untracked 
|  39|0x0000000689c00000, 0x000000068a000000, 0x000000068a000000|100%| O|  |TAMS 0x000000068a000000, 0x0000000689c00000| Untracked 
|  40|0x000000068a000000, 0x000000068a400000, 0x000000068a400000|100%| O|  |TAMS 0x000000068a400000, 0x000000068a000000| Untracked 
|  41|0x000000068a400000, 0x000000068a800000, 0x000000068a800000|100%| O|  |TAMS 0x000000068a800000, 0x000000068a400000| Untracked 
|  42|0x000000068a800000, 0x000000068ac00000, 0x000000068ac00000|100%| O|  |TAMS 0x000000068ac00000, 0x000000068a800000| Untracked 
|  43|0x000000068ac00000, 0x000000068b000000, 0x000000068b000000|100%| O|  |TAMS 0x000000068b000000, 0x000000068ac00000| Untracked 
|  44|0x000000068b000000, 0x000000068b400000, 0x000000068b400000|100%| O|  |TAMS 0x000000068b400000, 0x000000068b000000| Untracked 
|  45|0x000000068b400000, 0x000000068b800000, 0x000000068b800000|100%| O|  |TAMS 0x000000068b800000, 0x000000068b400000| Untracked 
|  46|0x000000068b800000, 0x000000068bc00000, 0x000000068bc00000|100%| O|  |TAMS 0x000000068bc00000, 0x000000068b800000| Untracked 
|  47|0x000000068bc00000, 0x000000068c000000, 0x000000068c000000|100%| O|  |TAMS 0x000000068c000000, 0x000000068bc00000| Untracked 
|  48|0x000000068c000000, 0x000000068c400000, 0x000000068c400000|100%| O|  |TAMS 0x000000068c400000, 0x000000068c000000| Untracked 
|  49|0x000000068c400000, 0x000000068c800000, 0x000000068c800000|100%| O|  |TAMS 0x000000068c800000, 0x000000068c400000| Untracked 
|  50|0x000000068c800000, 0x000000068cc00000, 0x000000068cc00000|100%| O|  |TAMS 0x000000068cc00000, 0x000000068c800000| Untracked 
|  51|0x000000068cc00000, 0x000000068d000000, 0x000000068d000000|100%| O|  |TAMS 0x000000068d000000, 0x000000068cc00000| Untracked 
|  52|0x000000068d000000, 0x000000068d400000, 0x000000068d400000|100%| O|  |TAMS 0x000000068d400000, 0x000000068d000000| Untracked 
|  53|0x000000068d400000, 0x000000068d800000, 0x000000068d800000|100%| O|  |TAMS 0x000000068d800000, 0x000000068d400000| Untracked 
|  54|0x000000068d800000, 0x000000068dc00000, 0x000000068dc00000|100%| O|  |TAMS 0x000000068dc00000, 0x000000068d800000| Untracked 
|  55|0x000000068dc00000, 0x000000068e000000, 0x000000068e000000|100%| O|  |TAMS 0x000000068e000000, 0x000000068dc00000| Untracked 
|  56|0x000000068e000000, 0x000000068e400000, 0x000000068e400000|100%| O|  |TAMS 0x000000068e400000, 0x000000068e000000| Untracked 
|  57|0x000000068e400000, 0x000000068e800000, 0x000000068e800000|100%| O|  |TAMS 0x000000068e800000, 0x000000068e400000| Untracked 
|  58|0x000000068e800000, 0x000000068ec00000, 0x000000068ec00000|100%| O|  |TAMS 0x000000068e800000, 0x000000068e800000| Untracked 
|  59|0x000000068ec00000, 0x000000068f000000, 0x000000068f000000|100%| O|  |TAMS 0x000000068f000000, 0x000000068ec00000| Untracked 
|  60|0x000000068f000000, 0x000000068f400000, 0x000000068f400000|100%| O|  |TAMS 0x000000068f400000, 0x000000068f000000| Untracked 
|  61|0x000000068f400000, 0x000000068f800000, 0x000000068f800000|100%| O|  |TAMS 0x000000068f800000, 0x000000068f400000| Untracked 
|  62|0x000000068f800000, 0x000000068fc00000, 0x000000068fc00000|100%| O|  |TAMS 0x000000068fc00000, 0x000000068f800000| Untracked 
|  63|0x000000068fc00000, 0x0000000690000000, 0x0000000690000000|100%| O|  |TAMS 0x0000000690000000, 0x000000068fc00000| Untracked 
|  64|0x0000000690000000, 0x0000000690400000, 0x0000000690400000|100%| O|  |TAMS 0x0000000690400000, 0x0000000690000000| Untracked 
|  65|0x0000000690400000, 0x0000000690800000, 0x0000000690800000|100%| O|  |TAMS 0x0000000690800000, 0x0000000690400000| Untracked 
|  66|0x0000000690800000, 0x0000000690c00000, 0x0000000690c00000|100%| O|  |TAMS 0x0000000690c00000, 0x0000000690800000| Untracked 
|  67|0x0000000690c00000, 0x0000000691000000, 0x0000000691000000|100%| O|  |TAMS 0x0000000690c00000, 0x0000000690c00000| Untracked 
|  68|0x0000000691000000, 0x0000000691400000, 0x0000000691400000|100%| O|  |TAMS 0x0000000691000000, 0x0000000691000000| Untracked 
|  69|0x0000000691400000, 0x0000000691800000, 0x0000000691800000|100%| O|  |TAMS 0x0000000691400000, 0x0000000691400000| Untracked 
|  70|0x0000000691800000, 0x0000000691c00000, 0x0000000691c00000|100%| O|  |TAMS 0x0000000691800000, 0x0000000691800000| Untracked 
|  71|0x0000000691c00000, 0x0000000692000000, 0x0000000692000000|100%| O|  |TAMS 0x0000000691c00000, 0x0000000691c00000| Untracked 
|  72|0x0000000692000000, 0x0000000692400000, 0x0000000692400000|100%| O|  |TAMS 0x0000000692000000, 0x0000000692000000| Untracked 
|  73|0x0000000692400000, 0x0000000692800000, 0x0000000692800000|100%| O|  |TAMS 0x0000000692400000, 0x0000000692400000| Untracked 
|  74|0x0000000692800000, 0x0000000692c00000, 0x0000000692c00000|100%| O|  |TAMS 0x0000000692800000, 0x0000000692800000| Untracked 
|  75|0x0000000692c00000, 0x0000000693000000, 0x0000000693000000|100%| O|  |TAMS 0x0000000692c00000, 0x0000000692c00000| Untracked 
|  76|0x0000000693000000, 0x0000000693400000, 0x0000000693400000|100%| O|  |TAMS 0x0000000693000000, 0x0000000693000000| Untracked 
|  77|0x0000000693400000, 0x0000000693800000, 0x0000000693800000|100%| O|  |TAMS 0x0000000693400000, 0x0000000693400000| Untracked 
|  78|0x0000000693800000, 0x0000000693c00000, 0x0000000693c00000|100%| O|  |TAMS 0x0000000693800000, 0x0000000693800000| Untracked 
|  79|0x0000000693c00000, 0x0000000694000000, 0x0000000694000000|100%| O|  |TAMS 0x0000000693c00000, 0x0000000693c00000| Untracked 
|  80|0x0000000694000000, 0x0000000694400000, 0x0000000694400000|100%| O|  |TAMS 0x0000000694000000, 0x0000000694000000| Untracked 
|  81|0x0000000694400000, 0x0000000694800000, 0x0000000694800000|100%| O|  |TAMS 0x0000000694400000, 0x0000000694400000| Untracked 
|  82|0x0000000694800000, 0x0000000694c00000, 0x0000000694c00000|100%| O|  |TAMS 0x0000000694800000, 0x0000000694800000| Untracked 
|  83|0x0000000694c00000, 0x0000000695000000, 0x0000000695000000|100%| O|  |TAMS 0x0000000694c00000, 0x0000000694c00000| Untracked 
|  84|0x0000000695000000, 0x0000000695400000, 0x0000000695400000|100%| O|  |TAMS 0x0000000695000000, 0x0000000695000000| Untracked 
|  85|0x0000000695400000, 0x00000006957cb400, 0x0000000695800000| 94%| O|  |TAMS 0x0000000695400000, 0x0000000695400000| Untracked 
|  86|0x0000000695800000, 0x0000000695800000, 0x0000000695c00000|  0%| F|  |TAMS 0x0000000695800000, 0x0000000695800000| Untracked 
|  87|0x0000000695c00000, 0x0000000695c00000, 0x0000000696000000|  0%| F|  |TAMS 0x0000000695c00000, 0x0000000695c00000| Untracked 
|  88|0x0000000696000000, 0x0000000696000000, 0x0000000696400000|  0%| F|  |TAMS 0x0000000696000000, 0x0000000696000000| Untracked 
|  89|0x0000000696400000, 0x0000000696400000, 0x0000000696800000|  0%| F|  |TAMS 0x0000000696400000, 0x0000000696400000| Untracked 
|  90|0x0000000696800000, 0x0000000696800000, 0x0000000696c00000|  0%| F|  |TAMS 0x0000000696800000, 0x0000000696800000| Untracked 
|  91|0x0000000696c00000, 0x0000000696c00000, 0x0000000697000000|  0%| F|  |TAMS 0x0000000696c00000, 0x0000000696c00000| Untracked 
|  92|0x0000000697000000, 0x0000000697000000, 0x0000000697400000|  0%| F|  |TAMS 0x0000000697000000, 0x0000000697000000| Untracked 
|  93|0x0000000697400000, 0x0000000697400000, 0x0000000697800000|  0%| F|  |TAMS 0x0000000697400000, 0x0000000697400000| Untracked 
|  94|0x0000000697800000, 0x0000000697800000, 0x0000000697c00000|  0%| F|  |TAMS 0x0000000697800000, 0x0000000697800000| Untracked 
|  95|0x0000000697c00000, 0x0000000697c00000, 0x0000000698000000|  0%| F|  |TAMS 0x0000000697c00000, 0x0000000697c00000| Untracked 
|  96|0x0000000698000000, 0x0000000698000000, 0x0000000698400000|  0%| F|  |TAMS 0x0000000698000000, 0x0000000698000000| Untracked 
|  97|0x0000000698400000, 0x0000000698400000, 0x0000000698800000|  0%| F|  |TAMS 0x0000000698400000, 0x0000000698400000| Untracked 
|  98|0x0000000698800000, 0x0000000698800000, 0x0000000698c00000|  0%| F|  |TAMS 0x0000000698800000, 0x0000000698800000| Untracked 
|  99|0x0000000698c00000, 0x0000000698c00000, 0x0000000699000000|  0%| F|  |TAMS 0x0000000698c00000, 0x0000000698c00000| Untracked 
| 100|0x0000000699000000, 0x0000000699000000, 0x0000000699400000|  0%| F|  |TAMS 0x0000000699000000, 0x0000000699000000| Untracked 
| 101|0x0000000699400000, 0x0000000699400000, 0x0000000699800000|  0%| F|  |TAMS 0x0000000699400000, 0x0000000699400000| Untracked 
| 102|0x0000000699800000, 0x0000000699800000, 0x0000000699c00000|  0%| F|  |TAMS 0x0000000699800000, 0x0000000699800000| Untracked 
| 103|0x0000000699c00000, 0x0000000699c00000, 0x000000069a000000|  0%| F|  |TAMS 0x0000000699c00000, 0x0000000699c00000| Untracked 
| 104|0x000000069a000000, 0x000000069a000000, 0x000000069a400000|  0%| F|  |TAMS 0x000000069a000000, 0x000000069a000000| Untracked 
| 105|0x000000069a400000, 0x000000069a400000, 0x000000069a800000|  0%| F|  |TAMS 0x000000069a400000, 0x000000069a400000| Untracked 
| 106|0x000000069a800000, 0x000000069a800000, 0x000000069ac00000|  0%| F|  |TAMS 0x000000069a800000, 0x000000069a800000| Untracked 
| 107|0x000000069ac00000, 0x000000069ac00000, 0x000000069b000000|  0%| F|  |TAMS 0x000000069ac00000, 0x000000069ac00000| Untracked 
| 108|0x000000069b000000, 0x000000069b000000, 0x000000069b400000|  0%| F|  |TAMS 0x000000069b000000, 0x000000069b000000| Untracked 
| 109|0x000000069b400000, 0x000000069b400000, 0x000000069b800000|  0%| F|  |TAMS 0x000000069b400000, 0x000000069b400000| Untracked 
| 110|0x000000069b800000, 0x000000069b800000, 0x000000069bc00000|  0%| F|  |TAMS 0x000000069b800000, 0x000000069b800000| Untracked 
| 111|0x000000069bc00000, 0x000000069bc00000, 0x000000069c000000|  0%| F|  |TAMS 0x000000069bc00000, 0x000000069bc00000| Untracked 
| 112|0x000000069c000000, 0x000000069c000000, 0x000000069c400000|  0%| F|  |TAMS 0x000000069c000000, 0x000000069c000000| Untracked 
| 113|0x000000069c400000, 0x000000069c400000, 0x000000069c800000|  0%| F|  |TAMS 0x000000069c400000, 0x000000069c400000| Untracked 
| 114|0x000000069c800000, 0x000000069c800000, 0x000000069cc00000|  0%| F|  |TAMS 0x000000069c800000, 0x000000069c800000| Untracked 
| 115|0x000000069cc00000, 0x000000069cc00000, 0x000000069d000000|  0%| F|  |TAMS 0x000000069cc00000, 0x000000069cc00000| Untracked 
| 116|0x000000069d000000, 0x000000069d000000, 0x000000069d400000|  0%| F|  |TAMS 0x000000069d000000, 0x000000069d000000| Untracked 
| 117|0x000000069d400000, 0x000000069d400000, 0x000000069d800000|  0%| F|  |TAMS 0x000000069d400000, 0x000000069d400000| Untracked 
| 118|0x000000069d800000, 0x000000069d800000, 0x000000069dc00000|  0%| F|  |TAMS 0x000000069d800000, 0x000000069d800000| Untracked 
| 119|0x000000069dc00000, 0x000000069dc00000, 0x000000069e000000|  0%| F|  |TAMS 0x000000069dc00000, 0x000000069dc00000| Untracked 
| 120|0x000000069e000000, 0x000000069e000000, 0x000000069e400000|  0%| F|  |TAMS 0x000000069e000000, 0x000000069e000000| Untracked 
| 121|0x000000069e400000, 0x000000069e400000, 0x000000069e800000|  0%| F|  |TAMS 0x000000069e400000, 0x000000069e400000| Untracked 
| 122|0x000000069e800000, 0x000000069e800000, 0x000000069ec00000|  0%| F|  |TAMS 0x000000069e800000, 0x000000069e800000| Untracked 
| 123|0x000000069ec00000, 0x000000069ec00000, 0x000000069f000000|  0%| F|  |TAMS 0x000000069ec00000, 0x000000069ec00000| Untracked 
| 124|0x000000069f000000, 0x000000069f000000, 0x000000069f400000|  0%| F|  |TAMS 0x000000069f000000, 0x000000069f000000| Untracked 
| 125|0x000000069f400000, 0x000000069f400000, 0x000000069f800000|  0%| F|  |TAMS 0x000000069f400000, 0x000000069f400000| Untracked 
| 126|0x000000069f800000, 0x000000069f800000, 0x000000069fc00000|  0%| F|  |TAMS 0x000000069f800000, 0x000000069f800000| Untracked 
| 127|0x000000069fc00000, 0x000000069fc00000, 0x00000006a0000000|  0%| F|  |TAMS 0x000000069fc00000, 0x000000069fc00000| Untracked 
| 128|0x00000006a0000000, 0x00000006a0000000, 0x00000006a0400000|  0%| F|  |TAMS 0x00000006a0000000, 0x00000006a0000000| Untracked 
| 129|0x00000006a0400000, 0x00000006a0400000, 0x00000006a0800000|  0%| F|  |TAMS 0x00000006a0400000, 0x00000006a0400000| Untracked 
| 130|0x00000006a0800000, 0x00000006a0800000, 0x00000006a0c00000|  0%| F|  |TAMS 0x00000006a0800000, 0x00000006a0800000| Untracked 
| 131|0x00000006a0c00000, 0x00000006a0c00000, 0x00000006a1000000|  0%| F|  |TAMS 0x00000006a0c00000, 0x00000006a0c00000| Untracked 
| 132|0x00000006a1000000, 0x00000006a1000000, 0x00000006a1400000|  0%| F|  |TAMS 0x00000006a1000000, 0x00000006a1000000| Untracked 
| 133|0x00000006a1400000, 0x00000006a1400000, 0x00000006a1800000|  0%| F|  |TAMS 0x00000006a1400000, 0x00000006a1400000| Untracked 
| 134|0x00000006a1800000, 0x00000006a1800000, 0x00000006a1c00000|  0%| F|  |TAMS 0x00000006a1800000, 0x00000006a1800000| Untracked 
| 135|0x00000006a1c00000, 0x00000006a1c00000, 0x00000006a2000000|  0%| F|  |TAMS 0x00000006a1c00000, 0x00000006a1c00000| Untracked 
| 136|0x00000006a2000000, 0x00000006a2000000, 0x00000006a2400000|  0%| F|  |TAMS 0x00000006a2000000, 0x00000006a2000000| Untracked 
| 137|0x00000006a2400000, 0x00000006a2400000, 0x00000006a2800000|  0%| F|  |TAMS 0x00000006a2400000, 0x00000006a2400000| Untracked 
| 138|0x00000006a2800000, 0x00000006a2800000, 0x00000006a2c00000|  0%| F|  |TAMS 0x00000006a2800000, 0x00000006a2800000| Untracked 
| 139|0x00000006a2c00000, 0x00000006a2c00000, 0x00000006a3000000|  0%| F|  |TAMS 0x00000006a2c00000, 0x00000006a2c00000| Untracked 
| 140|0x00000006a3000000, 0x00000006a3000000, 0x00000006a3400000|  0%| F|  |TAMS 0x00000006a3000000, 0x00000006a3000000| Untracked 
| 141|0x00000006a3400000, 0x00000006a3400000, 0x00000006a3800000|  0%| F|  |TAMS 0x00000006a3400000, 0x00000006a3400000| Untracked 
| 142|0x00000006a3800000, 0x00000006a3800000, 0x00000006a3c00000|  0%| F|  |TAMS 0x00000006a3800000, 0x00000006a3800000| Untracked 
| 143|0x00000006a3c00000, 0x00000006a3c00000, 0x00000006a4000000|  0%| F|  |TAMS 0x00000006a3c00000, 0x00000006a3c00000| Untracked 
| 144|0x00000006a4000000, 0x00000006a4000000, 0x00000006a4400000|  0%| F|  |TAMS 0x00000006a4000000, 0x00000006a4000000| Untracked 
| 145|0x00000006a4400000, 0x00000006a4400000, 0x00000006a4800000|  0%| F|  |TAMS 0x00000006a4400000, 0x00000006a4400000| Untracked 
| 146|0x00000006a4800000, 0x00000006a4800000, 0x00000006a4c00000|  0%| F|  |TAMS 0x00000006a4800000, 0x00000006a4800000| Untracked 
| 147|0x00000006a4c00000, 0x00000006a4c00000, 0x00000006a5000000|  0%| F|  |TAMS 0x00000006a4c00000, 0x00000006a4c00000| Untracked 
| 148|0x00000006a5000000, 0x00000006a5000000, 0x00000006a5400000|  0%| F|  |TAMS 0x00000006a5000000, 0x00000006a5000000| Untracked 
| 149|0x00000006a5400000, 0x00000006a5400000, 0x00000006a5800000|  0%| F|  |TAMS 0x00000006a5400000, 0x00000006a5400000| Untracked 
| 150|0x00000006a5800000, 0x00000006a5800000, 0x00000006a5c00000|  0%| F|  |TAMS 0x00000006a5800000, 0x00000006a5800000| Untracked 
| 151|0x00000006a5c00000, 0x00000006a5c00000, 0x00000006a6000000|  0%| F|  |TAMS 0x00000006a5c00000, 0x00000006a5c00000| Untracked 
| 152|0x00000006a6000000, 0x00000006a6000000, 0x00000006a6400000|  0%| F|  |TAMS 0x00000006a6000000, 0x00000006a6000000| Untracked 
| 153|0x00000006a6400000, 0x00000006a6400000, 0x00000006a6800000|  0%| F|  |TAMS 0x00000006a6400000, 0x00000006a6400000| Untracked 
| 154|0x00000006a6800000, 0x00000006a6800000, 0x00000006a6c00000|  0%| F|  |TAMS 0x00000006a6800000, 0x00000006a6800000| Untracked 
| 155|0x00000006a6c00000, 0x00000006a6c00000, 0x00000006a7000000|  0%| F|  |TAMS 0x00000006a6c00000, 0x00000006a6c00000| Untracked 
| 156|0x00000006a7000000, 0x00000006a7000000, 0x00000006a7400000|  0%| F|  |TAMS 0x00000006a7000000, 0x00000006a7000000| Untracked 
| 157|0x00000006a7400000, 0x00000006a7400000, 0x00000006a7800000|  0%| F|  |TAMS 0x00000006a7400000, 0x00000006a7400000| Untracked 
| 158|0x00000006a7800000, 0x00000006a7800000, 0x00000006a7c00000|  0%| F|  |TAMS 0x00000006a7800000, 0x00000006a7800000| Untracked 
| 159|0x00000006a7c00000, 0x00000006a7c00000, 0x00000006a8000000|  0%| F|  |TAMS 0x00000006a7c00000, 0x00000006a7c00000| Untracked 
| 160|0x00000006a8000000, 0x00000006a8000000, 0x00000006a8400000|  0%| F|  |TAMS 0x00000006a8000000, 0x00000006a8000000| Untracked 
| 161|0x00000006a8400000, 0x00000006a8400000, 0x00000006a8800000|  0%| F|  |TAMS 0x00000006a8400000, 0x00000006a8400000| Untracked 
| 162|0x00000006a8800000, 0x00000006a8800000, 0x00000006a8c00000|  0%| F|  |TAMS 0x00000006a8800000, 0x00000006a8800000| Untracked 
| 163|0x00000006a8c00000, 0x00000006a8c00000, 0x00000006a9000000|  0%| F|  |TAMS 0x00000006a8c00000, 0x00000006a8c00000| Untracked 
| 164|0x00000006a9000000, 0x00000006a9000000, 0x00000006a9400000|  0%| F|  |TAMS 0x00000006a9000000, 0x00000006a9000000| Untracked 
| 165|0x00000006a9400000, 0x00000006a9400000, 0x00000006a9800000|  0%| F|  |TAMS 0x00000006a9400000, 0x00000006a9400000| Untracked 
| 166|0x00000006a9800000, 0x00000006a9800000, 0x00000006a9c00000|  0%| F|  |TAMS 0x00000006a9800000, 0x00000006a9800000| Untracked 
| 167|0x00000006a9c00000, 0x00000006a9c00000, 0x00000006aa000000|  0%| F|  |TAMS 0x00000006a9c00000, 0x00000006a9c00000| Untracked 
| 168|0x00000006aa000000, 0x00000006aa000000, 0x00000006aa400000|  0%| F|  |TAMS 0x00000006aa000000, 0x00000006aa000000| Untracked 
| 169|0x00000006aa400000, 0x00000006aa400000, 0x00000006aa800000|  0%| F|  |TAMS 0x00000006aa400000, 0x00000006aa400000| Untracked 
| 170|0x00000006aa800000, 0x00000006aa800000, 0x00000006aac00000|  0%| F|  |TAMS 0x00000006aa800000, 0x00000006aa800000| Untracked 
| 171|0x00000006aac00000, 0x00000006aac00000, 0x00000006ab000000|  0%| F|  |TAMS 0x00000006aac00000, 0x00000006aac00000| Untracked 
| 172|0x00000006ab000000, 0x00000006ab000000, 0x00000006ab400000|  0%| F|  |TAMS 0x00000006ab000000, 0x00000006ab000000| Untracked 
| 173|0x00000006ab400000, 0x00000006ab400000, 0x00000006ab800000|  0%| F|  |TAMS 0x00000006ab400000, 0x00000006ab400000| Untracked 
| 174|0x00000006ab800000, 0x00000006ab800000, 0x00000006abc00000|  0%| F|  |TAMS 0x00000006ab800000, 0x00000006ab800000| Untracked 
| 175|0x00000006abc00000, 0x00000006abc00000, 0x00000006ac000000|  0%| F|  |TAMS 0x00000006abc00000, 0x00000006abc00000| Untracked 
| 176|0x00000006ac000000, 0x00000006ac000000, 0x00000006ac400000|  0%| F|  |TAMS 0x00000006ac000000, 0x00000006ac000000| Untracked 
| 177|0x00000006ac400000, 0x00000006ac400000, 0x00000006ac800000|  0%| F|  |TAMS 0x00000006ac400000, 0x00000006ac400000| Untracked 
| 178|0x00000006ac800000, 0x00000006ac800000, 0x00000006acc00000|  0%| F|  |TAMS 0x00000006ac800000, 0x00000006ac800000| Untracked 
| 179|0x00000006acc00000, 0x00000006acc00000, 0x00000006ad000000|  0%| F|  |TAMS 0x00000006acc00000, 0x00000006acc00000| Untracked 
| 180|0x00000006ad000000, 0x00000006ad000000, 0x00000006ad400000|  0%| F|  |TAMS 0x00000006ad000000, 0x00000006ad000000| Untracked 
| 181|0x00000006ad400000, 0x00000006ad400000, 0x00000006ad800000|  0%| F|  |TAMS 0x00000006ad400000, 0x00000006ad400000| Untracked 
| 182|0x00000006ad800000, 0x00000006ad800000, 0x00000006adc00000|  0%| F|  |TAMS 0x00000006ad800000, 0x00000006ad800000| Untracked 
| 183|0x00000006adc00000, 0x00000006adc00000, 0x00000006ae000000|  0%| F|  |TAMS 0x00000006adc00000, 0x00000006adc00000| Untracked 
| 184|0x00000006ae000000, 0x00000006ae000000, 0x00000006ae400000|  0%| F|  |TAMS 0x00000006ae000000, 0x00000006ae000000| Untracked 
| 185|0x00000006ae400000, 0x00000006ae400000, 0x00000006ae800000|  0%| F|  |TAMS 0x00000006ae400000, 0x00000006ae400000| Untracked 
| 186|0x00000006ae800000, 0x00000006ae800000, 0x00000006aec00000|  0%| F|  |TAMS 0x00000006ae800000, 0x00000006ae800000| Untracked 
| 187|0x00000006aec00000, 0x00000006aec00000, 0x00000006af000000|  0%| F|  |TAMS 0x00000006aec00000, 0x00000006aec00000| Untracked 
| 188|0x00000006af000000, 0x00000006af000000, 0x00000006af400000|  0%| F|  |TAMS 0x00000006af000000, 0x00000006af000000| Untracked 
| 189|0x00000006af400000, 0x00000006af400000, 0x00000006af800000|  0%| F|  |TAMS 0x00000006af400000, 0x00000006af400000| Untracked 
| 190|0x00000006af800000, 0x00000006af800000, 0x00000006afc00000|  0%| F|  |TAMS 0x00000006af800000, 0x00000006af800000| Untracked 
| 191|0x00000006afc00000, 0x00000006afc00000, 0x00000006b0000000|  0%| F|  |TAMS 0x00000006afc00000, 0x00000006afc00000| Untracked 
| 192|0x00000006b0000000, 0x00000006b0000000, 0x00000006b0400000|  0%| F|  |TAMS 0x00000006b0000000, 0x00000006b0000000| Untracked 
| 193|0x00000006b0400000, 0x00000006b0400000, 0x00000006b0800000|  0%| F|  |TAMS 0x00000006b0400000, 0x00000006b0400000| Untracked 
| 194|0x00000006b0800000, 0x00000006b0b5f470, 0x00000006b0c00000| 84%| E|  |TAMS 0x00000006b0800000, 0x00000006b0800000| Complete 
| 195|0x00000006b0c00000, 0x00000006b1000000, 0x00000006b1000000|100%| E|CS|TAMS 0x00000006b0c00000, 0x00000006b0c00000| Complete 
| 196|0x00000006b1000000, 0x00000006b1400000, 0x00000006b1400000|100%| E|CS|TAMS 0x00000006b1000000, 0x00000006b1000000| Complete 
| 197|0x00000006b1400000, 0x00000006b1800000, 0x00000006b1800000|100%| E|CS|TAMS 0x00000006b1400000, 0x00000006b1400000| Complete 
| 198|0x00000006b1800000, 0x00000006b1c00000, 0x00000006b1c00000|100%| E|CS|TAMS 0x00000006b1800000, 0x00000006b1800000| Complete 
| 199|0x00000006b1c00000, 0x00000006b2000000, 0x00000006b2000000|100%| E|CS|TAMS 0x00000006b1c00000, 0x00000006b1c00000| Complete 
| 200|0x00000006b2000000, 0x00000006b2400000, 0x00000006b2400000|100%| E|CS|TAMS 0x00000006b2000000, 0x00000006b2000000| Complete 
| 201|0x00000006b2400000, 0x00000006b2800000, 0x00000006b2800000|100%| E|CS|TAMS 0x00000006b2400000, 0x00000006b2400000| Complete 
| 202|0x00000006b2800000, 0x00000006b2c00000, 0x00000006b2c00000|100%| E|CS|TAMS 0x00000006b2800000, 0x00000006b2800000| Complete 
| 203|0x00000006b2c00000, 0x00000006b3000000, 0x00000006b3000000|100%| E|CS|TAMS 0x00000006b2c00000, 0x00000006b2c00000| Complete 
| 204|0x00000006b3000000, 0x00000006b333b0e0, 0x00000006b3400000| 80%| S|CS|TAMS 0x00000006b3000000, 0x00000006b3000000| Complete 
| 205|0x00000006b3400000, 0x00000006b3800000, 0x00000006b3800000|100%| S|CS|TAMS 0x00000006b3400000, 0x00000006b3400000| Complete 
| 206|0x00000006b3800000, 0x00000006b3c00000, 0x00000006b3c00000|100%| S|CS|TAMS 0x00000006b3800000, 0x00000006b3800000| Complete 
| 207|0x00000006b3c00000, 0x00000006b4000000, 0x00000006b4000000|100%| E|CS|TAMS 0x00000006b3c00000, 0x00000006b3c00000| Complete 
| 208|0x00000006b4000000, 0x00000006b4400000, 0x00000006b4400000|100%| E|  |TAMS 0x00000006b4000000, 0x00000006b4000000| Complete 
| 209|0x00000006b4400000, 0x00000006b4800000, 0x00000006b4800000|100%| E|CS|TAMS 0x00000006b4400000, 0x00000006b4400000| Complete 
| 210|0x00000006b4800000, 0x00000006b4c00000, 0x00000006b4c00000|100%| E|CS|TAMS 0x00000006b4800000, 0x00000006b4800000| Complete 
| 211|0x00000006b4c00000, 0x00000006b5000000, 0x00000006b5000000|100%| E|CS|TAMS 0x00000006b4c00000, 0x00000006b4c00000| Complete 
| 212|0x00000006b5000000, 0x00000006b5400000, 0x00000006b5400000|100%| E|CS|TAMS 0x00000006b5000000, 0x00000006b5000000| Complete 
| 213|0x00000006b5400000, 0x00000006b5800000, 0x00000006b5800000|100%| E|CS|TAMS 0x00000006b5400000, 0x00000006b5400000| Complete 
| 214|0x00000006b5800000, 0x00000006b5c00000, 0x00000006b5c00000|100%| E|CS|TAMS 0x00000006b5800000, 0x00000006b5800000| Complete 
| 215|0x00000006b5c00000, 0x00000006b6000000, 0x00000006b6000000|100%| E|CS|TAMS 0x00000006b5c00000, 0x00000006b5c00000| Complete 
| 216|0x00000006b6000000, 0x00000006b6400000, 0x00000006b6400000|100%| E|CS|TAMS 0x00000006b6000000, 0x00000006b6000000| Complete 
| 217|0x00000006b6400000, 0x00000006b6800000, 0x00000006b6800000|100%| E|CS|TAMS 0x00000006b6400000, 0x00000006b6400000| Complete 
| 218|0x00000006b6800000, 0x00000006b6c00000, 0x00000006b6c00000|100%| E|CS|TAMS 0x00000006b6800000, 0x00000006b6800000| Complete 
| 219|0x00000006b6c00000, 0x00000006b7000000, 0x00000006b7000000|100%| E|CS|TAMS 0x00000006b6c00000, 0x00000006b6c00000| Complete 
| 220|0x00000006b7000000, 0x00000006b7400000, 0x00000006b7400000|100%| E|CS|TAMS 0x00000006b7000000, 0x00000006b7000000| Complete 
| 221|0x00000006b7400000, 0x00000006b7800000, 0x00000006b7800000|100%| E|CS|TAMS 0x00000006b7400000, 0x00000006b7400000| Complete 
| 222|0x00000006b7800000, 0x00000006b7c00000, 0x00000006b7c00000|100%| E|CS|TAMS 0x00000006b7800000, 0x00000006b7800000| Complete 
| 223|0x00000006b7c00000, 0x00000006b8000000, 0x00000006b8000000|100%| E|CS|TAMS 0x00000006b7c00000, 0x00000006b7c00000| Complete 
| 224|0x00000006b8000000, 0x00000006b8400000, 0x00000006b8400000|100%| E|CS|TAMS 0x00000006b8000000, 0x00000006b8000000| Complete 
| 225|0x00000006b8400000, 0x00000006b8800000, 0x00000006b8800000|100%| E|CS|TAMS 0x00000006b8400000, 0x00000006b8400000| Complete 
| 226|0x00000006b8800000, 0x00000006b8c00000, 0x00000006b8c00000|100%| E|CS|TAMS 0x00000006b8800000, 0x00000006b8800000| Complete 
| 227|0x00000006b8c00000, 0x00000006b9000000, 0x00000006b9000000|100%| E|CS|TAMS 0x00000006b8c00000, 0x00000006b8c00000| Complete 
| 228|0x00000006b9000000, 0x00000006b9400000, 0x00000006b9400000|100%| E|CS|TAMS 0x00000006b9000000, 0x00000006b9000000| Complete 
| 229|0x00000006b9400000, 0x00000006b9800000, 0x00000006b9800000|100%| E|CS|TAMS 0x00000006b9400000, 0x00000006b9400000| Complete 
| 230|0x00000006b9800000, 0x00000006b9c00000, 0x00000006b9c00000|100%| E|CS|TAMS 0x00000006b9800000, 0x00000006b9800000| Complete 
| 231|0x00000006b9c00000, 0x00000006ba000000, 0x00000006ba000000|100%| E|CS|TAMS 0x00000006b9c00000, 0x00000006b9c00000| Complete 
| 232|0x00000006ba000000, 0x00000006ba400000, 0x00000006ba400000|100%| E|CS|TAMS 0x00000006ba000000, 0x00000006ba000000| Complete 
| 233|0x00000006ba400000, 0x00000006ba800000, 0x00000006ba800000|100%| E|CS|TAMS 0x00000006ba400000, 0x00000006ba400000| Complete 
| 234|0x00000006ba800000, 0x00000006bac00000, 0x00000006bac00000|100%| E|CS|TAMS 0x00000006ba800000, 0x00000006ba800000| Complete 
| 235|0x00000006bac00000, 0x00000006bb000000, 0x00000006bb000000|100%| E|CS|TAMS 0x00000006bac00000, 0x00000006bac00000| Complete 
| 236|0x00000006bb000000, 0x00000006bb400000, 0x00000006bb400000|100%| E|CS|TAMS 0x00000006bb000000, 0x00000006bb000000| Complete 
| 237|0x00000006bb400000, 0x00000006bb800000, 0x00000006bb800000|100%| E|CS|TAMS 0x00000006bb400000, 0x00000006bb400000| Complete 
| 238|0x00000006bb800000, 0x00000006bbc00000, 0x00000006bbc00000|100%| E|CS|TAMS 0x00000006bb800000, 0x00000006bb800000| Complete 
| 239|0x00000006bbc00000, 0x00000006bc000000, 0x00000006bc000000|100%| E|CS|TAMS 0x00000006bbc00000, 0x00000006bbc00000| Complete 
| 240|0x00000006bc000000, 0x00000006bc400000, 0x00000006bc400000|100%| E|CS|TAMS 0x00000006bc000000, 0x00000006bc000000| Complete 
| 241|0x00000006bc400000, 0x00000006bc800000, 0x00000006bc800000|100%| E|CS|TAMS 0x00000006bc400000, 0x00000006bc400000| Complete 
| 242|0x00000006bc800000, 0x00000006bcc00000, 0x00000006bcc00000|100%| E|CS|TAMS 0x00000006bc800000, 0x00000006bc800000| Complete 
| 243|0x00000006bcc00000, 0x00000006bd000000, 0x00000006bd000000|100%| E|CS|TAMS 0x00000006bcc00000, 0x00000006bcc00000| Complete 
| 244|0x00000006bd000000, 0x00000006bd400000, 0x00000006bd400000|100%| E|CS|TAMS 0x00000006bd000000, 0x00000006bd000000| Complete 
| 245|0x00000006bd400000, 0x00000006bd800000, 0x00000006bd800000|100%| E|CS|TAMS 0x00000006bd400000, 0x00000006bd400000| Complete 
| 401|0x00000006e4400000, 0x00000006e4800000, 0x00000006e4800000|100%| E|CS|TAMS 0x00000006e4400000, 0x00000006e4400000| Complete 
| 402|0x00000006e4800000, 0x00000006e4c00000, 0x00000006e4c00000|100%| E|CS|TAMS 0x00000006e4800000, 0x00000006e4800000| Complete 
| 403|0x00000006e4c00000, 0x00000006e5000000, 0x00000006e5000000|100%| E|CS|TAMS 0x00000006e4c00000, 0x00000006e4c00000| Complete 
| 404|0x00000006e5000000, 0x00000006e5400000, 0x00000006e5400000|100%| E|CS|TAMS 0x00000006e5000000, 0x00000006e5000000| Complete 

Card table byte_map: [0x0000017f1dec0000,0x0000017f1eac0000] _byte_map_base: 0x0000017f1aac0000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000017f06826dd0, (CMBitMap*) 0x0000017f06826d90
 Prev Bits: [0x0000017f256c0000, 0x0000017f2b6c0000)
 Next Bits: [0x0000017f1f6c0000, 0x0000017f256c0000)

Polling page: 0x0000017f045d0000

Metaspace:

Usage:
  Non-class:    100.16 MB used.
      Class:     16.06 MB used.
       Both:    116.22 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     101.38 MB ( 79%) committed,  2 nodes.
      Class space:      832.00 MB reserved,      17.00 MB (  2%) committed,  1 nodes.
             Both:      960.00 MB reserved,     118.38 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  9.78 MB
       Class:  15.01 MB
        Both:  24.79 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 180.75 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 2626.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1894.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 8556.
num_chunk_merges: 6.
num_chunk_splits: 5217.
num_chunks_enlarged: 2901.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=16524Kb max_used=16524Kb free=103475Kb
 bounds [0x0000017f15d90000, 0x0000017f16dc0000, 0x0000017f1d2c0000]
CodeHeap 'profiled nmethods': size=120000Kb used=33488Kb max_used=33488Kb free=86511Kb
 bounds [0x0000017f0e2c0000, 0x0000017f10380000, 0x0000017f157f0000]
CodeHeap 'non-nmethods': size=5760Kb used=2457Kb max_used=2548Kb free=3302Kb
 bounds [0x0000017f157f0000, 0x0000017f15a90000, 0x0000017f15d90000]
 total_blobs=18874 nmethods=17873 adapters=912
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 782.310 Thread 0x0000017f2db91e00 22322       3       java.util.stream.Collectors$$Lambda$592/0x0000017f2f448ee8::get (8 bytes)
Event: 782.310 Thread 0x0000017f2db91e00 nmethod 22322 0x0000017f0ee4d190 code [0x0000017f0ee4d340, 0x0000017f0ee4d5d8]
Event: 782.315 Thread 0x0000017f2db91e00 22326       3       org.gradle.internal.snapshot.CompositeFileSystemSnapshot::accept (55 bytes)
Event: 782.315 Thread 0x0000017f2db91e00 nmethod 22326 0x0000017f0ee4c790 code [0x0000017f0ee4c980, 0x0000017f0ee4cff8]
Event: 782.316 Thread 0x0000017f7407e2d0 22325   !   4       org.gradle.internal.serialize.kryo.KryoBackedDecoder::readBoolean (15 bytes)
Event: 782.317 Thread 0x0000017f2db91e00 22327       3       org.gradle.internal.logging.sink.ProgressLogEventGenerator$Operation::styledTextEvent (24 bytes)
Event: 782.317 Thread 0x0000017f2db91e00 nmethod 22327 0x0000017f0ee4bd10 code [0x0000017f0ee4bf00, 0x0000017f0ee4c4f8]
Event: 782.317 Thread 0x0000017f2db91e00 22328       3       org.gradle.internal.logging.sink.ProgressLogEventGenerator$Operation::doOutput (66 bytes)
Event: 782.318 Thread 0x0000017f2db91e00 nmethod 22328 0x0000017f0ee4b110 code [0x0000017f0ee4b320, 0x0000017f0ee4ba98]
Event: 782.318 Thread 0x0000017f2db91e00 22329  s    3       org.jetbrains.kotlin.gradle.plugin.statistics.BuildFusService::onFinish (105 bytes)
Event: 782.320 Thread 0x0000017f2db91e00 nmethod 22329 0x0000017f0ee49210 code [0x0000017f0ee49520, 0x0000017f0ee4ab78]
Event: 782.320 Thread 0x0000017f2db91e00 22330       3       org.gradle.tooling.events.task.internal.DefaultTaskFinishEvent::getResult (8 bytes)
Event: 782.320 Thread 0x0000017f2db91e00 nmethod 22330 0x0000017f0ee48d90 code [0x0000017f0ee48f20, 0x0000017f0ee49158]
Event: 782.322 Thread 0x0000017f7407e2d0 nmethod 22325 0x0000017f16db1f10 code [0x0000017f16db20c0, 0x0000017f16db2488]
Event: 782.322 Thread 0x0000017f7407e2d0 22319       4       org.gradle.api.internal.provider.AbstractMinimalProvider::getProducer (4 bytes)
Event: 782.322 Thread 0x0000017f7407e2d0 nmethod 22319 0x0000017f16db2790 code [0x0000017f16db2900, 0x0000017f16db2978]
Event: 782.322 Thread 0x0000017f7407e2d0 22324       4       org.gradle.api.internal.project.taskfactory.TaskIdentity::hashCode (14 bytes)
Event: 782.323 Thread 0x0000017f7407e2d0 nmethod 22324 0x0000017f16db2a90 code [0x0000017f16db2c00, 0x0000017f16db2c98]
Event: 782.323 Thread 0x0000017f7407e2d0 22323       4       java.util.concurrent.atomic.AtomicInteger::getAndIncrement (12 bytes)
Event: 782.323 Thread 0x0000017f7407e2d0 nmethod 22323 0x0000017f16db2d90 code [0x0000017f16db2f00, 0x0000017f16db2f78]

GC Heap History (20 events):
Event: 555.615 GC heap before
{Heap before GC invocations=95 (full 0):
 garbage-first heap   total 552960K, used 343394K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 28 young (114688K), 4 survivors (16384K)
 Metaspace       used 107632K, committed 109312K, reserved 983040K
  class space    used 14973K, committed 15680K, reserved 851968K
}
Event: 555.667 GC heap after
{Heap after GC invocations=96 (full 0):
 garbage-first heap   total 552960K, used 249568K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 107632K, committed 109312K, reserved 983040K
  class space    used 14973K, committed 15680K, reserved 851968K
}
Event: 556.067 GC heap before
{Heap before GC invocations=96 (full 0):
 garbage-first heap   total 552960K, used 265952K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 1 survivors (4096K)
 Metaspace       used 107632K, committed 109312K, reserved 983040K
  class space    used 14973K, committed 15680K, reserved 851968K
}
Event: 556.098 GC heap after
{Heap after GC invocations=97 (full 0):
 garbage-first heap   total 552960K, used 248106K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 107632K, committed 109312K, reserved 983040K
  class space    used 14973K, committed 15680K, reserved 851968K
}
Event: 616.341 GC heap before
{Heap before GC invocations=97 (full 0):
 garbage-first heap   total 552960K, used 452906K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 52 young (212992K), 1 survivors (4096K)
 Metaspace       used 108388K, committed 110144K, reserved 983040K
  class space    used 15070K, committed 15872K, reserved 851968K
}
Event: 616.440 GC heap after
{Heap after GC invocations=98 (full 0):
 garbage-first heap   total 552960K, used 267017K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 108388K, committed 110144K, reserved 983040K
  class space    used 15070K, committed 15872K, reserved 851968K
}
Event: 634.876 GC heap before
{Heap before GC invocations=99 (full 0):
 garbage-first heap   total 552960K, used 447241K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 51 young (208896K), 6 survivors (24576K)
 Metaspace       used 108539K, committed 110336K, reserved 983040K
  class space    used 15086K, committed 15872K, reserved 851968K
}
Event: 635.066 GC heap after
{Heap after GC invocations=100 (full 0):
 garbage-first heap   total 552960K, used 276917K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 108539K, committed 110336K, reserved 983040K
  class space    used 15086K, committed 15872K, reserved 851968K
}
Event: 635.122 GC heap before
{Heap before GC invocations=100 (full 0):
 garbage-first heap   total 552960K, used 281013K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 4 survivors (16384K)
 Metaspace       used 108539K, committed 110336K, reserved 983040K
  class space    used 15086K, committed 15872K, reserved 851968K
}
Event: 635.185 GC heap after
{Heap after GC invocations=101 (full 0):
 garbage-first heap   total 552960K, used 277329K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 108539K, committed 110336K, reserved 983040K
  class space    used 15086K, committed 15872K, reserved 851968K
}
Event: 652.410 GC heap before
{Heap before GC invocations=101 (full 0):
 garbage-first heap   total 552960K, used 449361K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 44 young (180224K), 1 survivors (4096K)
 Metaspace       used 109136K, committed 110976K, reserved 983040K
  class space    used 15173K, committed 16000K, reserved 851968K
}
Event: 652.444 GC heap after
{Heap after GC invocations=102 (full 0):
 garbage-first heap   total 1658880K, used 289145K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 109136K, committed 110976K, reserved 983040K
  class space    used 15173K, committed 16000K, reserved 851968K
}
Event: 709.893 GC heap before
{Heap before GC invocations=103 (full 0):
 garbage-first heap   total 1024000K, used 608633K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 83 young (339968K), 4 survivors (16384K)
 Metaspace       used 117189K, committed 119232K, reserved 983040K
  class space    used 16246K, committed 17152K, reserved 851968K
}
Event: 710.105 GC heap after
{Heap after GC invocations=104 (full 0):
 garbage-first heap   total 1024000K, used 332696K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 11 survivors (45056K)
 Metaspace       used 117189K, committed 119232K, reserved 983040K
  class space    used 16246K, committed 17152K, reserved 851968K
}
Event: 710.331 GC heap before
{Heap before GC invocations=104 (full 0):
 garbage-first heap   total 1024000K, used 336792K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 11 survivors (45056K)
 Metaspace       used 117192K, committed 119232K, reserved 983040K
  class space    used 16246K, committed 17152K, reserved 851968K
}
Event: 710.681 GC heap after
{Heap after GC invocations=105 (full 0):
 garbage-first heap   total 1024000K, used 337519K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 117192K, committed 119232K, reserved 983040K
  class space    used 16246K, committed 17152K, reserved 851968K
}
Event: 731.649 GC heap before
{Heap before GC invocations=105 (full 0):
 garbage-first heap   total 1024000K, used 525935K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 48 young (196608K), 2 survivors (8192K)
 Metaspace       used 117991K, committed 120064K, reserved 983040K
  class space    used 16351K, committed 17280K, reserved 851968K
}
Event: 731.714 GC heap after
{Heap after GC invocations=106 (full 0):
 garbage-first heap   total 1024000K, used 352072K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 117991K, committed 120064K, reserved 983040K
  class space    used 16351K, committed 17280K, reserved 851968K
}
Event: 742.593 GC heap before
{Heap before GC invocations=106 (full 0):
 garbage-first heap   total 1024000K, used 524104K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 48 young (196608K), 5 survivors (20480K)
 Metaspace       used 118687K, committed 120832K, reserved 983040K
  class space    used 16433K, committed 17408K, reserved 851968K
}
Event: 742.711 GC heap after
{Heap after GC invocations=107 (full 0):
 garbage-first heap   total 1024000K, used 363545K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 118687K, committed 120832K, reserved 983040K
  class space    used 16433K, committed 17408K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.975 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 1.093 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 1.464 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 1.468 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 1.515 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 1.522 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 1.526 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 5.471 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 11.765 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 12.591 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 12.662 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 23.669 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 23.673 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 25.480 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 30.248 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 37.150 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform10291218566899002353dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 779.251 Thread 0x0000017f69dea7f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017f168f2138 relative=0x00000000000007f8
Event: 779.251 Thread 0x0000017f69dea7f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017f168f2138 method=org.gradle.internal.snapshot.AbstractIncompleteFileSystemNode$2.handleAsDescendantOfChild(Lorg/gradle/internal/snapshot/VfsRelativePath;Ljava/lang/Object;)Ljava/lang/Obj
Event: 779.251 Thread 0x0000017f69dea7f0 DEOPT PACKING pc=0x0000017f168f2138 sp=0x0000003200bfbd20
Event: 779.251 Thread 0x0000017f69dea7f0 DEOPT UNPACKING pc=0x0000017f158469a3 sp=0x0000003200bfbc78 mode 2
Event: 781.684 Thread 0x0000017f69dea7f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017f15fe5640 relative=0x00000000000014e0
Event: 781.684 Thread 0x0000017f69dea7f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017f15fe5640 method=org.gradle.api.internal.provider.ValidatingMapEntryCollector.add(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Map;)V @ 113 c2
Event: 781.684 Thread 0x0000017f69dea7f0 DEOPT PACKING pc=0x0000017f15fe5640 sp=0x0000003200bfc3f0
Event: 781.684 Thread 0x0000017f69dea7f0 DEOPT UNPACKING pc=0x0000017f158469a3 sp=0x0000003200bfc3e8 mode 2
Event: 781.684 Thread 0x0000017f69dea7f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017f15fe5640 relative=0x00000000000014e0
Event: 781.684 Thread 0x0000017f69dea7f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017f15fe5640 method=org.gradle.api.internal.provider.ValidatingMapEntryCollector.add(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Map;)V @ 113 c2
Event: 781.684 Thread 0x0000017f69dea7f0 DEOPT PACKING pc=0x0000017f15fe5640 sp=0x0000003200bfc9f0
Event: 781.684 Thread 0x0000017f69dea7f0 DEOPT UNPACKING pc=0x0000017f158469a3 sp=0x0000003200bfc9e8 mode 2
Event: 781.720 Thread 0x0000017f69dea7f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017f15fe5640 relative=0x00000000000014e0
Event: 781.720 Thread 0x0000017f69dea7f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017f15fe5640 method=org.gradle.api.internal.provider.ValidatingMapEntryCollector.add(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Map;)V @ 113 c2
Event: 781.720 Thread 0x0000017f69dea7f0 DEOPT PACKING pc=0x0000017f15fe5640 sp=0x0000003200bfc9f0
Event: 781.720 Thread 0x0000017f69dea7f0 DEOPT UNPACKING pc=0x0000017f158469a3 sp=0x0000003200bfc9e8 mode 2
Event: 781.734 Thread 0x0000017f69dea7f0 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000017f15fe5640 relative=0x00000000000014e0
Event: 781.734 Thread 0x0000017f69dea7f0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000017f15fe5640 method=org.gradle.api.internal.provider.ValidatingMapEntryCollector.add(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Map;)V @ 113 c2
Event: 781.734 Thread 0x0000017f69dea7f0 DEOPT PACKING pc=0x0000017f15fe5640 sp=0x0000003200bfc3f0
Event: 781.734 Thread 0x0000017f69dea7f0 DEOPT UNPACKING pc=0x0000017f158469a3 sp=0x0000003200bfc3e8 mode 2

Classes loaded (20 events):
Event: 683.964 Loading class java/io/CharArrayWriter
Event: 683.965 Loading class java/io/CharArrayWriter done
Event: 683.984 Loading class java/io/SequenceInputStream
Event: 683.985 Loading class java/io/SequenceInputStream done
Event: 684.164 Loading class java/util/function/IntUnaryOperator
Event: 684.165 Loading class java/util/function/IntUnaryOperator done
Event: 731.436 Loading class java/util/HashSetBeanInfo
Event: 731.436 Loading class java/util/HashSetBeanInfo done
Event: 731.436 Loading class java/util/HashSetBeanInfo
Event: 731.436 Loading class java/util/HashSetBeanInfo done
Event: 731.437 Loading class java/util/HashSetCustomizer
Event: 731.437 Loading class java/util/HashSetCustomizer done
Event: 731.438 Loading class java/util/HashSetCustomizer
Event: 731.438 Loading class java/util/HashSetCustomizer done
Event: 732.306 Loading class java/util/HashMap$UnsafeHolder
Event: 732.306 Loading class java/util/HashMap$UnsafeHolder done
Event: 735.014 Loading class java/nio/file/FileTreeWalker$1
Event: 735.014 Loading class java/nio/file/FileTreeWalker$1 done
Event: 761.985 Loading class java/time/Instant$1
Event: 761.985 Loading class java/time/Instant$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 747.866 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b6722728}> (0x00000006b6722728) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 748.023 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b6016d80}> (0x00000006b6016d80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 748.232 Thread 0x0000017f700652c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b8f58bb0}> (0x00000006b8f58bb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 748.409 Thread 0x0000017f700652c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b6115c48}> (0x00000006b6115c48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 751.112 Thread 0x0000017f700652c0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b62c5ff8}> (0x00000006b62c5ff8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.406 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5c818c0}> (0x00000006b5c818c0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.508 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5d1fd40}> (0x00000006b5d1fd40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.548 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5dd6220}> (0x00000006b5dd6220) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.592 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5ecb060}> (0x00000006b5ecb060) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.620 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5fba730}> (0x00000006b5fba730) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.649 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b58150d8}> (0x00000006b58150d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.675 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b58bf4a0}> (0x00000006b58bf4a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.699 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b596a5b0}> (0x00000006b596a5b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.732 Thread 0x0000017f69dea7f0 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5a38a28}> (0x00000006b5a38a28) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.763 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5ae6cb8}> (0x00000006b5ae6cb8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.790 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b54363a8}> (0x00000006b54363a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.827 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5518778}> (0x00000006b5518778) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.847 Thread 0x0000017f69df0310 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b55c30e8}> (0x00000006b55c30e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.867 Thread 0x0000017f69dec650 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5678e68}> (0x00000006b5678e68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 752.906 Thread 0x0000017f69dec650 Exception <a 'sun/nio/fs/WindowsException'{0x00000006b5737a60}> (0x00000006b5737a60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]

VM Operations (20 events):
Event: 760.407 Executing VM operation: Cleanup
Event: 761.294 Executing VM operation: Cleanup done
Event: 762.307 Executing VM operation: Cleanup
Event: 762.307 Executing VM operation: Cleanup done
Event: 766.346 Executing VM operation: Cleanup
Event: 766.346 Executing VM operation: Cleanup done
Event: 770.394 Executing VM operation: Cleanup
Event: 770.394 Executing VM operation: Cleanup done
Event: 772.421 Executing VM operation: Cleanup
Event: 773.159 Executing VM operation: Cleanup done
Event: 775.180 Executing VM operation: Cleanup
Event: 775.762 Executing VM operation: Cleanup done
Event: 776.767 Executing VM operation: Cleanup
Event: 776.767 Executing VM operation: Cleanup done
Event: 779.796 Executing VM operation: Cleanup
Event: 779.796 Executing VM operation: Cleanup done
Event: 780.808 Executing VM operation: Cleanup
Event: 781.356 Executing VM operation: Cleanup done
Event: 782.363 Executing VM operation: Cleanup
Event: 782.363 Executing VM operation: Cleanup done

Events (20 events):
Event: 735.323 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10105c90
Event: 735.323 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10106510
Event: 735.323 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f1010cc10
Event: 735.323 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f1013b590
Event: 735.323 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10144f90
Event: 735.324 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f101c8290
Event: 735.324 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f1022c710
Event: 735.324 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f1022cf90
Event: 735.324 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10245690
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f102d4110
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f102d7c90
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10302090
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10302410
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f1030ab10
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f1031e190
Event: 735.325 Thread 0x0000017f2dc08050 flushing nmethod 0x0000017f10344e90
Event: 735.780 Thread 0x0000017f7407ed70 Thread exited: 0x0000017f7407ed70
Event: 752.914 Thread 0x0000017f7407f2c0 Thread added: 0x0000017f7407f2c0
Event: 756.988 Thread 0x0000017f7407f2c0 Thread exited: 0x0000017f7407f2c0
Event: 782.315 Thread 0x0000017f7407e2d0 Thread added: 0x0000017f7407e2d0


Dynamic libraries:
0x00007ff603630000 - 0x00007ff60363e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffacb2f0000 - 0x00007ffacb507000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffac9a70000 - 0x00007ffac9b34000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffac8be0000 - 0x00007ffac8fb1000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffac84d0000 - 0x00007ffac85e1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffaaeea0000 - 0x00007ffaaeeb7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffac9b60000 - 0x00007ffac9d11000 	C:\Windows\System32\USER32.dll
0x00007ffac8ac0000 - 0x00007ffac8ae6000 	C:\Windows\System32\win32u.dll
0x00007ffacb280000 - 0x00007ffacb2a9000 	C:\Windows\System32\GDI32.dll
0x00007ffac85f0000 - 0x00007ffac870b000 	C:\Windows\System32\gdi32full.dll
0x00007ffac8430000 - 0x00007ffac84ca000 	C:\Windows\System32\msvcp_win.dll
0x00007ffaaed30000 - 0x00007ffaaed4d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffab0c00000 - 0x00007ffab0e92000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffacb170000 - 0x00007ffacb217000 	C:\Windows\System32\msvcrt.dll
0x00007ffacb220000 - 0x00007ffacb251000 	C:\Windows\System32\IMM32.DLL
0x00007ffabf040000 - 0x00007ffabf04c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffabdda0000 - 0x00007ffabde2d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffa17d70000 - 0x00007ffa189e0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffac96e0000 - 0x00007ffac9791000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffac9d20000 - 0x00007ffac9dc7000 	C:\Windows\System32\sechost.dll
0x00007ffac8af0000 - 0x00007ffac8b18000 	C:\Windows\System32\bcrypt.dll
0x00007ffacb050000 - 0x00007ffacb164000 	C:\Windows\System32\RPCRT4.dll
0x00007ffac9130000 - 0x00007ffac91a1000 	C:\Windows\System32\WS2_32.dll
0x00007ffac8300000 - 0x00007ffac834d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffabdc00000 - 0x00007ffabdc34000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffabc100000 - 0x00007ffabc10a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffac82e0000 - 0x00007ffac82f3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffac7440000 - 0x00007ffac7458000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffabeaa0000 - 0x00007ffabeaaa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffac2e00000 - 0x00007ffac3032000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffac92c0000 - 0x00007ffac9650000 	C:\Windows\System32\combase.dll
0x00007ffac9990000 - 0x00007ffac9a67000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffaa95d0000 - 0x00007ffaa9602000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffac8a40000 - 0x00007ffac8abb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffac1190000 - 0x00007ffac119e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffaaded0000 - 0x00007ffaadef5000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffaad6b0000 - 0x00007ffaad787000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffaca760000 - 0x00007ffacafe8000 	C:\Windows\System32\SHELL32.dll
0x00007ffac8880000 - 0x00007ffac89bf000 	C:\Windows\System32\wintypes.dll
0x00007ffac6340000 - 0x00007ffac6c4d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffac9020000 - 0x00007ffac912a000 	C:\Windows\System32\SHCORE.dll
0x00007ffacaff0000 - 0x00007ffacb04e000 	C:\Windows\System32\shlwapi.dll
0x00007ffac8360000 - 0x00007ffac838b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffaad690000 - 0x00007ffaad6a8000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffaaeb80000 - 0x00007ffaaeb9a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffabe3f0000 - 0x00007ffabe51c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffac78a0000 - 0x00007ffac790a000 	C:\Windows\system32\mswsock.dll
0x00007ffaae4d0000 - 0x00007ffaae4e6000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffabdd90000 - 0x00007ffabdda0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffaad1e0000 - 0x00007ffaad207000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000005ace0000 - 0x000000005ad53000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffab9cf0000 - 0x00007ffab9cfa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffab0ed0000 - 0x00007ffab0edb000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffac97a0000 - 0x00007ffac97a8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffac7bf0000 - 0x00007ffac7c0b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffac73a0000 - 0x00007ffac73d7000 	C:\Windows\system32\rsaenh.dll
0x00007ffac7940000 - 0x00007ffac7968000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffac7bd0000 - 0x00007ffac7bdc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffac6eb0000 - 0x00007ffac6edd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffac9dd0000 - 0x00007ffac9dd9000 	C:\Windows\System32\NSI.dll
0x00007ffabe730000 - 0x00007ffabe749000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffabe010000 - 0x00007ffabe02f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffac6f20000 - 0x00007ffac7022000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffac11a0000 - 0x00007ffac11a9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffac1180000 - 0x00007ffac118e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffac8710000 - 0x00007ffac8876000 	C:\Windows\System32\CRYPT32.dll
0x00007ffac7d00000 - 0x00007ffac7d2d000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffac7cc0000 - 0x00007ffac7cf7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffabf6d0000 - 0x00007ffabf6d8000 	C:\Windows\system32\wshunix.dll
0x000000005a9e0000 - 0x000000005aa53000 	C:\Users\<USER>\AppData\Local\Temp\native-platform10291218566899002353dir\gradle-fileevents.dll
0x00007ffaaa3d0000 - 0x00007ffaaa3e7000 	C:\Windows\system32\napinsp.dll
0x00007ffaaa000000 - 0x00007ffaaa01b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffaa7ef0000 - 0x00007ffaa7f01000 	C:\Windows\System32\winrnr.dll
0x00007ffabd040000 - 0x00007ffabd055000 	C:\Windows\system32\wshbth.dll
0x00007ffaa1bc0000 - 0x00007ffaa1be7000 	C:\Windows\system32\nlansp_c.dll
0x00007ffab2af0000 - 0x00007ffab2afa000 	C:\Windows\System32\rasadhlp.dll
0x00007ffaba010000 - 0x00007ffaba093000 	C:\Windows\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform10291218566899002353dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 0:36 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (400M free)
TotalPageFile size 26992M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 689M, peak: 844M
current process commit charge ("private bytes"): 1436M, peak: 2066M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
