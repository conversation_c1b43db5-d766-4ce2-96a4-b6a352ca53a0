#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 234881024 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3678), pid=996, tid=11468
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Mon Jun  2 14:38:08 2025 W. Central Africa Standard Time elapsed time: 485.542715 seconds (0d 0h 8m 5s)

---------------  T H R E A D  ---------------

Current thread (0x000001f07fc9e110):  VMThread "VM Thread" [stack: 0x000000c9e4b00000,0x000000c9e4c00000] [id=11468]

Stack: [0x000000c9e4b00000,0x000000c9e4c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0x683dd9]
V  [jvm.dll+0x6782fa]
V  [jvm.dll+0x30a797]
V  [jvm.dll+0x311d06]
V  [jvm.dll+0x36252e]
V  [jvm.dll+0x36276d]
V  [jvm.dll+0x2e1a9c]
V  [jvm.dll+0x2dfe49]
V  [jvm.dll+0x2df5ac]
V  [jvm.dll+0x3220ab]
V  [jvm.dll+0x84444d]
V  [jvm.dll+0x845183]
V  [jvm.dll+0x8456af]
V  [jvm.dll+0x845a94]
V  [jvm.dll+0x845b5e]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]

VM_Operation (0x000000c9e61f8780): G1CollectForAllocation, mode: safepoint, requested by thread 0x000001f02575e920


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001f062391590, length=89, elements={
0x000001f07df3c2f0, 0x000001f07fca5080, 0x000001f07fca5f00, 0x000001f07fcb76e0,
0x000001f07fcb9410, 0x000001f07fcbbee0, 0x000001f07fcbc8a0, 0x000001f07fcbf650,
0x000001f07fcc2030, 0x000001f07fcdc030, 0x000001f07fc8f790, 0x000001f02577f830,
0x000001f060145b40, 0x000001f0256d38d0, 0x000001f05f7ac570, 0x000001f07fd1d040,
0x000001f02575e440, 0x000001f025994580, 0x000001f02575e920, 0x000001f0607c4890,
0x000001f0607c4380, 0x000001f0607c4da0, 0x000001f0607c3960, 0x000001f0607c3450,
0x000001f0607c2a30, 0x000001f0607c3e70, 0x000001f0607c57c0, 0x000001f0607c52b0,
0x000001f0607c2520, 0x000001f0612c6530, 0x000001f0612c8390, 0x000001f0612c92c0,
0x000001f0612c7460, 0x000001f0612c46d0, 0x000001f0612c97d0, 0x000001f0612c6f50,
0x000001f0612cc050, 0x000001f0612c88a0, 0x000001f0612c50f0, 0x000001f0612c7e80,
0x000001f0612c5b10, 0x000001f0612c8db0, 0x000001f0612c9ce0, 0x000001f0612ca700,
0x000001f0612c5600, 0x000001f0612c4be0, 0x000001f060c77790, 0x000001f060c76d70,
0x000001f060c73ad0, 0x000001f060c795f0, 0x000001f060c790e0, 0x000001f060c744f0,
0x000001f060c77280, 0x000001f060c7b450, 0x000001f060c786c0, 0x000001f060c75e40,
0x000001f060c7aa30, 0x000001f060c7af40, 0x000001f060c75930, 0x000001f060c77ca0,
0x000001f060c73fe0, 0x000001f060c781b0, 0x000001f060c76350, 0x000001f060c76860,
0x000001f060ee1800, 0x000001f060edb7d0, 0x000001f060ede050, 0x000001f060edcc10,
0x000001f060edbce0, 0x000001f060edc1f0, 0x000001f060edc700, 0x000001f060eda390,
0x000001f060edfeb0, 0x000001f060ee03c0, 0x000001f060ede560, 0x000001f060ee12f0,
0x000001f060edea70, 0x000001f060ee08d0, 0x000001f060edd120, 0x000001f060edd630,
0x000001f060edf490, 0x000001f060ee0de0, 0x000001f060edef80, 0x000001f060ee1d10,
0x000001f062769240, 0x000001f0627678f0, 0x000001f06276b5b0, 0x000001f060c74f10,
0x000001f060c75420
}

Java Threads: ( => current thread )
  0x000001f07df3c2f0 JavaThread "main" [_thread_blocked, id=6268, stack(0x000000c9e4500000,0x000000c9e4600000)]
  0x000001f07fca5080 JavaThread "Reference Handler" daemon [_thread_blocked, id=7256, stack(0x000000c9e4c00000,0x000000c9e4d00000)]
  0x000001f07fca5f00 JavaThread "Finalizer" daemon [_thread_blocked, id=17256, stack(0x000000c9e4d00000,0x000000c9e4e00000)]
  0x000001f07fcb76e0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=11316, stack(0x000000c9e4e00000,0x000000c9e4f00000)]
  0x000001f07fcb9410 JavaThread "Attach Listener" daemon [_thread_blocked, id=16684, stack(0x000000c9e4f00000,0x000000c9e5000000)]
  0x000001f07fcbbee0 JavaThread "Service Thread" daemon [_thread_blocked, id=20004, stack(0x000000c9e5000000,0x000000c9e5100000)]
  0x000001f07fcbc8a0 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=10496, stack(0x000000c9e5100000,0x000000c9e5200000)]
  0x000001f07fcbf650 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=10924, stack(0x000000c9e5200000,0x000000c9e5300000)]
  0x000001f07fcc2030 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=10840, stack(0x000000c9e5300000,0x000000c9e5400000)]
  0x000001f07fcdc030 JavaThread "Sweeper thread" daemon [_thread_blocked, id=12072, stack(0x000000c9e5400000,0x000000c9e5500000)]
  0x000001f07fc8f790 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=19264, stack(0x000000c9e5500000,0x000000c9e5600000)]
  0x000001f02577f830 JavaThread "Notification Thread" daemon [_thread_blocked, id=18808, stack(0x000000c9e5600000,0x000000c9e5700000)]
  0x000001f060145b40 JavaThread "Daemon health stats" [_thread_blocked, id=18948, stack(0x000000c9e5b00000,0x000000c9e5c00000)]
  0x000001f0256d38d0 JavaThread "Incoming local TCP Connector on port 58803" [_thread_in_native, id=19152, stack(0x000000c9e5c00000,0x000000c9e5d00000)]
  0x000001f05f7ac570 JavaThread "Daemon periodic checks" [_thread_blocked, id=18068, stack(0x000000c9e5d00000,0x000000c9e5e00000)]
  0x000001f07fd1d040 JavaThread "Daemon" [_thread_blocked, id=3128, stack(0x000000c9e5e00000,0x000000c9e5f00000)]
  0x000001f02575e440 JavaThread "Handler for socket connection from /127.0.0.1:58803 to /127.0.0.1:58804" [_thread_in_native, id=3836, stack(0x000000c9e5f00000,0x000000c9e6000000)]
  0x000001f025994580 JavaThread "Cancel handler" [_thread_blocked, id=12568, stack(0x000000c9e6000000,0x000000c9e6100000)]
  0x000001f02575e920 JavaThread "Daemon worker" [_thread_blocked, id=7428, stack(0x000000c9e6100000,0x000000c9e6200000)]
  0x000001f0607c4890 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:58803 to /127.0.0.1:58804" [_thread_blocked, id=19836, stack(0x000000c9e6200000,0x000000c9e6300000)]
  0x000001f0607c4380 JavaThread "Stdin handler" [_thread_blocked, id=7272, stack(0x000000c9e6300000,0x000000c9e6400000)]
  0x000001f0607c4da0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=17248, stack(0x000000c9e6400000,0x000000c9e6500000)]
  0x000001f0607c3960 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=11576, stack(0x000000c9e6500000,0x000000c9e6600000)]
  0x000001f0607c3450 JavaThread "File lock request listener" [_thread_in_native, id=11864, stack(0x000000c9e6600000,0x000000c9e6700000)]
  0x000001f0607c2a30 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=16260, stack(0x000000c9e6700000,0x000000c9e6800000)]
  0x000001f0607c3e70 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=7500, stack(0x000000c9e6800000,0x000000c9e6900000)]
  0x000001f0607c57c0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=18920, stack(0x000000c9e6900000,0x000000c9e6a00000)]
  0x000001f0607c52b0 JavaThread "File watcher server" daemon [_thread_in_native, id=9720, stack(0x000000c9e6a00000,0x000000c9e6b00000)]
  0x000001f0607c2520 JavaThread "File watcher consumer" daemon [_thread_blocked, id=17892, stack(0x000000c9e6b00000,0x000000c9e6c00000)]
  0x000001f0612c6530 JavaThread "jar transforms" [_thread_blocked, id=7464, stack(0x000000c9e6f00000,0x000000c9e7000000)]
  0x000001f0612c8390 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=18204, stack(0x000000c9e6c00000,0x000000c9e6d00000)]
  0x000001f0612c92c0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=11552, stack(0x000000c9e7000000,0x000000c9e7100000)]
  0x000001f0612c7460 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=10252, stack(0x000000c9e7100000,0x000000c9e7200000)]
  0x000001f0612c46d0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=2436, stack(0x000000c9e7200000,0x000000c9e7300000)]
  0x000001f0612c97d0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=16728, stack(0x000000c9e7300000,0x000000c9e7400000)]
  0x000001f0612c6f50 JavaThread "Unconstrained build operations" [_thread_blocked, id=13576, stack(0x000000c9e4300000,0x000000c9e4400000)]
  0x000001f0612cc050 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=8912, stack(0x000000c9e7400000,0x000000c9e7500000)]
  0x000001f0612c88a0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=20144, stack(0x000000c9e7500000,0x000000c9e7600000)]
  0x000001f0612c50f0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=20356, stack(0x000000c9e7600000,0x000000c9e7700000)]
  0x000001f0612c7e80 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=20332, stack(0x000000c9e7700000,0x000000c9e7800000)]
  0x000001f0612c5b10 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=16196, stack(0x000000c9e7800000,0x000000c9e7900000)]
  0x000001f0612c8db0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=10236, stack(0x000000c9e7a00000,0x000000c9e7b00000)]
  0x000001f0612c9ce0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=12040, stack(0x000000c9e7b00000,0x000000c9e7c00000)]
  0x000001f0612ca700 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=19368, stack(0x000000c9e7c00000,0x000000c9e7d00000)]
  0x000001f0612c5600 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=19064, stack(0x000000c9e7d00000,0x000000c9e7e00000)]
  0x000001f0612c4be0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=4048, stack(0x000000c9e7e00000,0x000000c9e7f00000)]
  0x000001f060c77790 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=19440, stack(0x000000c9e7f00000,0x000000c9e8000000)]
  0x000001f060c76d70 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=9996, stack(0x000000c9e7900000,0x000000c9e7a00000)]
  0x000001f060c73ad0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=20476, stack(0x000000c9e8000000,0x000000c9e8100000)]
  0x000001f060c795f0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=17808, stack(0x000000c9e8100000,0x000000c9e8200000)]
  0x000001f060c790e0 JavaThread "Memory manager" [_thread_blocked, id=10268, stack(0x000000c9e8200000,0x000000c9e8300000)]
  0x000001f060c744f0 JavaThread "build event listener" [_thread_blocked, id=18292, stack(0x000000c9e8400000,0x000000c9e8500000)]
  0x000001f060c77280 JavaThread "Execution worker" [_thread_blocked, id=9920, stack(0x000000c9e8800000,0x000000c9e8900000)]
  0x000001f060c7b450 JavaThread "Execution worker Thread 2" [_thread_blocked, id=17000, stack(0x000000c9e8900000,0x000000c9e8a00000)]
  0x000001f060c786c0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=4644, stack(0x000000c9e8a00000,0x000000c9e8b00000)]
  0x000001f060c75e40 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=20468, stack(0x000000c9e8b00000,0x000000c9e8c00000)]
  0x000001f060c7aa30 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=18736, stack(0x000000c9e8c00000,0x000000c9e8d00000)]
  0x000001f060c7af40 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=2596, stack(0x000000c9e8d00000,0x000000c9e8e00000)]
  0x000001f060c75930 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=11144, stack(0x000000c9e8e00000,0x000000c9e8f00000)]
  0x000001f060c77ca0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=20224, stack(0x000000c9e8f00000,0x000000c9e9000000)]
  0x000001f060c73fe0 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=7436, stack(0x000000c9e9000000,0x000000c9e9100000)]
  0x000001f060c781b0 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=19520, stack(0x000000c9e9100000,0x000000c9e9200000)]
  0x000001f060c76350 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=19796, stack(0x000000c9e9200000,0x000000c9e9300000)]
  0x000001f060c76860 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=2364, stack(0x000000c9e9300000,0x000000c9e9400000)]
  0x000001f060ee1800 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=20360, stack(0x000000c9e9400000,0x000000c9e9500000)]
  0x000001f060edb7d0 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=20368, stack(0x000000c9e9500000,0x000000c9e9600000)]
  0x000001f060ede050 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=17512, stack(0x000000c9e9600000,0x000000c9e9700000)]
  0x000001f060edcc10 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=18848, stack(0x000000c9e9700000,0x000000c9e9800000)]
  0x000001f060edbce0 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=18428, stack(0x000000c9e9800000,0x000000c9e9900000)]
  0x000001f060edc1f0 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=8260, stack(0x000000c9e9900000,0x000000c9e9a00000)]
  0x000001f060edc700 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=18684, stack(0x000000c9e9a00000,0x000000c9e9b00000)]
  0x000001f060eda390 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=19700, stack(0x000000c9e9b00000,0x000000c9e9c00000)]
  0x000001f060edfeb0 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=2860, stack(0x000000c9e9c00000,0x000000c9e9d00000)]
  0x000001f060ee03c0 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=17492, stack(0x000000c9e9d00000,0x000000c9e9e00000)]
  0x000001f060ede560 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=17748, stack(0x000000c9e9e00000,0x000000c9e9f00000)]
  0x000001f060ee12f0 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=17288, stack(0x000000c9e9f00000,0x000000c9ea000000)]
  0x000001f060edea70 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=16724, stack(0x000000c9ea000000,0x000000c9ea100000)]
  0x000001f060ee08d0 JavaThread "jar transforms Thread 2" [_thread_blocked, id=2108, stack(0x000000c9ea100000,0x000000c9ea200000)]
  0x000001f060edd120 JavaThread "jar transforms Thread 3" [_thread_blocked, id=18128, stack(0x000000c9ea300000,0x000000c9ea400000)]
  0x000001f060edd630 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=420, stack(0x000000c9ea200000,0x000000c9ea300000)]
  0x000001f060edf490 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=19460, stack(0x000000c9ea400000,0x000000c9ea500000)]
  0x000001f060ee0de0 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=20288, stack(0x000000c9ea500000,0x000000c9ea600000)]
  0x000001f060edef80 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=10564, stack(0x000000c9ea600000,0x000000c9ea700000)]
  0x000001f060ee1d10 JavaThread "jar transforms Thread 4" [_thread_blocked, id=5040, stack(0x000000c9ea700000,0x000000c9ea800000)]
  0x000001f062769240 JavaThread "build event listener" [_thread_blocked, id=5116, stack(0x000000c9ea800000,0x000000c9ea900000)]
  0x000001f0627678f0 JavaThread "Problems report writer" [_thread_blocked, id=12520, stack(0x000000c9e8300000,0x000000c9e8400000)]
  0x000001f06276b5b0 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=13724, stack(0x000000c9e6e00000,0x000000c9e6f00000)]
  0x000001f060c74f10 JavaThread "ForkJoinPool.commonPool-worker-5" daemon [_thread_blocked, id=12616, stack(0x000000c9e8500000,0x000000c9e8600000)]
  0x000001f060c75420 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=18424, stack(0x000000c9e8600000,0x000000c9e8700000)]

Other Threads:
=>0x000001f07fc9e110 VMThread "VM Thread" [stack: 0x000000c9e4b00000,0x000000c9e4c00000] [id=11468]
  0x000001f02577fd10 WatcherThread [stack: 0x000000c9e5700000,0x000000c9e5800000] [id=17108]
  0x000001f07dfc6460 GCTaskThread "GC Thread#0" [stack: 0x000000c9e4600000,0x000000c9e4700000] [id=5004]
  0x000001f02583e750 GCTaskThread "GC Thread#1" [stack: 0x000000c9e5800000,0x000000c9e5900000] [id=19736]
  0x000001f02583ea10 GCTaskThread "GC Thread#2" [stack: 0x000000c9e5900000,0x000000c9e5a00000] [id=2388]
  0x000001f0258fac30 GCTaskThread "GC Thread#3" [stack: 0x000000c9e5a00000,0x000000c9e5b00000] [id=17500]
  0x000001f07dfd33b0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000c9e4700000,0x000000c9e4800000] [id=18436]
  0x000001f07dfd3dd0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000c9e4800000,0x000000c9e4900000] [id=19704]
  0x000001f07fb5d140 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000c9e4900000,0x000000c9e4a00000] [id=3576]
  0x000001f07fb5da80 ConcurrentGCThread "G1 Service" [stack: 0x000000c9e4a00000,0x000000c9e4b00000] [id=18104]

Threads with active compile tasks:

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001f07df37de0] Threads_lock - owner thread: 0x000001f07fc9e110
[0x000001f07df396a0] Heap_lock - owner thread: 0x000001f02575e920

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001f026000000-0x000001f026bb0000-0x000001f026bb0000), size 12255232, SharedBaseAddress: 0x000001f026000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001f027000000-0x000001f05b000000, reserved size: 872415232
Narrow klass base: 0x000001f026000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 655360K, used 341120K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 120708K, committed 122560K, reserved 983040K
  class space    used 16702K, committed 17536K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HS|  |TAMS 0x0000000680400000, 0x0000000680000000| Complete 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%| O|  |TAMS 0x0000000680800000, 0x0000000680400000| Untracked 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000680f6dc00, 0x0000000681000000| 85%| O|  |TAMS 0x0000000680f6dc00, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000682000000, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682400000, 0x0000000682400000|100%| O|  |TAMS 0x0000000682400000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682800000, 0x0000000682800000|100%| O|  |TAMS 0x0000000682800000, 0x0000000682400000| Untracked 
|  10|0x0000000682800000, 0x0000000682c00000, 0x0000000682c00000|100%|HS|  |TAMS 0x0000000682c00000, 0x0000000682800000| Complete 
|  11|0x0000000682c00000, 0x0000000683000000, 0x0000000683000000|100%|HS|  |TAMS 0x0000000683000000, 0x0000000682c00000| Complete 
|  12|0x0000000683000000, 0x0000000683400000, 0x0000000683400000|100%| O|  |TAMS 0x0000000683000000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x0000000683800000, 0x0000000683800000|100%| O|  |TAMS 0x0000000683800000, 0x0000000683400000| Untracked 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| O|  |TAMS 0x0000000683c00000, 0x0000000683800000| Untracked 
|  15|0x0000000683c00000, 0x0000000684000000, 0x0000000684000000|100%| O|  |TAMS 0x0000000684000000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684400000, 0x0000000684400000|100%| O|  |TAMS 0x0000000684400000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684800000, 0x0000000684800000|100%| O|  |TAMS 0x0000000684800000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684c00000, 0x0000000684c00000|100%| O|  |TAMS 0x0000000684c00000, 0x0000000684800000| Untracked 
|  19|0x0000000684c00000, 0x0000000685000000, 0x0000000685000000|100%|HS|  |TAMS 0x0000000685000000, 0x0000000684c00000| Complete 
|  20|0x0000000685000000, 0x0000000685400000, 0x0000000685400000|100%| O|  |TAMS 0x0000000685400000, 0x0000000685000000| Untracked 
|  21|0x0000000685400000, 0x0000000685800000, 0x0000000685800000|100%| O|  |TAMS 0x0000000685800000, 0x0000000685400000| Untracked 
|  22|0x0000000685800000, 0x0000000685c00000, 0x0000000685c00000|100%| O|  |TAMS 0x0000000685c00000, 0x0000000685800000| Untracked 
|  23|0x0000000685c00000, 0x0000000686000000, 0x0000000686000000|100%| O|  |TAMS 0x0000000686000000, 0x0000000685c00000| Untracked 
|  24|0x0000000686000000, 0x0000000686400000, 0x0000000686400000|100%| O|  |TAMS 0x0000000686400000, 0x0000000686000000| Untracked 
|  25|0x0000000686400000, 0x0000000686800000, 0x0000000686800000|100%| O|  |TAMS 0x0000000686800000, 0x0000000686400000| Untracked 
|  26|0x0000000686800000, 0x0000000686c00000, 0x0000000686c00000|100%|HS|  |TAMS 0x0000000686c00000, 0x0000000686800000| Complete 
|  27|0x0000000686c00000, 0x0000000687000000, 0x0000000687000000|100%| O|  |TAMS 0x0000000687000000, 0x0000000686c00000| Untracked 
|  28|0x0000000687000000, 0x0000000687400000, 0x0000000687400000|100%| O|  |TAMS 0x0000000687400000, 0x0000000687000000| Untracked 
|  29|0x0000000687400000, 0x0000000687800000, 0x0000000687800000|100%| O|  |TAMS 0x0000000687800000, 0x0000000687400000| Untracked 
|  30|0x0000000687800000, 0x0000000687c00000, 0x0000000687c00000|100%| O|  |TAMS 0x0000000687c00000, 0x0000000687800000| Untracked 
|  31|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%| O|  |TAMS 0x0000000688000000, 0x0000000687c00000| Untracked 
|  32|0x0000000688000000, 0x0000000688400000, 0x0000000688400000|100%| O|  |TAMS 0x0000000688400000, 0x0000000688000000| Untracked 
|  33|0x0000000688400000, 0x0000000688800000, 0x0000000688800000|100%| O|  |TAMS 0x0000000688800000, 0x0000000688400000| Untracked 
|  34|0x0000000688800000, 0x0000000688c00000, 0x0000000688c00000|100%| O|  |TAMS 0x0000000688c00000, 0x0000000688800000| Untracked 
|  35|0x0000000688c00000, 0x0000000689000000, 0x0000000689000000|100%| O|  |TAMS 0x0000000689000000, 0x0000000688c00000| Untracked 
|  36|0x0000000689000000, 0x0000000689400000, 0x0000000689400000|100%| O|  |TAMS 0x0000000689400000, 0x0000000689000000| Untracked 
|  37|0x0000000689400000, 0x0000000689800000, 0x0000000689800000|100%| O|  |TAMS 0x0000000689800000, 0x0000000689400000| Untracked 
|  38|0x0000000689800000, 0x0000000689c00000, 0x0000000689c00000|100%| O|  |TAMS 0x0000000689c00000, 0x0000000689800000| Untracked 
|  39|0x0000000689c00000, 0x000000068a000000, 0x000000068a000000|100%| O|  |TAMS 0x000000068a000000, 0x0000000689c00000| Untracked 
|  40|0x000000068a000000, 0x000000068a400000, 0x000000068a400000|100%| O|  |TAMS 0x000000068a400000, 0x000000068a000000| Untracked 
|  41|0x000000068a400000, 0x000000068a800000, 0x000000068a800000|100%| O|  |TAMS 0x000000068a400000, 0x000000068a400000| Untracked 
|  42|0x000000068a800000, 0x000000068ac00000, 0x000000068ac00000|100%| O|  |TAMS 0x000000068ac00000, 0x000000068a800000| Untracked 
|  43|0x000000068ac00000, 0x000000068b000000, 0x000000068b000000|100%| O|  |TAMS 0x000000068b000000, 0x000000068ac00000| Untracked 
|  44|0x000000068b000000, 0x000000068b400000, 0x000000068b400000|100%| O|  |TAMS 0x000000068b400000, 0x000000068b000000| Untracked 
|  45|0x000000068b400000, 0x000000068b800000, 0x000000068b800000|100%| O|  |TAMS 0x000000068b613400, 0x000000068b400000| Untracked 
|  46|0x000000068b800000, 0x000000068bc00000, 0x000000068bc00000|100%| O|  |TAMS 0x000000068b800000, 0x000000068b800000| Untracked 
|  47|0x000000068bc00000, 0x000000068c000000, 0x000000068c000000|100%| O|  |TAMS 0x000000068bc00000, 0x000000068bc00000| Untracked 
|  48|0x000000068c000000, 0x000000068c400000, 0x000000068c400000|100%| O|  |TAMS 0x000000068c000000, 0x000000068c000000| Untracked 
|  49|0x000000068c400000, 0x000000068c800000, 0x000000068c800000|100%| O|  |TAMS 0x000000068c400000, 0x000000068c400000| Untracked 
|  50|0x000000068c800000, 0x000000068cc00000, 0x000000068cc00000|100%| O|  |TAMS 0x000000068c800000, 0x000000068c800000| Untracked 
|  51|0x000000068cc00000, 0x000000068d000000, 0x000000068d000000|100%| O|  |TAMS 0x000000068cc00000, 0x000000068cc00000| Untracked 
|  52|0x000000068d000000, 0x000000068d400000, 0x000000068d400000|100%| O|  |TAMS 0x000000068d000000, 0x000000068d000000| Untracked 
|  53|0x000000068d400000, 0x000000068d800000, 0x000000068d800000|100%| O|  |TAMS 0x000000068d400000, 0x000000068d400000| Untracked 
|  54|0x000000068d800000, 0x000000068dc00000, 0x000000068dc00000|100%| O|  |TAMS 0x000000068d800000, 0x000000068d800000| Untracked 
|  55|0x000000068dc00000, 0x000000068e000000, 0x000000068e000000|100%| O|  |TAMS 0x000000068dc00000, 0x000000068dc00000| Untracked 
|  56|0x000000068e000000, 0x000000068e400000, 0x000000068e400000|100%| O|  |TAMS 0x000000068e000000, 0x000000068e000000| Untracked 
|  57|0x000000068e400000, 0x000000068e800000, 0x000000068e800000|100%| O|  |TAMS 0x000000068e400000, 0x000000068e400000| Untracked 
|  58|0x000000068e800000, 0x000000068ec00000, 0x000000068ec00000|100%| O|  |TAMS 0x000000068e800000, 0x000000068e800000| Untracked 
|  59|0x000000068ec00000, 0x000000068f000000, 0x000000068f000000|100%| O|  |TAMS 0x000000068ec00000, 0x000000068ec00000| Untracked 
|  60|0x000000068f000000, 0x000000068f400000, 0x000000068f400000|100%| O|  |TAMS 0x000000068f000000, 0x000000068f000000| Untracked 
|  61|0x000000068f400000, 0x000000068f800000, 0x000000068f800000|100%| O|  |TAMS 0x000000068f400000, 0x000000068f400000| Untracked 
|  62|0x000000068f800000, 0x000000068fc00000, 0x000000068fc00000|100%| O|  |TAMS 0x000000068f800000, 0x000000068f800000| Untracked 
|  63|0x000000068fc00000, 0x0000000690000000, 0x0000000690000000|100%| O|  |TAMS 0x000000068fc00000, 0x000000068fc00000| Untracked 
|  64|0x0000000690000000, 0x0000000690400000, 0x0000000690400000|100%| O|  |TAMS 0x0000000690000000, 0x0000000690000000| Untracked 
|  65|0x0000000690400000, 0x0000000690800000, 0x0000000690800000|100%| O|  |TAMS 0x0000000690400000, 0x0000000690400000| Untracked 
|  66|0x0000000690800000, 0x0000000690c00000, 0x0000000690c00000|100%| O|  |TAMS 0x0000000690800000, 0x0000000690800000| Untracked 
|  67|0x0000000690c00000, 0x0000000691000000, 0x0000000691000000|100%| O|  |TAMS 0x0000000690c00000, 0x0000000690c00000| Untracked 
|  68|0x0000000691000000, 0x0000000691400000, 0x0000000691400000|100%| O|  |TAMS 0x0000000691000000, 0x0000000691000000| Untracked 
|  69|0x0000000691400000, 0x0000000691800000, 0x0000000691800000|100%| O|  |TAMS 0x0000000691400000, 0x0000000691400000| Untracked 
|  70|0x0000000691800000, 0x0000000691c00000, 0x0000000691c00000|100%| O|  |TAMS 0x0000000691800000, 0x0000000691800000| Untracked 
|  71|0x0000000691c00000, 0x0000000692000000, 0x0000000692000000|100%| O|  |TAMS 0x0000000691c00000, 0x0000000691c00000| Untracked 
|  72|0x0000000692000000, 0x0000000692400000, 0x0000000692400000|100%| O|  |TAMS 0x0000000692000000, 0x0000000692000000| Untracked 
|  73|0x0000000692400000, 0x0000000692800000, 0x0000000692800000|100%| O|  |TAMS 0x0000000692400000, 0x0000000692400000| Untracked 
|  74|0x0000000692800000, 0x0000000692c00000, 0x0000000692c00000|100%| O|  |TAMS 0x0000000692800000, 0x0000000692800000| Untracked 
|  75|0x0000000692c00000, 0x0000000693000000, 0x0000000693000000|100%| O|  |TAMS 0x0000000692c00000, 0x0000000692c00000| Untracked 
|  76|0x0000000693000000, 0x0000000693400000, 0x0000000693400000|100%| O|  |TAMS 0x0000000693000000, 0x0000000693000000| Untracked 
|  77|0x0000000693400000, 0x00000006935b2600, 0x0000000693800000| 42%| O|  |TAMS 0x0000000693400000, 0x0000000693400000| Untracked 
|  78|0x0000000693800000, 0x0000000693800000, 0x0000000693c00000|  0%| F|  |TAMS 0x0000000693800000, 0x0000000693800000| Untracked 
|  79|0x0000000693c00000, 0x0000000693c00000, 0x0000000694000000|  0%| F|  |TAMS 0x0000000693c00000, 0x0000000693c00000| Untracked 
|  80|0x0000000694000000, 0x0000000694000000, 0x0000000694400000|  0%| F|  |TAMS 0x0000000694000000, 0x0000000694000000| Untracked 
|  81|0x0000000694400000, 0x0000000694400000, 0x0000000694800000|  0%| F|  |TAMS 0x0000000694400000, 0x0000000694400000| Untracked 
|  82|0x0000000694800000, 0x0000000694800000, 0x0000000694c00000|  0%| F|  |TAMS 0x0000000694800000, 0x0000000694800000| Untracked 
|  83|0x0000000694c00000, 0x0000000694c00000, 0x0000000695000000|  0%| F|  |TAMS 0x0000000694c00000, 0x0000000694c00000| Untracked 
|  84|0x0000000695000000, 0x0000000695000000, 0x0000000695400000|  0%| F|  |TAMS 0x0000000695000000, 0x0000000695000000| Untracked 
|  85|0x0000000695400000, 0x0000000695400000, 0x0000000695800000|  0%| F|  |TAMS 0x0000000695400000, 0x0000000695400000| Untracked 
|  86|0x0000000695800000, 0x0000000695800000, 0x0000000695c00000|  0%| F|  |TAMS 0x0000000695800000, 0x0000000695800000| Untracked 
|  87|0x0000000695c00000, 0x0000000695c00000, 0x0000000696000000|  0%| F|  |TAMS 0x0000000695c00000, 0x0000000695c00000| Untracked 
|  88|0x0000000696000000, 0x0000000696000000, 0x0000000696400000|  0%| F|  |TAMS 0x0000000696000000, 0x0000000696000000| Untracked 
|  89|0x0000000696400000, 0x0000000696400000, 0x0000000696800000|  0%| F|  |TAMS 0x0000000696400000, 0x0000000696400000| Untracked 
|  90|0x0000000696800000, 0x0000000696800000, 0x0000000696c00000|  0%| F|  |TAMS 0x0000000696800000, 0x0000000696800000| Untracked 
|  91|0x0000000696c00000, 0x0000000696c00000, 0x0000000697000000|  0%| F|  |TAMS 0x0000000696c00000, 0x0000000696c00000| Untracked 
|  92|0x0000000697000000, 0x0000000697000000, 0x0000000697400000|  0%| F|  |TAMS 0x0000000697000000, 0x0000000697000000| Untracked 
|  93|0x0000000697400000, 0x0000000697400000, 0x0000000697800000|  0%| F|  |TAMS 0x0000000697400000, 0x0000000697400000| Untracked 
|  94|0x0000000697800000, 0x0000000697800000, 0x0000000697c00000|  0%| F|  |TAMS 0x0000000697800000, 0x0000000697800000| Untracked 
|  95|0x0000000697c00000, 0x0000000697c00000, 0x0000000698000000|  0%| F|  |TAMS 0x0000000697c00000, 0x0000000697c00000| Untracked 
|  96|0x0000000698000000, 0x0000000698000000, 0x0000000698400000|  0%| F|  |TAMS 0x0000000698000000, 0x0000000698000000| Untracked 
|  97|0x0000000698400000, 0x0000000698400000, 0x0000000698800000|  0%| F|  |TAMS 0x0000000698400000, 0x0000000698400000| Untracked 
|  98|0x0000000698800000, 0x0000000698800000, 0x0000000698c00000|  0%| F|  |TAMS 0x0000000698800000, 0x0000000698800000| Untracked 
|  99|0x0000000698c00000, 0x0000000698c00000, 0x0000000699000000|  0%| F|  |TAMS 0x0000000698c00000, 0x0000000698c00000| Untracked 
| 100|0x0000000699000000, 0x0000000699000000, 0x0000000699400000|  0%| F|  |TAMS 0x0000000699000000, 0x0000000699000000| Untracked 
| 101|0x0000000699400000, 0x0000000699400000, 0x0000000699800000|  0%| F|  |TAMS 0x0000000699400000, 0x0000000699400000| Untracked 
| 102|0x0000000699800000, 0x0000000699800000, 0x0000000699c00000|  0%| F|  |TAMS 0x0000000699800000, 0x0000000699800000| Untracked 
| 103|0x0000000699c00000, 0x0000000699c00000, 0x000000069a000000|  0%| F|  |TAMS 0x0000000699c00000, 0x0000000699c00000| Untracked 
| 104|0x000000069a000000, 0x000000069a000000, 0x000000069a400000|  0%| F|  |TAMS 0x000000069a000000, 0x000000069a000000| Untracked 
| 105|0x000000069a400000, 0x000000069a400000, 0x000000069a800000|  0%| F|  |TAMS 0x000000069a400000, 0x000000069a400000| Untracked 
| 106|0x000000069a800000, 0x000000069a800000, 0x000000069ac00000|  0%| F|  |TAMS 0x000000069a800000, 0x000000069a800000| Untracked 
| 107|0x000000069ac00000, 0x000000069ac00000, 0x000000069b000000|  0%| F|  |TAMS 0x000000069ac00000, 0x000000069ac00000| Untracked 
| 108|0x000000069b000000, 0x000000069b000000, 0x000000069b400000|  0%| F|  |TAMS 0x000000069b000000, 0x000000069b000000| Untracked 
| 109|0x000000069b400000, 0x000000069b400000, 0x000000069b800000|  0%| F|  |TAMS 0x000000069b400000, 0x000000069b400000| Untracked 
| 110|0x000000069b800000, 0x000000069b800000, 0x000000069bc00000|  0%| F|  |TAMS 0x000000069b800000, 0x000000069b800000| Untracked 
| 111|0x000000069bc00000, 0x000000069bc00000, 0x000000069c000000|  0%| F|  |TAMS 0x000000069bc00000, 0x000000069bc00000| Untracked 
| 112|0x000000069c000000, 0x000000069c000000, 0x000000069c400000|  0%| F|  |TAMS 0x000000069c000000, 0x000000069c000000| Untracked 
| 113|0x000000069c400000, 0x000000069c400000, 0x000000069c800000|  0%| F|  |TAMS 0x000000069c400000, 0x000000069c400000| Untracked 
| 114|0x000000069c800000, 0x000000069c800000, 0x000000069cc00000|  0%| F|  |TAMS 0x000000069c800000, 0x000000069c800000| Untracked 
| 115|0x000000069cc00000, 0x000000069cc00000, 0x000000069d000000|  0%| F|  |TAMS 0x000000069cc00000, 0x000000069cc00000| Untracked 
| 116|0x000000069d000000, 0x000000069d400000, 0x000000069d400000|100%| S|CS|TAMS 0x000000069d000000, 0x000000069d000000| Complete 
| 117|0x000000069d400000, 0x000000069d800000, 0x000000069d800000|100%| S|CS|TAMS 0x000000069d400000, 0x000000069d400000| Complete 
| 118|0x000000069d800000, 0x000000069dc00000, 0x000000069dc00000|100%| S|CS|TAMS 0x000000069d800000, 0x000000069d800000| Complete 
| 119|0x000000069dc00000, 0x000000069e000000, 0x000000069e000000|100%| S|CS|TAMS 0x000000069dc00000, 0x000000069dc00000| Complete 
| 120|0x000000069e000000, 0x000000069e400000, 0x000000069e400000|100%| S|CS|TAMS 0x000000069e000000, 0x000000069e000000| Complete 
| 121|0x000000069e400000, 0x000000069e400000, 0x000000069e800000|  0%| F|  |TAMS 0x000000069e400000, 0x000000069e400000| Untracked 
| 122|0x000000069e800000, 0x000000069e800000, 0x000000069ec00000|  0%| F|  |TAMS 0x000000069e800000, 0x000000069e800000| Untracked 
| 123|0x000000069ec00000, 0x000000069ec00000, 0x000000069f000000|  0%| F|  |TAMS 0x000000069ec00000, 0x000000069ec00000| Untracked 
| 124|0x000000069f000000, 0x000000069f000000, 0x000000069f400000|  0%| F|  |TAMS 0x000000069f000000, 0x000000069f000000| Untracked 
| 125|0x000000069f400000, 0x000000069f400000, 0x000000069f800000|  0%| F|  |TAMS 0x000000069f400000, 0x000000069f400000| Untracked 
| 126|0x000000069f800000, 0x000000069f800000, 0x000000069fc00000|  0%| F|  |TAMS 0x000000069f800000, 0x000000069f800000| Untracked 
| 127|0x000000069fc00000, 0x000000069fc00000, 0x00000006a0000000|  0%| F|  |TAMS 0x000000069fc00000, 0x000000069fc00000| Untracked 
| 128|0x00000006a0000000, 0x00000006a0000000, 0x00000006a0400000|  0%| F|  |TAMS 0x00000006a0000000, 0x00000006a0000000| Untracked 
| 129|0x00000006a0400000, 0x00000006a0400000, 0x00000006a0800000|  0%| F|  |TAMS 0x00000006a0400000, 0x00000006a0400000| Untracked 
| 130|0x00000006a0800000, 0x00000006a0800000, 0x00000006a0c00000|  0%| F|  |TAMS 0x00000006a0800000, 0x00000006a0800000| Untracked 
| 131|0x00000006a0c00000, 0x00000006a0c00000, 0x00000006a1000000|  0%| F|  |TAMS 0x00000006a0c00000, 0x00000006a0c00000| Untracked 
| 132|0x00000006a1000000, 0x00000006a1000000, 0x00000006a1400000|  0%| F|  |TAMS 0x00000006a1000000, 0x00000006a1000000| Untracked 
| 133|0x00000006a1400000, 0x00000006a1400000, 0x00000006a1800000|  0%| F|  |TAMS 0x00000006a1400000, 0x00000006a1400000| Untracked 
| 134|0x00000006a1800000, 0x00000006a1800000, 0x00000006a1c00000|  0%| F|  |TAMS 0x00000006a1800000, 0x00000006a1800000| Untracked 
| 135|0x00000006a1c00000, 0x00000006a1c00000, 0x00000006a2000000|  0%| F|  |TAMS 0x00000006a1c00000, 0x00000006a1c00000| Untracked 
| 136|0x00000006a2000000, 0x00000006a2000000, 0x00000006a2400000|  0%| F|  |TAMS 0x00000006a2000000, 0x00000006a2000000| Untracked 
| 137|0x00000006a2400000, 0x00000006a2400000, 0x00000006a2800000|  0%| F|  |TAMS 0x00000006a2400000, 0x00000006a2400000| Untracked 
| 138|0x00000006a2800000, 0x00000006a2800000, 0x00000006a2c00000|  0%| F|  |TAMS 0x00000006a2800000, 0x00000006a2800000| Untracked 
| 139|0x00000006a2c00000, 0x00000006a2c00000, 0x00000006a3000000|  0%| F|  |TAMS 0x00000006a2c00000, 0x00000006a2c00000| Untracked 
| 140|0x00000006a3000000, 0x00000006a3000000, 0x00000006a3400000|  0%| F|  |TAMS 0x00000006a3000000, 0x00000006a3000000| Untracked 
| 141|0x00000006a3400000, 0x00000006a3400000, 0x00000006a3800000|  0%| F|  |TAMS 0x00000006a3400000, 0x00000006a3400000| Untracked 
| 142|0x00000006a3800000, 0x00000006a3800000, 0x00000006a3c00000|  0%| F|  |TAMS 0x00000006a3800000, 0x00000006a3800000| Untracked 
| 143|0x00000006a3c00000, 0x00000006a3c00000, 0x00000006a4000000|  0%| F|  |TAMS 0x00000006a3c00000, 0x00000006a3c00000| Untracked 
| 144|0x00000006a4000000, 0x00000006a4000000, 0x00000006a4400000|  0%| F|  |TAMS 0x00000006a4000000, 0x00000006a4000000| Untracked 
| 145|0x00000006a4400000, 0x00000006a4400000, 0x00000006a4800000|  0%| F|  |TAMS 0x00000006a4400000, 0x00000006a4400000| Untracked 
| 146|0x00000006a4800000, 0x00000006a4800000, 0x00000006a4c00000|  0%| F|  |TAMS 0x00000006a4800000, 0x00000006a4800000| Untracked 
| 147|0x00000006a4c00000, 0x00000006a4c00000, 0x00000006a5000000|  0%| F|  |TAMS 0x00000006a4c00000, 0x00000006a4c00000| Untracked 
| 148|0x00000006a5000000, 0x00000006a5000000, 0x00000006a5400000|  0%| F|  |TAMS 0x00000006a5000000, 0x00000006a5000000| Untracked 
| 149|0x00000006a5400000, 0x00000006a5400000, 0x00000006a5800000|  0%| F|  |TAMS 0x00000006a5400000, 0x00000006a5400000| Untracked 
| 150|0x00000006a5800000, 0x00000006a5800000, 0x00000006a5c00000|  0%| F|  |TAMS 0x00000006a5800000, 0x00000006a5800000| Untracked 
| 151|0x00000006a5c00000, 0x00000006a5c00000, 0x00000006a6000000|  0%| F|  |TAMS 0x00000006a5c00000, 0x00000006a5c00000| Untracked 
| 152|0x00000006a6000000, 0x00000006a6000000, 0x00000006a6400000|  0%| F|  |TAMS 0x00000006a6000000, 0x00000006a6000000| Untracked 
| 153|0x00000006a6400000, 0x00000006a6400000, 0x00000006a6800000|  0%| F|  |TAMS 0x00000006a6400000, 0x00000006a6400000| Untracked 
| 154|0x00000006a6800000, 0x00000006a6800000, 0x00000006a6c00000|  0%| F|  |TAMS 0x00000006a6800000, 0x00000006a6800000| Untracked 
| 155|0x00000006a6c00000, 0x00000006a6c00000, 0x00000006a7000000|  0%| F|  |TAMS 0x00000006a6c00000, 0x00000006a6c00000| Untracked 
| 156|0x00000006a7000000, 0x00000006a7000000, 0x00000006a7400000|  0%| F|  |TAMS 0x00000006a7000000, 0x00000006a7000000| Untracked 
| 157|0x00000006a7400000, 0x00000006a7400000, 0x00000006a7800000|  0%| F|  |TAMS 0x00000006a7400000, 0x00000006a7400000| Untracked 
| 158|0x00000006a7800000, 0x00000006a7800000, 0x00000006a7c00000|  0%| F|  |TAMS 0x00000006a7800000, 0x00000006a7800000| Untracked 
|1535|0x00000007ffc00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x0000000800000000, 0x00000007ffc00000| Untracked 

Card table byte_map: [0x000001f07ebf0000,0x000001f07f7f0000] _byte_map_base: 0x000001f07b7f0000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001f07dfc6a80, (CMBitMap*) 0x000001f07dfc6ac0
 Prev Bits: [0x000001f017670000, 0x000001f01d670000)
 Next Bits: [0x000001f01d670000, 0x000001f023670000)

Polling page: 0x000001f07bc50000

Metaspace:

Usage:
  Non-class:    101.57 MB used.
      Class:     16.31 MB used.
       Both:    117.88 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     102.56 MB ( 80%) committed,  2 nodes.
      Class space:      832.00 MB reserved,      17.12 MB (  2%) committed,  1 nodes.
             Both:      960.00 MB reserved,     119.69 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  9.22 MB
       Class:  14.88 MB
        Both:  24.10 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 181.19 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 2148.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1915.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 7867.
num_chunk_merges: 12.
num_chunk_splits: 4884.
num_chunks_enlarged: 2883.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=13829Kb max_used=13829Kb free=106170Kb
 bounds [0x000001f00f540000, 0x000001f0102d0000, 0x000001f016a70000]
CodeHeap 'profiled nmethods': size=120000Kb used=31080Kb max_used=31196Kb free=88919Kb
 bounds [0x000001f007a70000, 0x000001f0098f0000, 0x000001f00efa0000]
CodeHeap 'non-nmethods': size=5760Kb used=2493Kb max_used=2588Kb free=3266Kb
 bounds [0x000001f00efa0000, 0x000001f00f240000, 0x000001f00f540000]
 total_blobs=17072 nmethods=16030 adapters=953
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 484.877 Thread 0x000001f07fcc2030 nmethod 19623 0x000001f0086f0110 code [0x000001f0086f02c0, 0x000001f0086f04a8]
Event: 485.262 Thread 0x000001f07fcbf650 nmethod 19617 0x000001f0102b8390 code [0x000001f0102b8840, 0x000001f0102bcb10]
Event: 485.263 Thread 0x000001f07fcbf650 19618       4       org.gradle.api.internal.artifacts.transform.TransformChain::requiresDependencies (33 bytes)
Event: 485.269 Thread 0x000001f07fcbf650 nmethod 19618 0x000001f0102c0890 code [0x000001f0102c0a20, 0x000001f0102c0bc8]
Event: 485.269 Thread 0x000001f07fcbf650 19621       4       org.gradle.api.internal.artifacts.transform.DefaultTransformedVariantFactory$VariantKey::hashCode (16 bytes)
Event: 485.271 Thread 0x000001f07fcbf650 nmethod 19621 0x000001f0102c0e10 code [0x000001f0102c0fa0, 0x000001f0102c1098]
Event: 485.303 Thread 0x000001f07fcc2030 19624       3       org.gradle.execution.plan.NodeComparator::compare (13 bytes)
Event: 485.304 Thread 0x000001f07fcc2030 nmethod 19624 0x000001f0086efb10 code [0x000001f0086efcc0, 0x000001f0086effe8]
Event: 485.304 Thread 0x000001f07fcc2030 19625       3       org.gradle.api.internal.AbstractTask::compareTo (35 bytes)
Event: 485.307 Thread 0x000001f07fcc2030 nmethod 19625 0x000001f0082d3310 code [0x000001f0082d3660, 0x000001f0082d5138]
Event: 485.307 Thread 0x000001f07fcc2030 19626       3       org.gradle.execution.plan.NodeSets::newSortedNodeSet (11 bytes)
Event: 485.308 Thread 0x000001f07fcc2030 nmethod 19626 0x000001f0086ef110 code [0x000001f0086ef300, 0x000001f0086ef898]
Event: 485.309 Thread 0x000001f07fcc2030 19627       1       org.gradle.execution.plan.Node::getGroup (5 bytes)
Event: 485.310 Thread 0x000001f07fcc2030 nmethod 19627 0x000001f0102c1210 code [0x000001f0102c13a0, 0x000001f0102c1478]
Event: 485.310 Thread 0x000001f07fcc2030 19628       3       com.google.common.collect.Iterators$ConcatenatedIterator::next (33 bytes)
Event: 485.310 Thread 0x000001f07fcc2030 nmethod 19628 0x000001f0082d2c90 code [0x000001f0082d2e60, 0x000001f0082d31e8]
Event: 485.320 Thread 0x000001f07fcc2030 19629       3       org.gradle.internal.reflect.annotations.impl.DefaultTypeAnnotationMetadataStore::extractPropertiesFrom (85 bytes)
Event: 485.322 Thread 0x000001f07fcc2030 nmethod 19629 0x000001f0082d1790 code [0x000001f0082d1a40, 0x000001f0082d27c8]
Event: 485.322 Thread 0x000001f07fcc2030 19630       3       org.gradle.internal.reflect.annotations.impl.DefaultTypeAnnotationMetadataStore::extractFunctionsFrom (88 bytes)
Event: 485.325 Thread 0x000001f07fcc2030 nmethod 19630 0x000001f008efdf90 code [0x000001f008efe2e0, 0x000001f008effa78]

GC Heap History (20 events):
Event: 391.377 GC heap after
{Heap after GC invocations=86 (full 0):
 garbage-first heap   total 655360K, used 262561K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 112734K, committed 114368K, reserved 983040K
  class space    used 15639K, committed 16384K, reserved 851968K
}
Event: 392.281 GC heap before
{Heap before GC invocations=86 (full 0):
 garbage-first heap   total 655360K, used 287137K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 112734K, committed 114368K, reserved 983040K
  class space    used 15639K, committed 16384K, reserved 851968K
}
Event: 392.294 GC heap after
{Heap after GC invocations=87 (full 0):
 garbage-first heap   total 655360K, used 263402K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 112734K, committed 114368K, reserved 983040K
  class space    used 15639K, committed 16384K, reserved 851968K
}
Event: 393.321 GC heap before
{Heap before GC invocations=87 (full 0):
 garbage-first heap   total 655360K, used 292074K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 112734K, committed 114368K, reserved 983040K
  class space    used 15639K, committed 16384K, reserved 851968K
}
Event: 393.330 GC heap after
{Heap after GC invocations=88 (full 0):
 garbage-first heap   total 655360K, used 263856K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 112734K, committed 114368K, reserved 983040K
  class space    used 15639K, committed 16384K, reserved 851968K
}
Event: 396.559 GC heap before
{Heap before GC invocations=88 (full 0):
 garbage-first heap   total 655360K, used 329392K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 17 young (69632K), 1 survivors (4096K)
 Metaspace       used 112993K, committed 114624K, reserved 983040K
  class space    used 15668K, committed 16384K, reserved 851968K
}
Event: 396.582 GC heap after
{Heap after GC invocations=89 (full 0):
 garbage-first heap   total 655360K, used 272770K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 112993K, committed 114624K, reserved 983040K
  class space    used 15668K, committed 16384K, reserved 851968K
}
Event: 396.934 GC heap before
{Heap before GC invocations=89 (full 0):
 garbage-first heap   total 655360K, used 289154K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 3 survivors (12288K)
 Metaspace       used 112994K, committed 114624K, reserved 983040K
  class space    used 15668K, committed 16384K, reserved 851968K
}
Event: 396.954 GC heap after
{Heap after GC invocations=90 (full 0):
 garbage-first heap   total 655360K, used 273833K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 112994K, committed 114624K, reserved 983040K
  class space    used 15668K, committed 16384K, reserved 851968K
}
Event: 399.233 GC heap before
{Heap before GC invocations=90 (full 0):
 garbage-first heap   total 655360K, used 355753K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 22 young (90112K), 1 survivors (4096K)
 Metaspace       used 113237K, committed 114880K, reserved 983040K
  class space    used 15696K, committed 16448K, reserved 851968K
}
Event: 399.251 GC heap after
{Heap after GC invocations=91 (full 0):
 garbage-first heap   total 655360K, used 277825K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 113237K, committed 114880K, reserved 983040K
  class space    used 15696K, committed 16448K, reserved 851968K
}
Event: 448.995 GC heap before
{Heap before GC invocations=91 (full 0):
 garbage-first heap   total 655360K, used 466241K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 48 young (196608K), 2 survivors (8192K)
 Metaspace       used 113648K, committed 115328K, reserved 983040K
  class space    used 15751K, committed 16512K, reserved 851968K
}
Event: 449.080 GC heap after
{Heap after GC invocations=92 (full 0):
 garbage-first heap   total 655360K, used 292665K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 113648K, committed 115328K, reserved 983040K
  class space    used 15751K, committed 16512K, reserved 851968K
}
Event: 451.072 GC heap before
{Heap before GC invocations=92 (full 0):
 garbage-first heap   total 655360K, used 382777K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 29 young (118784K), 6 survivors (24576K)
 Metaspace       used 113648K, committed 115328K, reserved 983040K
  class space    used 15751K, committed 16512K, reserved 851968K
}
Event: 451.211 GC heap after
{Heap after GC invocations=93 (full 0):
 garbage-first heap   total 655360K, used 295336K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 113648K, committed 115328K, reserved 983040K
  class space    used 15751K, committed 16512K, reserved 851968K
}
Event: 463.830 GC heap before
{Heap before GC invocations=93 (full 0):
 garbage-first heap   total 655360K, used 455080K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 42 young (172032K), 2 survivors (8192K)
 Metaspace       used 114516K, committed 116224K, reserved 983040K
  class space    used 15871K, committed 16640K, reserved 851968K
}
Event: 463.877 GC heap after
{Heap after GC invocations=94 (full 0):
 garbage-first heap   total 655360K, used 310340K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 5 survivors (20480K)
 Metaspace       used 114516K, committed 116224K, reserved 983040K
  class space    used 15871K, committed 16640K, reserved 851968K
}
Event: 466.974 GC heap before
{Heap before GC invocations=94 (full 0):
 garbage-first heap   total 655360K, used 351300K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 15 young (61440K), 5 survivors (20480K)
 Metaspace       used 115309K, committed 117120K, reserved 983040K
  class space    used 15973K, committed 16768K, reserved 851968K
}
Event: 467.084 GC heap after
{Heap after GC invocations=95 (full 0):
 garbage-first heap   total 655360K, used 316302K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 115309K, committed 117120K, reserved 983040K
  class space    used 15973K, committed 16768K, reserved 851968K
}
Event: 485.392 GC heap before
{Heap before GC invocations=95 (full 0):
 garbage-first heap   total 655360K, used 463758K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 38 young (155648K), 2 survivors (8192K)
 Metaspace       used 120708K, committed 122560K, reserved 983040K
  class space    used 16702K, committed 17536K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.083 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.153 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 0.465 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 0.470 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 0.480 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 0.487 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 0.492 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 1.852 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 2.956 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 3.786 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 3.832 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 20.984 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 21.006 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 21.980 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 24.381 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 40.169 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform17503492365716545503dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 480.632 Thread 0x000001f02575e920 DEOPT PACKING pc=0x000001f00fdfff40 sp=0x000000c9e61f8220
Event: 480.632 Thread 0x000001f02575e920 DEOPT UNPACKING pc=0x000001f00eff69a3 sp=0x000000c9e61f81c0 mode 2
Event: 483.327 Thread 0x000001f02575e920 DEOPT PACKING pc=0x000001f0085f7990 sp=0x000000c9e61f8de0
Event: 483.327 Thread 0x000001f02575e920 DEOPT UNPACKING pc=0x000001f00eff7143 sp=0x000000c9e61f8288 mode 0
Event: 484.785 Thread 0x000001f02575e920 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f01007d820 relative=0x0000000000002ea0
Event: 484.785 Thread 0x000001f02575e920 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f01007d820 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.RepositoryChainArtifactResolver.findSourceRepository(Lorg/gradle/internal/component/model/ModuleSources;)Lorg/gra
Event: 484.786 Thread 0x000001f02575e920 DEOPT PACKING pc=0x000001f01007d820 sp=0x000000c9e61f9b60
Event: 484.786 Thread 0x000001f02575e920 DEOPT UNPACKING pc=0x000001f00eff69a3 sp=0x000000c9e61f9990 mode 2
Event: 484.787 Thread 0x000001f02575e920 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f01007d820 relative=0x0000000000002ea0
Event: 484.788 Thread 0x000001f02575e920 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f01007d820 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.RepositoryChainArtifactResolver.findSourceRepository(Lorg/gradle/internal/component/model/ModuleSources;)Lorg/gra
Event: 484.788 Thread 0x000001f02575e920 DEOPT PACKING pc=0x000001f01007d820 sp=0x000000c9e61f9b60
Event: 484.788 Thread 0x000001f02575e920 DEOPT UNPACKING pc=0x000001f00eff69a3 sp=0x000000c9e61f9990 mode 2
Event: 484.792 Thread 0x000001f02575e920 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f01007d820 relative=0x0000000000002ea0
Event: 484.792 Thread 0x000001f02575e920 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f01007d820 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.RepositoryChainArtifactResolver.findSourceRepository(Lorg/gradle/internal/component/model/ModuleSources;)Lorg/gra
Event: 484.792 Thread 0x000001f02575e920 DEOPT PACKING pc=0x000001f01007d820 sp=0x000000c9e61f9b60
Event: 484.792 Thread 0x000001f02575e920 DEOPT UNPACKING pc=0x000001f00eff69a3 sp=0x000000c9e61f9990 mode 2
Event: 484.794 Thread 0x000001f02575e920 Uncommon trap: trap_request=0xffffffde fr.pc=0x000001f01007d820 relative=0x0000000000002ea0
Event: 484.794 Thread 0x000001f02575e920 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000001f01007d820 method=org.gradle.api.internal.artifacts.ivyservice.ivyresolve.RepositoryChainArtifactResolver.findSourceRepository(Lorg/gradle/internal/component/model/ModuleSources;)Lorg/gra
Event: 484.794 Thread 0x000001f02575e920 DEOPT PACKING pc=0x000001f01007d820 sp=0x000000c9e61f9b60
Event: 484.794 Thread 0x000001f02575e920 DEOPT UNPACKING pc=0x000001f00eff69a3 sp=0x000000c9e61f9990 mode 2

Classes loaded (20 events):
Event: 470.112 Loading class org/xml/sax/ext/Locator2 done
Event: 470.112 Loading class com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy done
Event: 470.129 Loading class org/xml/sax/helpers/AttributesImpl
Event: 470.130 Loading class org/xml/sax/helpers/AttributesImpl done
Event: 470.136 Loading class java/util/regex/Pattern$Behind
Event: 470.138 Loading class java/util/regex/Pattern$Behind done
Event: 470.138 Loading class java/util/regex/Pattern$Neg
Event: 470.138 Loading class java/util/regex/Pattern$Neg done
Event: 470.170 Loading class javax/xml/datatype/DatatypeConfigurationException
Event: 470.170 Loading class javax/xml/datatype/DatatypeConfigurationException done
Event: 470.173 Loading class jdk/internal/reflect/UnsafeBooleanFieldAccessorImpl
Event: 470.174 Loading class jdk/internal/reflect/UnsafeBooleanFieldAccessorImpl done
Event: 470.175 Loading class jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl
Event: 470.175 Loading class jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl done
Event: 475.561 Loading class java/io/CharArrayWriter
Event: 475.564 Loading class java/io/CharArrayWriter done
Event: 475.637 Loading class java/io/SequenceInputStream
Event: 475.638 Loading class java/io/SequenceInputStream done
Event: 475.746 Loading class java/util/function/IntUnaryOperator
Event: 475.747 Loading class java/util/function/IntUnaryOperator done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 484.418 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e90bec0}: com/android/build/gradle/tasks/PrefabPackageConfigurationTaskCustomizer> (0x000000069e90bec0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.420 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e92d6e0}: com/android/build/gradle/tasks/PrefabPackageConfigurationTask_DecoratedCustomizer> (0x000000069e92d6e0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.452 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ea21cd8}: com/android/build/gradle/internal/tasks/PackageRenderscriptTask_DecoratedBeanInfo> (0x000000069ea21cd8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.455 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ea323b8}: com/android/build/gradle/internal/tasks/PackageRenderscriptTaskBeanInfo> (0x000000069ea323b8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.459 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ea3f5e0}: org/gradle/api/tasks/SyncBeanInfo> (0x000000069ea3f5e0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.460 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ea4c7e8}: org/gradle/api/tasks/SyncCustomizer> (0x000000069ea4c7e8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.466 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ea75e10}: com/android/build/gradle/internal/tasks/PackageRenderscriptTaskCustomizer> (0x000000069ea75e10) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.493 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ea9f308}: com/android/build/gradle/internal/tasks/PackageRenderscriptTask_DecoratedCustomizer> (0x000000069ea9f308) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.528 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ebab990}: com/android/build/gradle/internal/res/GenerateLibraryRFileTask_DecoratedBeanInfo> (0x000000069ebab990) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.561 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ebbbfb0}: com/android/build/gradle/internal/res/GenerateLibraryRFileTaskBeanInfo> (0x000000069ebbbfb0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.562 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ebcc328}: com/android/build/gradle/tasks/ProcessAndroidResourcesBeanInfo> (0x000000069ebcc328) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.653 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ebdc8e0}: com/android/build/gradle/internal/tasks/NewIncrementalTaskBeanInfo> (0x000000069ebdc8e0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.658 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069ebece98}: com/android/build/gradle/internal/tasks/NewIncrementalTaskCustomizer> (0x000000069ebece98) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.683 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e40c5f0}: com/android/build/gradle/tasks/ProcessAndroidResourcesCustomizer> (0x000000069e40c5f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.684 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e42dbd0}: com/android/build/gradle/internal/res/GenerateLibraryRFileTaskCustomizer> (0x000000069e42dbd0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.691 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e464178}: com/android/build/gradle/internal/res/GenerateLibraryRFileTask_DecoratedCustomizer> (0x000000069e464178) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.725 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e551718}: com/android/build/gradle/tasks/ZipMergingTask_DecoratedBeanInfo> (0x000000069e551718) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.727 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e5618f0}: com/android/build/gradle/tasks/ZipMergingTaskBeanInfo> (0x000000069e5618f0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.728 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e571b80}: com/android/build/gradle/tasks/ZipMergingTaskCustomizer> (0x000000069e571b80) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 484.771 Thread 0x000001f02575e920 Exception <a 'java/lang/ClassNotFoundException'{0x000000069e594d10}: com/android/build/gradle/tasks/ZipMergingTask_DecoratedCustomizer> (0x000000069e594d10) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 476.004 Executing VM operation: Cleanup done
Event: 477.017 Executing VM operation: Cleanup
Event: 477.017 Executing VM operation: Cleanup done
Event: 478.028 Executing VM operation: Cleanup
Event: 478.044 Executing VM operation: Cleanup done
Event: 479.055 Executing VM operation: Cleanup
Event: 479.120 Executing VM operation: Cleanup done
Event: 480.124 Executing VM operation: Cleanup
Event: 480.126 Executing VM operation: Cleanup done
Event: 481.009 Executing VM operation: HandshakeAllThreads
Event: 481.009 Executing VM operation: HandshakeAllThreads done
Event: 482.020 Executing VM operation: Cleanup
Event: 482.021 Executing VM operation: Cleanup done
Event: 483.032 Executing VM operation: Cleanup
Event: 483.034 Executing VM operation: Cleanup done
Event: 484.042 Executing VM operation: Cleanup
Event: 484.053 Executing VM operation: Cleanup done
Event: 485.059 Executing VM operation: Cleanup
Event: 485.251 Executing VM operation: Cleanup done
Event: 485.392 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 469.947 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009553010
Event: 469.947 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009554f10
Event: 469.947 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009555a90
Event: 469.947 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009559090
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f00956e190
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009576790
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f00957b290
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f0095abb90
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f0095ae310
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f0095ba710
Event: 469.948 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f0095c0890
Event: 469.949 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f0095fda90
Event: 469.949 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f00962be90
Event: 469.952 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009640f10
Event: 469.952 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009645790
Event: 469.953 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009650290
Event: 469.953 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f009653090
Event: 469.953 Thread 0x000001f07fcdc030 flushing nmethod 0x000001f00969ed10
Event: 483.306 Thread 0x000001f069af1fd0 Thread added: 0x000001f069af1fd0
Event: 484.223 Thread 0x000001f069af1fd0 Thread exited: 0x000001f069af1fd0


Dynamic libraries:
0x00007ff617650000 - 0x00007ff61765e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ff8010d0000 - 0x00007ff8012e7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ff800080000 - 0x00007ff800144000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffffe1f0000 - 0x00007ffffe5c1000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffffe7b0000 - 0x00007ffffe8c1000 	C:\Windows\System32\ucrtbase.dll
0x00007fffe1c70000 - 0x00007fffe1c87000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007fffff590000 - 0x00007fffff741000 	C:\Windows\System32\USER32.dll
0x00007ffffeaa0000 - 0x00007ffffeac6000 	C:\Windows\System32\win32u.dll
0x00007fffff030000 - 0x00007fffff059000 	C:\Windows\System32\GDI32.dll
0x00007ffffe5d0000 - 0x00007ffffe6eb000 	C:\Windows\System32\gdi32full.dll
0x00007ffffea00000 - 0x00007ffffea9a000 	C:\Windows\System32\msvcp_win.dll
0x00007fffe21f0000 - 0x00007fffe220d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007fffe70a0000 - 0x00007fffe7332000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007fffff750000 - 0x00007fffff7f7000 	C:\Windows\System32\msvcrt.dll
0x00007ffffeff0000 - 0x00007fffff021000 	C:\Windows\System32\IMM32.DLL
0x00007fffe53a0000 - 0x00007fffe53ac000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007fffad6b0000 - 0x00007fffad73d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007fff3fb40000 - 0x00007fff407b0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ff8003d0000 - 0x00007ff800481000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffffef40000 - 0x00007ffffefe7000 	C:\Windows\System32\sechost.dll
0x00007ffffe950000 - 0x00007ffffe978000 	C:\Windows\System32\bcrypt.dll
0x00007fffff060000 - 0x00007fffff174000 	C:\Windows\System32\RPCRT4.dll
0x00007fffff180000 - 0x00007fffff1f1000 	C:\Windows\System32\WS2_32.dll
0x00007ffffe0d0000 - 0x00007ffffe11d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffff3930000 - 0x00007ffff3964000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffff2020000 - 0x00007ffff202a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffffe0b0000 - 0x00007ffffe0c3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffffd210000 - 0x00007ffffd228000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007fffe4860000 - 0x00007fffe486a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffff8bd0000 - 0x00007ffff8e02000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007fffff200000 - 0x00007fffff590000 	C:\Windows\System32\combase.dll
0x00007ff800270000 - 0x00007ff800347000 	C:\Windows\System32\OLEAUT32.dll
0x00007fffde290000 - 0x00007fffde2c2000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffffe8d0000 - 0x00007ffffe94b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007fffe3120000 - 0x00007fffe312e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007fffd61c0000 - 0x00007fffd61e5000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007fff90da0000 - 0x00007fff90e77000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ff800620000 - 0x00007ff800ea8000 	C:\Windows\System32\SHELL32.dll
0x00007ffffec40000 - 0x00007ffffed7f000 	C:\Windows\System32\wintypes.dll
0x00007ffffc110000 - 0x00007ffffca1d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ff800160000 - 0x00007ff80026a000 	C:\Windows\System32\SHCORE.dll
0x00007ff800020000 - 0x00007ff80007e000 	C:\Windows\System32\shlwapi.dll
0x00007ffffe130000 - 0x00007ffffe15b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007fffd5500000 - 0x00007fffd5518000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007fffdf850000 - 0x00007fffdf86a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffff2c90000 - 0x00007ffff2dbc000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffffd670000 - 0x00007ffffd6da000 	C:\Windows\system32\mswsock.dll
0x00007fffdf680000 - 0x00007fffdf696000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007fffe3b90000 - 0x00007fffe3ba0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007fffe3130000 - 0x00007fffe3157000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000072f20000 - 0x0000000072f93000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007fffe3ad0000 - 0x00007fffe3ada000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007fffe30c0000 - 0x00007fffe30cb000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ff800ed0000 - 0x00007ff800ed8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffffd9c0000 - 0x00007ffffd9db000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffffd170000 - 0x00007ffffd1a7000 	C:\Windows\system32\rsaenh.dll
0x00007ffffd710000 - 0x00007ffffd738000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffffd9a0000 - 0x00007ffffd9ac000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffffcc80000 - 0x00007ffffccad000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ff800010000 - 0x00007ff800019000 	C:\Windows\System32\NSI.dll
0x00007ffff2e20000 - 0x00007ffff2e39000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffff2b60000 - 0x00007ffff2b7f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffffccf0000 - 0x00007ffffcdf2000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007fffeae90000 - 0x00007fffeae99000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007fffe6700000 - 0x00007fffe670e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffffead0000 - 0x00007ffffec36000 	C:\Windows\System32\CRYPT32.dll
0x00007ffffdad0000 - 0x00007ffffdafd000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffffda90000 - 0x00007ffffdac7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007fffbc3e0000 - 0x00007fffbc3e8000 	C:\Windows\system32\wshunix.dll
0x0000000072ea0000 - 0x0000000072f13000 	C:\Users\<USER>\AppData\Local\Temp\native-platform17503492365716545503dir\gradle-fileevents.dll
0x00007fffe8960000 - 0x00007fffe896a000 	C:\Windows\System32\rasadhlp.dll
0x00007fffef860000 - 0x00007fffef8e3000 	C:\Windows\System32\fwpuclnt.dll
0x00007fffdf370000 - 0x00007fffdf387000 	C:\Windows\system32\napinsp.dll
0x00007fffd75c0000 - 0x00007fffd75db000 	C:\Windows\system32\pnrpnsp.dll
0x00007fffd75a0000 - 0x00007fffd75b1000 	C:\Windows\System32\winrnr.dll
0x00007ffff2c20000 - 0x00007ffff2c35000 	C:\Windows\system32\wshbth.dll
0x00007fffd7570000 - 0x00007fffd7597000 	C:\Windows\system32\nlansp_c.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform17503492365716545503dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 3:41 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for processor 0
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495
Processor Information for processor 1
  Max Mhz: 2712, Current Mhz: 1506, Mhz Limit: 2495
Processor Information for processor 2
  Max Mhz: 2712, Current Mhz: 1506, Mhz Limit: 2495
Processor Information for processor 3
  Max Mhz: 2712, Current Mhz: 1506, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (351M free)
TotalPageFile size 25047M (AvailPageFile size 144M)
current process WorkingSet (physical memory assigned to process): 605M, peak: 605M
current process commit charge ("private bytes"): 1060M, peak: 1284M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
