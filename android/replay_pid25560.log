JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 347 ciObject found
instanceKlass com/google/common/reflect/TypeVisitor
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass org/gradle/api/internal/component/SoftwareComponentContainerInternal
instanceKlass org/gradle/api/internal/component/UsageContext
instanceKlass org/gradle/api/component/SoftwareComponentVariant
instanceKlass org/gradle/api/publish/internal/component/DefaultAdhocSoftwareComponent
instanceKlass org/gradle/api/internal/component/SoftwareComponentInternal
instanceKlass org/gradle/api/component/AdhocComponentWithVariants
instanceKlass org/gradle/api/plugins/jvm/internal/DefaultJvmTestSuite
instanceKlass org/gradle/testing/base/internal/DefaultTestingExtension
instanceKlass org/gradle/testing/base/plugins/TestSuiteBasePlugin
instanceKlass org/gradle/testing/base/TestingExtension
instanceKlass org/gradle/api/plugins/jvm/JvmTestSuiteTarget
instanceKlass org/gradle/testing/base/TestSuiteTarget
instanceKlass org/gradle/api/attributes/TestSuiteType
instanceKlass org/gradle/api/plugins/JvmTestSuitePlugin
instanceKlass org/gradle/api/plugins/JavaPluginConvention
instanceKlass org/gradle/internal/jvm/DefaultModularitySpec
instanceKlass org/gradle/api/jvm/ModularitySpec
instanceKlass org/gradle/api/java/archives/Manifest
instanceKlass org/gradle/language/java/internal/JavaLanguageServices$JavaProjectScopeServices$1
instanceKlass org/gradle/api/internal/tasks/compile/daemon/CompilerWorkerExecutor
instanceKlass org/gradle/language/base/internal/compile/Compiler
instanceKlass org/gradle/api/internal/tasks/compile/DefaultJavaCompilerFactory
instanceKlass org/gradle/api/internal/tasks/compile/processing/AnnotationProcessorDeclarationSerializer
instanceKlass org/gradle/api/internal/tasks/compile/processing/AnnotationProcessorDetector$ProcessorServiceLocator
instanceKlass org/gradle/process/internal/worker/child/DefaultWorkerDirectoryProvider
instanceKlass org/gradle/workers/internal/WorkerDaemonClientCancellationHandler$KillWorkers
instanceKlass org/gradle/workers/internal/WorkerDaemonExpiration
instanceKlass org/gradle/workers/internal/WorkerDaemonClientsManager$LogLevelChangeEventListener
instanceKlass org/gradle/workers/internal/WorkerDaemonClientsManager$StopSessionScopedWorkers
instanceKlass org/gradle/workers/internal/WorkerDaemonStarter
instanceKlass org/gradle/workers/internal/WorkerDaemonClient
instanceKlass org/gradle/process/internal/health/memory/MemoryHolder
instanceKlass org/gradle/workers/internal/DefaultActionExecutionSpecFactory
instanceKlass org/gradle/internal/snapshot/impl/ArrayOfPrimitiveValueSnapshot
instanceKlass org/gradle/internal/snapshot/impl/EnumValueSnapshot
instanceKlass org/gradle/internal/snapshot/impl/AbstractMapSnapshot
instanceKlass org/gradle/workers/internal/IsolatableSerializerRegistry$IsolatableSerializer
instanceKlass org/gradle/process/internal/worker/child/ApplicationClassesInSystemClassLoaderWorkerImplementationFactory
instanceKlass org/gradle/process/internal/worker/MultiRequestWorkerProcessBuilder
instanceKlass org/gradle/process/internal/worker/SingleRequestWorkerProcessBuilder
instanceKlass org/gradle/process/internal/worker/WorkerProcessBuilder
instanceKlass org/gradle/process/internal/worker/WorkerProcessSettings
instanceKlass org/gradle/process/internal/worker/DefaultWorkerProcessFactory
instanceKlass org/gradle/process/internal/health/memory/DefaultMemoryManager$MemoryCheck
instanceKlass org/gradle/process/internal/health/memory/DefaultMemoryManager$OsMemoryListener
instanceKlass org/gradle/process/internal/health/memory/DefaultAvailableOsMemoryStatusAspect
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusAspect$Available
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusSnapshot
instanceKlass net/rubygrapefruit/platform/internal/jni/WindowsMemoryFunctions
instanceKlass net/rubygrapefruit/platform/internal/DefaultWindowsMemoryInfo
instanceKlass net/rubygrapefruit/platform/memory/WindowsMemoryInfo
instanceKlass net/rubygrapefruit/platform/memory/MemoryInfo
instanceKlass net/rubygrapefruit/platform/internal/DefaultWindowsMemory
instanceKlass org/gradle/process/internal/health/memory/JvmMemoryStatusListener
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatusAspect
instanceKlass org/gradle/process/internal/health/memory/DefaultMemoryManager
instanceKlass org/gradle/process/internal/health/memory/JvmMemoryStatus
instanceKlass org/gradle/process/internal/health/memory/DefaultJvmMemoryInfo
instanceKlass org/gradle/process/internal/health/memory/OsMemoryStatus
instanceKlass org/gradle/process/internal/health/memory/WindowsOsMemoryInfo
instanceKlass org/gradle/process/internal/health/memory/MBeanAttributeProvider
instanceKlass org/gradle/process/internal/health/memory/DefaultOsMemoryInfo
instanceKlass org/gradle/internal/jvm/inspection/DefaultJvmVersionDetector
instanceKlass org/gradle/internal/remote/internal/hub/MessageHubBackedServer
instanceKlass org/gradle/jvm/toolchain/JavadocTool
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchain
instanceKlass org/gradle/jvm/toolchain/JavaInstallationMetadata
instanceKlass org/gradle/internal/jvm/inspection/JvmVendor$1
instanceKlass org/gradle/internal/jvm/inspection/JvmVendor
instanceKlass org/gradle/jvm/toolchain/JvmImplementation
instanceKlass org/gradle/jvm/toolchain/JvmVendorSpec
instanceKlass org/gradle/jvm/toolchain/internal/DefaultToolchainSpec
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchainQueryService$1
instanceKlass net/rubygrapefruit/platform/internal/MutableSystemInfo
instanceKlass net/rubygrapefruit/platform/internal/DefaultSystemInfo
instanceKlass org/gradle/internal/jvm/inspection/DefaultJavaInstallationRegistry$Installations
instanceKlass org/gradle/api/internal/provider/sources/GradlePropertyValueSource$Parameters$Inject
instanceKlass org/gradle/jvm/toolchain/internal/AutoInstalledInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/CurrentInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/LocationListInstallationSupplier
instanceKlass org/gradle/jvm/toolchain/internal/EnvironmentVariableListInstallationSupplier
instanceKlass org/gradle/internal/jvm/inspection/DefaultJavaInstallationRegistry
instanceKlass org/gradle/internal/jvm/inspection/JavaInstallationCapability$1
instanceKlass org/gradle/internal/RenderingUtils
instanceKlass org/gradle/jvm/toolchain/internal/install/DefaultJdkCacheDirectory
instanceKlass net/rubygrapefruit/platform/internal/DefaultWindowsRegistry
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
instanceKlass com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
instanceKlass javax/xml/parsers/FactoryFinder$1
instanceKlass javax/xml/parsers/FactoryFinder
instanceKlass javax/xml/parsers/DocumentBuilderFactory
instanceKlass jdk/xml/internal/XMLSecurityManager
instanceKlass jdk/xml/internal/JdkXmlFeatures
instanceKlass javax/xml/xpath/XPathFactoryFinder$2
instanceKlass javax/xml/xpath/XPathFactoryFinder
instanceKlass jdk/xml/internal/SecuritySupport
instanceKlass javax/xml/xpath/XPathFactory
instanceKlass org/gradle/internal/xml/XmlFactories
instanceKlass org/gradle/api/internal/provider/sources/GradlePropertyValueSource$Parameters_Decorated
instanceKlass org/gradle/api/internal/provider/sources/GradlePropertyValueSource$Parameters
instanceKlass org/gradle/api/internal/provider/sources/AbstractPropertyValueSource$Parameters
instanceKlass org/gradle/api/internal/provider/sources/AbstractPropertyValueSource
instanceKlass org/gradle/api/plugins/JvmToolchainsPlugin
instanceKlass org/gradle/api/reporting/ReportSpec
instanceKlass org/gradle/api/reporting/ReportingExtension
instanceKlass org/gradle/api/plugins/ReportingBasePlugin
instanceKlass org/gradle/api/internal/artifacts/type/DefaultArtifactTypeContainer$DefaultArtifactTypeDefinition
instanceKlass org/gradle/internal/service/scopes/DefaultProjectFinder
instanceKlass org/gradle/api/internal/tasks/DefaultSourceSetContainer$1
instanceKlass org/gradle/api/plugins/JvmEcosystemPlugin
instanceKlass org/gradle/api/internal/project/DefaultProjectStateRegistry$CalculatedModelValueImpl
instanceKlass org/gradle/api/internal/plugins/BuildConfigurationRule
instanceKlass org/gradle/api/internal/file/DefaultFilePropertyFactory$PathToDirectoryTransformer
instanceKlass org/gradle/api/plugins/internal/DefaultBasePluginExtension
instanceKlass org/gradle/language/base/internal/plugins/CleanRule
instanceKlass org/gradle/language/base/plugins/LifecycleBasePlugin
instanceKlass org/gradle/api/plugins/BasePluginConvention
instanceKlass org/gradle/api/internal/plugins/DefaultArtifactPublicationSet
instanceKlass org/gradle/api/plugins/BasePlugin
instanceKlass org/gradle/api/plugins/internal/JavaConfigurationVariantMapping
instanceKlass org/gradle/api/internal/tasks/compile/HasCompileOptions
instanceKlass org/gradle/api/plugins/BasePluginExtension
instanceKlass org/gradle/api/file/SourceDirectorySet
instanceKlass org/gradle/api/plugins/internal/DefaultJavaPluginExtension
instanceKlass org/gradle/api/plugins/JavaPluginExtension
instanceKlass org/gradle/api/plugins/JavaBasePlugin$BackwardCompatibilityOutputDirectoryConvention
instanceKlass org/gradle/api/plugins/JavaBasePlugin
instanceKlass org/gradle/api/plugins/JavaPlatformPlugin
instanceKlass org/gradle/api/internal/plugins/PluginManagerInternal$PluginWithId
instanceKlass org/gradle/api/publish/maven/MavenPublication
instanceKlass org/gradle/api/publish/plugins/PublishingPlugin
instanceKlass org/gradle/api/tasks/VerificationTask
instanceKlass org/gradle/api/plugins/jvm/JvmTestSuite
instanceKlass org/gradle/testing/base/TestSuite
instanceKlass org/gradle/api/publish/ivy/IvyPublication
instanceKlass org/gradle/api/publish/Publication
instanceKlass org/gradle/api/plugins/jvm/internal/JvmFeatureInternal
instanceKlass org/gradle/jvm/component/internal/JvmSoftwareComponentInternal
instanceKlass org/gradle/api/plugins/JavaPlugin
instanceKlass org/gradle/api/plugins/JavaLibraryPlugin
instanceKlass org/gradle/plugin/devel/PluginDeclaration
instanceKlass org/gradle/plugin/devel/GradlePluginDevelopmentExtension
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultExcludeEverything
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/CachingExcludeFactory$ExcludePair
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultModuleIdSetExclude
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/NormalizingExcludeFactory$FlattenOperationResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ExcludeEverything
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/specs/ExcludeAllOf
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/CachingExcludeFactory$ExcludesKey
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/simple/DefaultModuleIdExclude
instanceKlass org/apache/ivy/plugins/matcher/PatternMatcher
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/PatternMatchers
instanceKlass org/gradle/api/internal/artifacts/capability/DefaultSpecificCapabilitySelector
instanceKlass org/gradle/api/internal/artifacts/capability/SpecificCapabilitySelector
instanceKlass org/gradle/api/internal/artifacts/capability/CapabilitySelectorInternal
instanceKlass org/gradle/internal/component/external/model/AbstractMutableModuleComponentResolveMetadata$DependencyConstraintImpl
instanceKlass org/gradle/internal/component/external/model/ComponentVariant$DependencyConstraint
instanceKlass org/gradle/internal/component/external/descriptor/DefaultExclude
instanceKlass org/gradle/internal/component/model/Exclude
instanceKlass org/gradle/plugin/use/resolve/internal/SimplePluginResolution
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$AddOpensCommandLineArgumentProvider
instanceKlass org/gradle/process/CommandLineArgumentProvider
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$LocalPluginPublication
instanceKlass org/gradle/plugin/use/resolve/internal/local/PluginPublication
instanceKlass org/gradle/api/internal/artifacts/ivyservice/projectmodule/ProjectPublication
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$PluginValidationAction
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$ClassManifestCollectorAction
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$PluginDescriptorCollectorAction
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$PluginValidationActionsState
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin$TestKitAndPluginClasspathDependenciesAction
instanceKlass org/gradle/plugin/devel/plugins/JavaGradlePluginPlugin
instanceKlass org/gradle/plugin/management/internal/MultiPluginRequests
instanceKlass org/gradle/api/internal/provider/DefaultValueSourceProviderFactory$ObtainedValueHolder
instanceKlass org/gradle/api/internal/provider/DefaultValueSourceProviderFactory$DefaultObtainedValue
instanceKlass org/gradle/api/internal/artifacts/dependencies/DefaultPluginDependency
instanceKlass org/gradle/api/internal/catalog/PluginDependencyValueSource$Params$Inject
instanceKlass java/io/ObjectInputStream$GetField
instanceKlass jdk/internal/access/JavaObjectInputFilterAccess
instanceKlass java/io/ObjectInputFilter$Config$BuiltinFilterFactory
instanceKlass java/io/ObjectInputFilter
instanceKlass sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
instanceKlass jdk/internal/logger/LoggerFinderLoader
instanceKlass jdk/internal/logger/LazyLoggers$LazyLoggerFactories
instanceKlass jdk/internal/logger/LazyLoggers$1
instanceKlass jdk/internal/logger/LazyLoggers
instanceKlass java/io/ObjectInputFilter$Config
instanceKlass java/io/ObjectInputStream$ValidationList
instanceKlass java/io/ObjectInputStream$HandleTable$HandleList
instanceKlass java/io/ObjectInputStream$HandleTable
instanceKlass jdk/internal/access/JavaObjectInputStreamReadString
instanceKlass jdk/internal/access/JavaObjectInputStreamAccess
instanceKlass org/gradle/internal/snapshot/impl/JavaSerializedValueSnapshot
instanceKlass com/google/common/collect/ImmutableList$SerializedForm
instanceKlass java/io/ObjectStreamClass$ExceptionInfo
instanceKlass com/google/common/collect/ImmutableMap$SerializedForm
instanceKlass java/io/ObjectStreamClass$ClassDataSlot
instanceKlass java/io/ObjectStreamClass$5
instanceKlass java/io/ObjectStreamClass$4
instanceKlass java/io/ObjectStreamClass$3
instanceKlass java/io/ObjectStreamClass$MemberSignature
instanceKlass java/io/ObjectStreamClass$1
instanceKlass java/io/ObjectStreamClass$FieldReflector
instanceKlass java/io/ObjectStreamClass$FieldReflectorKey
instanceKlass java/io/ObjectStreamClass$2
instanceKlass java/io/ClassCache
instanceKlass java/io/ObjectStreamClass$Caches
instanceKlass java/io/ObjectStreamClass
instanceKlass java/io/Bits
instanceKlass java/io/ObjectOutputStream$ReplaceTable
instanceKlass java/io/ObjectOutputStream$HandleTable
instanceKlass org/gradle/internal/snapshot/impl/ImplementationValue
instanceKlass org/gradle/api/internal/provider/ValueSourceProviderFactory$ValueListener$ObtainedValue
instanceKlass org/gradle/api/internal/provider/DefaultValueSourceProviderFactory$LazilyObtainedValue
instanceKlass org/gradle/api/internal/provider/DefaultValueSourceProviderFactory$DefaultValueSourceSpec
instanceKlass org/gradle/api/internal/catalog/PluginDependencyValueSource$Params_Decorated
instanceKlass org/gradle/api/internal/catalog/PluginDependencyValueSource$Params
instanceKlass org/gradle/plugin/use/PluginDependency
instanceKlass org/gradle/api/internal/catalog/PluginDependencyValueSource
instanceKlass kotlin/collections/MapWithDefault
instanceKlass org/gradle/kotlin/dsl/support/PluginDependenciesSpecScopeInternalServices_Decorated
instanceKlass org/gradle/kotlin/dsl/support/PluginDependenciesSpecScopeInternalServices
instanceKlass org/gradle/kotlin/dsl/VersionCatalogAccessorsKt
instanceKlass org/gradle/kotlin/dsl/support/CompiledKotlinPluginsBlock
instanceKlass org/gradle/kotlin/dsl/accessors/AbstractStage1BlockAccessorsUnitOfWork$identify$1
instanceKlass org/gradle/kotlin/dsl/accessors/AbstractStage1BlockAccessorsUnitOfWork$visitIdentityInputs$1
instanceKlass org/gradle/kotlin/dsl/accessors/AbstractStage1BlockAccessorsUnitOfWork$Companion
instanceKlass org/gradle/kotlin/dsl/accessors/AbstractStage1BlockAccessorsUnitOfWork
instanceKlass org/gradle/api/internal/artifacts/ProjectBackedModule
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementProjectScopeServices$ProjectBackedModuleMetaDataProvider
instanceKlass org/gradle/api/internal/resources/ApiTextResourceAdapter
instanceKlass org/gradle/api/resources/internal/TextResourceInternal
instanceKlass org/gradle/internal/resource/transfer/CachingTextUriResourceLoader
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/DefaultExternalResourceAccessor
instanceKlass org/gradle/util/internal/DistributionLocator
instanceKlass org/gradle/api/tasks/wrapper/WrapperVersionsResources
instanceKlass org/gradle/buildinit/plugins/WrapperPlugin
instanceKlass org/gradle/buildinit/plugins/BuildInitPlugin
instanceKlass org/gradle/kotlin/dsl/tooling/builders/AbstractKotlinDslScriptsModelBuilder$Companion
instanceKlass org/gradle/kotlin/dsl/tooling/builders/AbstractKotlinDslScriptsModelBuilder
instanceKlass org/gradle/kotlin/dsl/tooling/builders/internal/IsolatedScriptsModelBuilder
instanceKlass org/gradle/kotlin/dsl/tooling/builders/KotlinBuildScriptTemplateModelBuilder
instanceKlass org/gradle/kotlin/dsl/tooling/builders/KotlinBuildScriptModelBuilder
instanceKlass org/gradle/declarative/dsl/tooling/builders/DeclarativeSchemaModelBuilder
instanceKlass org/gradle/tooling/internal/protocol/cpp/InternalCppTestSuite
instanceKlass org/gradle/tooling/internal/protocol/cpp/InternalCppLibrary
instanceKlass org/gradle/tooling/internal/protocol/cpp/InternalCppApplication
instanceKlass org/gradle/language/cpp/internal/tooling/DefaultCppComponentModel
instanceKlass org/gradle/language/cpp/CppComponent
instanceKlass org/gradle/language/ComponentWithTargetMachines
instanceKlass org/gradle/language/ComponentWithDependencies
instanceKlass org/gradle/language/ComponentWithBinaries
instanceKlass org/gradle/language/cpp/internal/tooling/CppModelBuilder
instanceKlass org/gradle/tooling/provider/model/internal/PluginApplyingBuilder
instanceKlass org/gradle/plugins/ide/idea/model/IdeaModule
instanceKlass org/gradle/plugins/ide/internal/tooling/IsolatedIdeaModuleInternalBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/IsolatedGradleProjectInternalBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/BuildEnvironmentBuilder
instanceKlass org/gradle/tooling/model/GradleModuleVersion
instanceKlass org/gradle/plugins/ide/internal/tooling/PublicationsBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/model/TaskNameComparator
instanceKlass org/gradle/plugins/ide/internal/tooling/BuildInvocationsBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/BasicIdeaModelBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/model/PartialBasicGradleProject
instanceKlass org/gradle/plugins/ide/internal/tooling/GradleBuildBuilder
instanceKlass org/gradle/tooling/provider/model/internal/BuildScopeModelBuilder
instanceKlass org/gradle/plugins/ide/internal/configurer/HierarchicalElementAdapter
instanceKlass org/gradle/plugins/ide/internal/configurer/EclipseModelAwareUniqueProjectNameProvider
instanceKlass org/gradle/plugins/ide/eclipse/model/AbstractClasspathEntry
instanceKlass org/gradle/plugins/ide/eclipse/model/ClasspathEntry
instanceKlass org/objectweb/asm/Opcodes
instanceKlass org/gradle/plugins/ide/internal/tooling/EclipseModelBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/RunEclipseTasksBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/RunBuildDependenciesTaskBuilder
instanceKlass org/gradle/tooling/provider/model/ParameterizedToolingModelBuilder
instanceKlass org/gradle/tooling/model/idea/IdeaCompilerOutput
instanceKlass org/gradle/tooling/model/idea/IdeaLanguageLevel
instanceKlass org/gradle/plugins/ide/internal/tooling/IdeaModelBuilder
instanceKlass org/gradle/plugins/ide/internal/tooling/model/LaunchableGradleTask
instanceKlass org/gradle/tooling/internal/protocol/InternalLaunchable
instanceKlass org/gradle/tooling/internal/gradle/GradleProjectIdentity
instanceKlass org/gradle/tooling/internal/gradle/GradleBuildIdentity
instanceKlass org/gradle/tooling/internal/protocol/InternalProtocolInterface
instanceKlass org/gradle/plugins/ide/internal/tooling/GradleProjectBuilder
instanceKlass org/gradle/tooling/provider/model/internal/DefaultToolingModelBuilderRegistry$RegistrationImpl
instanceKlass org/gradle/tooling/provider/model/internal/DefaultToolingModelBuilderRegistry$VoidToolingModelBuilder
instanceKlass org/gradle/tooling/provider/model/internal/ToolingModelBuilderLookup$Builder
instanceKlass org/gradle/tooling/provider/model/internal/ToolingModelBuilderLookup$Registration
instanceKlass org/gradle/declarative/dsl/tooling/builders/internal/BuildScopeToolingServices$createIdeBuildScopeToolingModelBuilderRegistryAction$1
instanceKlass org/gradle/plugins/ide/internal/tooling/GradleProjectBuilderInternal
instanceKlass org/gradle/plugins/ide/internal/tooling/IdeaModelBuilderInternal
instanceKlass org/gradle/plugins/ide/internal/tooling/ToolingModelServices$BuildScopeToolingServices$1
instanceKlass org/gradle/api/internal/project/DefaultProjectTaskLister
instanceKlass org/gradle/tooling/provider/model/internal/DefaultIntermediateToolingModelProvider
instanceKlass org/gradle/tooling/provider/model/internal/ToolingModelProjectDependencyListener
instanceKlass org/gradle/api/internal/collections/FilteredElementSource$FilteringIterator
instanceKlass org/gradle/api/internal/collections/CollectionFilter$1
instanceKlass org/gradle/internal/reflect/MethodSet$1
instanceKlass org/gradle/api/internal/DefaultNamedDomainObjectCollection$FilteredIndex
instanceKlass org/gradle/api/internal/collections/DefaultCollectionEventRegister$FilteredEventRegister
instanceKlass org/gradle/api/internal/collections/FilteredElementSource
instanceKlass org/objectweb/asm/Context
instanceKlass org/gradle/api/internal/DefaultNamedDomainObjectCollection$ProviderBackedElementInfo
instanceKlass org/gradle/api/internal/provider/Collectors$ElementFromProvider
instanceKlass org/gradle/api/internal/provider/ChangingValue
instanceKlass org/gradle/api/internal/provider/ValueSupplier$SideEffect
instanceKlass org/gradle/api/internal/provider/ValueSupplier$ExecutionTimeValue
instanceKlass org/gradle/api/internal/tasks/DefaultTaskContainer$RegisterDetails
instanceKlass org/gradle/api/internal/tasks/RegisterTaskBuildOperationType$Details
instanceKlass org/gradle/api/internal/tasks/DefaultTaskContainer$3
instanceKlass java/util/function/LongUnaryOperator
instanceKlass org/gradle/model/internal/registry/RuleBindings$ScopeIndex
instanceKlass org/gradle/model/internal/registry/RuleBindings$PredicateMatches
instanceKlass org/gradle/model/internal/registry/DefaultModelRegistry$5
instanceKlass org/gradle/model/internal/registry/NodeAtState
instanceKlass org/gradle/model/internal/registry/DefaultModelRegistry$GoalGraph
instanceKlass org/gradle/model/internal/registry/RuleBindings$NodeAtStateIndex
instanceKlass org/gradle/model/internal/registry/RuleBindings$TypePredicateIndex
instanceKlass org/gradle/model/internal/registry/RuleBindings$PathPredicateIndex
instanceKlass org/gradle/model/internal/registry/RuleBindings
instanceKlass org/gradle/model/internal/registry/ModelGraph
instanceKlass org/gradle/model/internal/core/DefaultModelRegistration
instanceKlass org/gradle/model/internal/core/AbstractModelAction
instanceKlass org/gradle/model/internal/core/EmptyModelProjection
instanceKlass org/gradle/model/internal/core/ModelProjection
instanceKlass org/gradle/model/internal/core/ModelAdapter
instanceKlass org/gradle/model/internal/core/ModelPromise
instanceKlass org/gradle/model/internal/core/ModelRegistrations$Builder$DescriptorReference
instanceKlass org/gradle/model/internal/core/ModelRegistration
instanceKlass org/gradle/model/internal/core/ModelAction
instanceKlass org/gradle/model/internal/core/ModelRegistrations$Builder
instanceKlass org/gradle/model/internal/core/ModelRegistrations
instanceKlass org/gradle/model/internal/registry/BoringProjectState
instanceKlass org/gradle/model/internal/core/ModelPredicate
instanceKlass org/gradle/model/internal/registry/DefaultModelRegistry$ModelGoal
instanceKlass org/gradle/model/internal/registry/ModelNodeInternal
instanceKlass org/gradle/model/internal/registry/DefaultModelRegistry
instanceKlass org/gradle/model/internal/registry/ModelRegistryInternal
instanceKlass org/gradle/api/reporting/Reporting
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$DependentComponentsReportAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$ModelReportAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$ComponentReportAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$BuildEnvironmentReportTaskAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$DependencyReportTaskAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$DependencyInsightReportTaskAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$PropertyReportTaskAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$TaskReportTaskAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$ProjectReportTaskAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin$HelpAction
instanceKlass org/gradle/api/plugins/HelpTasksPlugin
instanceKlass org/gradle/api/internal/plugins/PluginInstantiator
instanceKlass org/gradle/api/internal/plugins/RuleBasedPluginTarget
instanceKlass org/gradle/model/internal/inspect/ModelRuleExtractor$CachedRuleSource
instanceKlass org/gradle/model/internal/inspect/MethodModelRuleExtractionContext
instanceKlass org/gradle/model/Rules
instanceKlass org/gradle/model/Validate
instanceKlass org/gradle/model/Finalize
instanceKlass org/gradle/model/Mutate
instanceKlass org/gradle/model/Defaults
instanceKlass org/gradle/model/internal/core/NodeInitializerRegistry
instanceKlass org/gradle/model/Model
instanceKlass org/gradle/model/internal/inspect/MethodModelRuleExtractors
instanceKlass org/gradle/model/internal/manage/instance/ManagedInstance
instanceKlass org/gradle/model/internal/manage/schema/extract/ManagedProxyClassGenerator$GeneratedView
instanceKlass org/gradle/model/internal/manage/instance/ModelElementState
instanceKlass org/gradle/model/internal/manage/instance/GeneratedViewState
instanceKlass org/gradle/model/internal/manage/binding/StructMethodBinding
instanceKlass org/gradle/model/internal/manage/binding/StructBindings
instanceKlass org/gradle/model/internal/manage/binding/StructBindingValidationProblemCollector
instanceKlass org/gradle/model/internal/manage/binding/DefaultStructBindingsStore
instanceKlass org/gradle/platform/base/ComponentType
instanceKlass org/gradle/platform/base/SourceComponentSpec
instanceKlass org/gradle/language/base/LanguageSourceSet
instanceKlass org/gradle/model/internal/typeregistration/BaseInstanceFactory
instanceKlass org/gradle/model/internal/typeregistration/InstanceFactory
instanceKlass org/gradle/model/internal/manage/schema/cache/ModelSchemaCache
instanceKlass org/gradle/model/internal/manage/schema/extract/DefaultModelSchemaStore
instanceKlass org/gradle/model/internal/manage/schema/extract/StructSchemaExtractionStrategySupport
instanceKlass org/gradle/model/internal/manage/schema/extract/JavaUtilCollectionStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelMapStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/AbstractProxyClassGenerator
instanceKlass org/gradle/model/internal/manage/schema/extract/SpecializedMapStrategy
instanceKlass org/gradle/model/internal/type/WildcardTypeWrapper
instanceKlass org/gradle/model/ModelSet
instanceKlass org/gradle/model/internal/manage/schema/CompositeSchema
instanceKlass org/gradle/model/internal/manage/schema/AbstractModelSchema
instanceKlass org/gradle/model/internal/manage/schema/ManagedImplSchema
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSetStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/JdkValueTypeStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/EnumStrategy
instanceKlass org/gradle/model/internal/manage/schema/ModelSchema
instanceKlass org/gradle/model/internal/manage/schema/extract/PrimitiveStrategy
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaExtractionContext
instanceKlass org/gradle/model/internal/manage/schema/extract/DefaultModelSchemaExtractor
instanceKlass org/gradle/platform/base/ComponentBinaries
instanceKlass org/gradle/platform/base/VariantComponentSpec
instanceKlass org/gradle/platform/base/VariantComponent
instanceKlass org/gradle/platform/base/BinaryTasks
instanceKlass org/gradle/model/internal/core/ModelPath$Cache
instanceKlass org/gradle/platform/base/BinaryContainer
instanceKlass org/gradle/model/ModelMap
instanceKlass org/gradle/model/internal/inspect/ExtractedModelRule
instanceKlass org/gradle/model/internal/inspect/RuleSourceValidationProblemCollector
instanceKlass org/gradle/model/internal/inspect/AbstractAnnotationDrivenModelRuleExtractor
instanceKlass org/gradle/model/internal/manage/schema/extract/ModelSchemaAspect
instanceKlass org/gradle/platform/base/internal/VariantAspectExtractionStrategy
instanceKlass org/gradle/configuration/project/NotifyProjectBeforeEvaluatedBuildOperationType$1
instanceKlass org/gradle/configuration/project/NotifyProjectBeforeEvaluatedBuildOperationType$Result
instanceKlass org/gradle/configuration/project/NotifyProjectBeforeEvaluatedBuildOperationType
instanceKlass org/gradle/configuration/internal/ExecuteListenerBuildOperationType$1
instanceKlass org/gradle/configuration/internal/ExecuteListenerBuildOperationType$Result
instanceKlass org/gradle/configuration/internal/ExecuteListenerBuildOperationType
instanceKlass org/gradle/api/internal/catalog/DefaultDependenciesAccessors$DefaultVersionCatalogsExtension
instanceKlass org/gradle/api/artifacts/VersionCatalogsExtension
instanceKlass org/gradle/api/internal/catalog/VersionCatalogView
instanceKlass org/gradle/api/artifacts/VersionCatalog
instanceKlass org/gradle/api/internal/plugins/DslObject
instanceKlass org/gradle/api/internal/catalog/AbstractExternalDependencyFactory$BundleFactory
instanceKlass org/gradle/api/internal/catalog/AbstractExternalDependencyFactory$SubDependencyFactory
instanceKlass org/gradle/api/internal/catalog/AbstractExternalDependencyFactory$PluginFactory
instanceKlass org/gradle/api/internal/catalog/AbstractExternalDependencyFactory$VersionFactory
instanceKlass org/gradle/api/internal/catalog/AbstractExternalDependencyFactory
instanceKlass org/gradle/api/internal/catalog/ExternalModuleDependencyFactory
instanceKlass org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator$ExecuteListenerDetails
instanceKlass org/gradle/configuration/internal/ExecuteListenerBuildOperationType$Details
instanceKlass org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator$Operation
instanceKlass org/gradle/configuration/project/LifecycleProjectEvaluator$NotifyProjectBeforeEvaluatedDetails
instanceKlass org/gradle/configuration/project/NotifyProjectBeforeEvaluatedBuildOperationType$Details
instanceKlass org/gradle/configuration/project/LifecycleProjectEvaluator$NotifyBeforeEvaluate
instanceKlass org/gradle/configuration/project/LifecycleProjectEvaluator$ConfigureProjectDetails
instanceKlass org/gradle/configuration/project/ConfigureProjectBuildOperationType$Details
instanceKlass org/gradle/configuration/project/LifecycleProjectEvaluator$EvaluateProject
instanceKlass org/gradle/configuration/project/LifecycleProjectEvaluator
instanceKlass org/gradle/configuration/project/DelayedConfigurationActions
instanceKlass org/gradle/configuration/project/BuildScriptProcessor
instanceKlass org/gradle/buildinit/plugins/internal/action/WrapperPluginAutoApplyAction
instanceKlass org/gradle/buildinit/plugins/internal/action/BuildInitAutoApplyAction
instanceKlass org/gradle/internal/buildconfiguration/DaemonJvmPropertiesConfigurator
instanceKlass org/gradle/kotlin/dsl/tooling/builders/internal/KotlinScriptingModelBuildersRegistrationAction
instanceKlass org/gradle/jvm/toolchain/internal/task/ShowToolchainsTaskConfigurator
instanceKlass org/gradle/api/plugins/internal/HelpTasksAutoApplyAction
instanceKlass org/gradle/configuration/project/ConfigureActionsProjectEvaluator
instanceKlass org/gradle/configuration/DeferredProjectEvaluationCondition
instanceKlass org/gradle/initialization/NotifyingBuildLoader$3$1
instanceKlass org/gradle/initialization/NotifyProjectsLoadedBuildOperationType$Details
instanceKlass org/gradle/initialization/NotifyingBuildLoader$3
instanceKlass org/gradle/initialization/NotifyingBuildLoader$BuildStructureOperationResult
instanceKlass org/gradle/initialization/LoadProjectsBuildOperationType$Result
instanceKlass org/gradle/initialization/NotifyingBuildLoader$DefaultProjectsIdentifiedProgressDetails
instanceKlass org/gradle/initialization/ProjectsIdentifiedProgressDetails
instanceKlass org/gradle/initialization/BuildStructureOperationProject
instanceKlass org/gradle/api/internal/project/ProjectHierarchyUtils
instanceKlass org/apache/commons/lang/reflect/MethodUtils
instanceKlass org/gradle/internal/Pair
instanceKlass org/gradle/initialization/ProjectPropertySettingBuildLoader$CachingPropertyApplicator
instanceKlass org/gradle/internal/extensibility/ExtensibleDynamicObject$InheritedDynamicObject
instanceKlass org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator$BuildOperationEmittingAction
instanceKlass org/gradle/internal/service/scopes/ProjectBackedPropertyHost
instanceKlass org/gradle/nativeplatform/internal/CompilerOutputFileNamingScheme
instanceKlass org/gradle/internal/extensibility/ExtensibleDynamicObject$2
instanceKlass org/gradle/api/internal/project/DefaultCrossProjectModelAccess
instanceKlass org/gradle/internal/BiAction
instanceKlass org/gradle/api/internal/tasks/DefaultTaskContainerFactory$1
instanceKlass org/gradle/api/internal/tasks/DefaultTaskContainer$7
instanceKlass org/gradle/api/internal/tasks/DefaultTaskContainer$6
instanceKlass org/gradle/model/internal/core/MutableModelNode
instanceKlass org/gradle/model/internal/core/ModelNode
instanceKlass org/gradle/model/internal/core/ModelPath
instanceKlass org/gradle/api/tasks/TaskProvider
instanceKlass org/gradle/api/internal/project/taskfactory/TaskIdentity
instanceKlass org/gradle/api/internal/tasks/RealizeTaskBuildOperationType$Result
instanceKlass org/gradle/api/internal/tasks/RegisterTaskBuildOperationType$Result
instanceKlass org/gradle/model/internal/core/rule/describe/SimpleModelRuleDescriptor$1
instanceKlass org/gradle/internal/Factories$2
instanceKlass org/gradle/model/internal/core/rule/describe/AbstractModelRuleDescriptor
instanceKlass org/gradle/model/internal/core/rule/describe/ModelRuleDescriptor
instanceKlass org/gradle/model/internal/core/ModelReference
instanceKlass org/gradle/api/internal/tasks/DefaultTaskContainerFactory
instanceKlass org/gradle/api/internal/project/taskfactory/TaskFactory
instanceKlass org/gradle/api/internal/project/taskfactory/AnnotationProcessingTaskFactory
instanceKlass org/gradle/api/internal/project/taskfactory/TaskActionFactory
instanceKlass org/gradle/api/internal/project/taskfactory/DefaultTaskClassInfoStore
instanceKlass org/gradle/nativeplatform/internal/CompilerOutputFileNamingSchemeFactory
instanceKlass org/gradle/nativeplatform/internal/services/NativeBinaryServices$ProjectCompilerServices
instanceKlass org/gradle/language/internal/DefaultNativeComponentFactory
instanceKlass org/gradle/language/internal/NativeComponentFactory
instanceKlass org/gradle/language/nativeplatform/internal/toolchains/ToolChainSelector$Result
instanceKlass org/gradle/language/nativeplatform/internal/toolchains/DefaultToolChainSelector
instanceKlass org/gradle/language/nativeplatform/internal/toolchains/ToolChainSelector
instanceKlass org/gradle/language/nativeplatform/internal/incremental/IncrementalCompilerBuilder$IncrementalCompiler
instanceKlass org/gradle/language/nativeplatform/internal/incremental/DefaultIncrementalCompilerBuilder
instanceKlass org/gradle/language/nativeplatform/internal/incremental/IncrementalCompilerBuilder
instanceKlass org/gradle/plugins/ide/internal/DefaultIdeArtifactRegistry
instanceKlass org/gradle/plugins/ide/internal/IdeArtifactRegistry
instanceKlass org/gradle/internal/snapshot/Snapshot
instanceKlass org/gradle/internal/snapshot/impl/DefaultSnapshottingService
instanceKlass org/gradle/internal/snapshot/SnapshottingService
instanceKlass org/gradle/internal/enterprise/test/TestTaskProperties
instanceKlass org/gradle/internal/enterprise/test/TestTaskFilters
instanceKlass org/gradle/internal/enterprise/test/TestTaskForkOptions
instanceKlass org/gradle/internal/enterprise/test/impl/DefaultTestTaskPropertiesService
instanceKlass org/gradle/internal/enterprise/test/TestTaskPropertiesService
instanceKlass org/gradle/buildinit/plugins/internal/ProjectLayoutSetupRegistry
instanceKlass org/gradle/buildinit/plugins/internal/services/BuildInitServices$1
instanceKlass org/gradle/api/plugins/jvm/internal/DefaultJvmLanguageUtilities
instanceKlass org/gradle/api/plugins/jvm/internal/JvmLanguageUtilities
instanceKlass org/gradle/api/publish/maven/internal/publisher/MavenDuplicatePublicationTracker
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchainSpecInternal
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchainSpecInternal$Key
instanceKlass org/gradle/jvm/toolchain/internal/install/SecureFileDownloader
instanceKlass org/gradle/jvm/toolchain/JavaToolchainRequest
instanceKlass org/gradle/jvm/toolchain/internal/install/DefaultJavaToolchainProvisioningService
instanceKlass org/gradle/jvm/toolchain/internal/install/JavaToolchainProvisioningService
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchainQueryService
instanceKlass org/gradle/jvm/toolchain/JavaCompiler
instanceKlass org/gradle/jvm/toolchain/JavaToolchainSpec
instanceKlass org/gradle/jvm/toolchain/internal/DefaultJavaToolchainService
instanceKlass org/gradle/jvm/toolchain/JavaToolchainService
instanceKlass org/gradle/api/plugins/jvm/internal/DefaultJvmPluginServices
instanceKlass org/gradle/api/plugins/jvm/internal/JvmPluginServices
instanceKlass org/gradle/api/plugins/jvm/internal/JvmEcosystemUtilities
instanceKlass org/gradle/api/tasks/SourceSetContainer
instanceKlass org/gradle/language/jvm/internal/JvmLanguageServices$ProjectScopeServices
instanceKlass org/gradle/jvm/toolchain/internal/ToolchainToolFactory
instanceKlass org/gradle/language/java/internal/JavaLanguageServices$JavaProjectScopeServices
instanceKlass org/gradle/jvm/toolchain/internal/JavaCompilerFactory
instanceKlass org/gradle/language/java/internal/JavaToolchainServices$ProjectScopeCompileServices
instanceKlass org/gradle/api/internal/tasks/compile/GroovyCompilerFactory
instanceKlass org/gradle/language/base/internal/compile/CompilerFactory
instanceKlass org/gradle/api/internal/tasks/compile/GroovyServices$ProjectServices
instanceKlass org/gradle/api/publish/ivy/internal/publisher/IvyDuplicatePublicationTracker
instanceKlass org/gradle/plugin/software/internal/SoftwareFeatureApplicator
instanceKlass org/gradle/plugin/software/internal/ModelDefaultsApplicator
instanceKlass org/gradle/plugin/internal/PluginUseServices$ProjectScopeServices
instanceKlass org/gradle/api/internal/artifacts/DependencyManagementProjectScopeServices
instanceKlass org/gradle/workers/internal/BuildOperationAwareWorker
instanceKlass org/gradle/workers/internal/IsolatedClassloaderWorkerFactory
instanceKlass org/gradle/workers/internal/WorkerDaemonFactory
instanceKlass org/gradle/workers/internal/WorkerFactory
instanceKlass org/gradle/workers/WorkerExecutor
instanceKlass org/gradle/workers/internal/WorkersServices$ProjectScopeServices
instanceKlass org/gradle/api/internal/project/taskfactory/TaskInstantiator
instanceKlass org/gradle/normalization/internal/RuntimeClasspathNormalizationInternal
instanceKlass org/gradle/normalization/RuntimeClasspathNormalization
instanceKlass org/gradle/normalization/InputNormalization
instanceKlass org/gradle/internal/service/scopes/WorkerSharedProjectScopeServices
instanceKlass org/gradle/api/internal/project/ant/AntLoggingAdapterFactory
instanceKlass org/gradle/internal/typeconversion/TypeConverter
instanceKlass org/gradle/internal/service/scopes/ProjectScopeServices
instanceKlass org/gradle/internal/model/RuleBasedPluginListener
instanceKlass org/gradle/configuration/project/ProjectConfigurationActionContainer
instanceKlass org/gradle/model/internal/registry/ModelRegistry
instanceKlass org/gradle/api/internal/project/DeferredProjectConfiguration
instanceKlass org/gradle/normalization/internal/InputNormalizationHandlerInternal
instanceKlass org/gradle/api/component/SoftwareComponentContainer
instanceKlass org/gradle/api/internal/file/DefaultProjectLayout
instanceKlass org/gradle/api/internal/file/TaskFileVarFactory
instanceKlass org/gradle/api/internal/tasks/TaskContainerInternal
instanceKlass org/gradle/api/internal/tasks/TaskResolver
instanceKlass org/gradle/api/internal/project/ProjectStateInternal
instanceKlass org/gradle/api/internal/project/ProjectInternal$DetachedResolver
instanceKlass org/gradle/api/project/IsolatedProject
instanceKlass org/gradle/initialization/NotifyingBuildLoader$2$1
instanceKlass org/gradle/initialization/LoadProjectsBuildOperationType$Details
instanceKlass org/gradle/initialization/LoadProjectsBuildOperationType$Result$Project
instanceKlass org/gradle/initialization/ProjectsIdentifiedProgressDetails$Project
instanceKlass org/gradle/initialization/NotifyingBuildLoader$2
instanceKlass org/gradle/api/internal/catalog/DefaultDependenciesAccessors$GeneratedAccessors
instanceKlass org/gradle/internal/snapshot/impl/AbstractListSnapshot
instanceKlass org/gradle/api/internal/catalog/DefaultDependenciesAccessors$AbstractAccessorUnitOfWork
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalogBuilder$VersionReferencingPluginModel
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalogBuilder$DefaultPluginAliasBuilder
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalogBuilder$VersionReferencingDependencyModel
instanceKlass org/gradle/api/internal/catalog/AbstractContextAwareModel
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalogBuilder$DefaultLibraryAliasBuilder
instanceKlass org/gradle/api/internal/catalog/AliasNormalizer
instanceKlass org/gradle/api/internal/catalog/parser/DependenciesModelHelper
instanceKlass org/tomlj/Parser$1
instanceKlass org/tomlj/MutableTomlTable$Element
instanceKlass org/tomlj/TomlPosition
instanceKlass org/tomlj/MutableTomlTable
instanceKlass org/antlr/v4/runtime/tree/AbstractParseTreeVisitor
instanceKlass org/tomlj/internal/TomlParserVisitor
instanceKlass org/antlr/v4/runtime/misc/FlexibleHashMap$Entry
instanceKlass org/antlr/v4/runtime/misc/FlexibleHashMap
instanceKlass org/antlr/v4/runtime/misc/DoubleKeyMap
instanceKlass org/antlr/v4/runtime/atn/LexerIndexedCustomAction
instanceKlass org/antlr/v4/runtime/atn/LexerActionExecutor
instanceKlass org/antlr/v4/runtime/tree/TerminalNodeImpl
instanceKlass org/antlr/v4/runtime/atn/LL1Analyzer
instanceKlass org/antlr/v4/runtime/CommonToken
instanceKlass org/antlr/v4/runtime/WritableToken
instanceKlass org/antlr/v4/runtime/misc/MurmurHash
instanceKlass org/antlr/v4/runtime/atn/SemanticContext
instanceKlass org/antlr/v4/runtime/DefaultErrorStrategy
instanceKlass org/antlr/v4/runtime/BufferedTokenStream
instanceKlass org/antlr/v4/runtime/tree/ErrorNode
instanceKlass org/antlr/v4/runtime/tree/TerminalNode
instanceKlass org/antlr/v4/runtime/tree/ParseTreeListener
instanceKlass org/antlr/v4/runtime/RuleContext
instanceKlass org/antlr/v4/runtime/tree/RuleNode
instanceKlass org/antlr/v4/runtime/ANTLRErrorStrategy
instanceKlass org/antlr/v4/runtime/atn/LexerATNSimulator$SimState
instanceKlass org/antlr/v4/runtime/misc/AbstractEqualityComparator
instanceKlass org/antlr/v4/runtime/misc/EqualityComparator
instanceKlass org/antlr/v4/runtime/misc/Array2DHashSet
instanceKlass org/antlr/v4/runtime/dfa/DFAState
instanceKlass org/antlr/v4/runtime/atn/ATNConfigSet
instanceKlass org/antlr/v4/runtime/atn/ATNConfig
instanceKlass org/antlr/v4/runtime/misc/IntegerList
instanceKlass org/antlr/v4/runtime/Token
instanceKlass org/antlr/v4/runtime/CommonTokenFactory
instanceKlass org/antlr/v4/runtime/TokenFactory
instanceKlass org/antlr/v4/runtime/BaseErrorListener
instanceKlass org/antlr/v4/runtime/dfa/DFASerializer
instanceKlass org/antlr/v4/runtime/dfa/DFA
instanceKlass org/antlr/v4/runtime/atn/LexerPopModeAction
instanceKlass org/antlr/v4/runtime/atn/LexerModeAction
instanceKlass org/antlr/v4/runtime/atn/LexerTypeAction
instanceKlass org/antlr/v4/runtime/atn/LexerChannelAction
instanceKlass org/antlr/v4/runtime/atn/LexerPushModeAction
instanceKlass org/antlr/v4/runtime/atn/LexerCustomAction
instanceKlass org/antlr/v4/runtime/atn/ATNDeserializer$3
instanceKlass org/antlr/v4/runtime/atn/ATNDeserializer$2
instanceKlass org/antlr/v4/runtime/misc/Interval
instanceKlass org/antlr/v4/runtime/misc/IntervalSet
instanceKlass org/antlr/v4/runtime/atn/ATNDeserializer$1
instanceKlass org/antlr/v4/runtime/misc/Pair
instanceKlass org/antlr/v4/runtime/misc/IntSet
instanceKlass org/antlr/v4/runtime/atn/ATN
instanceKlass org/antlr/v4/runtime/atn/ATNDeserializationOptions
instanceKlass org/antlr/v4/runtime/atn/LexerAction
instanceKlass org/antlr/v4/runtime/atn/ATNDeserializer$UnicodeDeserializer
instanceKlass org/antlr/v4/runtime/atn/ATNState
instanceKlass org/antlr/v4/runtime/atn/Transition
instanceKlass org/antlr/v4/runtime/atn/ATNDeserializer
instanceKlass org/antlr/v4/runtime/VocabularyImpl
instanceKlass org/antlr/v4/runtime/atn/PredictionContext
instanceKlass org/antlr/v4/runtime/atn/PredictionContextCache
instanceKlass org/antlr/v4/runtime/RuntimeMetaData
instanceKlass org/antlr/v4/runtime/Vocabulary
instanceKlass org/antlr/v4/runtime/atn/ATNSimulator
instanceKlass org/antlr/v4/runtime/Recognizer
instanceKlass org/tomlj/TomlParseResult
instanceKlass org/tomlj/TomlTable
instanceKlass org/antlr/v4/runtime/tree/ParseTree
instanceKlass org/antlr/v4/runtime/tree/SyntaxTree
instanceKlass org/antlr/v4/runtime/tree/Tree
instanceKlass org/antlr/v4/runtime/tree/ParseTreeVisitor
instanceKlass org/tomlj/ErrorReporter
instanceKlass org/antlr/v4/runtime/ANTLRErrorListener
instanceKlass org/antlr/v4/runtime/TokenStream
instanceKlass org/antlr/v4/runtime/TokenSource
instanceKlass org/tomlj/Parser
instanceKlass org/antlr/v4/runtime/CodePointCharStream$1
instanceKlass org/antlr/v4/runtime/CodePointCharStream
instanceKlass org/antlr/v4/runtime/CodePointBuffer$1
instanceKlass org/antlr/v4/runtime/CodePointBuffer$Builder
instanceKlass org/antlr/v4/runtime/CodePointBuffer
instanceKlass org/antlr/v4/runtime/CharStreams
instanceKlass org/antlr/v4/runtime/CharStream
instanceKlass org/antlr/v4/runtime/IntStream
instanceKlass org/tomlj/Toml
instanceKlass org/gradle/api/internal/catalog/parser/TomlCatalogFileParser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/LocalFileDependencyBackedArtifactSet$SingletonFileResolvedVariant
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/LocalFileDependencyBackedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/LocalDependencyFiles
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/FileDependencyArtifactSet
instanceKlass org/gradle/internal/locking/DefaultDependencyLockingState
instanceKlass org/gradle/api/internal/provider/AbstractCollectionProperty$FixedSupplier
instanceKlass org/gradle/internal/locking/DependencyLockingGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultLocalVariantGraphResolveStateBuilder$DefaultLocalFileDependencyMetadata
instanceKlass org/gradle/internal/component/local/model/LocalFileDependencyMetadata
instanceKlass org/gradle/api/internal/ExecuteDomainObjectCollectionCallbackBuildOperationType$1
instanceKlass org/gradle/api/internal/ExecuteDomainObjectCollectionCallbackBuildOperationType$Result
instanceKlass org/gradle/api/internal/ExecuteDomainObjectCollectionCallbackBuildOperationType
instanceKlass org/gradle/api/internal/DefaultCollectionCallbackActionDecorator$OperationDetails
instanceKlass org/gradle/api/internal/ExecuteDomainObjectCollectionCallbackBuildOperationType$Details
instanceKlass org/gradle/api/internal/DefaultCollectionCallbackActionDecorator$Operation
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalogBuilder$Import
instanceKlass org/gradle/api/problems/internal/Problem
instanceKlass org/gradle/api/initialization/dsl/VersionCatalogBuilder$LibraryAliasBuilder
instanceKlass org/gradle/api/initialization/dsl/VersionCatalogBuilder$PluginAliasBuilder
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalogBuilder
instanceKlass org/gradle/internal/management/VersionCatalogBuilderInternal
instanceKlass org/gradle/internal/configuration/inputs/NoOpInputsListener
instanceKlass org/gradle/internal/configuration/inputs/InstrumentedInputs
instanceKlass org/gradle/api/internal/catalog/DefaultVersionCatalog
instanceKlass org/gradle/api/internal/catalog/DefaultDependenciesAccessors
instanceKlass org/gradle/configuration/BuildOperationFiringProjectsPreparer$ConfigureBuild$1
instanceKlass org/gradle/initialization/ConfigureBuildBuildOperationType$Details
instanceKlass org/gradle/configuration/BuildOperationFiringProjectsPreparer$ConfigureBuild
instanceKlass org/gradle/internal/buildtree/BuildInclusionCoordinator$BuildSynchronizer
instanceKlass org/gradle/api/internal/project/ProjectLifecycleController
instanceKlass org/gradle/internal/resources/TaskExecutionLockRegistry$2
instanceKlass org/gradle/internal/resources/ProjectLockRegistry$2
instanceKlass org/gradle/internal/resources/LockCache$1
instanceKlass org/gradle/internal/resources/ProjectLockRegistry$1
instanceKlass org/gradle/api/internal/artifacts/DefaultProjectComponentIdentifier
instanceKlass org/gradle/api/internal/project/ProjectIdentity
instanceKlass org/gradle/internal/lazy/UnsafeLazy
instanceKlass org/gradle/api/internal/project/DefaultProjectStateRegistry$ProjectStateImpl
instanceKlass org/gradle/api/internal/project/DefaultProjectStateRegistry$DefaultBuildProjectRegistry
instanceKlass org/gradle/internal/build/BuildProjectRegistry
instanceKlass org/gradle/initialization/AbstractProjectSpec
instanceKlass org/gradle/initialization/ProjectSpec
instanceKlass org/gradle/initialization/ProjectSpecs
instanceKlass org/gradle/caching/internal/services/AbstractBuildCacheControllerFactory$ResultImpl
instanceKlass org/gradle/caching/internal/FinalizeBuildCacheConfigurationBuildOperationType$Result
instanceKlass org/gradle/caching/internal/services/AbstractBuildCacheControllerFactory$DetailsImpl
instanceKlass org/gradle/caching/internal/FinalizeBuildCacheConfigurationBuildOperationType$Details
instanceKlass org/gradle/caching/internal/FinalizeBuildCacheConfigurationBuildOperationType$Result$BuildCacheDescription
instanceKlass org/gradle/caching/internal/services/AbstractBuildCacheControllerFactory$1
instanceKlass org/gradle/caching/configuration/internal/DefaultBuildCacheConfiguration
instanceKlass org/gradle/caching/local/internal/DirectoryBuildCacheServiceFactory
instanceKlass org/gradle/jvm/toolchain/internal/DefaultJavaToolchainRepositoryHandler$ImmutableJavaToolchainRepository
instanceKlass org/gradle/internal/time/TimeFormatting
instanceKlass org/gradle/util/internal/NameValidator
instanceKlass com/google/common/base/Throwables
instanceKlass org/gradle/kotlin/dsl/accessors/AccessorsClassPath$Companion
instanceKlass org/gradle/kotlin/dsl/accessors/AccessorsClassPath
instanceKlass org/gradle/kotlin/dsl/accessors/GenerateProjectAccessors$identify$1
instanceKlass org/gradle/kotlin/dsl/accessors/GenerateProjectAccessors$visitIdentityInputs$3
instanceKlass org/gradle/kotlin/dsl/accessors/GenerateProjectAccessors$visitIdentityInputs$2
instanceKlass org/gradle/kotlin/dsl/accessors/GenerateProjectAccessors$visitIdentityInputs$1
instanceKlass org/gradle/kotlin/dsl/accessors/GenerateProjectAccessors$Companion
instanceKlass org/gradle/kotlin/dsl/accessors/GenerateProjectAccessors
instanceKlass org/gradle/kotlin/dsl/internal/sharedruntime/codegen/PrimitiveKotlinTypeStringsKt
instanceKlass org/gradle/model/internal/type/WildcardWrapper
instanceKlass org/gradle/model/internal/type/GenericArrayTypeWrapper
instanceKlass org/gradle/kotlin/dsl/accessors/KotlinTypeStringsKt
instanceKlass org/gradle/kotlin/dsl/accessors/SchemaType$Companion
instanceKlass org/gradle/kotlin/dsl/accessors/SchemaType
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectSchema
instanceKlass org/gradle/kotlin/dsl/provider/plugins/TargetTypedSchema
instanceKlass org/gradle/plugin/software/internal/DefaultSoftwareTypeRegistry$SoftwareTypeSchema
instanceKlass org/gradle/kotlin/dsl/accessors/ProjectSchemaEntry
instanceKlass kotlin/reflect/jvm/internal/UtilKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaDescriptorVisibilities
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/UtilsKt
instanceKlass java/util/ArrayList$SubList$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassDescriptor$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ScopesHolderForClass$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassDescriptor$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ScopesHolderForClass$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ScopesHolderForClass
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassMemberScope$$Lambda$4
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassMemberScope$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassMemberScope$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassMemberScope$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassMemberScope$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassDescriptor$LazyJavaClassTypeConstructor$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/Java16SealedRecordLoader$Cache
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/Java16SealedRecordLoader
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassDescriptor$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaTypeParameterResolver$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaTypeParameterResolver
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/RuntimeSourceElementFactory$RuntimeSourceElement
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/sources/JavaSourceElement
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaClassDescriptor$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/descriptors/JavaClassDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaClass
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaTypeParameterListOwner
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaClassifier
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaNamedElement
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/ReflectJavaModifierListOwner
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaModifierListOwner
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/ReflectJavaAnnotationOwner
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaClassFinder$Request
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageScope$KotlinClassLookupResult
instanceKlass kotlin/jvm/internal/ArrayIterator
instanceKlass kotlin/jvm/internal/ArrayIteratorKt
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectClassStructure
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/KotlinJvmBinaryClass$AnnotationArgumentVisitor
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/header/ReadKotlinClassHeaderAnnotationVisitor
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/KotlinJvmBinaryClass$AnnotationVisitor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectKotlinClass$Factory
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectKotlinClass
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/KotlinJvmBinaryClass
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectJavaClassFinderKt
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectKotlinClassFinderKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageScope$FindClassRequest
instanceKlass kotlin/reflect/jvm/internal/impl/incremental/UtilsKt
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/ChainedMemberScope$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/ChainedMemberScope
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageFragment$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaAnnotations$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaAnnotations
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaAnnotationsKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageFragment$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/JvmPackageScope$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageScope$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageScope$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$8
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$7
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$6
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$5
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$4
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaScope$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/JvmPackageScope
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/descriptors/LazyJavaPackageFragment$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/utils/DeserializationHelpersKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/ContextKt$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/ContextKt
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager$KeyWithComputation
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaPackageFragmentProvider$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/ReflectJavaElement
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaPackage
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaAnnotationOwner
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/structure/JavaElement
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaClassFinder$$Util
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PackageFragmentProviderKt
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/LazyScopeAdapter$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/LazyPackageViewDescriptorImpl$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/AbstractScopeAdapter
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/LazyPackageViewDescriptorImpl$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/LazyPackageViewDescriptorImpl$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/ResolutionAnchorProviderKt
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/FindClassInModuleKt
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/PackagePartScopeCache
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializationComponentsForJava$Companion$ModuleData
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/CompositePackageFragmentProvider
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/ModuleDependenciesImpl
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/ModuleDependencies
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInClassDescriptorFactory$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInClassDescriptorFactory$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInClassDescriptorFactory$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInClassDescriptorFactory
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/functions/BuiltInFictitiousFunctionClassFactory
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/deserialization/ClassDescriptorFactory
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/FlexibleTypeDeserializer$ThrowException
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ErrorReporter$1
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$EnumEntryOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$Annotation$Argument$Value$Type$1
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$Annotation$Argument$ValueOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/builtins/BuiltInsProtoBuf
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/SerializerExtensionProtocol
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/AbstractAnnotationLoader
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/DeserializedClassDataFinder
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/AbstractDeserializedPackageFragmentProvider$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInsPackageFragmentProvider$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/AbstractDeserializedPackageFragmentProvider
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/jvm/JavaDescriptorResolver
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ClassDeserializer$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ClassDeserializer$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ClassDeserializer
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/JvmEnumEntriesDeserializationSupport
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/EnumEntriesDeserializationSupport
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/ExtensionRegistryLite$ObjectIntPair
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$PackageOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$VersionRequirementTableOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$ClassOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$TypeParameter$Variance$1
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/Internal$EnumLiteMap
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$TypeParameterOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$AnnotationOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/jvm/JvmProtoBuf$JvmFieldSignatureOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/jvm/JvmProtoBuf$JvmPropertySignatureOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$ValueParameterOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$PropertyOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$ContractOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$TypeTableOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$TypeOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$FunctionOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/Internal$EnumLite
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/GeneratedMessageLite$ExtensionDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/GeneratedMessageLite$GeneratedExtension
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/jvm/JvmProtoBuf$JvmMethodSignatureOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/ByteString$ByteIterator
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/ByteString
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/SmallSortedMap$EmptySet$2
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/SmallSortedMap$EmptySet$1
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/SmallSortedMap$EmptySet
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/LazyFieldLite
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/FieldSet
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/AbstractParser
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/MessageLite$Builder
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/Parser
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/FieldSet$FieldDescriptorLite
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/AbstractMessageLite
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/ProtoBuf$ConstructorOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/GeneratedMessageLite$ExtendableMessageOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/MessageLite
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/MessageLiteOrBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/jvm/JvmProtoBuf
instanceKlass kotlin/reflect/jvm/internal/impl/protobuf/ExtensionRegistryLite
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/jvm/deserialization/JvmProtoBufUtil
instanceKlass kotlin/reflect/jvm/internal/impl/utils/WrappedValues$ThrowableWrapper
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInsCustomizer$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInsCustomizer$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInsCustomizer$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/types/KotlinTypeFactory$EMPTY_REFINED_TYPE_FACTORY$1
instanceKlass kotlin/reflect/jvm/internal/impl/types/KotlinTypeFactory
instanceKlass kotlin/reflect/jvm/internal/impl/util/ArrayMap
instanceKlass kotlin/reflect/jvm/internal/impl/util/TypeRegistry
instanceKlass kotlin/reflect/jvm/internal/impl/util/AbstractArrayMapOwner
instanceKlass kotlin/jvm/internal/CollectionToArray
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PropertyGetterDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/FieldDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PropertySetterDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/types/error/ErrorPropertyDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PropertyDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/VariableDescriptorWithAccessors
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/VariableDescriptor
instanceKlass kotlin/jvm/internal/StringCompanionObject
instanceKlass kotlin/reflect/jvm/internal/impl/types/error/ErrorTypeConstructor
instanceKlass kotlin/reflect/jvm/internal/impl/types/error/ErrorScope
instanceKlass kotlin/reflect/jvm/internal/impl/util/ModuleVisibilityHelper$EMPTY
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DescriptorVisibilities$12
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DescriptorVisibilities$11
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DescriptorVisibilities$10
instanceKlass kotlin/reflect/jvm/internal/impl/util/ModuleVisibilityHelper
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DescriptorVisibility
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/Visibility
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DescriptorVisibilities
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/FunctionDescriptor$CopyBuilder
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/receivers/ReceiverValue
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ClassConstructorDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ConstructorDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/types/error/ErrorModuleDescriptor$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/types/error/ErrorModuleDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/types/error/ErrorUtils
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeProjection
instanceKlass kotlin/reflect/jvm/internal/impl/types/model/TypeArgumentMarker
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeUtils
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/AbstractClassDescriptor$1$1
instanceKlass kotlin/reflect/jvm/internal/impl/types/checker/KotlinTypeRefinerKt
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/descriptorUtil/DescriptorUtilsKt
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/DescriptorUtils
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/MemberScopeImpl
instanceKlass kotlin/reflect/jvm/internal/impl/types/AbstractTypeConstructor$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/types/AbstractTypeConstructor$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/types/AbstractTypeConstructor$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/types/ClassifierBasedTypeConstructor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ReceiverParameterDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ParameterDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ValueDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/AbstractClassDescriptor$3
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/AbstractClassDescriptor$2
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/AbstractClassDescriptor$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/Modality$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/ModuleAwareClassDescriptor$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeConstructor
instanceKlass kotlin/reflect/jvm/internal/impl/types/model/TypeConstructorMarker
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/MemberScope
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/scopes/ResolutionScope
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/ModuleAwareClassDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ClassDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ClassifierDescriptorWithTypeParameters
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInsCustomizer$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PackageViewDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/KeywordStringsGenerated
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/RenderingUtilsKt
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ModuleDescriptor$DefaultImpls
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererImpl$RenderDeclarationDescriptorVisitor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DeclarationDescriptorVisitor
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$10
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/ClassifierNamePolicy$FULLY_QUALIFIED
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$9
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$8
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$7
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$6
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$5
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/ClassifierNamePolicy$SHORT
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$4
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererImpl$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/ExcludedTypeAnnotations
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$ValueParametersHandler$DEFAULT
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$ValueParametersHandler
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererOptionsImpl$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererOptionsImpl$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererModifier$Companion
instanceKlass kotlin/properties/ObservableProperty
instanceKlass kotlin/properties/ReadWriteProperty
instanceKlass kotlin/properties/ReadOnlyProperty
instanceKlass kotlin/properties/Delegates
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/ClassifierNamePolicy$SOURCE_CODE_QUALIFIED
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/ClassifierNamePolicy
instanceKlass kotlin/reflect/jvm/internal/KMutableProperty1Impl$$Lambda$0
instanceKlass kotlin/reflect/KMutableProperty$Setter
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererOptionsImpl
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRendererOptions
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/renderer/DescriptorRenderer
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/SourceElement$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/SourceElement
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PackageFragmentDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ClassOrPackageFragmentDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JavaToKotlinClassMapper
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltIns$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/impl/utils/DFS$Neighbors
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltInsCustomizer
instanceKlass kotlin/reflect/jvm/internal/impl/storage/StorageKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/JavaFlexibleTypeDeserializer
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/FlexibleTypeDeserializer
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/LocalClassifierTypeSettings$Default
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/LocalClassifierTypeSettings
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/DeserializationComponents
instanceKlass kotlin/reflect/jvm/internal/impl/types/DefaultTypeAttributeTranslator
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeAttributeTranslator
instanceKlass kotlin/reflect/jvm/internal/impl/types/extensions/TypeAttributeTranslators
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ContractDeserializer$Companion$DEFAULT$1
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ContractDeserializer$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ContractDeserializer
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/DeserializationConfiguration$Default
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/DeserializationConfiguration
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/constants/ConstantValue
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/AnnotationDeserializer
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/AbstractBinaryClassAnnotationAndConstantLoader$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/AbstractBinaryClassAnnotationLoader$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/AbstractBinaryClassAnnotationLoader
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/AnnotationAndConstantLoader
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/AnnotationLoader
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/BinaryClassAnnotationAndConstantLoaderImplKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/JavaClassDataFinder
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ClassDataFinder
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeParameterUpperBoundEraser$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeParameterUpperBoundEraser$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeParameterErasureOptions
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeParameterUpperBoundEraser$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/types/TypeParameterUpperBoundEraser
instanceKlass kotlin/reflect/jvm/internal/impl/types/ErasureProjectionComputer
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/types/JavaTypeResolver
instanceKlass kotlin/InitializedLazyImpl
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/TypeParameterResolver$EMPTY
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/TypeParameterResolver
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaResolverContext
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/LazyJavaPackageFragmentProvider
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PackageFragmentProviderOptimized
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PackageFragmentProvider
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/jvm/CompositeSyntheticJavaPartsProvider
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/jvm/SyntheticJavaPartsProvider$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/jvm/SyntheticJavaPartsProvider
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializationComponentsForJavaKt$makeLazyJavaPackageFragmentProvider$javaResolverComponents$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaModuleAnnotationsProvider
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/OverridingUtil$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaIncompatibilityRulesOverridabilityCondition$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaIncompatibilityRulesOverridabilityCondition
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/ErasedOverridabilityCondition
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/FieldOverridabilityCondition
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/ExternalOverridabilityCondition
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/PropertyAccessorDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/VariableAccessorDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/FunctionDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/CallableMemberDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/MemberDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/CallableDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/Substitutable
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DeclarationDescriptorWithVisibility
instanceKlass kotlin/reflect/jvm/internal/impl/types/checker/KotlinTypeChecker$TypeConstructorEquality
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/OverridingUtil
instanceKlass kotlin/reflect/jvm/internal/impl/types/AbstractTypePreparator
instanceKlass kotlin/reflect/jvm/internal/impl/types/AbstractTypeRefiner
instanceKlass kotlin/reflect/jvm/internal/impl/types/checker/NewKotlinTypeCheckerImpl
instanceKlass kotlin/reflect/jvm/internal/impl/types/checker/NewKotlinTypeChecker$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/types/checker/NewKotlinTypeChecker
instanceKlass kotlin/reflect/jvm/internal/impl/types/checker/KotlinTypeChecker
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaClassesTracker$Default
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaClassesTracker
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/JavaResolverSettings$Default
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/JavaResolverSettings
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/typeEnhancement/JavaTypeEnhancement
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/typeEnhancement/SignatureEnhancement
instanceKlass kotlin/reflect/jvm/internal/impl/utils/WrappedValues$1
instanceKlass kotlin/reflect/jvm/internal/impl/utils/WrappedValues
instanceKlass kotlin/collections/EmptyIterator
instanceKlass kotlin/collections/EmptySet
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/NullabilityAnnotationStates$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/Jsr305Settings$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/Jsr305Settings
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/NullabilityAnnotationStatesImpl$$Lambda$0
instanceKlass kotlin/KotlinVersionCurrentValue
instanceKlass kotlin/KotlinVersion$Companion
instanceKlass kotlin/KotlinVersion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/ReportLevel$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaNullabilityAnnotationsStatus$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaNullabilityAnnotationsStatus
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/NullabilityAnnotationStatesImpl
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/NullabilityAnnotationStates
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaNullabilityAnnotationSettingsKt
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaTypeEnhancementState$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaTypeEnhancementState
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/AbstractAnnotationTypeQualifierResolver$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/AbstractAnnotationTypeQualifierResolver
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/ReflectionTypes$ClassLookup
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/ReflectionTypes$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/ReflectionTypes$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/ReflectionTypes
instanceKlass kotlin/reflect/jvm/internal/impl/incremental/components/LookupTracker$DO_NOTHING
instanceKlass kotlin/reflect/jvm/internal/impl/incremental/components/LookupTracker
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/SupertypeLoopChecker$EMPTY
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/SupertypeLoopChecker
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager$CacheWithNullableValuesBasedOnMemoizedFunction$1
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/sam/SamConversionResolverImpl
instanceKlass kotlin/reflect/jvm/internal/impl/resolve/sam/SamConversionResolver
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/components/JavaPropertyInitializerEvaluator$DoNothing
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/components/JavaPropertyInitializerEvaluator
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/components/JavaResolverCache$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/components/JavaResolverCache
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/components/SignaturePropagator$1
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/components/SignaturePropagator
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/JavaResolverComponents
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/PackagePartProvider$Empty
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/PackagePartProvider
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializationComponentsForJavaKt
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/NotFoundClasses$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/NotFoundClasses$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/NotFoundClasses
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/SingleModuleClassResolver
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/lazy/ModuleClassResolver
instanceKlass kotlin/collections/EmptyList
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/jvm/deserialization/JvmMetadataVersion$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/deserialization/BinaryVersion$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/metadata/deserialization/BinaryVersion
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/header/KotlinClassHeader$Kind$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializedDescriptorResolver$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializedDescriptorResolver
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltIns$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/KotlinBuiltIns$4
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/ModuleDescriptorImpl$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/ModuleDescriptorImpl$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/PackageViewDescriptorFactory$Default
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ModuleCapability
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/PackageViewDescriptorFactory$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/impl/PackageViewDescriptorFactory
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/annotations/Annotations$Companion$EMPTY$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/annotations/Annotations$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/annotations/Annotations
instanceKlass kotlin/collections/EmptyMap
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/annotations/AnnotatedImpl
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltIns$WhenMappings
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JvmBuiltIns$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager$MapBasedMemoizedFunction
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/KotlinBuiltIns$3
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/KotlinBuiltIns$2
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager$LockBasedLazyValue
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/KotlinBuiltIns$1
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/deserialization/PlatformDependentDeclarationFilter
instanceKlass kotlin/reflect/jvm/internal/impl/types/model/TypeArgumentListMarker
instanceKlass kotlin/reflect/jvm/internal/impl/types/model/SimpleTypeMarker
instanceKlass kotlin/reflect/jvm/internal/impl/types/KotlinType
instanceKlass kotlin/reflect/jvm/internal/impl/types/model/KotlinTypeMarker
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ClassifierDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DeclarationDescriptorNonRoot
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DeclarationDescriptorWithSource
instanceKlass kotlin/reflect/jvm/internal/impl/incremental/components/LookupLocation
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/deserialization/AdditionalClassPartsProvider
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/ModuleDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/DeclarationDescriptor
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/annotations/Annotated
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/Named
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/KotlinBuiltIns
instanceKlass kotlin/reflect/jvm/internal/impl/storage/DefaultSimpleLock
instanceKlass kotlin/reflect/jvm/internal/impl/storage/SimpleLock$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/storage/EmptySimpleLock
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager$ExceptionHandlingStrategy$1
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager$ExceptionHandlingStrategy
instanceKlass kotlin/reflect/jvm/internal/impl/storage/MemoizedFunctionToNullable
instanceKlass kotlin/reflect/jvm/internal/impl/storage/CacheWithNullableValues
instanceKlass kotlin/reflect/jvm/internal/impl/storage/NullableLazyValue
instanceKlass kotlin/reflect/jvm/internal/impl/storage/NotNullLazyValue
instanceKlass kotlin/reflect/jvm/internal/impl/storage/CacheWithNotNullValues
instanceKlass kotlin/reflect/jvm/internal/impl/storage/MemoizedFunctionToNotNull
instanceKlass kotlin/reflect/jvm/internal/impl/storage/SimpleLock
instanceKlass kotlin/reflect/jvm/internal/impl/storage/LockBasedStorageManager
instanceKlass kotlin/reflect/jvm/internal/impl/storage/StorageManager
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/RuntimeSourceElementFactory
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/sources/JavaSourceElementFactory
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/RuntimeErrorReporter
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/ErrorReporter
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectJavaClassFinder
instanceKlass kotlin/reflect/jvm/internal/impl/load/java/JavaClassFinder
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializationComponentsForJava$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/DeserializationComponentsForJava
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/builtins/BuiltInsResourceLoader
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/ReflectKotlinClassFinder
instanceKlass kotlin/reflect/jvm/internal/impl/load/kotlin/KotlinClassFinder
instanceKlass kotlin/reflect/jvm/internal/impl/serialization/deserialization/KotlinMetadataFinder
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/RuntimeModuleData$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/components/RuntimeModuleData
instanceKlass kotlin/reflect/jvm/internal/WeakClassLoaderBox
instanceKlass kotlin/reflect/jvm/internal/ModuleByClassLoaderKt
instanceKlass kotlin/reflect/jvm/internal/impl/name/SpecialNames
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/CompanionObjectMapping
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqNamesUtilKt
instanceKlass kotlin/reflect/jvm/internal/impl/utils/CollectionsKt
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/PrimitiveType$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/PrimitiveType$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/PrimitiveType$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/StandardNames$FqNames
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JavaToKotlinClassMap$PlatformMutabilityMapping
instanceKlass kotlin/ranges/RangesKt__RangesKt
instanceKlass kotlin/reflect/jvm/internal/impl/name/StandardClassIdsKt
instanceKlass kotlin/reflect/jvm/internal/impl/name/StandardClassIds
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/StandardNames
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/functions/FunctionTypeKind
instanceKlass kotlin/reflect/jvm/internal/impl/builtins/jvm/JavaToKotlinClassMap
instanceKlass kotlin/jvm/functions/Function22
instanceKlass kotlin/jvm/functions/Function21
instanceKlass kotlin/jvm/functions/Function20
instanceKlass kotlin/jvm/functions/Function19
instanceKlass kotlin/jvm/functions/Function18
instanceKlass kotlin/jvm/functions/Function17
instanceKlass kotlin/jvm/functions/Function16
instanceKlass kotlin/jvm/functions/Function15
instanceKlass kotlin/jvm/functions/Function14
instanceKlass kotlin/jvm/functions/Function13
instanceKlass kotlin/jvm/functions/Function12
instanceKlass kotlin/jvm/functions/Function11
instanceKlass kotlin/jvm/functions/Function10
instanceKlass kotlin/jvm/functions/Function9
instanceKlass kotlin/jvm/functions/Function8
instanceKlass kotlin/jvm/functions/Function7
instanceKlass kotlin/jvm/functions/Function6
instanceKlass kotlin/jvm/functions/Function5
instanceKlass kotlin/jvm/functions/Function4
instanceKlass kotlin/jvm/functions/Function3
instanceKlass kotlin/Pair
instanceKlass kotlin/TuplesKt
instanceKlass kotlin/collections/ArraysUtilJVM
instanceKlass kotlin/reflect/jvm/internal/impl/descriptors/runtime/structure/ReflectClassUtilKt
instanceKlass kotlin/_Assertions
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqNameUnsafe$1
instanceKlass kotlin/reflect/jvm/internal/impl/name/Name
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqNameUnsafe
instanceKlass kotlin/reflect/jvm/internal/impl/name/FqName
instanceKlass kotlin/reflect/jvm/internal/impl/name/ClassId$Companion
instanceKlass kotlin/reflect/jvm/internal/impl/name/ClassId
instanceKlass kotlin/reflect/jvm/internal/RuntimeTypeMapper
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$17
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$16
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$15
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$14
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$13
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$12
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$11
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$10
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$9
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$8
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$7
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$6
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$5
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$4
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$Data$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Data$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Data
instanceKlass kotlin/jvm/JvmClassMappingKt
instanceKlass kotlin/ResultKt
instanceKlass kotlin/coroutines/EmptyCoroutineContext
instanceKlass kotlin/coroutines/CoroutineContext
instanceKlass kotlin/coroutines/jvm/internal/DebugProbesKt
instanceKlass kotlin/coroutines/intrinsics/IntrinsicsKt__IntrinsicsJvmKt
instanceKlass kotlin/sequences/SequenceScope
instanceKlass kotlin/sequences/SequencesKt__SequenceBuilderKt$sequence$$inlined$Sequence$1
instanceKlass kotlin/sequences/Sequence
instanceKlass kotlin/sequences/SequencesKt__SequenceBuilderKt
instanceKlass kotlin/coroutines/jvm/internal/BaseContinuationImpl
instanceKlass kotlin/coroutines/jvm/internal/CoroutineStackFrame
instanceKlass kotlin/coroutines/jvm/internal/SuspendFunction
instanceKlass org/gradle/internal/extensibility/DefaultExtensionsSchema
instanceKlass org/gradle/api/plugins/ExtensionsSchema
instanceKlass org/gradle/api/tasks/SourceSet
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultProjectSchemaProviderKt
instanceKlass org/gradle/kotlin/dsl/accessors/AccessorsClassPathKt
instanceKlass org/gradle/kotlin/dsl/ExtraPropertiesExtensionsKt
instanceKlass org/gradle/kotlin/dsl/concurrent/JavaSystemPropertiesAsyncIOScopeSettings$Companion
instanceKlass org/gradle/kotlin/dsl/concurrent/JavaSystemPropertiesAsyncIOScopeSettings
instanceKlass org/gradle/kotlin/dsl/concurrent/AsyncIOScopeSettings
instanceKlass org/gradle/kotlin/dsl/concurrent/DefaultAsyncIOScopeFactory
instanceKlass org/gradle/kotlin/dsl/provider/plugins/DefaultKotlinDslDclSchemaCollector
instanceKlass org/gradle/kotlin/dsl/provider/plugins/CrossBuildInMemoryKotlinDslDclSchemaCache
instanceKlass org/gradle/kotlin/dsl/provider/plugins/KotlinDslDclSchemaCache
instanceKlass org/gradle/kotlin/dsl/provider/plugins/CachedKotlinDslDclSchemaCollector
instanceKlass org/gradle/jvm/toolchain/internal/DefaultJavaToolchainRepository
instanceKlass org/gradle/toolchains/foojay/FoojayToolchainsConventionPlugin$apply$1$1$1$1
instanceKlass org/gradle/toolchains/foojay/FoojayToolchainsConventionPlugin$apply$1$1$1
instanceKlass org/gradle/kotlin/dsl/ToolchainManagementExtensionsKt$sam$org_gradle_api_Action$0
instanceKlass org/gradle/kotlin/dsl/ToolchainManagementExtensionsKt
instanceKlass org/gradle/toolchains/foojay/FoojayToolchainsConventionPlugin$apply$1
instanceKlass org/gradle/toolchains/foojay/FoojayToolchainResolver
instanceKlass org/gradle/jvm/toolchain/JavaToolchainResolver
instanceKlass org/gradle/jvm/toolchain/internal/DefaultJavaToolchainRepositoryHandler$RepositoryNamer
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchainRepositoryInternal
instanceKlass org/gradle/jvm/toolchain/internal/DefaultJavaToolchainRepositoryHandler
instanceKlass org/gradle/jvm/toolchain/internal/RealizedJavaToolchainRepository
instanceKlass org/gradle/jvm/toolchain/JavaToolchainRepository
instanceKlass org/gradle/jvm/toolchain/internal/JavaToolchainRepositoryHandlerInternal
instanceKlass org/gradle/jvm/toolchain/JavaToolchainRepositoryHandler
instanceKlass org/gradle/api/plugins/JvmToolchainManagementPlugin
instanceKlass org/gradle/toolchains/foojay/FoojayToolchainsPlugin
instanceKlass org/gradle/api/internal/plugins/DefaultPluginManager$OperationDetails
instanceKlass org/gradle/api/internal/plugins/ApplyPluginBuildOperationType$Details
instanceKlass org/gradle/api/internal/plugins/DefaultPluginManager$AddPluginBuildOperation
instanceKlass org/gradle/api/internal/plugins/DefaultPluginManager$1
instanceKlass org/gradle/api/internal/plugins/DefaultPotentialPluginWithId
instanceKlass org/gradle/api/internal/plugins/PluginInspector$PotentialImperativeClassPlugin
instanceKlass com/google/common/base/Predicates
instanceKlass org/gradle/model/internal/inspect/ModelRuleSourceDetector$3
instanceKlass org/gradle/model/RuleSource
instanceKlass org/gradle/toolchains/foojay/FoojayToolchainsConventionPlugin
instanceKlass org/gradle/internal/classloader/JarCompat
instanceKlass org/gradle/api/internal/plugins/PluginDescriptor
instanceKlass org/gradle/internal/classloader/TransformErrorHandler
instanceKlass org/gradle/internal/classloader/TransformReplacer$Loader
instanceKlass org/gradle/internal/classloader/TransformReplacer
instanceKlass org/gradle/internal/classpath/DefaultClassPath$Builder
instanceKlass org/gradle/internal/classpath/TransformedClassPath$Builder
instanceKlass org/gradle/internal/classpath/TransformedClassPath$1
instanceKlass java/util/stream/SortedOps
instanceKlass org/gradle/api/internal/initialization/transform/utils/InstrumentationClasspathMerger$ClassPathTransformedArtifact
instanceKlass java/util/Collections$2
instanceKlass org/gradle/internal/Deferrable$2
instanceKlass org/gradle/api/internal/tasks/properties/InputParameterUtils
instanceKlass org/gradle/api/internal/initialization/transform/BaseInstrumentingArtifactTransform$Parameters$Inject
instanceKlass org/gradle/internal/snapshot/RootTrackingFileSystemSnapshotHierarchyVisitor
instanceKlass org/gradle/api/tasks/TaskOutputs
instanceKlass org/gradle/internal/properties/bean/DefaultPropertyWalker$CachedPropertyValue
instanceKlass org/gradle/api/internal/initialization/transform/MergeInstrumentationAnalysisTransform$Parameters$Inject
instanceKlass org/gradle/api/internal/file/collections/DefaultConfigurableFileCollection$Configurer
instanceKlass org/gradle/internal/snapshot/impl/AbstractSetSnapshot
instanceKlass org/gradle/api/internal/file/collections/DefaultConfigurableFileCollection$ResolvedItemsCollector
instanceKlass org/gradle/api/internal/file/collections/ListBackedFileSet
instanceKlass org/gradle/api/internal/artifacts/PreResolvedResolvableArtifact
instanceKlass org/gradle/internal/execution/steps/IdentityCacheStep$DefaultExecuteDeferredWorkProgressDetails
instanceKlass org/gradle/operations/execution/ExecuteDeferredWorkProgressDetails
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$TransformWorkspaceResult
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$Builder$EntireInputArtifact
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$Builder$TransformWorkspaceOutput
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$Builder$ProducedExecutionOutput
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$Builder$TransformExecutionOutput
instanceKlass java/nio/channels/Channels
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$Builder
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResult$OutputVisitor
instanceKlass org/gradle/api/internal/artifacts/transform/TransformExecutionResultSerializer
instanceKlass org/gradle/internal/execution/steps/IdentityCacheStep$DefaultIdentityCacheResult
instanceKlass org/gradle/internal/execution/steps/ExecuteWorkBuildOperationFiringStep$ExecuteWorkResult
instanceKlass org/gradle/operations/execution/ExecuteWorkBuildOperationType$Result
instanceKlass org/gradle/internal/execution/steps/ExecuteWorkBuildOperationFiringStep$ExecuteWorkDetails
instanceKlass org/gradle/operations/execution/ExecuteWorkBuildOperationType$Details
instanceKlass org/gradle/internal/Deferrable$1
instanceKlass org/gradle/internal/Deferrable$3
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformExecution$DefaultIdentifyTransformExecutionProgressDetails
instanceKlass org/gradle/operations/dependencies/transforms/IdentifyTransformExecutionProgressDetails
instanceKlass org/gradle/api/internal/artifacts/transform/TransformWorkspaceIdentity
instanceKlass org/gradle/internal/execution/UnitOfWork$InputFileValueSupplier
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformExecution$1
instanceKlass org/gradle/operations/dependencies/transforms/SnapshotTransformInputsBuildOperationType$Details
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformExecution
instanceKlass org/gradle/internal/Deferrable
instanceKlass org/gradle/api/internal/artifacts/transform/TransformStepSubject
instanceKlass org/gradle/api/internal/artifacts/transform/TransformingAsyncArtifactListener$TransformedArtifact
instanceKlass org/gradle/api/internal/artifacts/transform/TransformingAsyncArtifactListener$1
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/EndCollection
instanceKlass org/gradle/api/internal/artifacts/transform/TransformingAsyncArtifactListener
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitorToResolvedFileVisitorAdapter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ResolvedFileCollectionVisitor
instanceKlass org/gradle/api/internal/file/collections/DefaultConfigurableFileCollection$2
instanceKlass org/gradle/api/internal/provider/ValueSupplier$UnknownProducer
instanceKlass org/gradle/api/internal/provider/ValueSupplier$NoProducer
instanceKlass org/gradle/api/internal/provider/ValueSupplier$ValueProducer
instanceKlass org/gradle/api/internal/file/collections/UnpackingVisitor
instanceKlass org/gradle/api/internal/file/AbstractFileCollection$1
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$IsolatedParameters
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$FingerprintTransformInputsOperation$Result$1
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$FingerprintTransformInputsOperation$Result
instanceKlass org/gradle/internal/properties/bean/DefaultPropertyWalker$1
instanceKlass org/gradle/internal/properties/annotations/TypeMetadataWalker$InstanceMetadataVisitor
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$1
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$FingerprintTransformInputsOperation$Details$1
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$FingerprintTransformInputsOperation$Details
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$IsolateTransformParameters$2
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$InvokeSerializationConstructorAndInitializeFieldsStrategy
instanceKlass sun/reflect/ReflectionFactory$1
instanceKlass sun/reflect/ReflectionFactory
instanceKlass org/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl$SerializationConstructorImpl
instanceKlass org/gradle/api/internal/initialization/transform/InstrumentationAnalysisTransform$Parameters$Inject
instanceKlass org/gradle/internal/snapshot/impl/AbstractManagedValueSnapshot
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformedArtifactSet$CalculateArtifacts
instanceKlass org/gradle/api/internal/artifacts/transform/BoundTransformStep
instanceKlass org/gradle/api/internal/artifacts/transform/AbstractTransformedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/transform/TransformedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformedVariantFactory$Factory
instanceKlass org/gradle/api/internal/artifacts/transform/TransformedVariant
instanceKlass org/gradle/api/internal/artifacts/transform/ConsumerProvidedVariantFinder$CachedVariant
instanceKlass org/gradle/api/internal/artifacts/transform/TransformChain
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultVariantDefinition
instanceKlass org/gradle/api/internal/artifacts/transform/ConsumerProvidedVariantFinder$ChainNode
instanceKlass org/gradle/api/internal/artifacts/transform/ConsumerProvidedVariantFinder$ChainState
instanceKlass org/gradle/api/internal/artifacts/transform/ConsumerProvidedVariantFinder$TransformCache$CacheKey
instanceKlass org/gradle/api/internal/attributes/CompatibilityRule$1
instanceKlass java/util/stream/DistinctOps
instanceKlass org/gradle/api/internal/initialization/transform/utils/InstrumentationClasspathMerger$OriginalArtifactIdentifier
instanceKlass org/gradle/api/internal/artifacts/result/DefaultResolvedArtifactResult
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$ReleaseLocks
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$4$1
instanceKlass org/gradle/internal/resources/DefaultResourceLockCoordinationService$2
instanceKlass org/gradle/internal/MutableReference
instanceKlass org/gradle/internal/work/DefaultWorkerLeaseService$4
instanceKlass org/gradle/internal/Factories$1
instanceKlass org/gradle/internal/Factories
instanceKlass org/gradle/api/internal/artifacts/DownloadArtifactBuildOperationType$1
instanceKlass org/gradle/api/internal/artifacts/DownloadArtifactBuildOperationType$Result
instanceKlass org/gradle/api/internal/artifacts/DownloadArtifactBuildOperationType
instanceKlass org/gradle/internal/operations/BuildOperationType
instanceKlass org/gradle/api/internal/artifacts/DownloadArtifactBuildOperationType$DetailsImpl
instanceKlass org/gradle/api/internal/artifacts/DownloadArtifactBuildOperationType$Details
instanceKlass org/gradle/internal/operations/DefaultBuildOperationQueue$WorkerRunnable
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactBackedResolvedVariant$DownloadArtifactFile
instanceKlass org/gradle/internal/operations/DefaultBuildOperationQueue
instanceKlass org/gradle/internal/operations/DefaultBuildOperationExecutor$QueueWorker
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ParallelResolveArtifactSet$VisitingSet$StartVisitAction
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$Visitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactBackedResolvedVariant$SingleArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$Artifacts
instanceKlass org/gradle/api/internal/artifacts/dsl/ArtifactFile
instanceKlass org/gradle/internal/component/external/model/UrlBackedArtifactMetadata
instanceKlass org/gradle/internal/component/external/model/GradleDependencyMetadata
instanceKlass org/gradle/internal/component/external/model/LazyVariantBackedConfigurationMetadata$RuleAwareVariant
instanceKlass org/gradle/internal/component/external/model/AbstractVariantBackedConfigurationMetadata
instanceKlass org/gradle/internal/component/external/model/AbstractMutableModuleComponentResolveMetadata$ImmutableVariantImpl
instanceKlass org/gradle/internal/component/external/model/ComponentVariant
instanceKlass org/gradle/internal/component/external/model/AbstractMutableModuleComponentResolveMetadata$DependencyImpl
instanceKlass org/gradle/internal/component/external/model/ComponentVariant$Dependency
instanceKlass org/gradle/internal/component/external/model/AbstractMutableModuleComponentResolveMetadata$FileImpl
instanceKlass org/gradle/internal/component/external/model/ComponentVariant$File
instanceKlass org/gradle/internal/component/external/model/AbstractMutableModuleComponentResolveMetadata$MutableVariantImpl
instanceKlass org/gradle/internal/component/external/model/ExternalModuleDependencyMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/excludes/factories/Optimizations
instanceKlass org/gradle/internal/component/model/DefaultCompatibilityCheckResult
instanceKlass org/gradle/api/internal/initialization/transform/utils/InstrumentationClasspathMerger
instanceKlass org/gradle/api/internal/initialization/transform/utils/InstrumentationTransformUtils
instanceKlass org/gradle/api/internal/initialization/transform/services/CacheInstrumentationDataBuildService$1
instanceKlass org/gradle/internal/classpath/types/ExternalPluginsInstrumentationTypeRegistry
instanceKlass org/gradle/api/internal/initialization/transform/utils/DefaultInstrumentationAnalysisSerializer
instanceKlass org/gradle/api/internal/initialization/transform/utils/CachedInstrumentationAnalysisSerializer
instanceKlass org/gradle/api/internal/initialization/transform/services/CacheInstrumentationDataBuildService$ResolutionData
instanceKlass org/gradle/api/internal/initialization/transform/services/CacheInstrumentationDataBuildService$ResolutionScope
instanceKlass org/gradle/api/internal/initialization/transform/utils/InstrumentationAnalysisSerializer
instanceKlass org/gradle/internal/isolated/IsolationScheme$ServicesForIsolatedObject
instanceKlass org/gradle/internal/snapshot/impl/NullValueSnapshot
instanceKlass org/gradle/api/internal/artifacts/result/DefaultResolvedComponentResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/NoRepositoriesResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/CachingDependencyResultFactory
instanceKlass org/gradle/api/artifacts/result/DependencyResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ResolutionResultGraphBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/InMemoryResolutionResultBuilder
instanceKlass org/gradle/api/internal/tasks/DefaultTaskDependency$TaskDependencySet
instanceKlass org/gradle/internal/graph/CachingDirectedGraphWalker$NodeDetails
instanceKlass org/gradle/api/internal/tasks/WorkDependencyResolver$1
instanceKlass org/gradle/api/internal/tasks/TaskDependencyUtil
instanceKlass org/gradle/plugin/use/internal/DefaultPluginRequestApplicator$ApplyAction
instanceKlass org/gradle/plugin/management/internal/PluginCoordinates
instanceKlass org/gradle/plugin/use/resolve/internal/ArtifactRepositoriesPluginResolver$ExternalPluginResolution
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSetResolver$1$1
instanceKlass org/gradle/api/internal/artifacts/ResolveArtifactsBuildOperationType$Result
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ParallelResolveArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSetResolver$1$2
instanceKlass org/gradle/api/internal/artifacts/ResolveArtifactsBuildOperationType$Details
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSetResolver$1
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DefaultSelectedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/CompositeResolvedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DefaultVisitedArtifactResults$DefaultSelectedArtifactResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactSet$1
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/DefaultCachedArtifact
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/artifacts/ArtifactAtRepositoryKey
instanceKlass org/gradle/api/internal/artifacts/DefaultResolvedArtifact
instanceKlass org/gradle/api/artifacts/ResolvedArtifact
instanceKlass org/gradle/api/internal/artifacts/DefaultResolvableArtifact
instanceKlass org/gradle/api/internal/attributes/matching/DefaultAttributeMatcher$CoercingAttributeValuePredicate
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DefaultResolvedVariantSet
instanceKlass org/gradle/internal/resolve/result/BuildableArtifactResolveResult
instanceKlass org/gradle/internal/resolve/resolver/DefaultComponentArtifactResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactBackedResolvedVariant
instanceKlass org/gradle/internal/component/model/DefaultExternalComponentGraphResolveState$ExternalArtifactResolveMetadata
instanceKlass org/gradle/internal/logging/sink/ProgressLogEventGenerator$1
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationResolutionBuildOperationResult$LazyDesugaringAttributeContainer
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationResolutionBuildOperationResult
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationDependenciesBuildOperationType$Result
instanceKlass org/gradle/api/internal/artifacts/DefaultResolverResults
instanceKlass org/gradle/api/internal/artifacts/DefaultResolverResults$DefaultLegacyResolverResults
instanceKlass org/gradle/api/internal/artifacts/ResolverResults$LegacyResolverResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultResolvedConfiguration
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactSelectionSpec
instanceKlass org/gradle/api/internal/artifacts/ivyservice/DefaultLenientConfiguration
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/TransientConfigurationResultsLoader
instanceKlass org/gradle/api/internal/artifacts/transform/ResolvedVariantTransformer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedVariant
instanceKlass org/gradle/internal/resolve/resolver/ComponentArtifactResolver
instanceKlass org/gradle/internal/resolve/resolver/DefaultVariantArtifactResolver
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformUpstreamDependenciesResolver$2
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformUpstreamDependenciesResolver$1
instanceKlass org/gradle/api/internal/artifacts/transform/TransformUpstreamDependencies
instanceKlass org/gradle/api/internal/artifacts/transform/TransformDependencies
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformUpstreamDependenciesResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactSelectionServices
instanceKlass org/gradle/api/internal/artifacts/transform/TransformationChainSelector
instanceKlass org/gradle/api/internal/artifacts/transform/AttributeMatchingArtifactVariantSelector
instanceKlass org/gradle/api/internal/artifacts/transform/ArtifactVariantSelector
instanceKlass org/gradle/internal/resolve/resolver/VariantArtifactResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DefaultVisitedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/transform/TransformUpstreamDependenciesResolver$Factory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/results/DefaultVisitedGraphResults
instanceKlass org/gradle/api/internal/artifacts/result/ResolvedComponentResultInternal
instanceKlass org/gradle/api/internal/artifacts/result/MinimalResolutionResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ResolvedComponentVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/StreamingResolutionResultBuilder$RootFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/DefaultResolvedGraphResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/DefaultVisitedFileDependencyResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/SelectedArtifactResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DefaultVisitedArtifactResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/DefaultBinaryStore$SimpleBinaryData
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedVariantSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/VariantResolvingArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ProjectComponentIdentifierInternal
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/VersionConflictResolutionDetails
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionReasons$DefaultComponentSelectionReason
instanceKlass org/gradle/cache/internal/BinaryStore$WriteAction
instanceKlass org/gradle/internal/component/model/GraphVariantSelectionResult
instanceKlass org/gradle/internal/component/model/DefaultMultipleCandidateResult
instanceKlass org/gradle/api/internal/attributes/matching/AttributeSelectionSchema$PrecedenceResult
instanceKlass org/gradle/api/internal/attributes/matching/MultipleCandidateMatcher
instanceKlass org/gradle/internal/component/model/LoggingAttributeMatchingExplanationBuilder
instanceKlass org/gradle/internal/component/model/AttributeMatchingExplanationBuilder$1
instanceKlass org/gradle/internal/component/model/AttributeMatchingExplanationBuilder
instanceKlass org/gradle/api/internal/attributes/matching/DefaultAttributeMatcher$MatchingCandidateCacheKey
instanceKlass org/gradle/api/internal/attributes/matching/DefaultAttributeMatcher$CachedQuery
instanceKlass org/gradle/api/internal/attributes/matching/CachingAttributeSelectionSchema$MatchValueKey
instanceKlass org/gradle/api/internal/attributes/matching/CachingAttributeSelectionSchema$ExtraAttributesKey
instanceKlass org/gradle/api/internal/attributes/MultipleCandidatesResult
instanceKlass org/gradle/api/attributes/MultipleCandidatesDetails
instanceKlass org/gradle/api/internal/attributes/CompatibilityCheckResult
instanceKlass org/gradle/api/attributes/CompatibilityCheckDetails
instanceKlass org/gradle/api/internal/attributes/matching/DefaultAttributeSelectionSchema
instanceKlass org/gradle/api/internal/attributes/matching/CachingAttributeSelectionSchema
instanceKlass org/gradle/internal/component/model/DefaultExternalComponentGraphResolveState$DefaultConfigurationArtifactResolveState
instanceKlass org/gradle/internal/component/model/DefaultExternalComponentGraphResolveState$DefaultConfigurationGraphResolveState
instanceKlass org/gradle/internal/component/model/ConfigurationGraphResolveState
instanceKlass org/gradle/internal/component/model/DefaultExternalComponentGraphResolveState$ExternalGraphSelectionCandidates
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleComponentGraphSpecificResolveState
instanceKlass org/gradle/internal/resolve/ResolveExceptionAnalyzer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryChainModuleResolution
instanceKlass org/gradle/api/internal/artifacts/DefaultComponentSelection
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/ComponentMetadataAdapter
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/CachedMetadataProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryChainComponentMetaDataResolver$2
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ComponentMetaDataResolveState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/dynamicversions/DefaultResolvedModuleVersion
instanceKlass org/gradle/api/internal/artifacts/result/DefaultResolvedVariantResult
instanceKlass org/gradle/api/attributes/DocsType$Impl
instanceKlass org/gradle/internal/component/external/model/DefaultConfigurationMetadata$1
instanceKlass org/gradle/internal/component/external/model/DefaultConfigurationMetadata$2
instanceKlass org/gradle/internal/component/external/model/DefaultConfigurationMetadata$Builder
instanceKlass org/gradle/internal/component/external/model/ShadowedImmutableCapability
instanceKlass org/gradle/api/internal/capabilities/ShadowedCapability
instanceKlass org/gradle/internal/component/external/model/AbstractConfigurationMetadata
instanceKlass org/gradle/internal/component/external/model/DefaultModuleComponentArtifactMetadata
instanceKlass org/gradle/internal/component/model/DefaultIvyArtifactName
instanceKlass org/gradle/internal/component/external/model/ivy/IvyModuleResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryChainModuleSource
instanceKlass org/gradle/internal/component/model/ImmutableModuleSources
instanceKlass org/gradle/internal/component/external/model/AbstractModuleComponentResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultMetadataFileSource
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleDescriptorHashModuleSource
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/data/PomDependencyMgt
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/GradlePomModuleDescriptorBuilder
instanceKlass org/gradle/internal/component/model/MutableModuleSources
instanceKlass org/gradle/internal/component/external/model/VariantMetadataRules
instanceKlass org/gradle/normalization/InputNormalizationHandler
instanceKlass org/gradle/api/ProjectState
instanceKlass org/gradle/internal/component/external/model/maven/MavenModuleResolveMetadata
instanceKlass org/gradle/internal/component/external/model/MutableComponentVariant
instanceKlass org/gradle/internal/component/external/model/AbstractMutableModuleComponentResolveMetadata
instanceKlass org/gradle/internal/component/external/model/ExternalDependencyDescriptor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataSerializer$Reader
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/StringDeduplicatingDecoder
instanceKlass org/gradle/internal/resource/local/AbstractLocallyAvailableResource
instanceKlass org/gradle/internal/file/PathTraversalChecker
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultCachePolicy$AbstractResolutionControl
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentIdentifierSerializer$1
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/DefaultCachedMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/WritableArtifactCacheLockingAccessCoordinator$CacheLockingIndexedCache
instanceKlass org/gradle/cache/internal/CompositeCleanupAction$ScopedCleanupAction
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleMetadataCacheEntry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/modulecache/ModuleComponentAtRepositoryKey
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/PotentialConflictFactory$NoConflict
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/PotentialConflictFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/selectors/SelectorStateResolverResults
instanceKlass org/gradle/internal/component/local/model/DefaultProjectComponentSelector
instanceKlass org/gradle/internal/component/local/model/ProjectComponentSelectorInternal
instanceKlass org/gradle/internal/resolve/result/DefaultResourceAwareResolveResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/AbstractVersionSelector
instanceKlass org/gradle/api/internal/artifacts/dependencies/DefaultResolvedVersionConstraint
instanceKlass org/gradle/internal/component/model/DefaultComponentOverrideMetadata
instanceKlass org/gradle/internal/component/model/ComponentOverrideMetadata
instanceKlass org/gradle/internal/resolve/result/BuildableComponentIdResolveResult
instanceKlass org/gradle/internal/resolve/result/ComponentIdResolveResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/SelectorState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ResolveState$SelectorCacheKey
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/LenientPlatformDependencyMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/DependencyGraphSelector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/DependencyState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/strict/StrictVersionConstraints
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/DefaultPendingDependenciesVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/DependencyGraphBuilder$1
instanceKlass org/gradle/api/internal/artifacts/ComponentVariantNodeIdentifier
instanceKlass org/gradle/api/internal/artifacts/NodeIdentifier
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/selectors/SelectorStateResolver
instanceKlass org/gradle/internal/component/model/ComponentGraphSpecificResolveState$1
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ComponentState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/DependencyGraphComponent
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ModuleSelectors$SelectorComparator
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/strategy/VersionParser$DefaultVersion
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ModuleSelectors
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/PendingDependencies
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/selectors/ResolvableSelectorState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ModuleResolveState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CandidateModule
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ReplaceSelectionWithConflictResultAction
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ResolveOptimizations
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/DeselectVersionAction
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/PendingDependenciesVisitor
instanceKlass org/gradle/api/internal/artifacts/ResolvedVersionConstraint
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/ComponentResolutionState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/StringVersioned
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/builder/ResolveState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/selectors/ComponentStateFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/PotentialConflict
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler$Candidate
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/DefaultCapabilitiesConflictHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/ConflictContainer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/ConflictResolverDetails
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/DefaultConflictHandler
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/UserConfiguredCapabilityResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/LastCandidateCapabilityResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/LatestModuleConflictResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DependencySubstitutionApplicator$SubstitutionResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/NoOpSubstitution
instanceKlass org/gradle/api/internal/artifacts/ivyservice/clientmodule/ClientModuleResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/CompositeDependencyGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedArtifactsGraphVisitor
instanceKlass org/gradle/api/internal/attributes/immutable/artifact/ImmutableArtifactTypeRegistry
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/CompositeDependencyArtifactsVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/ComponentResolversChain$ArtifactResolverChain
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ErrorHandlingArtifactResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/ComponentResolversChain$ComponentMetaDataResolverChain
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/ComponentResolversChain$DependencyToComponentIdResolverChain
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/VirtualComponentMetadataResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/ComponentResolversChain
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ErrorHandlingModuleComponentRepository$ErrorHandlingModuleComponentRepositoryAccess
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ErrorHandlingModuleComponentRepository
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/BaseModuleComponentRepository
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/CachingModuleComponentRepository$ResolveAndCacheRepositoryAccess
instanceKlass org/gradle/internal/resolve/result/BuildableModuleComponentMetaDataResolveResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/CachingModuleComponentRepository$LocateInCacheRepositoryAccess
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/CachingModuleComponentRepository
instanceKlass org/gradle/api/internal/artifacts/dsl/WrappingComponentMetadataContext
instanceKlass org/gradle/api/artifacts/ComponentMetadataContext
instanceKlass org/gradle/api/artifacts/ComponentMetadataDetails
instanceKlass org/gradle/api/internal/artifacts/dsl/DefaultComponentMetadataProcessor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ExternalModuleComponentResolverFactory$DefaultMetadataResolutionContext
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/ExternalResourceResolver$AbstractRepositoryAccess
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ModuleComponentRepositoryAccess
instanceKlass org/gradle/api/internal/artifacts/repositories/resolver/VersionLister
instanceKlass org/gradle/api/internal/artifacts/repositories/AbstractArtifactRepository$1
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultImmutableMetadataSources
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/RedirectingGradleMetadataModuleMetadataSource
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/MavenMetadataArtifactProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/parser/DescriptorParseContext
instanceKlass org/gradle/internal/component/model/ModuleDescriptorArtifactMetadata
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/GradleModuleMetadataCompatibilityConverter
instanceKlass org/gradle/api/internal/artifacts/repositories/metadata/DefaultGradleModuleMetadataSource
instanceKlass org/gradle/api/internal/artifacts/repositories/DefaultMavenArtifactRepository$MavenSnapshotDecoratingSource
instanceKlass org/gradle/internal/resource/transfer/CacheAwareExternalResourceAccessor$ResourceFileStore
instanceKlass org/gradle/internal/resource/ExternalResource$ContentAndMetadataAction
instanceKlass org/gradle/internal/resource/transfer/DefaultCacheAwareExternalResourceAccessor
instanceKlass org/gradle/internal/resource/transport/DefaultExternalResourceRepository
instanceKlass org/gradle/internal/resource/transfer/ProgressLoggingExternalResourceLister$1
instanceKlass org/gradle/internal/resource/ExternalResourceListBuildOperationType$Result
instanceKlass org/gradle/internal/resource/transfer/ProgressLoggingExternalResourceAccessor$1
instanceKlass org/gradle/internal/resource/ExternalResourceReadMetadataBuildOperationType$Result
instanceKlass org/gradle/internal/resource/transfer/AbstractProgressLoggingHandler
instanceKlass org/gradle/internal/resource/transfer/CacheAwareExternalResourceAccessor
instanceKlass org/gradle/internal/resource/transport/AbstractRepositoryTransport
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultExternalResourceCachePolicy
instanceKlass org/gradle/internal/resource/transfer/DefaultExternalResourceConnector$NoOpStats
instanceKlass org/gradle/internal/resource/transfer/DefaultExternalResourceConnector$1
instanceKlass org/gradle/internal/resource/transfer/DefaultExternalResourceConnector$ExternalResourceAccessStats
instanceKlass org/gradle/internal/resource/transfer/DefaultExternalResourceConnector
instanceKlass org/apache/http/HttpEntityEnclosingRequest
instanceKlass org/apache/http/HttpEntity
instanceKlass org/gradle/internal/resource/transport/http/HttpResourceUploader
instanceKlass org/gradle/internal/resource/transport/http/HttpResourceLister
instanceKlass org/gradle/internal/resource/transfer/ExternalResourceReadResponse
instanceKlass org/gradle/internal/resource/transfer/AbstractExternalResourceAccessor
instanceKlass org/apache/http/message/AbstractHttpMessage
instanceKlass org/apache/http/client/methods/AbortableHttpRequest
instanceKlass org/apache/http/client/methods/HttpExecutionAware
instanceKlass org/apache/http/client/methods/Configurable
instanceKlass org/apache/http/client/methods/HttpUriRequest
instanceKlass org/apache/http/protocol/HttpContext
instanceKlass org/slf4j/spi/LocationAwareLogger
instanceKlass org/apache/commons/logging/impl/SLF4JLog
instanceKlass org/apache/commons/logging/impl/SLF4JLocationAwareLog
instanceKlass org/apache/commons/logging/Log
instanceKlass org/apache/commons/logging/LogFactory
instanceKlass org/apache/http/conn/ssl/DefaultHostnameVerifier
instanceKlass org/gradle/internal/resource/transport/http/DefaultHttpSettings$Builder
instanceKlass org/gradle/internal/resource/transport/http/DefaultHttpSettings$2$2
instanceKlass javax/net/ssl/X509TrustManager
instanceKlass javax/net/ssl/TrustManager
instanceKlass com/google/common/base/Suppliers$MemoizingSupplier
instanceKlass com/google/common/base/Suppliers$NonSerializableMemoizingSupplier
instanceKlass org/gradle/internal/resource/transport/http/DefaultHttpSettings$2$1
instanceKlass org/gradle/internal/resource/transport/http/DefaultHttpSettings$2
instanceKlass org/gradle/internal/resource/transport/http/DefaultHttpSettings$1
instanceKlass org/gradle/internal/resource/transport/http/HttpTimeoutSettings
instanceKlass org/gradle/internal/resource/transport/http/HttpProxySettings
instanceKlass javax/net/ssl/HostnameVerifier
instanceKlass org/gradle/internal/resource/transport/http/DefaultHttpSettings
instanceKlass org/gradle/api/internal/artifacts/repositories/transport/RepositoryTransportFactory$DefaultResourceConnectorSpecification
instanceKlass org/gradle/internal/verifier/HttpRedirectVerifierFactory
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ExternalModuleComponentResolverFactory$ParentModuleLookupResolver
instanceKlass org/gradle/internal/resolve/result/BuildableArtifactFileResolveResult
instanceKlass org/gradle/internal/resolve/result/BuildableTypedResolveResult
instanceKlass org/gradle/internal/resolve/result/ErroringResolveResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryChainArtifactResolver
instanceKlass org/gradle/internal/resolve/result/BuildableComponentResolveResult
instanceKlass org/gradle/internal/resolve/result/ResourceAwareResolveResult
instanceKlass org/gradle/internal/resolve/result/ComponentResolveResult
instanceKlass org/gradle/internal/resolve/result/ResolveResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryChainComponentMetaDataResolver
instanceKlass org/gradle/internal/component/model/ComponentGraphSpecificResolveState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/DynamicVersionResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/RepositoryChainDependencyToComponentIdResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/ComponentSelectionRulesProcessor
instanceKlass org/gradle/api/internal/artifacts/ComponentSelectionInternal
instanceKlass org/gradle/api/artifacts/ComponentSelection
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/MetadataProvider
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/Versioned
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/DefaultVersionedComponentChooser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/VersionedComponentChooser
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ivyresolve/UserResolverChain
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/ResolutionFailureCollector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/VisitedFileDependencyResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/FileDependencyCollectingGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/VisitedArtifactResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/DefaultResolvedArtifactsBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/projectresult/ResolvedLocalComponentsResultGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectorSerializer$OptimizingAttributeContainerSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/DependencyResultSerializer
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/ResolvedGraphComponent
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/StreamingResolutionResultBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/ResolvedConfigurationDependencyGraphVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/ResolvedGraphResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/DefaultResolvedConfigurationBuilder
instanceKlass org/gradle/api/artifacts/ResolvedDependency
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/TransientConfigurationResults
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/oldresult/TransientConfigurationResultsBuilder
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/CachedStoreFactory$SimpleStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/CachedStoreFactory$Stats
instanceKlass org/gradle/cache/internal/Store
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/CachedStoreFactory
instanceKlass org/gradle/cache/internal/BinaryStore$BinaryData
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/DefaultBinaryStore
instanceKlass org/gradle/cache/internal/BinaryStore
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/store/ResolutionResultsStoreFactory$1
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$DefaultConfigurationIdentity
instanceKlass org/gradle/api/internal/artifacts/cache/ArtifactResolutionControl
instanceKlass org/gradle/api/internal/artifacts/cache/ModuleResolutionControl
instanceKlass org/gradle/api/internal/artifacts/cache/DependencyResolutionControl
instanceKlass org/gradle/api/internal/artifacts/cache/ResolutionControl
instanceKlass org/gradle/internal/typeconversion/FlatteningNotationParser
instanceKlass org/gradle/api/internal/artifacts/DependencySubstitutionInternal
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultComponentSelectionRules
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultCachePolicy
instanceKlass org/gradle/api/internal/artifacts/ComponentSelectionRulesInternal
instanceKlass org/gradle/api/artifacts/ComponentSelectionRules
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultResolutionStrategy
instanceKlass org/gradle/api/artifacts/DependencySubstitution
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/DefaultComponentSelectionDescriptor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionReasonInternal
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionDescriptorInternal
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/result/ComponentSelectionReasons
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DefaultDependencySubstitutions$ProjectPathConverter
instanceKlass org/gradle/api/artifacts/DependencySubstitutions$Substitution
instanceKlass org/gradle/api/internal/artifacts/ivyservice/dependencysubstitution/DefaultDependencySubstitutions
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/UpgradeCapabilityResolver
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler$Resolver
instanceKlass org/gradle/internal/component/external/model/DefaultComponentVariantIdentifier
instanceKlass org/gradle/api/artifacts/ComponentVariantIdentifier
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler$CandidateDetails
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultCapabilitiesResolution$DefaultCapabilityResolutionDetails
instanceKlass org/gradle/api/artifacts/CapabilityResolutionDetails
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/CapabilitiesConflictHandler$ResolutionDetails
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/graph/conflicts/ConflictResolutionResult
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/DefaultCapabilitiesResolution
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolutionstrategy/CapabilitiesResolutionInternal
instanceKlass org/gradle/api/artifacts/CapabilitiesResolution
instanceKlass org/gradle/api/internal/attributes/AttributeValue$1
instanceKlass org/gradle/internal/component/model/DelegatingDependencyMetadata
instanceKlass org/gradle/internal/component/local/model/DslOriginDependencyMetadata
instanceKlass org/gradle/internal/component/external/model/DefaultModuleComponentSelector
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/LocalVariantGraphResolveStateBuilder$DependencyState
instanceKlass org/gradle/internal/component/local/model/DefaultLocalVariantGraphResolveState$DefaultLocalVariantArtifactResolveState
instanceKlass org/gradle/internal/component/model/VariantArtifactResolveState
instanceKlass org/gradle/internal/component/local/model/DefaultLocalVariantGraphResolveState
instanceKlass org/gradle/internal/component/local/model/DefaultLocalVariantGraphResolveMetadata
instanceKlass org/gradle/internal/component/local/model/DefaultLocalVariantGraphResolveState$VariantDependencyMetadata
instanceKlass org/gradle/internal/component/model/DefaultVariantMetadata
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/DefaultLocalVariantGraphResolveStateBuilder$1
instanceKlass org/gradle/internal/component/external/model/ImmutableCapabilities
instanceKlass org/gradle/api/internal/artifacts/configurations/Configurations
instanceKlass org/gradle/internal/component/model/ComponentConfigurationIdentifier
instanceKlass org/gradle/internal/component/model/VariantResolveMetadata$Identifier
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/DefaultRootComponentMetadataBuilder$1
instanceKlass org/gradle/internal/component/external/model/DefaultImmutableCapability
instanceKlass org/gradle/internal/component/local/model/LocalComponentGraphResolveState$LocalComponentGraphSelectionCandidates
instanceKlass org/gradle/internal/component/model/ComponentArtifactResolveMetadata
instanceKlass org/gradle/internal/component/model/GraphSelectionCandidates
instanceKlass org/gradle/internal/component/model/AbstractComponentGraphResolveState
instanceKlass org/gradle/internal/component/model/ComponentArtifactResolveState
instanceKlass org/gradle/api/internal/artifacts/ivyservice/moduleconverter/dependencies/LocalVariantGraphResolveStateBuilder$DependencyCache
instanceKlass org/gradle/internal/component/local/model/LocalComponentGraphResolveStateFactory$ConfigurationsProviderVariantFactory
instanceKlass org/gradle/internal/component/local/model/LocalComponentGraphResolveMetadata
instanceKlass org/gradle/api/internal/artifacts/DefaultModuleVersionIdentifier
instanceKlass org/gradle/api/internal/artifacts/repositories/descriptor/UrlRepositoryDescriptor$Builder
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationResolutionBuildOperationDetails$RepositoryImpl
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationDependenciesBuildOperationType$Repository
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationResolutionBuildOperationDetails
instanceKlass org/gradle/internal/operations/trace/CustomOperationTraceSerialization
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolveConfigurationDependenciesBuildOperationType$Details
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$1
instanceKlass org/gradle/api/internal/artifacts/ivyservice/ResolvedArtifactCollectingVisitor
instanceKlass org/gradle/internal/model/CalculatedValueContainer$GlobalContext
instanceKlass org/gradle/internal/model/CalculatedValueContainerFactory$SupplierBackedCalculator
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultArtifactCollection$ArtifactSetResult
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$DefaultResolutionHost
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolutionResultProvider$1
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$ResolverResultsResolutionResultProvider
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolutionResultProviderBackedSelectedArtifactSet
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ResolvedFileVisitor
instanceKlass org/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/ArtifactVisitor
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultArtifactCollection
instanceKlass org/gradle/api/internal/artifacts/configurations/ArtifactCollectionInternal
instanceKlass org/gradle/api/internal/artifacts/resolver/DefaultResolutionOutputs$DefaultArtifactView
instanceKlass org/gradle/api/specs/Specs$2
instanceKlass org/gradle/api/specs/Specs$1
instanceKlass org/gradle/api/specs/Specs
instanceKlass org/gradle/api/internal/artifacts/resolver/DefaultResolutionOutputs$DefaultArtifactViewConfiguration
instanceKlass org/gradle/api/internal/artifacts/resolver/DefaultResolutionOutputs
instanceKlass org/gradle/api/artifacts/ArtifactView$ViewConfiguration
instanceKlass org/gradle/internal/service/scopes/DetachedDependencyMetadataProvider
instanceKlass org/gradle/api/internal/artifacts/configurations/DetachedConfigurationsProvider
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/ModuleFactoryHelper
instanceKlass org/gradle/api/internal/artifacts/dependencies/ModuleDependencyCapabilitiesInternal
instanceKlass org/gradle/api/internal/artifacts/dependencies/DefaultDependencyArtifact
instanceKlass org/gradle/api/internal/artifacts/DefaultExcludeRuleContainer
instanceKlass org/gradle/api/artifacts/ExcludeRuleContainer
instanceKlass org/gradle/plugin/use/internal/PluginArtifactRepository
instanceKlass org/gradle/kotlin/dsl/PluginDependenciesSpecScope
instanceKlass Settings_gradle$1$1
instanceKlass Settings_gradle$1
instanceKlass org/gradle/kotlin/dsl/support/PluginAwareScript
instanceKlass org/gradle/kotlin/dsl/NamedDomainObjectCollectionExtensionsKt
instanceKlass org/gradle/kotlin/dsl/support/CompiledKotlinSettingsScript$SettingsScriptHost
instanceKlass org/gradle/kotlin/dsl/support/DefaultKotlinScript$Host
instanceKlass kotlin/reflect/jvm/internal/KProperty1Impl$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/KProperty1Impl$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl$$Lambda$5
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl$$Lambda$4
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val$1
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties$Val
instanceKlass kotlin/reflect/jvm/internal/ReflectProperties
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl$$Lambda$0
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater$AtomicReferenceFieldUpdaterImpl$1
instanceKlass java/util/concurrent/atomic/AtomicReferenceFieldUpdater
instanceKlass kotlin/SafePublicationLazyImpl$Companion
instanceKlass kotlin/SafePublicationLazyImpl
instanceKlass kotlin/reflect/jvm/internal/KClassImpl$$Lambda$0
instanceKlass kotlin/text/Regex$Companion
instanceKlass kotlin/text/Regex
instanceKlass kotlin/jvm/internal/DefaultConstructorMarker
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KClassifierImpl
instanceKlass kotlin/reflect/jvm/internal/CachesKt$$Lambda$4
instanceKlass kotlin/reflect/jvm/internal/CachesKt$$Lambda$3
instanceKlass kotlin/reflect/jvm/internal/CachesKt$$Lambda$2
instanceKlass kotlin/reflect/jvm/internal/CachesKt$$Lambda$1
instanceKlass kotlin/reflect/jvm/internal/CacheByClass
instanceKlass kotlin/Result$Failure
instanceKlass kotlin/Result$Companion
instanceKlass kotlin/Result
instanceKlass kotlin/reflect/jvm/internal/CacheByClassKt
instanceKlass kotlin/reflect/jvm/internal/CachesKt$$Lambda$0
instanceKlass kotlin/reflect/jvm/internal/CachesKt
instanceKlass kotlin/reflect/jvm/internal/KPropertyImpl$Companion
instanceKlass kotlin/reflect/jvm/internal/KCallableImpl
instanceKlass kotlin/reflect/jvm/internal/KTypeParameterOwnerImpl
instanceKlass kotlin/reflect/jvm/internal/KDeclarationContainerImpl
instanceKlass kotlin/jvm/internal/ClassBasedDeclarationContainer
instanceKlass kotlin/reflect/KMutableProperty2
instanceKlass kotlin/reflect/KProperty2
instanceKlass kotlin/jvm/functions/Function2
instanceKlass kotlin/reflect/KMutableProperty0
instanceKlass kotlin/reflect/KClass
instanceKlass kotlin/reflect/KTypeParameter
instanceKlass kotlin/reflect/KMutableProperty1
instanceKlass kotlin/reflect/KMutableProperty
instanceKlass kotlin/reflect/KType
instanceKlass kotlin/jvm/internal/ReflectionFactory
instanceKlass kotlin/reflect/KClassifier
instanceKlass kotlin/jvm/internal/Reflection
instanceKlass kotlin/reflect/KProperty1
instanceKlass org/gradle/kotlin/dsl/support/DefaultKotlinScript
instanceKlass org/gradle/kotlin/dsl/KotlinScript
instanceKlass org/gradle/kotlin/dsl/support/EmbeddedKotlinProvider$pinEmbeddedKotlinDependenciesOn$1$1$1
instanceKlass kotlin/io/CloseableKt
instanceKlass kotlin/Unit
instanceKlass org/gradle/api/internal/artifacts/ImmutableVersionConstraint
instanceKlass org/gradle/api/internal/artifacts/dependencies/AbstractVersionConstraint
instanceKlass org/gradle/api/internal/artifacts/VersionConstraintInternal
instanceKlass org/gradle/api/artifacts/MutableVersionConstraint
instanceKlass org/gradle/api/internal/catalog/parser/StrictVersionParser$RichVersion
instanceKlass org/gradle/api/internal/artifacts/dsl/ParsedModuleStringNotation
instanceKlass org/gradle/api/attributes/plugin/GradlePluginApiVersion$Impl
instanceKlass org/gradle/api/attributes/Bundling$Impl
instanceKlass org/gradle/api/internal/initialization/StandaloneDomainObjectContext$CalculatedModelValueImpl
instanceKlass org/gradle/api/artifacts/ConfigurationVariant
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfigurationPublications$ConfigurationVariantFactory
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$AllArtifactsProvider
instanceKlass org/gradle/api/internal/artifacts/configurations/PublishArtifactSetProvider
instanceKlass org/gradle/api/internal/artifacts/DefaultPublishArtifactSet$ArtifactsFileCollection
instanceKlass org/gradle/api/internal/tasks/DefaultTaskDependency$VisitBehavior
instanceKlass org/gradle/api/internal/artifacts/DefaultDependencySet$MutationValidationAction
instanceKlass org/gradle/api/artifacts/result/ResolutionResult
instanceKlass org/gradle/api/artifacts/ArtifactCollection
instanceKlass org/gradle/api/internal/artifacts/resolver/ResolutionOutputsInternal
instanceKlass org/gradle/api/internal/artifacts/resolver/ResolutionOutputs
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolutionResultProvider
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$ConfigurationResolutionAccess
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$ConfigurationDescription
instanceKlass org/gradle/api/artifacts/ExcludeRule
instanceKlass org/gradle/api/internal/file/collections/FileSystemMirroringFileTree
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfigurationPublications
instanceKlass org/gradle/api/internal/artifacts/configurations/DefaultConfiguration$ConfigurationResolvableDependencies
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolvableDependenciesInternal
instanceKlass org/gradle/api/internal/DelegatingDomainObjectSet
instanceKlass org/gradle/internal/component/model/LocalComponentDependencyMetadata
instanceKlass org/gradle/api/internal/artifacts/configurations/ResolutionStrategyInternal
instanceKlass org/gradle/api/internal/artifacts/ResolverResults
instanceKlass org/gradle/api/internal/attributes/FreezableAttributeContainer
instanceKlass org/gradle/api/internal/artifacts/ResolveContext$FailureResolutions
instanceKlass org/gradle/api/artifacts/ConfigurationPublications
instanceKlass org/gradle/api/artifacts/ResolvableDependencies
instanceKlass org/gradle/api/artifacts/ArtifactView
instanceKlass org/gradle/api/artifacts/ResolutionStrategy
instanceKlass org/gradle/operations/dependencies/configurations/ConfigurationIdentity
instanceKlass org/gradle/api/artifacts/DependencyConstraintSet
instanceKlass org/gradle/api/artifacts/PublishArtifactSet
instanceKlass org/gradle/api/artifacts/DependencySet
instanceKlass org/gradle/api/internal/artifacts/resolver/ResolutionAccess
instanceKlass org/gradle/api/artifacts/DependencyResolutionListener
instanceKlass org/gradle/api/internal/artifacts/configurations/ConfigurationRolesForMigration
instanceKlass org/gradle/api/internal/initialization/transform/ProjectDependencyInstrumentingArtifactTransform$Parameters_Decorated
instanceKlass org/gradle/api/internal/initialization/transform/ProjectDependencyInstrumentingArtifactTransform$Parameters
instanceKlass org/gradle/api/internal/initialization/transform/BaseInstrumentingArtifactTransform$InstrumentingClassTransformProvider
instanceKlass org/gradle/api/internal/initialization/transform/BaseInstrumentingArtifactTransform$Parameters_Decorated
instanceKlass org/gradle/api/internal/initialization/transform/BaseInstrumentingArtifactTransform$Parameters
instanceKlass org/gradle/api/internal/initialization/transform/BaseInstrumentingArtifactTransform
instanceKlass org/gradle/api/internal/file/collections/DefaultConfigurableFileCollection$UnresolvedItemsCollector
instanceKlass org/gradle/api/internal/tasks/AbstractTaskDependency$1
instanceKlass org/gradle/api/internal/tasks/AbstractTaskDependency
instanceKlass org/gradle/api/internal/tasks/TaskDependencyContainerInternal
instanceKlass org/gradle/api/internal/tasks/TaskDependencyInternal
instanceKlass org/gradle/api/internal/file/collections/DefaultConfigurableFileCollection$EmptyCollector
instanceKlass org/gradle/api/internal/file/collections/DefaultConfigurableFileCollection$ValueCollector
instanceKlass org/gradle/api/internal/initialization/transform/MergeInstrumentationAnalysisTransform$Parameters_Decorated
instanceKlass org/gradle/api/internal/initialization/transform/MergeInstrumentationAnalysisTransform$Parameters
instanceKlass org/gradle/api/internal/initialization/transform/MergeInstrumentationAnalysisTransform
instanceKlass org/gradle/api/internal/artifacts/transform/TransformStep
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformRegistrationFactory$DefaultTransformRegistration
instanceKlass org/gradle/internal/model/CalculatedValueContainer$CalculationState
instanceKlass org/gradle/internal/model/CalculatedValueContainer
instanceKlass org/gradle/api/internal/tasks/WorkNodeAction
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform$IsolateTransformParameters
instanceKlass org/gradle/work/InputChanges
instanceKlass org/gradle/internal/instantiation/generator/DependencyInjectingInstantiator$1
instanceKlass org/gradle/api/internal/tasks/properties/FileParameterUtils
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransform
instanceKlass org/gradle/internal/execution/model/InputNormalizer$1
instanceKlass org/gradle/internal/properties/PropertyValue$1
instanceKlass org/gradle/internal/properties/PropertyValue
instanceKlass org/gradle/api/internal/artifacts/transform/DefaultTransformRegistrationFactory$NormalizerCollectingVisitor
instanceKlass org/gradle/api/problems/internal/DefaultProblemGroup
instanceKlass org/gradle/api/problems/internal/GradleCoreProblemGroup$CompilationProblemGroup
instanceKlass org/gradle/api/problems/internal/GradleCoreProblemGroup$ValidationProblemGroup
instanceKlass org/gradle/api/problems/internal/GradleCoreProblemGroup
instanceKlass org/gradle/api/problems/ProblemGroup
instanceKlass org/gradle/api/problems/internal/DefaultProblemId
instanceKlass org/gradle/api/problems/ProblemId
instanceKlass org/gradle/api/problems/internal/InternalProblemBuilder
instanceKlass org/gradle/api/problems/internal/InternalProblemSpec
instanceKlass org/gradle/internal/reflect/ProblemRecordingTypeValidationContext
instanceKlass org/gradle/internal/properties/annotations/DefaultTypeMetadataStore$DefaultTypeMetadata
instanceKlass com/google/common/collect/FluentIterable
instanceKlass org/gradle/api/reflect/TypeOf$4
instanceKlass org/gradle/model/internal/type/ParameterizedTypeWrapper
instanceKlass org/gradle/internal/properties/annotations/DefaultTypeMetadataStore$DefaultPropertyMetadata
instanceKlass org/objectweb/asm/ClassReader
instanceKlass org/gradle/api/internal/initialization/transform/InstrumentationArtifactMetadata
instanceKlass org/gradle/api/internal/initialization/transform/services/InjectedInstrumentationServices
instanceKlass com/google/common/collect/SortedIterables
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadata
instanceKlass com/google/common/collect/AbstractMapBasedMultimap$KeySet$1
instanceKlass org/gradle/internal/reflect/annotations/impl/AbstractHasAnnotationMetadata
instanceKlass com/google/common/collect/MultimapBuilder$ArrayListSupplier
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore$HasAnnotationMetadataBuilder
instanceKlass groovy/transform/Generated
instanceKlass org/gradle/api/artifacts/transform/TransformOutputs
instanceKlass org/gradle/internal/reflect/annotations/impl/DefaultTypeAnnotationMetadataStore$TypeAnnotationMetadataVisitor
instanceKlass org/gradle/internal/reflect/validation/ReplayingTypeValidationContext
instanceKlass com/google/common/collect/NullnessCasts
instanceKlass org/gradle/api/internal/initialization/transform/InstrumentationAnalysisTransform$Parameters_Decorated
instanceKlass org/gradle/api/internal/initialization/transform/InstrumentationAnalysisTransform$Parameters
instanceKlass org/gradle/api/internal/initialization/transform/InstrumentationAnalysisTransform
instanceKlass org/gradle/api/services/internal/BuildServiceDetails
instanceKlass org/gradle/internal/instantiation/generator/AsmBackedClassGenerator$AttachedProperty
instanceKlass org/gradle/api/services/internal/DefaultBuildServicesRegistry$DefaultServiceSpec
instanceKlass org/gradle/internal/reflect/Types$1
instanceKlass org/gradle/internal/reflect/Types
instanceKlass org/gradle/internal/reflect/Types$TypeVisitor
instanceKlass org/gradle/api/internal/initialization/transform/services/CacheInstrumentationDataBuildService
instanceKlass com/google/common/reflect/Reflection
instanceKlass com/google/common/reflect/Types$TypeVariableInvocationHandler
instanceKlass com/google/common/reflect/Types$TypeVariableImpl
instanceKlass com/google/common/reflect/Types$NativeTypeVariableEquals
instanceKlass org/gradle/api/internal/DynamicPropertyNamer
instanceKlass org/gradle/api/services/BuildServiceParameters$None
instanceKlass org/gradle/api/services/BuildService
instanceKlass org/gradle/api/services/internal/DefaultBuildServicesRegistry$DefaultServiceRegistration
instanceKlass org/gradle/api/services/BuildServiceParameters
instanceKlass org/gradle/api/services/BuildServiceSpec
instanceKlass org/gradle/internal/resources/SharedResource
instanceKlass org/gradle/api/services/BuildServiceRegistration
instanceKlass org/gradle/api/services/internal/BuildServiceProvider$Listener
instanceKlass kotlin/annotation/MustBeDocumented
instanceKlass org/gradle/internal/flow/services/BuildFlowScope$State
instanceKlass org/gradle/api/flow/FlowParameters
instanceKlass org/gradle/api/flow/FlowScope$Registration
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler$DirectDependencyAdder
instanceKlass org/gradle/api/artifacts/type/ArtifactTypeDefinition
instanceKlass org/gradle/api/artifacts/type/ArtifactTypeContainer
instanceKlass org/gradle/api/artifacts/dsl/ExternalModuleDependencyVariantSpec
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyHandler
instanceKlass org/gradle/api/internal/artifacts/dsl/DependencyHandlerInternal
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyConstraintHandler$DependencyConstraintAdder
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyConstraintHandler$1
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DynamicAddDependencyMethods
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DynamicAddDependencyMethods$DependencyAdder
instanceKlass org/gradle/api/internal/artifacts/dsl/dependencies/DefaultDependencyConstraintHandler
