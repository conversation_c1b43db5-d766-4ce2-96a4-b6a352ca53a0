JvmtiExport can_access_local_variables 0
JvmtiExport can_hotswap_or_post_breakpoint 0
JvmtiExport can_post_on_exceptions 0
# 228 ciObject found
instanceKlass java/util/regex/Pattern
ciInstanceKlass java/lang/Cloneable 1 0 7 100 1 100 1 1 1
instanceKlass org/gradle/api/Action
instanceKlass org/gradle/internal/IoActions
instanceKlass org/gradle/internal/InternalTransformer
instanceKlass org/gradle/util/internal/GUtil
instanceKlass org/gradle/internal/classpath/TransformedClassPath
instanceKlass java/util/Collections$1
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry$DefaultModule
instanceKlass java/util/Collections$UnmodifiableCollection$1
instanceKlass org/gradle/internal/service/CachingServiceLocator
instanceKlass org/gradle/internal/service/DefaultServiceLocator
instanceKlass org/gradle/internal/service/ServiceLocator
instanceKlass org/gradle/internal/classloader/DefaultClassLoaderFactory
instanceKlass org/gradle/api/internal/DefaultClassPathProvider
instanceKlass org/gradle/api/internal/ClassPathProvider
instanceKlass org/gradle/api/internal/DefaultClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ManifestUtil
instanceKlass org/gradle/internal/Cast
instanceKlass java/util/AbstractList$Itr
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList$Builder
instanceKlass org/gradle/internal/classloader/ClassLoaderSpec
instanceKlass org/gradle/internal/classloader/ClassLoaderVisitor
instanceKlass org/gradle/internal/classpath/DefaultClassPath
instanceKlass org/gradle/internal/classpath/ClassPath
instanceKlass org/gradle/internal/installation/GradleInstallation$1
instanceKlass java/io/FileFilter
instanceKlass org/gradle/internal/installation/GradleInstallation
instanceKlass org/gradle/internal/classloader/ClasspathUtil
instanceKlass org/gradle/internal/installation/CurrentGradleInstallationLocator
instanceKlass org/gradle/internal/installation/CurrentGradleInstallation
instanceKlass org/gradle/api/specs/Spec
instanceKlass org/gradle/api/internal/classpath/Module
instanceKlass org/gradle/api/internal/classpath/DefaultModuleRegistry
instanceKlass org/gradle/api/internal/classpath/GlobalCacheRootsProvider
instanceKlass org/gradle/internal/classloader/ClassLoaderHierarchy
instanceKlass org/gradle/internal/classloader/ClassLoaderFactory
instanceKlass org/gradle/api/internal/ClassPathRegistry
instanceKlass org/gradle/api/internal/classpath/ModuleRegistry
instanceKlass org/gradle/launcher/bootstrap/ProcessBootstrap
instanceKlass org/gradle/launcher/GradleMain
instanceKlass java/io/FilePermissionCollection$1
instanceKlass sun/security/util/SecurityProperties
instanceKlass sun/security/util/FilePermCompat
instanceKlass java/io/FilePermission$1
instanceKlass jdk/internal/access/JavaIOFilePermissionAccess
instanceKlass sun/net/www/MessageHeader
instanceKlass java/net/URLConnection
instanceKlass sun/security/util/ManifestEntryVerifier
instanceKlass java/util/StringTokenizer
instanceKlass java/security/CodeSigner
instanceKlass java/util/jar/JarVerifier
instanceKlass java/net/URLClassLoader$1
instanceKlass java/io/RandomAccessFile$1
instanceKlass org/gradle/wrapper/Install$InstallCheck
instanceKlass org/gradle/wrapper/BootstrapMainStarter$1
instanceKlass sun/nio/ch/FileKey
instanceKlass sun/nio/ch/FileLockTable
instanceKlass sun/nio/ch/NativeThread
instanceKlass sun/nio/ch/NativeDispatcher
instanceKlass sun/nio/ch/NativeThreadSet
instanceKlass sun/nio/ch/IOUtil
instanceKlass java/nio/file/attribute/FileAttribute
instanceKlass java/nio/channels/spi/AbstractInterruptibleChannel
instanceKlass java/nio/channels/InterruptibleChannel
instanceKlass java/nio/channels/ScatteringByteChannel
instanceKlass java/nio/channels/GatheringByteChannel
instanceKlass java/nio/channels/SeekableByteChannel
instanceKlass java/nio/channels/ByteChannel
instanceKlass java/nio/channels/WritableByteChannel
instanceKlass java/nio/channels/ReadableByteChannel
instanceKlass java/nio/channels/Channel
instanceKlass java/util/Formatter
instanceKlass java/lang/Process
instanceKlass java/io/Reader
instanceKlass java/lang/Readable
instanceKlass org/gradle/wrapper/Install$1
instanceKlass java/util/concurrent/Callable
instanceKlass java/math/MutableBigInteger
instanceKlass java/lang/StrictMath
instanceKlass sun/security/provider/ByteArrayAccess
instanceKlass java/lang/constant/DynamicConstantDesc
instanceKlass java/lang/constant/DirectMethodHandleDesc$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl$1
instanceKlass java/lang/constant/DirectMethodHandleDescImpl
instanceKlass java/lang/constant/DirectMethodHandleDesc
instanceKlass java/lang/constant/MethodHandleDesc$1
instanceKlass java/lang/constant/MethodHandleDesc
instanceKlass java/lang/constant/MethodTypeDescImpl
instanceKlass java/lang/constant/MethodTypeDesc
instanceKlass java/lang/constant/ReferenceClassDescImpl
instanceKlass java/lang/constant/ConstantUtils
instanceKlass java/lang/constant/ClassDesc
instanceKlass java/lang/constant/ConstantDescs
instanceKlass java/lang/invoke/VarHandle$2
instanceKlass java/lang/invoke/VarHandle$TypesAndInvokers
instanceKlass java/lang/invoke/VarHandle$AccessDescriptor
instanceKlass java/lang/invoke/VarForm
instanceKlass java/lang/invoke/VarHandleByteArrayBase
instanceKlass java/lang/invoke/VarHandleGuards
instanceKlass jdk/internal/util/Preconditions$1
instanceKlass java/util/function/BiFunction
instanceKlass java/lang/invoke/VarHandle$1
instanceKlass java/lang/ClassValue$Version
instanceKlass java/lang/ClassValue$Identity
instanceKlass java/lang/ClassValue
instanceKlass java/lang/invoke/VarHandles
instanceKlass sun/security/provider/ByteArrayAccess$LE
instanceKlass sun/security/util/MessageDigestSpi2
instanceKlass sun/security/jca/GetInstance$Instance
instanceKlass jdk/internal/event/Event
instanceKlass java/util/Collections$EmptyIterator
instanceKlass java/util/LinkedHashMap$LinkedHashIterator
instanceKlass sun/security/util/SecurityProviderConstants
instanceKlass java/security/Provider$UString
instanceKlass java/security/Provider$Service
instanceKlass sun/security/provider/NativePRNG$NonBlocking
instanceKlass sun/security/provider/NativePRNG$Blocking
instanceKlass sun/security/provider/NativePRNG
instanceKlass sun/security/provider/SunEntries$1
instanceKlass sun/security/provider/SunEntries
instanceKlass sun/security/util/SecurityConstants
instanceKlass java/security/Security$2
instanceKlass jdk/internal/access/JavaSecurityPropertiesAccess
instanceKlass java/security/Security$1
instanceKlass java/security/Security
instanceKlass sun/security/jca/ProviderList$2
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$PreparedASCIIToBinaryBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ASCIIToBinaryConverter
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$ExceptionalBinaryToASCIIBuffer
instanceKlass jdk/internal/math/FloatingDecimal$BinaryToASCIIConverter
instanceKlass jdk/internal/math/FloatingDecimal
instanceKlass java/security/Provider$EngineDescription
instanceKlass java/security/Provider$ServiceKey
instanceKlass sun/security/jca/ProviderConfig
instanceKlass sun/security/jca/ProviderList
instanceKlass sun/security/jca/Providers
instanceKlass sun/security/jca/GetInstance
instanceKlass java/security/MessageDigestSpi
instanceKlass org/gradle/internal/file/locking/ExclusiveFileAccessManager
instanceKlass org/gradle/wrapper/PathAssembler
instanceKlass org/gradle/wrapper/Install
instanceKlass org/gradle/wrapper/Download$DefaultDownloadProgressListener
instanceKlass java/util/concurrent/ConcurrentHashMap$MapEntry
instanceKlass java/util/Properties$EntrySet
instanceKlass java/net/Authenticator
instanceKlass org/gradle/wrapper/Download
instanceKlass org/gradle/util/internal/WrapperDistributionUrlConverter
instanceKlass org/gradle/wrapper/WrapperConfiguration
instanceKlass org/gradle/wrapper/WrapperExecutor
instanceKlass org/gradle/wrapper/Logger
instanceKlass java/util/Collections$SynchronizedCollection
instanceKlass java/io/FileInputStream$1
instanceKlass java/util/Properties$LineReader
instanceKlass org/gradle/wrapper/SystemPropertiesHandler
instanceKlass java/util/TreeMap$Entry
instanceKlass java/lang/ProcessEnvironment$CheckedEntry
instanceKlass java/lang/ProcessEnvironment$CheckedEntrySet$1
instanceKlass java/util/NavigableMap
instanceKlass java/util/SortedMap
instanceKlass java/lang/ProcessEnvironment$EntryComparator
instanceKlass java/lang/ProcessEnvironment$NameComparator
instanceKlass org/gradle/wrapper/GradleUserHomeLookup
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$ClassData
instanceKlass jdk/internal/org/objectweb/asm/Frame
instanceKlass java/lang/invoke/LambdaFormBuffer
instanceKlass java/lang/invoke/LambdaFormEditor$TransformKey
instanceKlass java/lang/invoke/LambdaFormEditor
instanceKlass sun/invoke/util/Wrapper$1
instanceKlass java/lang/invoke/DelegatingMethodHandle$Holder
instanceKlass java/lang/invoke/DirectMethodHandle$2
instanceKlass java/lang/invoke/ClassSpecializer$Factory
instanceKlass java/lang/invoke/ClassSpecializer$SpeciesData
instanceKlass java/lang/invoke/ClassSpecializer$1
instanceKlass java/lang/invoke/ClassSpecializer
instanceKlass java/lang/invoke/InnerClassLambdaMetafactory$1
instanceKlass org/gradle/cli/CommandLineParser$OptionParserState
instanceKlass org/gradle/cli/ParsedCommandLine
instanceKlass org/gradle/cli/CommandLineOption
instanceKlass java/util/regex/ASCII
instanceKlass java/util/regex/Pattern$TreeInfo
instanceKlass java/util/regex/Pattern$BitClass
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassDefiner
instanceKlass jdk/internal/org/objectweb/asm/ClassReader
instanceKlass java/lang/invoke/MethodHandles$Lookup$ClassFile
instanceKlass jdk/internal/org/objectweb/asm/Handler
instanceKlass jdk/internal/org/objectweb/asm/AnnotationVisitor
instanceKlass jdk/internal/org/objectweb/asm/Attribute
instanceKlass jdk/internal/org/objectweb/asm/Label
instanceKlass jdk/internal/org/objectweb/asm/MethodVisitor
instanceKlass jdk/internal/org/objectweb/asm/FieldVisitor
instanceKlass java/util/regex/CharPredicates
instanceKlass sun/invoke/empty/Empty
instanceKlass sun/invoke/util/VerifyType
instanceKlass java/lang/invoke/LambdaProxyClassArchive
instanceKlass jdk/internal/org/objectweb/asm/ByteVector
instanceKlass jdk/internal/org/objectweb/asm/Symbol
instanceKlass jdk/internal/org/objectweb/asm/SymbolTable
instanceKlass jdk/internal/org/objectweb/asm/ClassVisitor
instanceKlass java/lang/invoke/InfoFromMemberName
instanceKlass java/lang/invoke/MethodHandleInfo
instanceKlass jdk/internal/org/objectweb/asm/ConstantDynamic
instanceKlass sun/invoke/util/BytecodeDescriptor
instanceKlass jdk/internal/org/objectweb/asm/Handle
instanceKlass sun/security/action/GetBooleanAction
instanceKlass jdk/internal/org/objectweb/asm/Type
instanceKlass java/lang/invoke/AbstractValidatingLambdaMetafactory
instanceKlass java/lang/invoke/MethodHandleImpl$1
instanceKlass jdk/internal/access/JavaLangInvokeAccess
instanceKlass java/lang/invoke/Invokers$Holder
instanceKlass java/lang/invoke/BootstrapMethodInvoker
instanceKlass java/util/regex/Pattern$BmpCharPredicate
instanceKlass java/util/regex/Pattern$CharPredicate
instanceKlass java/lang/invoke/InvokerBytecodeGenerator$2
instanceKlass java/lang/invoke/InvokerBytecodeGenerator
instanceKlass java/lang/invoke/LambdaForm$Holder
instanceKlass java/lang/invoke/LambdaForm$Name
instanceKlass java/lang/invoke/Invokers
instanceKlass java/lang/invoke/MethodHandleImpl
instanceKlass sun/invoke/util/ValueConversions
instanceKlass java/lang/invoke/DirectMethodHandle$Holder
instanceKlass java/lang/invoke/LambdaForm$NamedFunction
instanceKlass sun/invoke/util/Wrapper$Format
instanceKlass java/lang/invoke/MethodTypeForm
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet
instanceKlass java/lang/invoke/LambdaMetafactory
instanceKlass org/gradle/cli/CommandLineParser
instanceKlass java/util/regex/IntHashSet
instanceKlass java/util/regex/Matcher
instanceKlass java/util/regex/MatchResult
instanceKlass java/util/ArrayList$Itr
instanceKlass java/util/regex/Pattern$Node
instanceKlass java/util/regex/Pattern
instanceKlass sun/nio/fs/WindowsUriSupport
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder$1
instanceKlass java/nio/file/FileSystems$DefaultFileSystemHolder
instanceKlass java/nio/file/FileSystems
instanceKlass java/nio/file/Paths
instanceKlass java/net/URI$Parser
instanceKlass java/lang/Void
instanceKlass java/lang/PublicMethods$Key
instanceKlass java/lang/PublicMethods$MethodList
instanceKlass java/nio/channels/FileLock
instanceKlass java/io/FilenameFilter
instanceKlass org/gradle/cli/ParsedCommandLineOption
instanceKlass org/gradle/cli/CommandLineParser$ParserState
instanceKlass org/gradle/wrapper/GradleWrapperMain
instanceKlass java/security/SecureClassLoader$DebugHolder
instanceKlass java/security/PermissionCollection
instanceKlass java/security/SecureClassLoader$1
instanceKlass java/security/SecureClassLoader$CodeSourceKey
instanceKlass java/util/zip/Checksum$1
instanceKlass java/util/zip/CRC32
instanceKlass java/util/zip/Checksum
instanceKlass sun/nio/ByteBuffered
instanceKlass java/lang/Package$VersionInfo
instanceKlass java/lang/NamedPackage
instanceKlass java/util/jar/Attributes$Name
instanceKlass java/util/jar/Attributes
instanceKlass jdk/internal/loader/Resource
instanceKlass sun/security/action/GetIntegerAction
instanceKlass sun/security/util/Debug
instanceKlass sun/security/util/SignatureFileVerifier
instanceKlass java/util/zip/ZipFile$InflaterCleanupAction
instanceKlass java/util/zip/Inflater$InflaterZStreamRef
instanceKlass java/util/zip/Inflater
instanceKlass java/util/zip/ZipEntry
instanceKlass jdk/internal/util/jar/JarIndex
instanceKlass java/nio/Bits$1
instanceKlass jdk/internal/misc/VM$BufferPool
instanceKlass java/nio/Bits
instanceKlass sun/nio/ch/DirectBuffer
instanceKlass jdk/internal/perf/PerfCounter$CoreCounters
instanceKlass jdk/internal/perf/Perf
instanceKlass jdk/internal/perf/Perf$GetPerfAction
instanceKlass jdk/internal/perf/PerfCounter
instanceKlass java/nio/file/attribute/FileTime
instanceKlass java/util/zip/ZipUtils
instanceKlass java/util/zip/ZipFile$Source$End
instanceKlass java/io/RandomAccessFile$2
instanceKlass jdk/internal/access/JavaIORandomAccessFileAccess
instanceKlass java/io/RandomAccessFile
instanceKlass java/io/DataInput
instanceKlass java/io/DataOutput
instanceKlass sun/nio/fs/WindowsNativeDispatcher$CompletionStatus
instanceKlass sun/nio/fs/WindowsNativeDispatcher$AclInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$Account
instanceKlass sun/nio/fs/WindowsNativeDispatcher$DiskFreeSpace
instanceKlass sun/nio/fs/WindowsNativeDispatcher$VolumeInformation
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstStream
instanceKlass sun/nio/fs/WindowsNativeDispatcher$FirstFile
instanceKlass java/util/Enumeration
instanceKlass java/util/concurrent/ConcurrentHashMap$Traverser
instanceKlass java/util/concurrent/ConcurrentHashMap$CollectionView
instanceKlass jdk/internal/loader/NativeLibraries$NativeLibraryImpl
instanceKlass jdk/internal/loader/NativeLibrary
instanceKlass java/util/ArrayDeque$DeqIterator
instanceKlass jdk/internal/loader/NativeLibraries$1
instanceKlass jdk/internal/loader/NativeLibraries$LibraryPaths
instanceKlass sun/nio/fs/WindowsNativeDispatcher
instanceKlass sun/nio/fs/NativeBuffer$Deallocator
instanceKlass sun/nio/fs/NativeBuffer
instanceKlass java/lang/ThreadLocal$ThreadLocalMap
instanceKlass sun/nio/fs/NativeBuffers
instanceKlass sun/nio/fs/WindowsFileAttributes
instanceKlass java/nio/file/attribute/DosFileAttributes
instanceKlass sun/nio/fs/AbstractBasicFileAttributeView
instanceKlass sun/nio/fs/DynamicFileAttributeView
instanceKlass sun/nio/fs/WindowsFileAttributeViews
instanceKlass sun/nio/fs/Util
instanceKlass java/nio/file/attribute/BasicFileAttributeView
instanceKlass java/nio/file/attribute/FileAttributeView
instanceKlass java/nio/file/attribute/AttributeView
instanceKlass java/nio/file/Files
instanceKlass java/nio/file/CopyOption
instanceKlass java/nio/file/attribute/BasicFileAttributes
instanceKlass sun/nio/fs/WindowsPath
instanceKlass java/nio/file/Path
instanceKlass java/nio/file/Watchable
instanceKlass java/util/zip/ZipFile$Source$Key
instanceKlass sun/nio/fs/WindowsPathParser$Result
instanceKlass sun/nio/fs/WindowsPathParser
instanceKlass java/util/Collections$UnmodifiableCollection
instanceKlass java/util/Arrays$ArrayItr
instanceKlass java/nio/file/FileSystem
instanceKlass java/nio/file/OpenOption
instanceKlass java/nio/file/spi/FileSystemProvider
instanceKlass sun/nio/fs/DefaultFileSystemProvider
instanceKlass java/util/zip/ZipFile$Source
instanceKlass java/lang/ref/Cleaner$Cleanable
instanceKlass jdk/internal/ref/CleanerImpl
instanceKlass java/lang/ref/Cleaner$1
instanceKlass java/lang/ref/Cleaner
instanceKlass jdk/internal/ref/CleanerFactory$1
instanceKlass java/util/concurrent/ThreadFactory
instanceKlass jdk/internal/ref/CleanerFactory
instanceKlass java/util/zip/ZipCoder
instanceKlass java/util/zip/ZipFile$CleanableResource
instanceKlass java/lang/Runtime$Version
instanceKlass java/util/jar/JavaUtilJarAccessImpl
instanceKlass jdk/internal/access/JavaUtilJarAccess
instanceKlass jdk/internal/loader/FileURLMapper
instanceKlass jdk/internal/loader/URLClassPath$JarLoader$1
instanceKlass java/util/zip/ZipFile$1
instanceKlass jdk/internal/access/JavaUtilZipFileAccess
instanceKlass java/util/zip/ZipFile
instanceKlass java/util/zip/ZipConstants
instanceKlass jdk/internal/loader/URLClassPath$Loader
instanceKlass jdk/internal/loader/URLClassPath$3
instanceKlass java/security/PrivilegedExceptionAction
instanceKlass sun/util/locale/LocaleUtils
instanceKlass sun/util/locale/BaseLocale
instanceKlass java/util/Locale
instanceKlass sun/net/util/URLUtil
instanceKlass java/lang/StringCoding
instanceKlass sun/nio/cs/ArrayDecoder
instanceKlass java/nio/charset/CharsetDecoder
instanceKlass sun/launcher/LauncherHelper
instanceKlass java/lang/reflect/Array
instanceKlass java/lang/invoke/StringConcatFactory$3
instanceKlass java/lang/invoke/StringConcatFactory$2
instanceKlass java/lang/invoke/StringConcatFactory$1
instanceKlass java/lang/invoke/StringConcatFactory
instanceKlass java/lang/ModuleLayer$Controller
instanceKlass java/util/concurrent/CopyOnWriteArrayList
instanceKlass jdk/internal/module/ServicesCatalog$ServiceProvider
instanceKlass jdk/internal/loader/AbstractClassLoaderValue$Memoizer
instanceKlass java/util/ImmutableCollections$ListItr
instanceKlass java/util/ListIterator
instanceKlass java/lang/ModuleLayer
instanceKlass jdk/internal/module/ModuleLoaderMap$Modules
instanceKlass jdk/internal/module/ModuleLoaderMap$Mapper
instanceKlass java/util/function/Function
instanceKlass jdk/internal/module/ModuleLoaderMap
instanceKlass java/util/AbstractMap$1$1
instanceKlass java/lang/module/ResolvedModule
instanceKlass java/lang/module/Configuration
instanceKlass jdk/internal/loader/BuiltinClassLoader$LoadedModule
instanceKlass jdk/internal/loader/AbstractClassLoaderValue
instanceKlass jdk/internal/module/ServicesCatalog
instanceKlass jdk/internal/util/Preconditions
instanceKlass sun/net/util/IPAddressUtil
instanceKlass java/net/URLStreamHandler
instanceKlass java/util/HexFormat
instanceKlass sun/net/www/ParseUtil
instanceKlass java/net/URL$3
instanceKlass jdk/internal/access/JavaNetURLAccess
instanceKlass java/net/URL$DefaultFactory
instanceKlass java/net/URLStreamHandlerFactory
instanceKlass jdk/internal/loader/URLClassPath
instanceKlass java/security/Principal
instanceKlass java/security/ProtectionDomain$Key
instanceKlass java/security/ProtectionDomain$JavaSecurityAccessImpl
instanceKlass jdk/internal/access/JavaSecurityAccess
instanceKlass java/lang/ClassLoader$ParallelLoaders
instanceKlass java/security/cert/Certificate
instanceKlass jdk/internal/loader/ArchivedClassLoaders
instanceKlass java/util/Deque
instanceKlass java/util/Queue
instanceKlass jdk/internal/loader/ClassLoaderHelper
instanceKlass jdk/internal/loader/NativeLibraries
instanceKlass jdk/internal/loader/BootLoader
instanceKlass java/util/Optional
instanceKlass jdk/internal/module/SystemModuleFinders$SystemModuleFinder
instanceKlass java/lang/module/ModuleFinder
instanceKlass jdk/internal/module/SystemModuleFinders$3
instanceKlass jdk/internal/module/ModuleHashes$HashSupplier
instanceKlass jdk/internal/module/SystemModuleFinders$2
instanceKlass java/util/function/Supplier
instanceKlass java/lang/module/ModuleReference
instanceKlass jdk/internal/module/ModuleResolution
instanceKlass java/util/Collections$UnmodifiableMap
instanceKlass jdk/internal/module/ModuleHashes$Builder
instanceKlass jdk/internal/module/ModuleHashes
instanceKlass jdk/internal/module/ModuleTarget
instanceKlass java/util/ImmutableCollections$Set12$1
instanceKlass java/lang/Enum
instanceKlass java/lang/module/ModuleDescriptor$Version
instanceKlass java/lang/module/ModuleDescriptor$Provides
instanceKlass java/lang/module/ModuleDescriptor$Opens
instanceKlass java/util/ImmutableCollections$SetN$SetNIterator
instanceKlass java/lang/module/ModuleDescriptor$Exports
instanceKlass java/lang/module/ModuleDescriptor$Requires
instanceKlass jdk/internal/module/Builder
instanceKlass jdk/internal/module/SystemModules$default
instanceKlass jdk/internal/module/SystemModules
instanceKlass jdk/internal/module/SystemModulesMap
instanceKlass java/net/URI$1
instanceKlass jdk/internal/access/JavaNetUriAccess
instanceKlass java/net/URI
instanceKlass jdk/internal/module/SystemModuleFinders
instanceKlass jdk/internal/module/ArchivedModuleGraph
instanceKlass jdk/internal/module/ArchivedBootLayer
instanceKlass jdk/internal/module/ModuleBootstrap$Counters
instanceKlass jdk/internal/module/ModulePatcher
instanceKlass java/io/FileSystem
instanceKlass java/io/DefaultFileSystem
instanceKlass java/io/File
instanceKlass java/lang/module/ModuleDescriptor$1
instanceKlass jdk/internal/access/JavaLangModuleAccess
instanceKlass sun/invoke/util/VerifyAccess
instanceKlass java/lang/module/ModuleDescriptor
instanceKlass jdk/internal/module/ModuleBootstrap
instanceKlass java/lang/invoke/MethodHandleStatics
instanceKlass java/util/Collections
instanceKlass sun/io/Win32ErrorMode
instanceKlass jdk/internal/misc/OSEnvironment
instanceKlass jdk/internal/misc/Signal$NativeHandler
instanceKlass java/util/Hashtable$Entry
instanceKlass jdk/internal/misc/Signal
instanceKlass java/lang/Terminator$1
instanceKlass jdk/internal/misc/Signal$Handler
instanceKlass java/lang/Terminator
instanceKlass java/nio/ByteOrder
instanceKlass java/nio/Buffer$1
instanceKlass jdk/internal/access/JavaNioAccess
instanceKlass jdk/internal/misc/ScopedMemoryAccess
instanceKlass java/nio/charset/CodingErrorAction
instanceKlass sun/nio/cs/SingleByte
instanceKlass java/lang/StringUTF16
instanceKlass sun/nio/cs/MS1252$Holder
instanceKlass sun/nio/cs/ArrayEncoder
instanceKlass java/nio/charset/CharsetEncoder
instanceKlass java/lang/reflect/Modifier
instanceKlass java/lang/Class$1
instanceKlass java/lang/Class$Atomic
instanceKlass java/lang/Class$ReflectionData
instanceKlass jdk/internal/util/ArraysSupport
instanceKlass java/nio/charset/StandardCharsets
instanceKlass sun/nio/cs/HistoricallyNamedCharset
instanceKlass sun/security/action/GetPropertyAction
instanceKlass java/lang/ThreadLocal
instanceKlass java/nio/charset/spi/CharsetProvider
instanceKlass java/nio/charset/Charset
instanceKlass java/io/Writer
instanceKlass java/io/OutputStream
instanceKlass java/io/Flushable
instanceKlass java/io/FileDescriptor$1
instanceKlass jdk/internal/access/JavaIOFileDescriptorAccess
instanceKlass java/io/FileDescriptor
instanceKlass jdk/internal/util/StaticProperty
instanceKlass java/util/HashMap$HashIterator
instanceKlass java/lang/Integer$IntegerCache
instanceKlass java/lang/CharacterData
instanceKlass java/util/Arrays
instanceKlass java/lang/VersionProps
instanceKlass java/lang/StringConcatHelper
instanceKlass jdk/internal/misc/VM
instanceKlass jdk/internal/util/SystemProps$Raw
instanceKlass jdk/internal/util/SystemProps
instanceKlass java/lang/System$2
instanceKlass jdk/internal/access/JavaLangAccess
instanceKlass java/lang/ref/Reference$1
instanceKlass jdk/internal/access/JavaLangRefAccess
instanceKlass java/lang/ref/ReferenceQueue$Lock
instanceKlass java/lang/ref/ReferenceQueue
instanceKlass jdk/internal/reflect/ReflectionFactory
instanceKlass jdk/internal/reflect/ReflectionFactory$GetReflectionFactoryAction
instanceKlass java/security/PrivilegedAction
instanceKlass java/util/concurrent/locks/LockSupport
instanceKlass java/util/concurrent/ConcurrentHashMap$Node
instanceKlass java/util/concurrent/ConcurrentHashMap$CounterCell
instanceKlass java/util/concurrent/locks/ReentrantLock
instanceKlass java/util/concurrent/locks/Lock
instanceKlass java/lang/Runtime
instanceKlass java/util/HashMap$Node
instanceKlass java/util/KeyValueHolder
instanceKlass java/util/Map$Entry
instanceKlass java/util/ImmutableCollections$MapN$MapNIterator
instanceKlass java/lang/Math
instanceKlass jdk/internal/reflect/Reflection
instanceKlass java/lang/invoke/MethodHandles$Lookup
instanceKlass java/lang/StringLatin1
instanceKlass java/security/Permission
instanceKlass java/security/Guard
instanceKlass java/lang/invoke/MemberName$Factory
instanceKlass java/lang/invoke/MethodHandles
instanceKlass jdk/internal/access/SharedSecrets
instanceKlass java/lang/reflect/ReflectAccess
instanceKlass jdk/internal/access/JavaLangReflectAccess
instanceKlass java/util/ImmutableCollections
instanceKlass java/util/Objects
instanceKlass java/util/Set
instanceKlass jdk/internal/misc/CDS
instanceKlass java/lang/Module$ArchivedData
instanceKlass java/lang/String$CaseInsensitiveComparator
instanceKlass java/util/Comparator
instanceKlass java/io/ObjectStreamField
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload
instanceKlass jdk/internal/vm/vector/VectorSupport
instanceKlass java/lang/reflect/RecordComponent
instanceKlass java/util/Iterator
instanceKlass java/lang/Number
instanceKlass java/lang/Character
instanceKlass java/lang/Boolean
instanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer
instanceKlass java/lang/LiveStackFrame
instanceKlass java/lang/StackFrameInfo
instanceKlass java/lang/StackWalker$StackFrame
instanceKlass java/lang/StackStreamFactory$AbstractStackWalker
instanceKlass java/lang/StackWalker
instanceKlass java/nio/Buffer
instanceKlass java/lang/StackTraceElement
instanceKlass java/util/RandomAccess
instanceKlass java/util/List
instanceKlass java/util/AbstractCollection
instanceKlass java/util/Collection
instanceKlass java/lang/Iterable
instanceKlass java/util/concurrent/ConcurrentMap
instanceKlass java/util/AbstractMap
instanceKlass java/security/CodeSource
instanceKlass jdk/internal/loader/ClassLoaders
instanceKlass java/util/jar/Manifest
instanceKlass java/net/URL
instanceKlass java/io/InputStream
instanceKlass java/io/Closeable
instanceKlass java/lang/AutoCloseable
instanceKlass jdk/internal/module/Modules
instanceKlass jdk/internal/misc/Unsafe
instanceKlass jdk/internal/misc/UnsafeConstants
instanceKlass java/lang/AbstractStringBuilder
instanceKlass java/lang/Appendable
instanceKlass java/lang/AssertionStatusDirectives
instanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext
instanceKlass jdk/internal/invoke/NativeEntryPoint
instanceKlass java/lang/invoke/CallSite
instanceKlass java/lang/invoke/MethodType
instanceKlass java/lang/invoke/TypeDescriptor$OfMethod
instanceKlass java/lang/invoke/LambdaForm
instanceKlass java/lang/invoke/MethodHandleNatives
instanceKlass java/lang/invoke/ResolvedMethodName
instanceKlass java/lang/invoke/MemberName
instanceKlass java/lang/invoke/VarHandle
instanceKlass java/lang/invoke/MethodHandle
instanceKlass jdk/internal/reflect/CallerSensitive
instanceKlass java/lang/annotation/Annotation
instanceKlass jdk/internal/reflect/FieldAccessor
instanceKlass jdk/internal/reflect/ConstantPool
instanceKlass jdk/internal/reflect/ConstructorAccessor
instanceKlass jdk/internal/reflect/MethodAccessor
instanceKlass jdk/internal/reflect/MagicAccessorImpl
instanceKlass java/lang/reflect/Parameter
instanceKlass java/lang/reflect/Member
instanceKlass java/lang/reflect/AccessibleObject
instanceKlass java/lang/Module
instanceKlass java/util/Map
instanceKlass java/util/Dictionary
instanceKlass java/lang/ThreadGroup
instanceKlass java/lang/Thread$UncaughtExceptionHandler
instanceKlass java/lang/Thread
instanceKlass java/lang/Runnable
instanceKlass java/lang/ref/Reference
instanceKlass java/lang/Record
instanceKlass java/security/AccessController
instanceKlass java/security/AccessControlContext
instanceKlass java/security/ProtectionDomain
instanceKlass java/lang/SecurityManager
instanceKlass java/lang/Throwable
instanceKlass java/lang/System
instanceKlass java/lang/ClassLoader
instanceKlass java/lang/Cloneable
instanceKlass java/lang/Class
instanceKlass java/lang/invoke/TypeDescriptor$OfField
instanceKlass java/lang/invoke/TypeDescriptor
instanceKlass java/lang/reflect/Type
instanceKlass java/lang/reflect/GenericDeclaration
instanceKlass java/lang/reflect/AnnotatedElement
instanceKlass java/lang/String
instanceKlass java/lang/constant/ConstantDesc
instanceKlass java/lang/constant/Constable
instanceKlass java/lang/CharSequence
instanceKlass java/lang/Comparable
instanceKlass java/io/Serializable
ciInstanceKlass java/lang/Object 1 1 92 100 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 3 8 1 100 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Class 1 1 1611 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 7 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 11 8 1 18 8 1 10 12 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 18 12 1 1 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 100 1 100 1 10 10 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 9 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 1 10 100 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 1 10 10 10 12 1 1 10 12 1 1 10 12 10 12 1 1 100 1 8 1 10 10 12 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 100 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 11 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 10 12 100 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 1 100 1 10 8 1 10 12 1 11 11 12 1 1 11 100 12 1 1 11 12 1 8 1 10 12 1 10 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 9 12 1 10 12 1 1 10 10 12 1 10 7 12 1 1 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 100 1 10 12 1 1 100 11 100 1 9 12 1 1 9 12 1 100 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 12 1 1 9 12 1 100 1 10 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 8 10 7 8 1 18 8 1 8 1 10 12 1 9 12 1 9 12 1 1 10 12 1 100 1 100 1 10 12 1 9 12 1 1 7 1 10 10 12 1 10 100 1 9 12 1 8 1 10 12 1 100 1 10 12 1 10 12 1 1 9 12 1 100 1 8 1 10 100 1 4 10 10 12 11 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 8 1 8 1 10 12 1 1 9 100 12 1 1 11 12 100 1 11 100 12 1 1 9 12 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 9 12 1 9 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 100 1 11 12 1 10 100 12 1 1 1 10 12 1 10 11 12 1 11 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 100 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 18 12 1 1 11 12 1 1 18 11 12 1 18 12 1 11 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 8 1 10 12 1 7 1 9 12 1 1 100 1 100 1 100 1 100 1 100 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 11 12 16 1 16 15 16 15 10 12 16 16 15 10 12 16 15 16 1 15 10 12 16 1 1 1 1 1 1 1 1 100 1 1 100 1 100 1 1 100 1 100 1 1
staticfield java/lang/Class EMPTY_CLASS_ARRAY [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/Class serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/io/Serializable 1 0 7 100 1 100 1 1 1
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle
instanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask
instanceKlass jdk/internal/vm/vector/VectorSupport$Vector
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorPayload 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorShuffle 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$VectorMask 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport$Vector 0 0 28 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass jdk/internal/vm/vector/VectorSupport 0 0 487 100 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 1 100 1 10 12 1 1 11 100 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 1 1 11 100 12 1 1 100 1 11 100 12 1 1 11 100 12 1 1 11 100 12 1 1 11 100 1 100 1 9 12 1 1 10 100 12 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/RecordComponent 0 0 196 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 10 100 12 1 1 9 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 9 12 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 9 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/Iterator 1 1 53 100 1 8 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/System 1 1 803 10 100 12 1 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 9 12 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 10 12 1 1 100 1 10 12 10 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 100 1 100 1 8 1 10 12 1 10 12 1 1 7 1 10 12 1 100 1 8 1 10 10 12 1 100 1 8 1 10 8 1 10 7 12 1 1 8 1 10 12 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 18 12 1 100 1 9 100 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 7 1 10 12 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 8 1 11 12 1 10 12 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 11 12 1 1 7 1 11 12 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 8 1 7 1 9 7 12 1 1 1 10 12 1 7 1 9 12 10 9 12 7 1 10 12 8 1 10 12 1 1 8 1 10 7 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 7 12 1 1 1 9 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 8 1 10 8 1 8 1 8 1 8 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 1 8 1 10 10 10 12 1 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 7 1 10 10 12 1 10 12 1 9 12 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/System in Ljava/io/InputStream; java/io/BufferedInputStream
staticfield java/lang/System out Ljava/io/PrintStream; java/io/PrintStream
staticfield java/lang/System err Ljava/io/PrintStream; java/io/PrintStream
instanceKlass org/gradle/internal/classloader/FilteringClassLoader
instanceKlass jdk/internal/reflect/DelegatingClassLoader
instanceKlass java/security/SecureClassLoader
ciInstanceKlass java/lang/ClassLoader 1 1 1098 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 12 1 10 7 12 1 1 1 10 100 12 1 10 7 1 10 7 1 7 1 7 1 10 12 1 10 12 1 9 12 1 1 10 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 10 12 1 1 7 1 10 8 1 10 12 1 1 10 12 1 10 7 1 7 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 10 12 1 1 10 12 1 1 100 1 8 1 10 8 1 10 12 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 7 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 12 1 10 12 1 1 8 1 8 1 10 7 12 1 1 100 1 10 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 10 7 1 100 1 10 12 1 1 10 12 1 10 100 1 10 12 1 100 1 18 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 8 1 100 1 10 10 12 1 9 12 1 10 7 12 1 1 10 12 1 100 1 8 1 10 12 1 10 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 100 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 7 1 18 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 18 12 1 11 100 12 1 1 1 100 1 10 12 1 1 10 12 1 10 11 12 1 1 10 18 10 12 1 1 11 100 12 1 18 12 1 11 12 1 1 10 12 10 12 1 1 10 12 1 1 100 1 8 1 10 10 12 1 8 1 8 1 10 100 12 1 1 10 12 1 100 1 10 10 12 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 100 1 10 11 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 9 12 1 1 9 12 9 12 1 9 12 1 9 12 1 8 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 11 12 1 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 1 16 15 10 12 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 16 1 1 100 1 100 1 1
staticfield java/lang/ClassLoader nocerts [Ljava/security/cert/Certificate; 0 [Ljava/security/cert/Certificate;
staticfield java/lang/ClassLoader $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/DelegatingClassLoader 0 0 18 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1
instanceKlass java/net/URLClassLoader
instanceKlass jdk/internal/loader/BuiltinClassLoader
ciInstanceKlass java/security/SecureClassLoader 1 1 102 10 7 12 1 1 1 7 1 10 12 1 9 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 7 1 10 12 1 7 1 10 12 1 11 7 12 1 1 1 7 1 11 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
instanceKlass jdk/internal/loader/ClassLoaders$BootClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader
instanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/loader/BuiltinClassLoader 1 1 737 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 10 12 1 9 12 1 10 12 1 9 12 1 10 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 7 1 10 12 1 10 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 7 1 8 1 8 1 10 9 12 1 1 10 7 12 1 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 100 1 10 100 12 1 1 1 10 12 1 100 1 8 1 10 12 1 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 10 11 12 1 1 11 10 12 1 1 100 1 10 12 1 10 100 12 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 100 1 10 12 1 1 11 12 1 100 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 18 12 1 1 10 12 1 10 12 1 1 18 100 1 10 100 12 1 1 1 7 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 100 1 10 12 1 10 7 12 1 1 1 10 12 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 11 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 8 1 8 1 10 10 12 1 8 1 8 1 10 7 12 1 1 1 11 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 10 12 10 12 1 10 12 1 100 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 8 1 10 7 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 16 15 10 12 16 1 1 1 100 1 1 1 1 1 100 1 100 1 1
staticfield jdk/internal/loader/BuiltinClassLoader packageToModule Ljava/util/Map; java/util/concurrent/ConcurrentHashMap
staticfield jdk/internal/loader/BuiltinClassLoader $assertionsDisabled Z 1
ciInstanceKlass java/security/AccessController 1 1 295 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 1 10 11 7 12 1 1 1 10 7 12 1 1 11 7 1 100 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 100 1 10 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 3 1 1 1
staticfield java/security/AccessController $assertionsDisabled Z 1
instanceKlass jdk/internal/reflect/DelegatingConstructorAccessorImpl
instanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl
ciInstanceKlass jdk/internal/reflect/ConstructorAccessorImpl 1 1 27 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
instanceKlass jdk/internal/reflect/FieldAccessorImpl
instanceKlass jdk/internal/reflect/ConstructorAccessorImpl
instanceKlass jdk/internal/reflect/MethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MagicAccessorImpl 1 1 16 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/DelegatingMethodAccessorImpl
instanceKlass jdk/internal/reflect/NativeMethodAccessorImpl
ciInstanceKlass jdk/internal/reflect/MethodAccessorImpl 1 1 25 10 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1
ciInstanceKlass java/lang/Module 1 1 959 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 7 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 9 12 1 1 11 12 1 9 100 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 18 12 1 1 10 12 1 1 11 12 1 9 12 1 11 12 10 100 12 1 1 100 1 8 1 10 7 1 11 12 1 1 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 7 12 1 1 11 12 1 1 9 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 10 12 10 7 12 1 1 10 7 12 1 1 10 7 1 18 12 1 1 11 100 12 1 1 1 18 12 1 11 12 1 1 10 100 12 1 1 1 11 12 1 1 10 7 12 1 1 4 7 1 11 12 1 7 1 7 1 10 10 7 12 1 1 1 10 11 7 12 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 7 1 10 12 1 10 11 12 1 1 10 12 10 12 1 1 9 12 1 100 1 10 10 12 1 1 11 100 1 10 12 1 1 11 12 1 10 10 12 1 11 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 18 12 1 11 12 1 18 12 1 10 12 1 10 12 1 10 12 7 1 10 12 1 10 12 1 10 12 1 9 12 1 7 1 10 10 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 18 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 100 1 8 1 100 1 10 100 1 100 1 3 10 12 1 100 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 1 10 100 12 1 1 8 1 10 12 1 8 1 10 12 1 10 12 10 12 1 8 1 10 10 100 12 1 1 100 1 10 10 12 1 10 7 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 11 12 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 16 15 10 12 16 16 15 10 16 1 15 10 12 16 1 15 10 12 16 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Module ALL_UNNAMED_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module ALL_UNNAMED_MODULE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module EVERYONE_MODULE Ljava/lang/Module; java/lang/Module
staticfield java/lang/Module EVERYONE_SET Ljava/util/Set; java/util/ImmutableCollections$Set12
staticfield java/lang/Module $assertionsDisabled Z 1
ciInstanceKlass java/util/ArrayList 1 1 492 10 7 12 1 1 1 7 1 9 7 12 1 1 1 9 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 11 7 12 1 1 1 9 12 1 1 10 12 1 1 7 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 10 7 12 1 1 1 10 7 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 100 1 10 11 12 1 1 11 100 12 1 1 1 11 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 11 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 100 1 8 1 10 100 1 10 12 1 7 1 10 12 1 10 12 1 1 7 1 10 12 1 10 12 1 1 11 100 12 1 1 100 1 10 12 1 10 12 1 1 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/util/ArrayList EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
staticfield java/util/ArrayList DEFAULTCAPACITY_EMPTY_ELEMENTDATA [Ljava/lang/Object; 0 [Ljava/lang/Object;
ciInstanceKlass java/util/concurrent/ConcurrentHashMap 1 1 1210 7 1 7 1 3 10 12 1 1 3 100 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 100 1 11 12 1 1 11 12 1 11 12 1 1 9 12 1 1 9 12 1 1 9 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 4 10 12 1 9 12 1 10 12 1 1 100 1 10 5 0 10 12 1 10 12 1 1 5 0 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 100 1 10 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 7 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 10 12 1 1 11 12 1 1 11 7 12 1 1 1 11 7 12 1 1 1 11 12 1 7 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 9 10 12 1 1 9 12 1 10 12 1 1 5 0 9 12 1 1 7 1 10 12 1 9 12 1 1 7 1 10 12 1 9 12 1 7 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 11 100 1 10 12 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 8 1 10 12 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 9 10 12 1 9 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 100 1 10 12 11 100 12 1 1 10 11 7 12 1 10 12 1 100 1 10 12 1 100 1 10 10 9 100 12 1 1 1 10 12 3 10 100 12 1 1 9 12 1 10 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 100 12 1 1 9 12 1 9 7 12 1 1 10 12 1 1 10 12 1 3 9 12 1 9 12 1 10 12 1 1 7 1 9 3 9 12 1 100 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 100 12 1 1 1 100 10 12 1 100 1 5 0 10 100 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 100 1 10 12 1 10 100 1 100 1 10 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 10 12 1 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 10 100 1 10 10 100 1 10 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 100 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 10 12 1 9 12 1 1 9 12 1 1 10 12 1 1 8 10 12 1 1 8 8 8 8 7 10 12 1 1 10 12 1 100 1 8 1 10 7 1 100 1 100 1 1 1 5 0 1 1 3 1 3 1 1 1 1 3 1 3 1 3 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/util/concurrent/ConcurrentHashMap NCPU I 4
staticfield java/util/concurrent/ConcurrentHashMap serialPersistentFields [Ljava/io/ObjectStreamField; 3 [Ljava/io/ObjectStreamField;
staticfield java/util/concurrent/ConcurrentHashMap U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/util/concurrent/ConcurrentHashMap SIZECTL J 20
staticfield java/util/concurrent/ConcurrentHashMap TRANSFERINDEX J 32
staticfield java/util/concurrent/ConcurrentHashMap BASECOUNT J 24
staticfield java/util/concurrent/ConcurrentHashMap CELLSBUSY J 36
staticfield java/util/concurrent/ConcurrentHashMap CELLVALUE J 144
staticfield java/util/concurrent/ConcurrentHashMap ABASE I 16
staticfield java/util/concurrent/ConcurrentHashMap ASHIFT I 2
ciInstanceKlass java/lang/String 1 1 1396 10 7 12 1 1 1 8 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 7 12 1 1 10 12 9 7 12 1 1 3 10 7 12 1 1 1 7 1 11 12 1 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 11 12 1 1 10 12 1 1 10 12 10 12 1 1 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 11 12 1 11 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 3 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 100 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 100 1 10 10 12 1 10 12 1 1 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 11 7 1 11 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 9 12 1 1 11 7 12 1 1 1 10 10 12 1 10 12 1 1 10 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 1 10 10 12 1 1 10 12 10 10 12 1 10 12 10 10 12 10 10 12 1 10 12 1 10 12 10 10 12 10 12 1 10 12 10 12 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 7 12 1 1 1 10 12 1 1 10 10 7 12 1 1 1 11 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 100 1 100 1 8 1 10 10 10 12 1 8 1 10 12 1 3 3 7 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 10 12 10 12 1 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 12 1 1 10 10 12 1 8 1 10 12 1 1 18 12 1 1 11 100 12 1 1 1 7 1 3 18 12 1 18 12 1 8 1 10 100 12 1 1 1 11 12 1 1 10 12 10 10 12 1 10 11 12 1 1 10 12 1 1 11 12 1 18 3 11 10 12 1 11 11 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 11 100 12 1 100 1 100 1 10 12 1 100 1 10 10 100 12 1 1 1 100 1 10 100 1 10 10 12 1 10 10 12 1 8 1 10 10 12 1 8 1 8 1 10 12 1 10 12 1 10 10 12 10 100 12 1 1 10 100 12 1 1 10 100 12 1 1 8 1 10 12 1 10 12 1 1 10 10 12 8 1 8 1 10 8 1 8 1 8 1 8 1 10 12 1 10 12 1 8 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 10 10 12 10 12 7 1 9 12 1 1 7 1 10 100 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 15 10 12 15 10 12 15 10 12 1 1 1 1 100 1 100 1 1 1
staticfield java/lang/String COMPACT_STRINGS Z 1
staticfield java/lang/String serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/String CASE_INSENSITIVE_ORDER Ljava/util/Comparator; java/lang/String$CaseInsensitiveComparator
ciInstanceKlass java/security/ProtectionDomain 1 1 324 10 7 12 1 1 1 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 7 1 9 12 1 9 12 1 1 7 1 9 12 1 1 9 12 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 9 100 12 1 1 10 12 1 1 10 100 1 10 12 1 1 8 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 8 1 11 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 100 12 1 1 1 10 100 1 10 12 1 10 12 1 1 11 100 12 1 1 11 12 1 100 1 11 100 12 1 1 1 10 12 1 10 11 12 1 1 11 12 1 1 10 12 1 10 7 12 1 1 10 100 12 1 1 11 12 1 10 12 8 1 8 1 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1
staticfield java/security/ProtectionDomain filePermCompatInPD Z 0
ciInstanceKlass java/security/CodeSource 1 1 395 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 100 12 1 1 10 100 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 8 1 8 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 1 8 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 100 1 10 12 1 10 12 10 12 1 1 10 100 12 1 1 10 12 1 100 1 10 12 10 8 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 100 1 100 1 8 1 8 1 10 10 12 1 1 10 100 12 1 1 1 100 1 10 12 10 12 1 1 11 100 12 1 1 10 10 12 1 11 10 12 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 11 12 1 1 11 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringBuilder 1 1 409 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 100 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 100 1 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders 1 1 183 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 1 7 1 11 100 12 1 1 1 100 1 11 12 1 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 1 10 12 1 7 1 8 1 10 7 12 1 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 8 1 10 7 12 1 1 8 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/loader/ClassLoaders JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/loader/ClassLoaders BOOT_LOADER Ljdk/internal/loader/ClassLoaders$BootClassLoader; jdk/internal/loader/ClassLoaders$BootClassLoader
staticfield jdk/internal/loader/ClassLoaders PLATFORM_LOADER Ljdk/internal/loader/ClassLoaders$PlatformClassLoader; jdk/internal/loader/ClassLoaders$PlatformClassLoader
staticfield jdk/internal/loader/ClassLoaders APP_LOADER Ljdk/internal/loader/ClassLoaders$AppClassLoader; jdk/internal/loader/ClassLoaders$AppClassLoader
ciInstanceKlass jdk/internal/misc/Unsafe 1 1 1285 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 5 0 5 0 5 0 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 100 1 8 1 10 100 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 100 1 10 10 12 1 1 8 1 10 8 1 8 1 10 12 1 1 9 7 12 1 1 1 9 100 1 9 100 1 9 100 1 9 9 100 1 9 100 1 9 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 5 0 5 0 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 3 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 5 0 10 12 1 1 5 0 10 12 1 5 0 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 5 0 5 0 5 0 10 12 1 1 10 12 1 10 12 1 10 12 10 100 12 1 1 8 1 100 1 11 12 1 1 8 1 11 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 1 10 12 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 7 1 9 12 1 10 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/Unsafe theUnsafe Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_INT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_BASE_OFFSET I 16
staticfield jdk/internal/misc/Unsafe ARRAY_BOOLEAN_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_BYTE_INDEX_SCALE I 1
staticfield jdk/internal/misc/Unsafe ARRAY_SHORT_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_CHAR_INDEX_SCALE I 2
staticfield jdk/internal/misc/Unsafe ARRAY_INT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_LONG_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_FLOAT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ARRAY_DOUBLE_INDEX_SCALE I 8
staticfield jdk/internal/misc/Unsafe ARRAY_OBJECT_INDEX_SCALE I 4
staticfield jdk/internal/misc/Unsafe ADDRESS_SIZE I 8
ciInstanceKlass java/util/Map 1 1 259 11 7 12 1 1 1 11 12 1 1 10 100 12 1 1 11 12 1 1 11 7 12 1 1 1 11 100 12 1 1 1 11 12 1 1 7 1 11 12 1 11 12 1 100 1 100 1 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 11 12 1 10 12 1 1 11 12 1 11 100 12 1 9 7 12 1 1 1 100 1 10 12 7 1 7 1 10 12 1 7 1 10 7 1 11 12 1 1 7 1 11 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadGroup 1 1 293 10 7 12 1 1 1 9 7 12 1 1 1 8 1 9 12 1 1 7 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 9 12 1 9 12 1 1 10 7 12 1 1 1 100 10 12 1 1 10 7 12 1 1 1 10 100 12 1 9 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 100 1 10 10 12 1 10 12 1 10 12 1 7 10 12 1 9 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 100 1 100 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 1 8 1 10 8 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/security/Provider
ciInstanceKlass java/util/Properties 1 1 651 10 7 12 1 1 1 100 1 10 7 12 1 1 7 1 10 12 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 7 1 10 12 1 10 12 1 1 8 1 10 12 1 7 1 10 12 10 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 3 10 10 100 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 10 12 1 1 100 1 9 100 12 1 1 1 10 12 1 10 12 1 1 100 1 10 10 10 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 1 100 1 11 12 1 11 12 1 10 12 1 1 8 1 10 12 1 10 12 1 100 1 10 10 12 1 1 10 100 12 1 1 9 100 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 1 1 100 1 10 10 12 1 11 100 12 1 1 10 7 12 1 1 1 8 1 10 100 12 1 1 11 8 1 10 100 1 11 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 7 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 100 1 10 11 100 12 1 1 4 11 10 12 1 1 10 100 12 1 1 11 12 1 10 12 1 1 10 100 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 100 12 1 1 1 100 1 6 0 10 12 1 1 11 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1
staticfield java/util/Properties UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
instanceKlass java/util/Hashtable
ciInstanceKlass java/util/Dictionary 1 1 36 10 7 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/Properties
ciInstanceKlass java/util/Hashtable 1 1 512 100 1 10 7 12 1 1 1 9 7 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 8 1 10 12 1 9 12 1 1 7 1 9 12 1 1 4 10 7 12 1 1 1 9 12 1 4 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 100 1 10 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 10 12 1 3 9 12 1 9 12 1 3 10 12 1 10 12 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 100 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 9 12 1 1 10 100 1 100 1 10 12 1 10 8 1 10 10 12 1 8 1 10 8 1 10 100 12 1 1 1 100 1 10 12 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 1 10 10 12 1 1 11 12 1 1 11 12 1 100 1 10 10 10 100 12 1 1 11 100 12 1 1 1 100 1 10 11 100 12 1 1 11 100 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 8 1 10 4 10 12 4 10 12 1 8 1 10 12 10 100 12 1 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/zip/ZipFile$ZipFileInputStream
instanceKlass java/io/FilterInputStream
instanceKlass java/io/FileInputStream
instanceKlass java/io/ByteArrayInputStream
ciInstanceKlass java/io/InputStream 1 1 184 100 1 10 7 12 1 1 1 100 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 100 1 3 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 3 100 1 8 1 10 10 100 12 1 1 1 100 1 10 11 100 12 1 1 1 10 12 1 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 100 1 10 100 12 1 1 1 5 0 10 12 1 10 12 1 1 100 1 10 8 1 10 8 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/io/ByteArrayInputStream 1 1 96 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 10 100 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/misc/InnocuousThread
instanceKlass java/lang/ref/Finalizer$FinalizerThread
instanceKlass java/lang/ref/Reference$ReferenceHandler
ciInstanceKlass java/lang/Thread 1 1 612 9 7 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 1 3 8 1 100 1 5 0 10 12 1 1 10 7 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 100 1 8 1 10 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 10 7 12 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 9 12 1 10 12 1 1 9 12 1 100 1 10 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 10 10 12 1 1 10 12 1 10 12 1 100 1 11 7 12 1 1 9 100 12 1 1 1 10 12 1 10 12 1 10 12 9 12 1 1 10 9 12 1 10 12 1 100 1 10 10 12 1 1 9 12 1 10 12 1 11 100 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 10 12 1 10 12 1 10 12 1 9 100 12 1 1 1 10 12 1 1 10 12 1 100 1 8 1 10 10 12 1 10 12 8 1 10 12 1 8 1 10 8 1 8 1 10 100 12 1 1 10 100 12 1 1 1 100 1 8 1 10 9 12 1 9 12 1 1 10 12 1 1 10 10 12 1 1 9 12 1 10 12 1 1 100 1 10 12 11 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 10 12 1 1 100 1 10 12 1 11 100 12 1 1 1 100 1 10 12 1 10 12 1 1 11 12 1 10 12 1 100 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 9 12 1 10 12 1 1 11 100 12 1 1 1 10 100 12 1 1 1 11 12 1 10 12 1 7 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 1
staticfield java/lang/Thread EMPTY_STACK_TRACE [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
instanceKlass java/lang/Exception
instanceKlass java/lang/Error
ciInstanceKlass java/lang/Throwable 1 1 393 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 100 1 100 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 10 10 12 1 100 1 8 1 10 10 12 1 1 10 7 12 1 1 10 12 1 8 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 10 12 1 100 1 10 10 7 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 8 1 8 1 9 12 1 1 10 100 12 1 1 100 1 10 11 12 1 8 1 8 1 10 7 12 1 1 8 1 10 12 1 8 1 100 1 10 12 1 9 12 1 1 10 12 1 10 100 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 11 100 12 1 1 1 11 100 12 1 1 1 11 12 1 8 1 10 12 1 1 8 1 10 10 9 100 12 1 1 1 8 1 10 12 1 1 10 100 1 8 1 10 11 12 1 1 8 1 9 12 1 10 100 12 1 1 11 9 12 1 1 11 12 1 1 100 10 12 1 10 12 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Throwable UNASSIGNED_STACK [Ljava/lang/StackTraceElement; 0 [Ljava/lang/StackTraceElement;
staticfield java/lang/Throwable SUPPRESSED_SENTINEL Ljava/util/List; java/util/Collections$EmptyList
staticfield java/lang/Throwable EMPTY_THROWABLE_ARRAY [Ljava/lang/Throwable; 0 [Ljava/lang/Throwable;
staticfield java/lang/Throwable $assertionsDisabled Z 1
instanceKlass java/lang/InterruptedException
instanceKlass java/io/IOException
instanceKlass java/net/URISyntaxException
instanceKlass java/lang/ReflectiveOperationException
instanceKlass java/lang/RuntimeException
ciInstanceKlass java/lang/Exception 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/NoSuchFieldException
instanceKlass java/lang/NoSuchMethodException
instanceKlass java/lang/ClassNotFoundException
ciInstanceKlass java/lang/ReflectiveOperationException 1 1 34 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/AssertionError
instanceKlass java/lang/VirtualMachineError
instanceKlass java/lang/LinkageError
instanceKlass java/lang/ThreadDeath
ciInstanceKlass java/lang/Error 1 1 40 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ThreadDeath 0 0 21 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassNotFoundException 1 1 96 7 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ClassNotFoundException serialPersistentFields [Ljava/io/ObjectStreamField; 1 [Ljava/io/ObjectStreamField;
instanceKlass java/lang/IncompatibleClassChangeError
instanceKlass java/lang/BootstrapMethodError
instanceKlass java/lang/NoClassDefFoundError
ciInstanceKlass java/lang/LinkageError 1 1 31 10 7 12 1 1 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/Record 0 0 22 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StringLatin1 1 1 380 7 1 10 100 12 1 1 1 100 1 10 12 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 10 12 1 10 12 1 1 10 9 7 12 1 1 1 10 12 1 1 10 7 12 1 1 1 10 12 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 10 100 12 1 1 1 10 7 12 1 1 1 10 12 1 10 12 1 100 1 100 1 8 1 10 12 1 8 1 10 12 100 1 10 10 10 7 12 1 1 1 8 1 8 1 8 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
staticfield java/lang/StringLatin1 $assertionsDisabled Z 1
ciMethod java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 464 0 1709 0 -1
ciInstanceKlass java/lang/StringUTF16 1 1 598 100 1 7 1 10 100 12 1 1 1 100 1 10 7 1 3 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 9 12 1 1 9 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 10 12 10 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 8 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 10 100 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 5 0 5 0 10 12 1 10 12 10 12 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1
staticfield java/lang/StringUTF16 HI_BYTE_SHIFT I 0
staticfield java/lang/StringUTF16 LO_BYTE_SHIFT I 8
staticfield java/lang/StringUTF16 $assertionsDisabled Z 1
ciMethod java/lang/StringUTF16 newString ([BII)Ljava/lang/String; 0 0 1 0 -1
ciInstanceKlass java/lang/Boolean 1 1 151 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 8 1 10 7 12 1 1 9 12 1 1 9 12 1 8 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 9 100 12 1 1 9 12 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/Boolean TRUE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean FALSE Ljava/lang/Boolean; java/lang/Boolean
staticfield java/lang/Boolean TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/util/concurrent/locks/AbstractOwnableSynchronizer 0 0 32 10 100 12 1 1 1 9 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/math/BigInteger
instanceKlass java/util/concurrent/atomic/AtomicLong
instanceKlass java/util/concurrent/atomic/AtomicInteger
instanceKlass java/lang/Long
instanceKlass java/lang/Integer
instanceKlass java/lang/Short
instanceKlass java/lang/Byte
instanceKlass java/lang/Double
instanceKlass java/lang/Float
ciInstanceKlass java/lang/Number 1 1 37 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/LiveStackFrameInfo
ciInstanceKlass java/lang/StackFrameInfo 0 0 132 10 100 12 1 1 1 9 100 12 1 1 1 9 100 1 9 12 1 1 11 100 12 1 1 1 9 12 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 1 11 12 1 10 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/lang/LiveStackFrameInfo 0 0 97 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 8 1 8 1 10 100 1 10 12 1 100 1 10 12 1 100 1 100 1 1 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/Character 1 1 576 7 1 100 1 100 1 9 12 1 1 8 1 9 12 1 1 100 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 3 3 3 3 3 10 12 1 1 10 12 1 3 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 3 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 10 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 12 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 5 0 10 12 1 10 12 1 10 10 12 1 10 10 12 1 1 10 10 12 1 10 10 12 1 9 12 1 1 100 1 10 10 12 1 10 12 1 1 3 10 100 12 1 1 1 10 12 1 10 100 12 1 1 100 1 10 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 9 100 12 1 1 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 10 10 12 1 1 10 10 12 1 1 100 1 8 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 3 1 3 1 3 1 3 1 1 1 1 1 3 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 3 1 1 3 1 1 1 1 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1
staticfield java/lang/Character TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Character $assertionsDisabled Z 1
ciInstanceKlass java/lang/Float 1 1 223 7 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 4 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 4 4 4 10 7 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 3 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 4 1 1 1 4 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Float TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Double 1 1 285 7 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 100 1 10 12 1 1 10 100 12 1 1 1 6 0 8 1 10 12 1 1 8 1 10 12 1 1 8 1 6 0 10 12 1 1 100 1 5 0 5 0 8 1 8 1 10 100 12 1 1 1 10 100 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 6 0 6 0 6 0 10 7 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 5 0 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 1 6 0 1 1 1 6 0 1 1 3 1 3 1 3 1 3 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Double TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Byte 1 1 215 7 1 100 1 10 100 12 1 1 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 8 1 8 1 10 7 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Byte TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Short 1 1 224 7 1 100 1 100 1 10 100 12 1 1 1 10 12 1 1 100 1 100 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 1 8 1 9 12 1 1 100 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 10 12 1 1 10 8 1 8 1 10 100 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 3 3 5 0 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 1 1 3 1 3 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/Short TYPE Ljava/lang/Class; java/lang/Class
ciInstanceKlass java/lang/Integer 1 1 445 7 1 100 1 7 1 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 10 12 1 1 3 10 12 1 1 3 10 12 1 1 10 12 1 1 10 7 12 1 1 1 11 7 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 1 5 0 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 9 12 1 1 9 12 1 1 10 12 1 10 7 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 5 0 3 3 3 3 10 12 1 3 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 3 3 3 3 3 3 9 12 1 1 100 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Integer TYPE Ljava/lang/Class; java/lang/Class
staticfield java/lang/Integer digits [C 36
staticfield java/lang/Integer DigitTens [B 100
staticfield java/lang/Integer DigitOnes [B 100
staticfield java/lang/Integer sizeTable [I 10
ciMethod java/lang/Integer intValue ()I 256 0 128 0 -1
ciInstanceKlass java/lang/Long 1 1 506 7 1 100 1 7 1 100 1 10 12 1 1 9 12 1 1 9 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 10 12 10 12 1 10 12 1 10 12 1 5 0 5 0 100 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 12 1 5 0 5 0 9 12 1 1 9 12 1 5 0 100 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 10 12 1 1 5 0 10 12 1 1 5 0 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 1 100 1 10 11 10 12 1 1 8 1 10 12 1 1 8 1 100 1 10 12 1 1 10 12 1 8 1 8 1 11 12 1 1 10 12 1 10 12 1 10 12 1 5 0 5 0 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 100 1 9 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 12 1 1 10 12 1 1 5 0 10 12 1 10 12 1 5 0 5 0 5 0 10 12 1 1 5 0 5 0 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 3 1 3 1 5 0 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/lang/Long TYPE Ljava/lang/Class; java/lang/Class
instanceKlass java/lang/ref/PhantomReference
instanceKlass java/lang/ref/FinalReference
instanceKlass java/lang/ref/WeakReference
instanceKlass java/lang/ref/SoftReference
ciInstanceKlass java/lang/ref/Reference 1 1 195 9 7 12 1 1 1 9 7 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 100 1 10 12 1 10 7 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 100 1 100 1 10 12 1 9 12 1 9 12 1 100 1 10 10 12 1 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 7 1 10 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Reference processPendingLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Reference $assertionsDisabled Z 1
instanceKlass java/lang/invoke/LambdaFormEditor$Transform
ciInstanceKlass java/lang/ref/SoftReference 1 1 47 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass sun/nio/ch/FileLockTable$FileLockReference
instanceKlass java/lang/ClassValue$Entry
instanceKlass java/lang/invoke/MethodType$ConcurrentWeakInternSet$WeakEntry
instanceKlass java/lang/ThreadLocal$ThreadLocalMap$Entry
instanceKlass java/util/WeakHashMap$Entry
ciInstanceKlass java/lang/ref/WeakReference 1 1 31 10 7 12 1 1 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/ref/Finalizer
ciInstanceKlass java/lang/ref/FinalReference 1 1 47 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ref/Finalizer 1 1 152 9 7 12 1 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 12 1 100 1 11 100 12 1 1 100 1 10 12 1 100 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 10 12 1 10 7 12 1 1 1 7 1 10 7 1 10 10 7 12 1 1 1 10 12 1 1 10 7 12 1 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/ref/Finalizer lock Ljava/lang/Object; java/lang/Object
staticfield java/lang/ref/Finalizer $assertionsDisabled Z 1
instanceKlass jdk/internal/ref/PhantomCleanable
instanceKlass jdk/internal/ref/Cleaner
ciInstanceKlass java/lang/ref/PhantomReference 1 1 39 10 100 12 1 1 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/reflect/Executable
instanceKlass java/lang/reflect/Field
ciInstanceKlass java/lang/reflect/AccessibleObject 1 1 398 10 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 7 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 7 12 1 1 1 11 12 1 100 1 10 12 1 7 1 100 1 10 12 1 10 12 1 1 7 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 100 1 10 10 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 10 11 100 1 100 1 8 1 10 10 12 1 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 10 100 1 8 1 10 11 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 1 100 1 10 12 1 7 1 10 12 1 10 12 1 1 10 100 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 8 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 9 12 1 100 1 10 7 1 10 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 7 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/reflect/AccessibleObject reflectionFactory Ljdk/internal/reflect/ReflectionFactory; jdk/internal/reflect/ReflectionFactory
instanceKlass java/lang/reflect/Constructor
instanceKlass java/lang/reflect/Method
ciInstanceKlass java/lang/reflect/Executable 1 1 548 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 8 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 8 1 8 1 8 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 8 1 10 12 1 100 1 8 1 10 12 1 8 1 11 100 12 1 1 1 100 1 10 12 1 1 11 12 1 8 1 18 8 1 10 12 1 10 12 1 1 18 8 1 10 12 1 100 1 10 12 1 10 12 1 11 100 12 1 1 10 12 1 1 8 1 8 1 10 12 1 1 10 12 1 1 10 10 12 1 100 1 10 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 12 1 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 3 100 1 8 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 8 1 8 1 8 1 9 12 1 10 12 1 100 1 8 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 10 12 1 100 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 10 10 10 10 100 12 1 1 1 10 12 1 9 12 1 10 12 1 1 9 12 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 15 10 100 12 1 1 1 16 15 16 1 16 1 15 10 12 16 1 100 1 1 100 1 100 1 1
ciInstanceKlass java/lang/reflect/Constructor 1 1 433 10 100 12 1 1 1 10 100 12 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 7 1 100 1 8 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 11 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 100 12 1 1 10 12 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Method 1 1 450 9 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 7 12 1 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 10 100 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 100 1 10 100 12 1 1 1 10 7 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 8 1 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 11 100 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 100 12 1 1 1 100 1 100 1 100 1 10 12 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/reflect/Field 0 0 437 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 9 12 1 1 9 12 1 10 12 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 1 10 100 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 1 8 1 10 12 10 12 1 8 1 8 1 10 11 100 1 9 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 1 10 12 9 12 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 100 1 10 12 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/lang/reflect/Parameter 0 0 226 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 10 100 12 1 1 1 10 12 1 10 12 10 12 1 8 1 10 12 1 9 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 10 100 12 1 1 100 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 9 12 1 100 1 10 11 12 1 1 11 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciInstanceKlass java/lang/StringBuffer 0 0 470 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 1 10 10 100 12 1 1 1 10 10 12 1 10 8 10 100 12 1 1 1 8 10 12 1 8 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 12 100 1 8 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 10 12 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 9 100 12 1 1 1 9 100 1 9 12 1 1 100 1 100 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StringBuilder
instanceKlass java/lang/StringBuffer
ciInstanceKlass java/lang/AbstractStringBuilder 1 1 547 7 1 7 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 3 3 10 12 1 10 12 1 1 11 7 1 100 1 100 1 10 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 10 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 1 100 1 10 12 10 12 1 1 10 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 10 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 10 12 1 1 18 12 1 1 100 1 10 100 12 1 1 1 18 10 12 1 1 10 12 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 10 12 1 10 8 1 8 1 8 1 10 10 100 1 10 12 1 100 1 10 100 1 10 100 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 1 1 1 100 1 1 1 1 1 1 15 10 100 12 1 1 1 16 1 15 10 12 16 15 10 12 1 1 1 1 100 1 100 1 1
staticfield java/lang/AbstractStringBuilder EMPTYVALUE [B 0
ciMethod java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 486 0 2261 0 -1
ciMethod java/lang/AbstractStringBuilder <init> (I)V 224 0 792 0 -1
ciMethod java/lang/AbstractStringBuilder isLatin1 ()Z 956 0 4337 0 -1
ciInstanceKlass java/lang/SecurityManager 0 0 576 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 100 1 10 100 1 10 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 100 1 8 1 10 9 12 1 1 9 12 1 8 1 9 12 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 10 10 12 1 1 100 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 1 8 1 100 1 8 1 10 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 8 1 100 1 8 1 8 1 10 8 1 10 12 1 100 1 8 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 10 100 12 1 1 11 100 12 1 1 1 18 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 18 11 12 1 18 12 1 11 12 1 1 9 12 1 1 9 12 1 9 12 1 9 12 1 100 1 10 100 12 1 1 10 12 1 10 12 1 18 12 1 18 10 100 12 1 1 1 18 12 1 10 12 1 18 18 8 1 10 12 1 9 12 1 1 11 100 12 1 1 1 8 1 100 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 8 1 100 1 10 9 12 1 8 1 10 12 1 8 1 100 1 10 10 100 12 1 1 10 100 1 9 100 12 1 1 1 11 12 1 1 10 12 1 11 12 1 10 12 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 100 12 1 1 1 16 1 16 15 10 12 16 1 15 10 12 16 15 11 100 1 16 1 16 1 15 10 12 16 15 10 12 16 15 10 12 1 16 1 15 11 12 1 15 10 12 16 15 10 16 1 1 1 1 100 1 100 1 1
instanceKlass org/gradle/internal/service/ServiceLookupException
instanceKlass org/gradle/api/GradleException
instanceKlass org/gradle/api/UncheckedIOException
instanceKlass org/gradle/api/internal/classpath/UnknownModuleException
instanceKlass java/lang/IndexOutOfBoundsException
instanceKlass org/gradle/cli/CommandLineArgumentException
instanceKlass java/lang/IllegalStateException
instanceKlass java/lang/IllegalArgumentException
instanceKlass java/lang/ArithmeticException
instanceKlass java/lang/NullPointerException
instanceKlass java/lang/IllegalMonitorStateException
instanceKlass java/lang/ArrayStoreException
instanceKlass java/lang/ClassCastException
ciInstanceKlass java/lang/RuntimeException 1 1 40 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ArithmeticException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NullPointerException 1 1 52 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 1 9 12 1 1 10 12 1 1 10 12 1 1 1 1 5 0 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1
instanceKlass java/util/TreeMap
instanceKlass java/util/IdentityHashMap
instanceKlass java/util/WeakHashMap
instanceKlass java/util/Collections$EmptyMap
instanceKlass sun/util/PreHashedMap
instanceKlass java/util/HashMap
instanceKlass java/util/ImmutableCollections$AbstractImmutableMap
instanceKlass java/util/concurrent/ConcurrentHashMap
ciInstanceKlass java/util/AbstractMap 1 1 192 10 7 12 1 1 1 10 7 12 1 1 1 11 7 12 1 1 1 10 11 12 1 1 11 7 12 1 1 1 11 12 1 1 7 1 11 12 1 10 12 1 1 11 12 1 100 1 10 11 12 1 11 7 1 10 12 1 1 11 12 1 9 12 1 1 7 1 10 12 1 9 12 1 1 100 1 10 11 11 12 1 1 11 12 1 100 1 100 1 11 12 1 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 100 1 1 1 1 1
ciInstanceKlass java/security/AccessControlContext 1 1 373 9 7 12 1 1 1 9 12 1 1 10 100 12 1 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 100 1 10 12 1 11 100 12 1 1 1 11 12 1 11 12 1 11 12 1 1 100 1 11 12 1 1 10 12 1 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 1 10 100 1 100 1 8 1 10 12 1 10 12 1 1 100 1 10 100 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 10 7 12 1 1 1 9 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 10 12 1 1 10 100 12 1 1 1 10 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 100 1 10 12 1 10 12 1 1 100 1 10 12 1 8 1 10 12 1 10 12 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 10 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 3 1 1
ciInstanceKlass java/net/URL 1 1 743 10 7 12 1 1 1 10 12 1 10 7 12 1 1 9 12 1 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 8 1 10 12 1 1 100 1 10 10 12 1 1 8 1 10 12 1 1 9 12 1 100 1 8 1 10 12 1 10 12 1 8 1 9 12 1 10 12 1 1 9 12 1 10 12 1 10 12 1 9 12 1 9 12 1 8 1 9 12 1 10 12 1 1 8 1 9 12 1 1 10 12 1 1 10 7 12 1 1 1 8 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 8 1 10 12 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 1 100 1 8 1 10 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 8 1 10 12 1 10 10 7 12 1 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 100 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 10 10 12 1 100 1 10 12 1 10 12 1 1 8 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 9 12 1 1 100 1 8 1 10 10 12 1 9 12 1 1 10 7 12 1 1 8 1 10 7 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 10 7 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 100 1 10 9 12 1 1 10 7 12 1 1 8 1 10 12 1 1 100 1 10 10 100 12 1 1 1 8 9 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 11 7 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 8 10 100 12 1 1 100 1 10 8 8 10 12 1 8 8 8 100 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 100 1 8 1 10 10 10 12 1 1 10 12 1 10 12 1 1 8 1 7 1 10 10 10 7 1 10 12 1 9 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 100 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/net/URL defaultFactory Ljava/net/URLStreamHandlerFactory; java/net/URL$DefaultFactory
staticfield java/net/URL streamHandlerLock Ljava/lang/Object; java/lang/Object
staticfield java/net/URL serialPersistentFields [Ljava/io/ObjectStreamField; 7 [Ljava/io/ObjectStreamField;
ciInstanceKlass java/util/jar/Manifest 1 1 336 10 7 12 1 1 1 7 1 10 9 7 12 1 1 1 7 1 10 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 11 7 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 11 100 1 10 12 1 10 12 1 1 11 12 1 1 10 12 1 11 12 1 1 11 100 12 1 1 1 11 100 12 1 1 11 12 1 1 100 1 10 12 1 8 1 11 12 1 100 1 10 12 1 1 11 12 1 10 12 1 10 12 1 10 100 12 1 1 1 8 1 10 12 1 1 10 9 100 12 1 1 1 10 12 1 1 10 100 12 1 10 12 1 10 12 1 9 100 12 1 1 1 8 1 10 12 1 8 1 8 1 7 1 10 12 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 1 8 1 10 10 12 1 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 11 12 1 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 10 12 1 11 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/HashMap$Values
instanceKlass java/util/ArrayDeque
instanceKlass java/util/AbstractSet
instanceKlass java/util/ImmutableCollections$AbstractImmutableCollection
instanceKlass java/util/AbstractList
ciInstanceKlass java/util/AbstractCollection 1 1 160 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 1 11 7 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 10 7 12 1 1 1 7 1 10 100 12 1 1 1 10 100 12 1 1 1 100 1 10 11 12 1 11 7 1 10 12 1 10 12 1 10 100 12 1 1 1 11 8 1 100 1 10 10 12 1 1 8 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/util/ArrayList$SubList
instanceKlass org/gradle/internal/classpath/DefaultClassPath$ImmutableUniqueList
instanceKlass java/util/Vector
instanceKlass sun/security/jca/ProviderList$3
instanceKlass java/util/Arrays$ArrayList
instanceKlass java/util/Collections$EmptyList
instanceKlass java/util/ArrayList
ciInstanceKlass java/util/AbstractList 1 1 218 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 10 12 1 1 11 100 12 1 1 1 11 12 1 1 11 12 1 10 100 12 1 1 1 10 12 1 11 12 1 11 12 1 11 12 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 11 100 1 11 7 1 10 12 1 100 1 10 12 1 10 12 1 1 100 1 100 1 10 12 1 100 1 10 100 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 100 1 8 1 8 1 8 1 10 100 1 11 10 10 12 1 11 12 1 10 12 1 1 8 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1
ciInstanceKlass java/lang/AssertionStatusDirectives 0 0 24 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives$CallSiteContext 1 1 49 10 7 12 1 1 1 7 1 10 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass jdk/internal/invoke/NativeEntryPoint 0 0 92 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 100 1 9 12 1 9 12 1 9 12 1 1 9 12 1 1 9 12 1 1 100 1 8 1 10 12 1 11 100 12 1 1 1 10 12 1 1 10 12 1 11 100 12 1 1 11 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/invoke/VolatileCallSite
instanceKlass java/lang/invoke/MutableCallSite
instanceKlass java/lang/invoke/ConstantCallSite
ciInstanceKlass java/lang/invoke/CallSite 1 1 302 10 7 12 1 1 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 10 12 1 1 9 12 1 9 100 12 1 1 1 8 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 1 9 12 1 8 1 100 1 10 12 1 10 12 1 100 1 8 1 10 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 8 10 12 1 1 9 12 1 1 100 1 10 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 100 1 8 1 10 10 12 10 12 1 1 100 1 100 1 100 1 8 1 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/CallSite $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/VolatileCallSite 0 0 37 10 100 12 1 1 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/invoke/MethodType 1 1 771 7 1 10 7 12 1 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 9 7 12 1 1 8 1 10 100 12 1 1 1 9 7 1 9 7 1 10 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 1 100 1 8 1 10 12 1 100 1 10 10 12 1 10 12 1 1 10 12 1 1 11 7 12 1 1 1 9 12 1 11 12 1 1 7 7 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 9 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 10 10 12 1 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 100 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 11 12 1 1 11 12 1 10 100 12 1 1 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 9 12 1 1 7 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 8 1 10 7 12 1 1 1 11 12 1 1 9 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 100 1 10 12 1 1 11 100 12 1 1 1 18 12 1 1 11 12 1 1 18 12 1 11 12 1 100 1 11 100 12 1 1 10 12 1 100 1 10 12 1 10 100 12 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 10 100 12 1 1 10 12 1 100 10 12 1 1 10 12 1 10 7 1 7 1 9 12 1 1 100 1 100 1 100 1 1 1 5 0 1 1 1 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 16 16 15 10 12 16 1 1 1 1 100 1 1 100 1 1 100 1 100 1 1
staticfield java/lang/invoke/MethodType internTable Ljava/lang/invoke/MethodType$ConcurrentWeakInternSet; java/lang/invoke/MethodType$ConcurrentWeakInternSet
staticfield java/lang/invoke/MethodType NO_PTYPES [Ljava/lang/Class; 0 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType objectOnlyTypes [Ljava/lang/invoke/MethodType; 20 [Ljava/lang/invoke/MethodType;
staticfield java/lang/invoke/MethodType METHOD_HANDLE_ARRAY [Ljava/lang/Class; 1 [Ljava/lang/Class;
staticfield java/lang/invoke/MethodType serialPersistentFields [Ljava/io/ObjectStreamField; 0 [Ljava/io/ObjectStreamField;
staticfield java/lang/invoke/MethodType $assertionsDisabled Z 1
ciInstanceKlass java/lang/BootstrapMethodError 0 0 45 10 100 12 1 1 1 10 12 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$AppClassLoader 1 1 119 8 1 10 7 12 1 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 7 1 8 1 10 12 10 7 12 1 1 1 10 100 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
ciInstanceKlass jdk/internal/loader/ClassLoaders$PlatformClassLoader 1 1 42 8 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1
ciInstanceKlass java/lang/ArrayStoreException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/ClassCastException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/NoClassDefFoundError 0 0 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/IllegalMonitorStateException 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackOverflowError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
instanceKlass java/lang/StackOverflowError
instanceKlass java/lang/OutOfMemoryError
instanceKlass java/lang/InternalError
ciInstanceKlass java/lang/VirtualMachineError 1 1 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/OutOfMemoryError 1 1 26 10 100 12 1 1 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/InternalError 0 0 34 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 100 1 1 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackTraceElement 0 0 224 10 100 12 1 1 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 12 1 8 1 10 100 12 1 1 1 100 1 9 12 1 8 1 9 12 1 9 12 1 9 12 1 1 8 1 10 12 1 1 10 12 1 100 1 10 10 12 1 1 8 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 10 10 12 1 1 10 12 1 10 12 1 1 100 1 1 1 1 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
instanceKlass java/nio/LongBuffer
instanceKlass java/nio/ByteBuffer
ciInstanceKlass java/nio/Buffer 1 1 224 100 1 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 8 1 10 12 1 1 10 12 1 8 1 9 12 1 1 100 1 8 1 10 12 1 8 1 8 1 9 12 10 12 1 8 1 8 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 100 1 10 100 1 10 100 1 10 10 100 12 1 1 1 10 11 100 12 1 1 10 7 12 1 1 1 10 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 7 1 10 10 7 12 1 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/nio/Buffer UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield java/nio/Buffer SCOPED_MEMORY_ACCESS Ljdk/internal/misc/ScopedMemoryAccess; jdk/internal/misc/ScopedMemoryAccess
staticfield java/nio/Buffer $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/misc/UnsafeConstants 1 1 34 10 100 12 1 1 1 9 7 12 1 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 1 1 1 1 1 1 1
staticfield jdk/internal/misc/UnsafeConstants ADDRESS_SIZE0 I 8
staticfield jdk/internal/misc/UnsafeConstants PAGE_SIZE I 4096
staticfield jdk/internal/misc/UnsafeConstants BIG_ENDIAN Z 0
staticfield jdk/internal/misc/UnsafeConstants UNALIGNED_ACCESS Z 1
staticfield jdk/internal/misc/UnsafeConstants DATA_CACHE_LINE_FLUSH_SIZE I 0
instanceKlass java/lang/invoke/DelegatingMethodHandle
instanceKlass java/lang/invoke/BoundMethodHandle
instanceKlass java/lang/invoke/DirectMethodHandle
ciInstanceKlass java/lang/invoke/MethodHandle 1 1 644 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 1 7 1 7 1 10 12 1 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 10 12 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 10 7 12 1 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 8 1 10 100 12 1 1 1 9 12 1 1 100 1 10 9 100 12 1 1 1 9 100 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 11 12 1 10 12 1 10 12 1 1 10 100 12 1 1 1 100 1 11 12 1 10 100 1 11 12 1 100 1 10 12 1 11 12 1 9 100 12 1 1 1 11 12 1 1 11 100 12 1 1 1 10 12 1 1 9 12 1 11 12 1 9 12 1 9 12 1 9 12 1 11 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 10 10 7 12 1 1 10 12 1 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 100 1 10 100 12 1 1 1 10 12 1 9 7 12 1 1 1 10 12 1 1 10 12 1 1 8 1 9 12 1 9 12 1 1 9 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 11 100 12 1 1 9 12 1 10 12 1 1 10 12 1 9 12 1 10 12 1 8 10 12 1 1 8 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 100 1 1 100 1 1 1 1
staticfield java/lang/invoke/MethodHandle FORM_OFFSET J 20
staticfield java/lang/invoke/MethodHandle UPDATE_OFFSET J 13
staticfield java/lang/invoke/MethodHandle $assertionsDisabled Z 1
instanceKlass java/lang/invoke/DirectMethodHandle$Special
instanceKlass java/lang/invoke/DirectMethodHandle$Accessor
instanceKlass java/lang/invoke/DirectMethodHandle$Interface
instanceKlass java/lang/invoke/DirectMethodHandle$Constructor
ciInstanceKlass java/lang/invoke/DirectMethodHandle 1 1 940 7 1 7 1 100 1 7 1 7 1 10 7 12 1 1 1 10 7 12 1 1 1 100 1 10 12 1 10 12 1 1 10 7 12 1 1 10 12 1 1 10 12 1 10 12 1 7 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 12 1 1 100 1 10 9 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 1 1 8 1 10 12 1 1 7 1 10 12 1 7 1 10 10 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 10 12 1 10 12 1 7 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 7 12 1 1 1 7 1 10 12 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 9 7 12 1 1 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 9 12 1 9 12 1 8 1 10 12 1 10 12 1 1 9 12 1 1 7 1 10 12 1 1 100 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 12 1 10 12 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 9 12 1 1 10 7 1 9 12 9 12 1 10 7 12 1 1 1 10 12 1 7 1 7 1 7 1 9 12 1 1 10 7 12 1 10 12 1 1 10 12 1 100 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 100 1 10 100 12 1 1 1 10 12 1 10 12 1 8 1 9 12 1 9 12 1 10 12 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 1 10 12 1 1 9 7 12 1 1 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 9 12 1 8 1 9 12 1 1 9 12 1 1 10 12 1 10 12 1 1 9 7 1 10 12 1 9 12 1 1 10 12 10 12 1 10 12 1 10 12 1 10 8 1 8 1 8 1 8 1 10 12 1 1 9 12 1 1 10 12 1 10 100 12 1 1 1 8 9 12 1 1 10 12 1 1 8 1 8 8 9 12 1 8 1 8 8 8 8 8 1 8 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/DirectMethodHandle IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/DirectMethodHandle FT_UNCHECKED_REF I 8
staticfield java/lang/invoke/DirectMethodHandle ACCESSOR_FORMS [Ljava/lang/invoke/LambdaForm; 132 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/DirectMethodHandle ALL_WRAPPERS [Lsun/invoke/util/Wrapper; 10 [Lsun/invoke/util/Wrapper;
staticfield java/lang/invoke/DirectMethodHandle NFS [Ljava/lang/invoke/LambdaForm$NamedFunction; 12 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/DirectMethodHandle OBJ_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle LONG_OBJ_TYPE Ljava/lang/invoke/MethodType; java/lang/invoke/MethodType
staticfield java/lang/invoke/DirectMethodHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/LambdaForm 1 1 1052 100 1 9 7 12 1 1 1 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 100 1 10 9 12 1 10 12 1 1 9 12 1 10 7 12 1 1 1 9 12 1 9 12 1 9 12 1 1 9 12 1 10 12 1 1 7 1 10 12 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 10 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 1 7 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 1 9 12 1 1 9 12 1 9 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 10 12 1 1 10 12 1 9 12 1 1 10 100 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 9 12 1 9 12 1 9 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 9 12 1 7 1 10 12 1 1 9 12 1 10 12 1 10 12 1 1 10 12 10 12 1 10 12 1 1 10 12 1 1 10 10 12 1 1 10 12 1 1 7 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 10 12 1 1 8 1 8 1 8 1 10 12 1 9 12 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 9 12 1 1 8 1 10 100 12 1 1 1 10 7 12 1 1 10 12 10 10 12 1 1 9 12 1 8 10 12 1 1 100 1 10 12 1 1 10 12 1 9 7 12 1 1 9 12 1 1 8 1 10 100 12 1 1 10 12 1 1 100 1 100 1 10 10 12 1 1 10 12 1 1 8 1 8 1 100 1 8 1 10 12 10 12 1 10 12 1 10 12 1 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 100 1 8 1 100 1 8 1 100 1 8 1 10 12 1 8 1 9 10 7 12 1 1 1 10 12 1 9 12 1 1 10 12 1 10 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 8 1 100 10 12 1 10 12 1 9 12 1 1 10 7 12 1 1 8 1 8 1 100 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 9 12 1 1 8 1 10 12 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 9 12 1 1 7 1 10 7 12 1 1 1 9 12 1 10 12 1 10 12 1 8 1 10 12 1 9 12 1 1 7 1 10 7 12 1 1 1 8 1 100 1 10 12 1 9 12 1 9 12 1 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 9 7 12 1 1 10 12 1 1 10 12 1 10 12 1 9 12 10 12 1 10 10 12 1 9 9 12 1 7 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 7 1 9 1 1 1 1 3 1 3 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 100 1 1 100 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/LambdaForm COMPILE_THRESHOLD I 0
staticfield java/lang/invoke/LambdaForm INTERNED_ARGUMENTS [[Ljava/lang/invoke/LambdaForm$Name; 5 [[Ljava/lang/invoke/LambdaForm$Name;
staticfield java/lang/invoke/LambdaForm IMPL_NAMES Ljava/lang/invoke/MemberName$Factory; java/lang/invoke/MemberName$Factory
staticfield java/lang/invoke/LambdaForm LF_identity [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm LF_zero [Ljava/lang/invoke/LambdaForm; 6 [Ljava/lang/invoke/LambdaForm;
staticfield java/lang/invoke/LambdaForm NF_identity [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm NF_zero [Ljava/lang/invoke/LambdaForm$NamedFunction; 6 [Ljava/lang/invoke/LambdaForm$NamedFunction;
staticfield java/lang/invoke/LambdaForm createFormsLock Ljava/lang/Object; java/lang/Object
staticfield java/lang/invoke/LambdaForm DEBUG_NAME_COUNTERS Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm DEBUG_NAMES Ljava/util/HashMap; null
staticfield java/lang/invoke/LambdaForm TRACE_INTERPRETER Z 0
staticfield java/lang/invoke/LambdaForm $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MethodHandleNatives 1 1 684 100 1 10 7 12 1 1 1 9 7 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 7 1 10 7 12 1 1 1 10 100 12 1 1 1 7 1 10 10 12 1 1 8 1 10 12 1 8 1 10 12 1 1 8 1 10 12 1 1 9 100 12 1 1 1 8 1 10 100 12 1 1 1 100 1 10 12 100 1 100 1 8 1 7 1 10 10 12 1 7 1 9 7 12 1 1 10 12 1 1 10 12 1 10 7 12 1 1 1 7 1 10 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 1 9 12 1 8 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 100 12 1 1 1 100 1 8 1 10 100 12 1 1 1 7 1 8 1 10 12 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 1 100 1 100 1 10 12 1 10 12 1 8 1 8 1 10 10 12 1 1 10 12 1 1 8 1 10 100 12 1 1 1 8 1 8 1 10 12 1 1 10 7 12 1 1 1 100 1 10 12 1 1 7 1 9 12 1 1 10 7 12 1 1 1 10 10 12 1 9 12 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 10 12 1 10 12 1 1 7 1 7 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 7 12 1 1 1 10 12 1 1 100 1 8 1 10 9 7 12 1 1 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 1 1 100 1 100 1 10 10 100 1 100 1 10 100 1 10 10 12 1 1 10 100 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 8 1 100 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 11 7 12 1 1 1 10 12 1 10 12 1 10 10 12 1 10 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/MethodHandleNatives JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield java/lang/invoke/MethodHandleNatives $assertionsDisabled Z 1
ciInstanceKlass jdk/internal/reflect/CallerSensitive 0 0 17 100 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/ConstantPool 0 0 142 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl 0 0 47 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 8 11 100 12 1 1 1 10 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/FieldAccessorImpl 0 0 59 10 100 12 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
instanceKlass jdk/internal/reflect/UnsafeStaticFieldAccessorImpl
ciInstanceKlass jdk/internal/reflect/UnsafeFieldAccessorImpl 0 0 254 10 100 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 10 12 1 9 12 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 100 1 10 10 12 1 1 10 12 1 1 8 1 10 10 12 1 100 1 8 1 10 12 1 8 1 10 12 1 8 1 10 12 1 100 1 10 12 1 1 10 8 1 10 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 8 1 10 100 12 1 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 10 100 12 1 1 1 8 1 8 1 8 1 10 12 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/reflect/NativeConstructorAccessorImpl 1 1 126 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 9 12 1 1 9 12 1 1 10 7 12 1 1 1 100 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 100 1 10 12 1 1 10 12 1 1 8 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl U Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
staticfield jdk/internal/reflect/NativeConstructorAccessorImpl GENERATED_OFFSET J 16
ciInstanceKlass java/lang/invoke/ConstantCallSite 1 1 65 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 100 1 10 12 9 12 1 1 100 1 10 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/ConstantCallSite UNSAFE Ljdk/internal/misc/Unsafe; jdk/internal/misc/Unsafe
ciInstanceKlass java/lang/invoke/MutableCallSite 0 0 63 10 100 12 1 1 1 10 12 1 9 100 12 1 1 1 10 12 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1
instanceKlass java/lang/invoke/VarHandleByteArrayAsLongs$ByteArrayViewVarHandle
instanceKlass java/lang/invoke/VarHandleByteArrayAsInts$ByteArrayViewVarHandle
ciInstanceKlass java/lang/invoke/VarHandle 1 1 390 10 7 12 1 1 1 10 7 12 1 1 9 12 1 1 9 12 1 1 100 1 10 8 1 10 12 1 1 10 7 12 1 1 1 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 10 12 1 9 12 1 1 10 7 12 1 1 10 12 1 9 7 12 1 1 1 9 12 1 1 10 12 1 1 100 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 10 12 1 1 9 7 12 1 1 1 10 12 1 10 12 1 1 10 12 1 10 10 100 12 1 1 1 10 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 1 9 12 1 1 10 9 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 10 12 1 10 12 1 10 100 12 1 1 100 1 10 9 7 12 1 1 1 9 12 1 1 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 7 1 10 10 7 12 1 1 1 9 12 1 1 8 10 12 1 1 7 1 10 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 1 1 1 1 1 100 1 1 1
staticfield java/lang/invoke/VarHandle AIOOBE_SUPPLIER Ljava/util/function/BiFunction; jdk/internal/util/Preconditions$1
staticfield java/lang/invoke/VarHandle VFORM_OFFSET J 16
staticfield java/lang/invoke/VarHandle $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/MemberName 1 1 757 7 1 7 1 100 1 9 12 1 1 10 7 12 1 1 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 1 10 7 12 1 1 1 10 12 1 9 100 12 1 1 10 12 1 100 1 100 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 9 12 1 1 8 1 10 100 12 1 1 1 7 1 10 10 12 1 1 100 1 100 1 10 12 1 1 9 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 8 1 8 1 8 1 10 100 12 1 1 1 10 12 1 9 12 1 1 3 10 12 1 10 12 1 10 12 1 10 10 7 12 1 1 1 10 12 1 10 12 1 10 12 1 10 12 1 7 1 8 10 12 1 1 10 12 1 1 8 1 9 100 1 8 9 100 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 100 1 10 12 1 1 10 12 1 8 1 10 12 1 10 12 1 8 1 10 12 1 1 10 12 1 1 100 1 10 12 1 1 10 12 8 1 8 1 100 1 10 12 1 10 100 12 1 1 1 100 1 10 12 10 12 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 10 12 1 3 10 12 1 3 10 12 1 3 3 3 3 3 3 3 100 1 10 12 1 10 7 12 1 1 1 10 12 1 3 9 12 1 10 12 1 1 3 10 12 1 10 10 7 12 1 1 1 10 12 1 1 10 100 1 10 10 12 1 10 12 1 10 12 1 10 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 10 10 12 100 1 10 10 10 12 1 1 10 12 1 1 10 10 12 1 8 10 100 1 10 12 1 10 100 1 10 12 1 10 12 1 10 12 1 10 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 1 100 1 8 1 10 7 1 10 12 1 10 12 10 12 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 10 12 1 8 1 10 10 12 1 10 12 1 8 1 8 1 10 10 12 1 8 1 10 100 12 1 1 1 8 1 10 12 1 10 12 1 1 10 12 1 8 1 8 1 8 1 8 1 100 1 10 8 1 8 1 8 1 8 1 10 12 1 100 1 100 1 100 1 10 100 1 10 100 1 10 100 12 1 1 1 9 7 12 1 1 1 100 1 100 1 1 1 1 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 1 1 1 1 1 1 1 3 1 3 1 1 1 1 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
staticfield java/lang/invoke/MemberName $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/ResolvedMethodName 1 1 16 10 100 12 1 1 1 100 1 1 1 1 1 1 1 1
ciInstanceKlass java/lang/StackWalker 0 0 235 9 100 12 1 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 1 100 1 8 1 10 12 1 10 12 1 10 12 1 10 100 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 11 12 1 1 100 1 8 1 10 10 100 12 1 1 9 12 1 1 10 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 100 1 8 1 10 10 12 1 1 10 100 12 1 1 1 9 100 12 1 1 11 100 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 15 10 100 12 1 1 1 16 15 10 12 16 1 1 1 1 1 1 1 100 1 100 1 1
ciInstanceKlass java/lang/StackStreamFactory$AbstractStackWalker 1 0 306 100 1 100 1 3 10 100 12 1 1 1 10 100 12 1 1 10 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 12 1 1 9 12 1 1 9 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 10 100 12 1 1 1 10 12 1 1 9 12 1 1 9 100 12 1 1 1 100 1 10 8 1 10 12 1 1 10 12 1 8 1 10 12 1 1 10 100 12 1 1 1 100 1 8 1 10 12 1 8 1 10 12 9 100 12 1 1 1 10 100 12 1 1 9 12 1 8 1 5 0 8 1 8 1 9 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 100 12 1 1 1 9 12 1 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 8 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass jdk/internal/module/Modules 1 1 504 10 100 12 1 1 1 9 7 12 1 1 1 11 7 12 1 1 1 11 12 1 1 11 12 1 1 11 12 1 1 11 12 1 11 12 1 11 12 1 11 12 1 11 12 1 1 10 100 12 1 1 1 10 100 12 1 1 1 18 12 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 100 12 1 1 10 12 1 1 11 12 1 9 12 1 1 11 100 12 1 1 1 10 12 1 1 10 10 12 1 10 9 12 1 1 10 100 12 1 1 10 12 1 1 10 100 12 1 1 100 1 11 100 12 1 1 1 10 100 12 1 1 1 11 100 12 1 1 10 12 1 1 10 100 12 1 1 10 100 12 1 1 1 10 12 1 1 10 12 1 1 11 12 1 1 18 12 1 1 11 100 12 1 1 10 100 12 1 1 1 11 100 12 1 1 1 100 1 11 12 1 1 11 100 12 1 1 1 11 12 1 1 10 12 1 1 10 100 12 1 1 18 12 1 1 11 12 1 1 18 12 1 1 11 12 1 1 10 12 1 18 18 10 12 1 1 9 12 1 1 11 100 12 1 1 1 100 1 10 11 12 1 11 12 1 1 11 12 1 1 10 100 1 10 12 1 1 10 100 12 1 1 10 12 1 1 11 12 10 12 1 1 100 1 10 18 12 1 10 12 1 1 100 1 8 1 10 12 1 10 100 12 1 1 18 12 1 11 11 12 10 12 1 10 10 100 1 18 12 1 10 10 10 7 12 1 1 10 7 12 1 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 100 12 1 1 1 16 15 10 12 1 16 16 15 10 12 1 16 1 16 1 15 10 12 1 16 1 16 1 15 10 12 16 1 15 10 16 1 15 10 12 16 1 15 10 12 16 15 10 12 16 15 10 12 1 1 1 100 1 100 1 1
staticfield jdk/internal/module/Modules JLA Ljdk/internal/access/JavaLangAccess; java/lang/System$2
staticfield jdk/internal/module/Modules JLMA Ljdk/internal/access/JavaLangModuleAccess; java/lang/module/ModuleDescriptor$1
staticfield jdk/internal/module/Modules $assertionsDisabled Z 1
ciInstanceKlass java/lang/invoke/Invokers$Holder 1 1 99 1 100 1 100 1 1 1 1 1 1 1 7 1 7 1 7 1 1 12 10 1 1 12 10 1 1 12 10 1 1 100 1 1 12 9 1 1 1 12 10 1 1 1 12 10 1 1 12 10 1 1 1 12 10 1 1 100 1 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 1 12 10 1 1 12 10 1 12 10 1 12 10 12 10 1 1 12 10 1 12 10 1 1 1
ciMethod java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;)Ljava/lang/Object; 720 0 360 0 -1
ciInstanceKlass java/util/regex/Pattern 1 1 1512 7 1 10 12 1 1 9 12 1 1 9 12 1 1 10 12 1 1 7 1 10 12 1 9 12 1 1 10 12 1 10 12 1 1 10 12 1 1 7 1 10 12 10 12 1 10 12 1 1 10 12 1 10 12 1 11 100 12 1 1 1 11 12 1 1 10 12 1 1 11 12 1 7 1 10 12 1 1 10 12 1 10 12 1 1 11 7 12 1 1 1 100 1 10 12 1 1 8 1 10 12 1 1 7 1 10 8 1 10 12 1 1 10 10 100 1 3 10 12 1 10 12 1 8 1 10 12 1 10 100 12 1 1 9 12 1 9 12 1 9 12 1 9 12 1 7 1 9 12 1 1 10 12 1 9 12 1 9 12 1 10 7 1 100 1 8 1 10 12 1 1 10 12 1 100 1 8 1 10 12 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 100 1 10 10 100 12 1 1 1 10 12 1 1 8 1 10 12 10 12 1 10 100 12 1 1 1 10 12 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 12 1 100 1 10 11 100 1 10 12 1 1 8 1 18 12 1 1 11 12 1 1 10 10 12 1 1 8 1 9 12 1 10 12 1 8 1 10 12 1 10 12 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 10 100 12 1 1 1 10 100 12 1 1 9 12 1 9 12 1 1 10 7 12 1 1 10 12 1 100 1 100 1 8 1 10 10 12 1 10 12 1 10 7 12 1 1 1 10 12 1 10 12 9 12 1 9 12 1 10 12 1 10 12 1 9 12 1 7 1 9 12 1 1 9 12 1 1 10 9 12 1 1 10 12 1 1 9 7 12 1 1 10 12 1 1 9 12 1 10 12 1 8 1 8 1 7 1 10 7 12 1 1 7 1 10 7 1 7 1 9 12 1 11 12 1 1 11 7 12 1 1 11 12 1 100 1 9 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 12 1 3 10 12 1 1 10 12 1 7 1 10 10 7 12 1 10 12 1 10 12 10 12 1 1 100 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 100 1 10 100 1 10 10 100 1 10 12 1 7 1 10 7 1 10 12 1 1 10 10 12 1 10 12 1 8 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 100 12 1 1 1 10 12 1 100 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 1 100 1 9 12 1 10 10 7 12 1 1 10 12 1 1 9 12 1 1 11 7 12 1 1 100 1 10 10 12 1 11 7 1 10 12 1 100 1 10 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 100 1 10 100 1 10 9 12 1 10 12 1 8 1 10 12 1 10 12 1 11 100 12 1 1 8 1 8 1 11 12 1 10 12 1 10 12 1 10 12 1 100 1 10 8 1 7 1 10 11 12 1 1 8 1 11 12 1 8 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 10 12 1 1 10 12 1 1 8 1 10 12 1 1 10 12 1 10 12 1 8 1 10 12 1 10 12 1 1 10 12 1 10 12 1 8 1 8 1 9 100 12 1 1 1 10 12 1 10 12 1 8 1 8 1 8 1 8 1 8 1 8 1 10 12 1 1 10 12 1 10 12 1 1 8 1 8 1 8 1 8 1 10 12 1 1 8 1 10 12 1 10 12 1 8 1 8 1 7 1 10 12 1 8 1 10 12 1 8 1 11 10 12 1 1 100 1 10 100 1 10 100 1 9 7 12 1 1 1 10 12 1 8 1 8 1 10 12 1 11 12 1 1 9 100 12 1 1 1 100 1 10 10 12 1 1 9 12 1 8 1 10 12 1 1 100 1 9 12 1 9 12 1 10 12 1 100 1 10 100 1 10 100 1 10 11 11 12 1 8 1 10 12 1 8 1 8 1 10 12 1 9 12 1 9 12 1 9 12 1 100 1 9 7 1 100 1 9 12 1 9 12 1 9 12 1 9 12 1 10 12 1 9 10 12 3 11 100 1 10 100 1 10 12 1 9 9 9 12 1 8 1 10 10 9 12 1 1 10 12 1 9 12 1 10 12 1 1 7 1 10 12 1 7 1 10 12 1 10 12 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 10 12 1 10 12 1 3 8 1 8 1 8 1 8 1 10 12 1 10 12 1 10 12 10 12 1 10 12 1 1 10 12 1 8 1 10 12 1 8 1 8 1 8 1 11 100 1 10 12 1 100 1 10 100 1 10 100 1 10 100 1 10 10 9 12 1 9 12 1 10 12 1 18 12 1 18 18 12 18 18 18 12 18 12 18 12 18 3 3 18 18 12 18 18 18 12 1 1 18 100 1 10 100 1 10 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 7 12 1 1 10 9 12 7 1 10 100 1 1 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 3 1 1 5 0 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 100 1 1 15 10 7 12 1 1 1 16 1 15 10 12 16 16 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 16 15 10 12 16 15 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 100 1 1
staticfield java/util/regex/Pattern accept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$Node
staticfield java/util/regex/Pattern lastAccept Ljava/util/regex/Pattern$Node; java/util/regex/Pattern$LastNode
staticfield java/util/regex/Pattern $assertionsDisabled Z 1
ciMethod java/util/regex/Pattern x ()I 0 0 1 0 -1
ciMethod java/util/regex/Pattern c ()I 0 0 1 0 -1
ciMethod java/util/regex/Pattern o ()I 0 0 1 0 -1
ciMethod java/util/regex/Pattern read ()I 26 0 13 0 -1
ciMethod java/util/regex/Pattern ref (I)Ljava/util/regex/Pattern$Node; 0 0 1 0 -1
ciMethod java/util/regex/Pattern skip ()I 170 0 360 0 -1
ciMethod java/util/regex/Pattern peek ()I 786 0 3942 0 -1
ciMethod java/util/regex/Pattern u ()I 0 0 1 0 -1
ciMethod java/util/regex/Pattern error (Ljava/lang/String;)Ljava/util/regex/PatternSyntaxException; 0 0 1 0 -1
ciMethod java/util/regex/Pattern escape (ZZZ)I 208 0 360 0 -1
ciMethod java/util/regex/Pattern unread ()V 338 0 720 0 -1
ciMethod java/util/regex/Pattern N ()I 0 0 1 0 -1
ciMethod java/util/regex/Pattern namedGroups ()Ljava/util/Map; 0 0 1 0 -1
ciMethod java/util/regex/Pattern has (I)Z 512 0 13871 0 -1
ciMethod java/util/regex/Pattern peekPastWhitespace (I)I 0 0 1 0 -1
ciMethod java/util/regex/Pattern newCharProperty (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharProperty; 8 0 6 0 -1
ciMethod java/util/regex/Pattern HorizWS ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/Pattern VertWS ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/Pattern groupname (I)Ljava/lang/String; 0 0 1 0 -1
ciInstanceKlass java/lang/invoke/BoundMethodHandle$Species_L 1 1 125 10 7 12 1 1 1 9 7 12 1 1 1 9 12 1 1 10 12 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 10 100 12 1 1 1 100 1 10 100 12 1 1 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 9 12 1 10 12 1 10 12 1 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1
ciInstanceKlass java/util/regex/Pattern$CharPredicate 1 1 75 18 12 1 1 18 18 12 1 18 12 1 11 7 12 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 15 10 7 12 1 1 1 16 15 11 12 15 11 12 15 11 12 15 11 12 1 1 100 1 100 1 1
instanceKlass java/util/regex/Pattern$Start
instanceKlass java/util/regex/Pattern$First
instanceKlass java/util/regex/Pattern$Begin
instanceKlass java/util/regex/Pattern$Branch
instanceKlass java/util/regex/Pattern$BranchConn
instanceKlass java/util/regex/Pattern$CharPropertyGreedy
instanceKlass java/util/regex/Pattern$CharProperty
instanceKlass java/util/regex/Pattern$GroupTail
instanceKlass java/util/regex/Pattern$BnM
instanceKlass java/util/regex/Pattern$Dollar
instanceKlass java/util/regex/Pattern$SliceNode
instanceKlass java/util/regex/Pattern$GroupHead
instanceKlass java/util/regex/Pattern$LastNode
ciInstanceKlass java/util/regex/Pattern$Node 1 1 61 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 9 100 12 1 1 1 9 12 1 1 9 12 1 10 12 1 1 9 7 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1
ciInstanceKlass java/util/regex/Pattern$Begin 0 0 57 10 100 12 1 1 1 9 100 12 1 1 1 9 12 1 1 9 100 12 1 1 1 10 12 1 1 9 12 1 9 12 1 1 9 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1
ciMethod java/util/regex/Pattern$CharPredicate negate ()Ljava/util/regex/Pattern$CharPredicate; 2 0 1 0 -1
ciMethod java/util/regex/Pattern$CharPredicate union (Ljava/util/regex/Pattern$CharPredicate;Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharPredicate; 0 0 1 0 -1
ciInstanceKlass java/util/regex/Pattern$BmpCharPredicate 1 1 94 7 1 18 12 1 1 18 12 1 18 18 18 12 1 11 7 12 1 1 11 100 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1 1 1 1 1 1 1 7 1 1 15 10 7 12 1 1 1 16 15 11 12 15 11 12 15 11 12 15 11 12 15 11 12 1 1 1 100 1 100 1 1 1 100 1 8 1 1 12 10 1 1
ciInstanceKlass java/util/regex/CharPredicates 1 1 647 10 100 12 1 1 1 18 12 1 1 18 18 18 18 18 18 18 18 18 10 7 12 1 1 18 11 100 12 1 1 1 18 18 10 12 1 18 18 10 12 1 10 12 1 10 12 1 11 12 1 11 12 1 18 18 10 12 1 11 12 1 10 7 12 1 1 1 8 1 10 12 1 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 8 8 8 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 1 10 12 10 12 8 8 1 8 8 1 8 8 1 8 1 8 8 1 8 8 1 8 1 8 8 8 8 1 8 8 1 10 12 10 12 10 12 10 12 10 12 9 100 12 1 1 1 10 12 1 1 10 12 1 1 10 12 1 9 12 1 10 100 12 1 1 1 18 12 1 100 1 10 100 12 1 1 18 12 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 8 1 100 1 10 12 1 1 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 3 10 12 1 1 10 100 12 1 1 7 1 10 12 1 18 18 18 18 18 18 18 18 18 18 18 18 18 18 18 12 18 12 1 18 12 1 18 12 1 18 18 10 12 1 1 10 12 1 10 12 1 10 12 1 1 10 12 1 1 10 12 1 10 12 1 10 12 1 10 12 1 1 10 12 1 3 3 3 3 3 3 3 3 3 3 3 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 15 10 7 12 1 1 1 16 15 10 12 1 15 10 15 10 12 1 15 10 12 1 15 15 15 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 1 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 15 10 12 1 1 1 1 100 1 1 100 1 100 1 1
ciMethod java/util/regex/CharPredicates DIGIT ()Ljava/util/regex/Pattern$CharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/CharPredicates WORD ()Ljava/util/regex/Pattern$CharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/CharPredicates ALPHABETIC ()Ljava/util/regex/Pattern$CharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/CharPredicates JOIN_CONTROL ()Ljava/util/regex/Pattern$CharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/CharPredicates WHITE_SPACE ()Ljava/util/regex/Pattern$CharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 4 0 2 0 -1
ciMethod java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 0 1 0 -1
ciMethod java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 0 0 1 0 -1
ciInstanceKlass java/util/regex/Pattern$Dollar 1 1 82 10 7 12 1 1 1 9 7 12 1 1 1 9 7 12 1 1 9 12 1 1 10 12 1 1 11 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 9 12 1 10 12 1 1 9 100 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1
instanceKlass java/util/regex/Pattern$BmpCharProperty
ciInstanceKlass java/util/regex/Pattern$CharProperty 1 1 84 10 7 12 1 1 1 9 7 12 1 1 1 9 100 12 1 1 1 10 100 12 1 1 1 10 12 1 1 11 100 12 1 1 1 9 12 1 1 10 12 1 1 9 12 1 1 9 7 12 1 1 9 12 1 10 12 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 1 100 1 1 1 1 1 1
ciInstanceKlass java/util/regex/CharPredicates$$Lambda$16+0x800000024 1 1 20 1 7 1 7 1 100 1 1 12 10 1 1 1 7 1 12 10 1 1
ciMethod java/util/regex/Pattern$Dollar <init> (Z)V 2 0 1 0 -1
ciMethod java/util/regex/Pattern$Begin <init> ()V 0 0 1 0 -1
ciMethod java/util/regex/Pattern$Node <init> ()V 512 0 1790 0 -1
ciMethod java/util/Map containsKey (Ljava/lang/Object;)Z 0 0 1 0 -1
ciMethod java/util/Map get (Ljava/lang/Object;)Ljava/lang/Object; 0 0 1 0 -1
ciMethod java/lang/StringBuilder <init> ()V 210 0 713 0 -1
ciMethod java/lang/StringBuilder toString ()Ljava/lang/String; 220 0 788 0 -1
ciMethod java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 486 0 2261 0 -1
ciMethod java/lang/Object <init> ()V 4144 0 16420 0 -1
ciInstanceKlass java/lang/invoke/LambdaForm$MH+0x000001b810004000 1 1 41 1 7 1 100 1 1 1 1 1 1 1 7 1 1 12 9 1 1 1 1 1 7 1 1 12 10 1 7 1 1 12 11 1 7 12 9 1 1 1 1
staticfield java/lang/invoke/LambdaForm$MH+0x000001b810004000 _D_0 Ljava/lang/invoke/LambdaForm; java/lang/invoke/LambdaForm
ciMethodData java/lang/Object <init> ()V 2 16420 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 4 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder <init> ()V 1 713 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x30002 0x260 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder <init> (I)V 1 792 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 21 0x10002 0x2a8 0x70007 0x0 0x38 0x2a8 0x160003 0x2a8 0x28 0x1b0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder append (Ljava/lang/String;)Ljava/lang/StringBuilder; 2 2261 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x20002 0x7e2 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder append (Ljava/lang/String;)Ljava/lang/AbstractStringBuilder; 2 2261 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 42 0x10007 0x7e2 0x58 0x0 0x50005 0x0 0x0 0x0 0x0 0x0 0x0 0xa0005 0x7e2 0x0 0x0 0x0 0x0 0x0 0x150005 0x7e2 0x0 0x0 0x0 0x0 0x0 0x1e0005 0x7e2 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringBuilder toString ()Ljava/lang/String; 1 788 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x10005 0x2a6 0x0 0x0 0x0 0x0 0x0 0x40007 0x0 0x48 0x2a6 0x100002 0x2a6 0x130003 0x2a6 0x28 0x1f0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/AbstractStringBuilder isLatin1 ()Z 2 4337 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 18 0x30007 0x0 0x58 0xf13 0xa0007 0x0 0x38 0xf13 0xe0003 0xf13 0x18 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/lang/StringLatin1 newString ([BII)Ljava/lang/String; 2 1709 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 19 0x10007 0x5c3 0x20 0x2 0x100002 0x5c3 0x140002 0x5c3 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/lang/StringUTF16 newString ([BII)Ljava/lang/String; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 31 0x10007 0x0 0x20 0x0 0xb0007 0x0 0x60 0x0 0x110002 0x0 0x160007 0x0 0x30 0x0 0x1f0002 0x0 0x320002 0x0 0x360002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x3 0x0 0x0 0x0 oops 0 methods 0
ciMethod java/lang/invoke/MethodHandle invokeBasic ()Ljava/lang/Object; 0 0 1 0 -1
ciMethodData java/util/regex/Pattern has (I)Z 2 13871 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 184 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 14 0x60007 0x352f 0x38 0x0 0xa0003 0x0 0x18 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern peekPastWhitespace (I)I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 43 0x10002 0x0 0x40007 0x0 0x40 0x0 0xa0007 0x0 0xd8 0x0 0xe0002 0x0 0x110007 0x0 0x38 0x0 0x250003 0x0 0xffffffffffffffd0 0x2b0007 0x0 0xffffffffffffff68 0x0 0x2f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x330003 0x0 0xffffffffffffff10 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern peek ()I 2 3942 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 184 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0xc0005 0xddd 0x0 0x0 0x0 0x0 0x0 0xf0007 0xddd 0x58 0x0 0x140005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern$Node <init> ()V 2 1790 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 184 1 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 9 0x10002 0x5fe 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern skip ()I 1 360 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern read ()I 1 13 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x130005 0x0 0x0 0x0 0x0 0x0 0x0 0x160007 0x0 0x58 0x0 0x1b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern unread ()V 1 720 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern error (Ljava/lang/String;)Ljava/util/regex/PatternSyntaxException; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0xf0002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern escape (ZZZ)I 1 360 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 986 0x10005 0xff 0x0 0x0 0x0 0x0 0x0 0x80008 0x98 0xfc 0x1c58 0x0 0x4d0 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x508 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x598 0x0 0x600 0x0 0x6a0 0x0 0x6b8 0x0 0x7f8 0x0 0x7f8 0x0 0x810 0x0 0x878 0x0 0x938 0x0 0x938 0x0 0x938 0x0 0x938 0x0 0x938 0x0 0x950 0x0 0x988 0x0 0x988 0x0 0x988 0x0 0x9a0 0x0 0xa08 0x0 0xb48 0x0 0xb48 0x0 0xb60 0x0 0xc20 0x0 0xd60 0x0 0xdc8 0x0 0xde0 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0x1c58 0x0 0xec8 0x0 0xec8 0x0 0x10f0 0x3 0x1128 0x0 0x1230 0x0 0x1230 0x0 0x1230 0x0 0x1248 0x0 0x12d0 0x0 0x12d0 0x0 0x12e8 0x0 0x1880 0x0 0x1880 0x0 0x1898 0x0 0x1898 0x0 0x1898 0x0 0x1898 0x0 0x18b0 0x0 0x18b0 0x0 0x19b8 0x0 0x19b8 0x0 0x19f0 0x0 0x1a98 0x0 0x1ba0 0x0 0x1bd8 0x0 0x1bf0 0x1450005 0x0 0x0 0x0 0x0 0x0 0x0 0x14a0007 0x0 0x38 0x0 0x14d0003 0x0 0x1730 0x1510007 0x0 0x58 0x0 0x15b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1640007 0x0 0x38 0x0 0x1670003 0x0 0x16a0 0x16b0007 0x0 0x30 0x0 0x1730002 0x0 0x17c0007 0x0 0x38 0x0 0x17f0003 0x0 0x1638 0x1830007 0x0 0x68 0x0 0x1920005 0x0 0x0 0x0 0x0 0x0 0x0 0x1950002 0x0 0x19d0003 0x0 0x15b8 0x1a10007 0x0 0x140 0x0 0x1a90005 0x0 0x0 0x0 0x0 0x0 0x0 0x1ac0007 0x0 0x48 0x0 0x1af0002 0x0 0x1b20003 0x0 0x28 0x1b50002 0x0 0x1c00005 0x0 0x0 0x0 0x0 0x0 0x0 0x1c90007 0x0 0x58 0x0 0x1d20005 0x0 0x0 0x0 0x0 0x0 0x0 0x1da0003 0x0 0x1460 0x1de0007 0x0 0x38 0x0 0x1e10003 0x0 0x1428 0x1e50007 0x0 0x30 0x0 0x1ed0002 0x0 0x1f60007 0x0 0xc0 0x0 0x1fa0002 0x0 0x1fd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2060007 0x0 0x58 0x0 0x20f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2170003 0x0 0x1320 0x21b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x21f0003 0x0 0x12d0 0x2230007 0x0 0x38 0x0 0x2260003 0x0 0x1298 0x22a0007 0x0 0x30 0x0 0x2320002 0x0 0x23b0007 0x0 0x140 0x0 0x2430005 0x0 0x0 0x0 0x0 0x0 0x0 0x2460007 0x0 0x48 0x0 0x2490002 0x0 0x24c0003 0x0 0x28 0x24f0002 0x0 0x25a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2630007 0x0 0x58 0x0 0x26c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2740003 0x0 0x1110 0x2780007 0x0 0xc0 0x0 0x27c0002 0x0 0x27f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2880007 0x0 0x58 0x0 0x2910005 0x0 0x0 0x0 0x0 0x0 0x0 0x29a0007 0x0 0x140 0x0 0x2a20005 0x0 0x0 0x0 0x0 0x0 0x0 0x2a50007 0x0 0x48 0x0 0x2a80002 0x0 0x2ab0003 0x0 0x28 0x2ae0002 0x0 0x2b90005 0x0 0x0 0x0 0x0 0x0 0x0 0x2c20007 0x0 0x58 0x0 0x2cb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2d40007 0x0 0x38 0x0 0x2d70003 0x0 0xed8 0x2db0007 0x0 0x30 0x0 0x2e30002 0x0 0x2eb0003 0x0 0xe90 0x2ef0007 0x0 0x38 0x0 0x2f20003 0x0 0xe58 0x2f60007 0x0 0xb0 0x0 0x2fb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x2fe0007 0x0 0x48 0x0 0x3070002 0x0 0x30d0003 0x0 0x28 0x3160002 0x0 0x3220007 0x0 0x38 0x0 0x3250003 0x0 0xd70 0x3290007 0x0 0x1f0 0x0 0x32d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3320007 0x0 0x150 0x0 0x3360005 0x0 0x0 0x0 0x0 0x0 0x0 0x33b0007 0x0 0x88 0x0 0x33f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3440007 0x0 0xc50 0x0 0x34c0002 0x0 0x3550005 0x0 0x0 0x0 0x0 0x0 0x0 0x3590005 0x0 0x0 0x0 0x0 0x0 0x0 0x3680005 0x0 0x0 0x0 0x0 0x0 0x0 0x36b0002 0x0 0x3740005 0x0 0x0 0x0 0x0 0x0 0x0 0x3790007 0x2 0x108 0x1 0x3810005 0x1 0x0 0x0 0x0 0x0 0x0 0x3840007 0x1 0x48 0x0 0x3870002 0x0 0x38a0003 0x0 0x28 0x38d0002 0x1 0x3940007 0x0 0x58 0x1 0x39d0005 0x1 0x0 0x0 0x0 0x0 0x0 0x3ab0003 0x0 0xa28 0x3af0007 0x0 0x88 0x0 0x3b30002 0x0 0x3ba0007 0x0 0x58 0x0 0x3c30005 0x0 0x0 0x0 0x0 0x0 0x0 0x3cb0003 0x0 0x988 0x3cf0007 0x0 0x38 0x0 0x3d20003 0x0 0x950 0x3d60005 0x0 0x0 0x0 0x0 0x0 0x0 0x3db0007 0x0 0x58 0x0 0x3e20005 0x0 0x0 0x0 0x0 0x0 0x0 0x3e80005 0x0 0x0 0x0 0x0 0x0 0x0 0x3eb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f10005 0x0 0x0 0x0 0x0 0x0 0x0 0x3f60005 0x0 0x0 0x0 0x0 0x0 0x0 0x3fb0007 0x0 0x148 0x0 0x4030002 0x0 0x4090005 0x0 0x0 0x0 0x0 0x0 0x0 0x40e0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4140005 0x0 0x0 0x0 0x0 0x0 0x0 0x4170005 0x0 0x0 0x0 0x0 0x0 0x0 0x41a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x41f0007 0x0 0x2a8 0x0 0x4290005 0x0 0x0 0x0 0x0 0x0 0x0 0x42c0007 0x0 0x160 0x0 0x4350005 0x0 0x0 0x0 0x0 0x0 0x0 0x43a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x43f0004 0x0 0x0 0x0 0x0 0x0 0x0 0x4420005 0x0 0x0 0x0 0x0 0x0 0x0 0x4480005 0x0 0x0 0x0 0x0 0x0 0x0 0x44b0002 0x0 0x4510003 0x0 0x108 0x45a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x45f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4640004 0x0 0x0 0x0 0x0 0x0 0x0 0x4670005 0x0 0x0 0x0 0x0 0x0 0x0 0x46a0002 0x0 0x4720003 0x0 0x3d8 0x4780003 0x0 0x3c0 0x47f0007 0x0 0x108 0x0 0x4870005 0x0 0x0 0x0 0x0 0x0 0x0 0x48a0007 0x0 0x48 0x0 0x48d0002 0x0 0x4900003 0x0 0x28 0x4930002 0x0 0x49a0007 0x0 0x58 0x0 0x4a30005 0x0 0x0 0x0 0x0 0x0 0x0 0x4af0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4b40007 0x0 0x20 0x0 0x4bb0007 0x0 0x88 0x0 0x4bf0002 0x0 0x4c60007 0x0 0x58 0x0 0x4cf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x4d80007 0x0 0x108 0x0 0x4e00005 0x0 0x0 0x0 0x0 0x0 0x0 0x4e30007 0x0 0x48 0x0 0x4e60002 0x0 0x4e90003 0x0 0x28 0x4ec0002 0x0 0x4f30007 0x0 0x58 0x0 0x4fc0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5050005 0x0 0x0 0x0 0x0 0x0 0x0 0x5090003 0x0 0x80 0x50d0007 0x0 0x38 0x0 0x5100003 0x0 0x48 0x5140007 0x0 0x30 0x0 0x51c0002 0x0 0x52b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x4 0x0 0x0 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern newCharProperty (Ljava/util/regex/Pattern$CharPredicate;)Ljava/util/regex/Pattern$CharProperty; 1 6 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 36 0x10007 0x2 0x20 0x0 0x70004 0x0 0x0 0x1b87d045ab0 0x2 0x0 0x0 0xa0007 0x0 0x68 0x2 0x120004 0x0 0x0 0x1b87d045ab0 0x2 0x0 0x0 0x150002 0x2 0x230002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 2 7 java/util/regex/CharPredicates$$Lambda$16+0x800000024 18 java/util/regex/CharPredicates$$Lambda$16+0x800000024 methods 0
ciMethodData java/util/regex/Pattern o ()I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 67 0x10005 0x0 0x0 0x0 0x0 0x0 0x0 0xe0007 0x0 0x160 0x0 0x120005 0x0 0x0 0x0 0x0 0x0 0x0 0x1f0007 0x0 0xd0 0x0 0x230005 0x0 0x0 0x0 0x0 0x0 0x0 0x300007 0x0 0x40 0x0 0x3c0007 0x0 0x20 0x0 0x550005 0x0 0x0 0x0 0x0 0x0 0x0 0x660005 0x0 0x0 0x0 0x0 0x0 0x0 0x720005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern ref (I)Ljava/util/regex/Pattern$Node; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 87 0x30007 0x0 0x1b8 0x0 0x70005 0x0 0x0 0x0 0x0 0x0 0x0 0xc0008 0x16 0x0 0x148 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x0 0xc0 0x570007 0x0 0x38 0x0 0x5c0003 0x0 0x68 0x630005 0x0 0x0 0x0 0x0 0x0 0x0 0x670003 0x0 0x18 0x6c0003 0x0 0xfffffffffffffe60 0x760005 0x0 0x0 0x0 0x0 0x0 0x0 0x790007 0x0 0x68 0x0 0x840005 0x0 0x0 0x0 0x0 0x0 0x0 0x870002 0x0 0x900002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates DIGIT ()Ljava/util/regex/Pattern$CharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates ASCII_DIGIT ()Ljava/util/regex/Pattern$BmpCharPredicate; 1 2 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/lang/invoke/Invokers$Holder linkToTargetMethod (Ljava/lang/Object;)Ljava/lang/Object; 1 360 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 29 0x10004 0x0 0x0 0x0 0x0 0x0 0x0 0x4000b 0x0 0x0 0x0 0x0 0x0 0x0 0x1 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 0xc 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern HorizWS ()Ljava/util/regex/Pattern$BmpCharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern N ()I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 91 0x10005 0x0 0x0 0x0 0x0 0x0 0x0 0x60007 0x0 0x218 0x0 0xf0005 0x0 0x0 0x0 0x0 0x0 0x0 0x140007 0x0 0x78 0x0 0x1f0007 0x0 0xffffffffffffffa8 0x0 0x260005 0x0 0x0 0x0 0x0 0x0 0x0 0x3b0002 0x0 0x400002 0x0 0x4a0002 0x0 0x500005 0x0 0x0 0x0 0x0 0x0 0x0 0x540005 0x0 0x0 0x0 0x0 0x0 0x0 0x5a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x5d0005 0x0 0x0 0x0 0x0 0x0 0x0 0x600005 0x0 0x0 0x0 0x0 0x0 0x0 0x680005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates WHITE_SPACE ()Ljava/util/regex/Pattern$CharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates ASCII_SPACE ()Ljava/util/regex/Pattern$BmpCharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern VertWS ()Ljava/util/regex/Pattern$BmpCharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates WORD ()Ljava/util/regex/Pattern$CharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 23 0x2 0x0 0x3000a 0x0 0x1 0x0 0x80002 0x0 0xb0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates ALPHABETIC ()Ljava/util/regex/Pattern$CharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates JOIN_CONTROL ()Ljava/util/regex/Pattern$CharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/CharPredicates ASCII_WORD ()Ljava/util/regex/Pattern$BmpCharPredicate; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 7 0xa 0x0 0x1 0x0 0x0 0x9 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern$Dollar <init> (Z)V 1 1 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 10 0x10002 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern c ()I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 27 0x80007 0x0 0x58 0x0 0xc0005 0x0 0x0 0x0 0x0 0x0 0x0 0x170005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern groupname (I)Ljava/lang/String; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 63 0x40002 0x0 0x90002 0x0 0xc0007 0x0 0x58 0x0 0x130005 0x0 0x0 0x0 0x0 0x0 0x0 0x1a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x1f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x240002 0x0 0x270007 0x0 0xffffffffffffff80 0x0 0x2d0007 0x0 0x58 0x0 0x340005 0x0 0x0 0x0 0x0 0x0 0x0 0x390005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x2 0x0 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern namedGroups ()Ljava/util/Map; 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 15 0x60007 0x0 0x30 0x0 0xf0002 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern u ()I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 73 0x10005 0x0 0x0 0x0 0x0 0x0 0x0 0x70002 0x0 0xa0007 0x0 0x1b8 0x0 0xe0005 0x0 0x0 0x0 0x0 0x0 0x0 0x130005 0x0 0x0 0x0 0x0 0x0 0x0 0x180007 0x0 0xf0 0x0 0x1c0005 0x0 0x0 0x0 0x0 0x0 0x0 0x210007 0x0 0x98 0x0 0x250005 0x0 0x0 0x0 0x0 0x0 0x0 0x2b0002 0x0 0x2e0007 0x0 0x30 0x0 0x350002 0x0 0x3b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethodData java/util/regex/Pattern x ()I 1 0 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 103 0x10005 0x0 0x0 0x0 0x0 0x0 0x0 0x60002 0x0 0x90007 0x0 0xc0 0x0 0xd0005 0x0 0x0 0x0 0x0 0x0 0x0 0x120002 0x0 0x150007 0x0 0x40 0x0 0x190002 0x0 0x200002 0x0 0x250003 0x0 0x1c8 0x2b0007 0x0 0x1b0 0x0 0x2f0005 0x0 0x0 0x0 0x0 0x0 0x0 0x320002 0x0 0x350007 0x0 0x148 0x0 0x3b0005 0x0 0x0 0x0 0x0 0x0 0x0 0x400002 0x0 0x430007 0x0 0x88 0x0 0x4a0002 0x0 0x530007 0x0 0xffffffffffffff88 0x0 0x5a0005 0x0 0x0 0x0 0x0 0x0 0x0 0x610007 0x0 0x58 0x0 0x680005 0x0 0x0 0x0 0x0 0x0 0x0 0x720005 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 oops 0 methods 0
ciMethod java/lang/invoke/LambdaForm$MH+0x000001b810004000 invoke (Ljava/lang/Object;)Ljava/lang/Object; 720 0 360 0 -1
ciMethodData java/lang/invoke/LambdaForm$MH+0x000001b810004000 invoke (Ljava/lang/Object;)Ljava/lang/Object; 1 360 orig 80 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 data 20 0x10004 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x0 0x9 0x1 0x0 0xc 0x2 0x0 0x0 oops 0 methods 0
compile java/util/regex/Pattern escape (ZZZ)I -1 3
