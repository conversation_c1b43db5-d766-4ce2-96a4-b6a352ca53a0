#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 262160 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=25560, tid=5208
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Sat Jun 28 15:15:05 2025 W. Central Africa Standard Time elapsed time: 256.657604 seconds (0d 0h 4m 16s)

---------------  T H R E A D  ---------------

Current thread (0x000002834f4ee630):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=5208, stack(0x000000c2e3400000,0x000000c2e3500000)]


Current CompileTask:
C2: 256657 7097   !   4       com.google.common.reflect.TypeVisitor::visit (225 bytes)

Stack: [0x000000c2e3400000,0x000000c2e3500000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad0a4]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0xad1b4]
V  [jvm.dll+0x66406c]
V  [jvm.dll+0x221e39]
V  [jvm.dll+0x6aa7e1]
V  [jvm.dll+0x219695]
V  [jvm.dll+0x1a53e6]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002838b435380, length=52, elements={
0x00000283280ff4e0, 0x000002834f4d6010, 0x000002834f4d6e90, 0x000002834f4e8670,
0x000002834f4e94f0, 0x000002834f4eced0, 0x000002834f4ed890, 0x000002834f4ee630,
0x000002834f4fa060, 0x000002834f568050, 0x000002834f4bd130, 0x000002834f68fb90,
0x000002834f92d160, 0x0000028389046870, 0x000002838977c470, 0x000002838974e030,
0x000002838974e510, 0x000002838931b060, 0x000002838931c9b0, 0x000002838931ba80,
0x000002838931bf90, 0x000002838931c4a0, 0x000002838931d8e0, 0x000002838931cec0,
0x000002838931d3d0, 0x000002838931ddf0, 0x000002838931e300, 0x000002838931b570,
0x000002838a612c90, 0x000002838a60ff00, 0x000002838a60d170, 0x000002838a611850,
0x000002838a611d60, 0x000002838a612270, 0x000002838a612780, 0x000002838a6136b0,
0x000002838a6131a0, 0x000002838a60c240, 0x000002838a613bc0, 0x000002838a60e5b0,
0x000002838a60c750, 0x000002838a60cc60, 0x000002838a60db90, 0x000002838a60e0a0,
0x000002838a60eac0, 0x000002838a60efd0, 0x000002838a60f4e0, 0x000002838a60f9f0,
0x000002838a610e30, 0x000002838a611340, 0x000002838ae81cf0, 0x000002838ae84570
}

Java Threads: ( => current thread )
  0x00000283280ff4e0 JavaThread "main" [_thread_blocked, id=21880, stack(0x000000c2e2700000,0x000000c2e2800000)]
  0x000002834f4d6010 JavaThread "Reference Handler" daemon [_thread_blocked, id=19232, stack(0x000000c2e2e00000,0x000000c2e2f00000)]
  0x000002834f4d6e90 JavaThread "Finalizer" daemon [_thread_blocked, id=17048, stack(0x000000c2e2f00000,0x000000c2e3000000)]
  0x000002834f4e8670 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=25344, stack(0x000000c2e3000000,0x000000c2e3100000)]
  0x000002834f4e94f0 JavaThread "Attach Listener" daemon [_thread_blocked, id=23000, stack(0x000000c2e3100000,0x000000c2e3200000)]
  0x000002834f4eced0 JavaThread "Service Thread" daemon [_thread_blocked, id=10012, stack(0x000000c2e3200000,0x000000c2e3300000)]
  0x000002834f4ed890 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=21060, stack(0x000000c2e3300000,0x000000c2e3400000)]
=>0x000002834f4ee630 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=5208, stack(0x000000c2e3400000,0x000000c2e3500000)]
  0x000002834f4fa060 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=19152, stack(0x000000c2e3500000,0x000000c2e3600000)]
  0x000002834f568050 JavaThread "Sweeper thread" daemon [_thread_blocked, id=18112, stack(0x000000c2e3600000,0x000000c2e3700000)]
  0x000002834f4bd130 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=22992, stack(0x000000c2e3700000,0x000000c2e3800000)]
  0x000002834f68fb90 JavaThread "Notification Thread" daemon [_thread_blocked, id=17992, stack(0x000000c2e3800000,0x000000c2e3900000)]
  0x000002834f92d160 JavaThread "Daemon health stats" [_thread_blocked, id=22780, stack(0x000000c2e3d00000,0x000000c2e3e00000)]
  0x0000028389046870 JavaThread "Incoming local TCP Connector on port 56926" [_thread_in_native, id=20188, stack(0x000000c2e3e00000,0x000000c2e3f00000)]
  0x000002838977c470 JavaThread "Daemon periodic checks" [_thread_blocked, id=22580, stack(0x000000c2e3f00000,0x000000c2e4000000)]
  0x000002838974e030 JavaThread "Daemon" [_thread_blocked, id=24940, stack(0x000000c2e4000000,0x000000c2e4100000)]
  0x000002838974e510 JavaThread "Handler for socket connection from /127.0.0.1:56926 to /127.0.0.1:56929" [_thread_in_native, id=25204, stack(0x000000c2e4100000,0x000000c2e4200000)]
  0x000002838931b060 JavaThread "Cancel handler" [_thread_blocked, id=2576, stack(0x000000c2e4200000,0x000000c2e4300000)]
  0x000002838931c9b0 JavaThread "Daemon worker" [_thread_in_vm, id=24264, stack(0x000000c2e4300000,0x000000c2e4400000)]
  0x000002838931ba80 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56926 to /127.0.0.1:56929" [_thread_blocked, id=20792, stack(0x000000c2e4400000,0x000000c2e4500000)]
  0x000002838931bf90 JavaThread "Stdin handler" [_thread_blocked, id=21208, stack(0x000000c2e4500000,0x000000c2e4600000)]
  0x000002838931c4a0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=8400, stack(0x000000c2e4600000,0x000000c2e4700000)]
  0x000002838931d8e0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=21272, stack(0x000000c2e4700000,0x000000c2e4800000)]
  0x000002838931cec0 JavaThread "File lock request listener" [_thread_in_native, id=24340, stack(0x000000c2e4800000,0x000000c2e4900000)]
  0x000002838931d3d0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=3888, stack(0x000000c2e4900000,0x000000c2e4a00000)]
  0x000002838931ddf0 JavaThread "File lock release action executor" [_thread_blocked, id=20092, stack(0x000000c2e4a00000,0x000000c2e4b00000)]
  0x000002838931e300 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=15160, stack(0x000000c2e4d00000,0x000000c2e4e00000)]
  0x000002838931b570 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=18116, stack(0x000000c2e4e00000,0x000000c2e4f00000)]
  0x000002838a612c90 JavaThread "File watcher server" daemon [_thread_in_native, id=16152, stack(0x000000c2e4f00000,0x000000c2e5000000)]
  0x000002838a60ff00 JavaThread "File watcher consumer" daemon [_thread_blocked, id=17772, stack(0x000000c2e5000000,0x000000c2e5100000)]
  0x000002838a60d170 JavaThread "jar transforms" [_thread_blocked, id=21004, stack(0x000000c2e2500000,0x000000c2e2600000)]
  0x000002838a611850 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=18240, stack(0x000000c2e2600000,0x000000c2e2700000)]
  0x000002838a611d60 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=25520, stack(0x000000c2e5100000,0x000000c2e5200000)]
  0x000002838a612270 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=15988, stack(0x000000c2e5200000,0x000000c2e5300000)]
  0x000002838a612780 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=16804, stack(0x000000c2e5300000,0x000000c2e5400000)]
  0x000002838a6136b0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=15860, stack(0x000000c2e5400000,0x000000c2e5500000)]
  0x000002838a6131a0 JavaThread "Unconstrained build operations" [_thread_blocked, id=17236, stack(0x000000c2e5500000,0x000000c2e5600000)]
  0x000002838a60c240 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=16768, stack(0x000000c2e5600000,0x000000c2e5700000)]
  0x000002838a613bc0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=7092, stack(0x000000c2e5700000,0x000000c2e5800000)]
  0x000002838a60e5b0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=21480, stack(0x000000c2e5800000,0x000000c2e5900000)]
  0x000002838a60c750 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=4984, stack(0x000000c2e5900000,0x000000c2e5a00000)]
  0x000002838a60cc60 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=22960, stack(0x000000c2e5a00000,0x000000c2e5b00000)]
  0x000002838a60db90 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=26588, stack(0x000000c2e4b00000,0x000000c2e4c00000)]
  0x000002838a60e0a0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=26592, stack(0x000000c2e4c00000,0x000000c2e4d00000)]
  0x000002838a60eac0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=26596, stack(0x000000c2e5b00000,0x000000c2e5c00000)]
  0x000002838a60efd0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=20964, stack(0x000000c2e5c00000,0x000000c2e5d00000)]
  0x000002838a60f4e0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=1900, stack(0x000000c2e5d00000,0x000000c2e5e00000)]
  0x000002838a60f9f0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=22976, stack(0x000000c2e5e00000,0x000000c2e5f00000)]
  0x000002838a610e30 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=22484, stack(0x000000c2e5f00000,0x000000c2e6000000)]
  0x000002838a611340 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=24580, stack(0x000000c2e6000000,0x000000c2e6100000)]
  0x000002838ae81cf0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=26376, stack(0x000000c2e6100000,0x000000c2e6200000)]
  0x000002838ae84570 JavaThread "Memory manager" [_thread_blocked, id=17136, stack(0x000000c2e6200000,0x000000c2e6300000)]

Other Threads:
  0x000002834f4cf090 VMThread "VM Thread" [stack: 0x000000c2e2d00000,0x000000c2e2e00000] [id=24860]
  0x0000028328100360 WatcherThread [stack: 0x000000c2e3900000,0x000000c2e3a00000] [id=4676]
  0x00000283281868c0 GCTaskThread "GC Thread#0" [stack: 0x000000c2e2800000,0x000000c2e2900000] [id=8368]
  0x000002834f866e40 GCTaskThread "GC Thread#1" [stack: 0x000000c2e3a00000,0x000000c2e3b00000] [id=25504]
  0x000002834f867100 GCTaskThread "GC Thread#2" [stack: 0x000000c2e3b00000,0x000000c2e3c00000] [id=16944]
  0x000002834f92dd30 GCTaskThread "GC Thread#3" [stack: 0x000000c2e3c00000,0x000000c2e3d00000] [id=25424]
  0x0000028328193810 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000c2e2900000,0x000000c2e2a00000] [id=24384]
  0x0000028328194230 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000c2e2a00000,0x000000c2e2b00000] [id=17932]
  0x00000283281ae550 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000c2e2b00000,0x000000c2e2c00000] [id=22308]
  0x000002838bfc4930 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000c2e2400000,0x000000c2e2500000] [id=23748]
  0x000002834f38e150 ConcurrentGCThread "G1 Service" [stack: 0x000000c2e2c00000,0x000000c2e2d00000] [id=17732]

Threads with active compile tasks:
C2 CompilerThread0   256706 7097   !   4       com.google.common.reflect.TypeVisitor::visit (225 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000028350000000-0x0000028350bb0000-0x0000028350bb0000), size 12255232, SharedBaseAddress: 0x0000028350000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000028351000000-0x0000028385000000, reserved size: 872415232
Narrow klass base: 0x0000028350000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 98304K, used 63042K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 2 survivors (8192K)
 Metaspace       used 55078K, committed 55744K, reserved 917504K
  class space    used 7760K, committed 8064K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HS|  |TAMS 0x0000000680400000, 0x0000000680000000| Complete 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%| O|  |TAMS 0x0000000680800000, 0x0000000680400000| Untracked 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000681e5f600, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682291000, 0x0000000682400000| 64%| O|  |TAMS 0x0000000682000000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682400000, 0x0000000682800000|  0%| F|  |TAMS 0x0000000682400000, 0x0000000682400000| Untracked 
|  10|0x0000000682800000, 0x0000000682800000, 0x0000000682c00000|  0%| F|  |TAMS 0x0000000682800000, 0x0000000682800000| Untracked 
|  11|0x0000000682c00000, 0x0000000682c00000, 0x0000000683000000|  0%| F|  |TAMS 0x0000000682c00000, 0x0000000682c00000| Untracked 
|  12|0x0000000683000000, 0x0000000683000000, 0x0000000683400000|  0%| F|  |TAMS 0x0000000683000000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x0000000683400000, 0x0000000683800000|  0%| F|  |TAMS 0x0000000683400000, 0x0000000683400000| Untracked 
|  14|0x0000000683800000, 0x0000000683aff968, 0x0000000683c00000| 74%| S|CS|TAMS 0x0000000683800000, 0x0000000683800000| Complete 
|  15|0x0000000683c00000, 0x0000000684000000, 0x0000000684000000|100%| S|CS|TAMS 0x0000000683c00000, 0x0000000683c00000| Complete 
|  16|0x0000000684000000, 0x0000000684000000, 0x0000000684400000|  0%| F|  |TAMS 0x0000000684000000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684400000, 0x0000000684800000|  0%| F|  |TAMS 0x0000000684400000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684a37cb8, 0x0000000684c00000| 55%| E|  |TAMS 0x0000000684800000, 0x0000000684800000| Complete 
|  19|0x0000000684c00000, 0x0000000685000000, 0x0000000685000000|100%| E|CS|TAMS 0x0000000684c00000, 0x0000000684c00000| Complete 
|  20|0x0000000685000000, 0x0000000685400000, 0x0000000685400000|100%| E|CS|TAMS 0x0000000685000000, 0x0000000685000000| Complete 
|  21|0x0000000685400000, 0x0000000685800000, 0x0000000685800000|100%| E|CS|TAMS 0x0000000685400000, 0x0000000685400000| Complete 
|  22|0x0000000685800000, 0x0000000685c00000, 0x0000000685c00000|100%| E|CS|TAMS 0x0000000685800000, 0x0000000685800000| Complete 
|  31|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%| E|CS|TAMS 0x0000000687c00000, 0x0000000687c00000| Complete 

Card table byte_map: [0x000002833f820000,0x0000028340420000] _byte_map_base: 0x000002833c420000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000028328186ee0, (CMBitMap*) 0x0000028328186f20
 Prev Bits: [0x0000028341020000, 0x0000028347020000)
 Next Bits: [0x0000028347020000, 0x000002834d020000)

Polling page: 0x00000283260c0000

Metaspace:

Usage:
  Non-class:     46.21 MB used.
      Class:      7.58 MB used.
       Both:     53.79 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      46.56 MB ( 73%) committed,  1 nodes.
      Class space:      832.00 MB reserved,       7.88 MB ( <1%) committed,  1 nodes.
             Both:      896.00 MB reserved,      54.44 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  944.00 KB
       Class:  8.03 MB
        Both:  8.95 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 59.00 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 740.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 871.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 2992.
num_chunk_merges: 6.
num_chunk_splits: 1981.
num_chunks_enlarged: 1344.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3467Kb max_used=3467Kb free=116532Kb
 bounds [0x00000283376f0000, 0x0000028337a60000, 0x000002833ec20000]
CodeHeap 'profiled nmethods': size=120000Kb used=12107Kb max_used=12107Kb free=107892Kb
 bounds [0x000002832fc20000, 0x0000028330800000, 0x0000028337150000]
CodeHeap 'non-nmethods': size=5760Kb used=2348Kb max_used=2414Kb free=3411Kb
 bounds [0x0000028337150000, 0x00000283373c0000, 0x00000283376f0000]
 total_blobs=7143 nmethods=6268 adapters=787
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 256.441 Thread 0x000002834f4fa060 nmethod 7090 0x0000028337a52290 code [0x0000028337a52420, 0x0000028337a524f8]
Event: 256.565 Thread 0x000002834f4ee630 7091       4       java.util.ImmutableCollections$Set12$1::hasNext (13 bytes)
Event: 256.566 Thread 0x000002834f4ee630 nmethod 7091 0x0000028337a52590 code [0x0000028337a52700, 0x0000028337a52798]
Event: 256.567 Thread 0x000002834f4fa060 7092       3       java.util.HashMap::keysToArray (81 bytes)
Event: 256.568 Thread 0x000002834f4fa060 nmethod 7092 0x00000283307ed810 code [0x00000283307ed9e0, 0x00000283307edeb8]
Event: 256.594 Thread 0x000002834f4fa060 7093       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::access$1400 (4 bytes)
Event: 256.594 Thread 0x000002834f4fa060 nmethod 7093 0x00000283307ee090 code [0x00000283307ee220, 0x00000283307ee318]
Event: 256.601 Thread 0x000002834f4fa060 7094       3       java.util.TreeMap::addEntryToEmptyMap (37 bytes)
Event: 256.602 Thread 0x000002834f4fa060 nmethod 7094 0x00000283307ee390 code [0x00000283307ee580, 0x00000283307eec98]
Event: 256.602 Thread 0x000002834f4fa060 7095       3       sun.reflect.generics.parser.SignatureParser::parseTypeVariableSignature (95 bytes)
Event: 256.604 Thread 0x000002834f4fa060 nmethod 7095 0x00000283307eee90 code [0x00000283307ef160, 0x00000283307efc68]
Event: 256.607 Thread 0x000002834f4ee630 7096       4       java.util.Arrays::hashCode (56 bytes)
Event: 256.609 Thread 0x000002834f4ee630 nmethod 7096 0x0000028337a52890 code [0x0000028337a52a20, 0x0000028337a52b78]
Event: 256.609 Thread 0x000002834f4ee630 7097   !   4       com.google.common.reflect.TypeVisitor::visit (225 bytes)
Event: 256.611 Thread 0x000002834f4fa060 7098       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::addActionMethod (108 bytes)
Event: 256.613 Thread 0x000002834f4fa060 nmethod 7098 0x00000283307f0110 code [0x00000283307f0400, 0x00000283307f1958]
Event: 256.613 Thread 0x000002834f4fa060 7100       3       org.gradle.util.internal.CollectionUtils::collectArray (49 bytes)
Event: 256.614 Thread 0x000002834f4fa060 nmethod 7100 0x00000283307f1f10 code [0x00000283307f20e0, 0x00000283307f2548]
Event: 256.614 Thread 0x000002834f4fa060 7099       3       org.gradle.util.internal.CollectionUtils::collectArray (17 bytes)
Event: 256.614 Thread 0x000002834f4fa060 nmethod 7099 0x00000283307f2710 code [0x00000283307f28c0, 0x00000283307f2b78]

GC Heap History (20 events):
Event: 91.411 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 98304K, used 68811K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 29261K, committed 29696K, reserved 917504K
  class space    used 4067K, committed 4288K, reserved 851968K
}
Event: 91.431 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 98304K, used 27889K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 29261K, committed 29696K, reserved 917504K
  class space    used 4067K, committed 4288K, reserved 851968K
}
Event: 91.714 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 98304K, used 31985K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 2 survivors (8192K)
 Metaspace       used 29432K, committed 29824K, reserved 917504K
  class space    used 4080K, committed 4288K, reserved 851968K
}
Event: 91.730 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 98304K, used 28600K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 29432K, committed 29824K, reserved 917504K
  class space    used 4080K, committed 4288K, reserved 851968K
}
Event: 109.736 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 98304K, used 65464K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 32764K, committed 33216K, reserved 917504K
  class space    used 4529K, committed 4736K, reserved 851968K
}
Event: 109.757 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 98304K, used 31023K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 32764K, committed 33216K, reserved 917504K
  class space    used 4529K, committed 4736K, reserved 851968K
}
Event: 118.692 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 98304K, used 55599K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 35581K, committed 35968K, reserved 917504K
  class space    used 4943K, committed 5120K, reserved 851968K
}
Event: 118.707 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total 98304K, used 32393K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 35581K, committed 35968K, reserved 917504K
  class space    used 4943K, committed 5120K, reserved 851968K
}
Event: 128.003 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 98304K, used 69257K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 38104K, committed 38592K, reserved 917504K
  class space    used 5225K, committed 5440K, reserved 851968K
}
Event: 128.014 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 98304K, used 33122K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 38104K, committed 38592K, reserved 917504K
  class space    used 5225K, committed 5440K, reserved 851968K
}
Event: 143.338 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 98304K, used 74082K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 41379K, committed 41856K, reserved 917504K
  class space    used 5753K, committed 5952K, reserved 851968K
}
Event: 143.346 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 98304K, used 34092K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 41379K, committed 41856K, reserved 917504K
  class space    used 5753K, committed 5952K, reserved 851968K
}
Event: 156.917 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 98304K, used 75052K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 45255K, committed 45760K, reserved 917504K
  class space    used 6340K, committed 6592K, reserved 851968K
}
Event: 156.927 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 98304K, used 35458K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 45255K, committed 45760K, reserved 917504K
  class space    used 6340K, committed 6592K, reserved 851968K
}
Event: 182.105 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 98304K, used 72322K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 49618K, committed 50176K, reserved 917504K
  class space    used 6941K, committed 7232K, reserved 851968K
}
Event: 182.148 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 98304K, used 37444K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 49618K, committed 50176K, reserved 917504K
  class space    used 6941K, committed 7232K, reserved 851968K
}
Event: 217.772 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 98304K, used 74308K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 52762K, committed 53312K, reserved 917504K
  class space    used 7433K, committed 7680K, reserved 851968K
}
Event: 217.789 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 98304K, used 38690K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 52762K, committed 53312K, reserved 917504K
  class space    used 7433K, committed 7680K, reserved 851968K
}
Event: 250.136 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 98304K, used 71458K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 2 survivors (8192K)
 Metaspace       used 53246K, committed 53824K, reserved 917504K
  class space    used 7455K, committed 7680K, reserved 851968K
}
Event: 250.173 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 98304K, used 42562K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 53246K, committed 53824K, reserved 917504K
  class space    used 7455K, committed 7680K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.058 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.661 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 2.306 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 2.324 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 2.334 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 2.340 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 2.345 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 7.008 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 14.052 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 15.385 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 15.461 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 38.238 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 38.258 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 42.337 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 47.512 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 59.181 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform3671749702568290337dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 253.971 Thread 0x000002838931c9b0 Uncommon trap: trap_request=0xffffff6e fr.pc=0x0000028337987650 relative=0x0000000000000750
Event: 253.971 Thread 0x000002838931c9b0 Uncommon trap: reason=loop_limit_check action=maybe_recompile pc=0x0000028337987650 method=java.util.regex.Pattern$Start.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 34 c2
Event: 253.971 Thread 0x000002838931c9b0 DEOPT PACKING pc=0x0000028337987650 sp=0x000000c2e43f1320
Event: 253.971 Thread 0x000002838931c9b0 DEOPT UNPACKING pc=0x00000283371a69a3 sp=0x000000c2e43f1228 mode 2
Event: 253.971 Thread 0x000002838931c9b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000028337975e68 relative=0x0000000000000808
Event: 253.971 Thread 0x000002838931c9b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000028337975e68 method=java.util.regex.Pattern$Start.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 44 c2
Event: 253.971 Thread 0x000002838931c9b0 DEOPT PACKING pc=0x0000028337975e68 sp=0x000000c2e43f12b0
Event: 253.972 Thread 0x000002838931c9b0 DEOPT UNPACKING pc=0x00000283371a69a3 sp=0x000000c2e43f1210 mode 2
Event: 253.972 Thread 0x000002838931c9b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000028337975e68 relative=0x0000000000000808
Event: 253.972 Thread 0x000002838931c9b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000028337975e68 method=java.util.regex.Pattern$Start.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 44 c2
Event: 253.972 Thread 0x000002838931c9b0 DEOPT PACKING pc=0x0000028337975e68 sp=0x000000c2e43f12b0
Event: 253.972 Thread 0x000002838931c9b0 DEOPT UNPACKING pc=0x00000283371a69a3 sp=0x000000c2e43f1210 mode 2
Event: 253.972 Thread 0x000002838931c9b0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x000002833796bdc8 relative=0x00000000000005e8
Event: 253.972 Thread 0x000002838931c9b0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x000002833796bdc8 method=java.util.regex.Pattern$Start.match(Ljava/util/regex/Matcher;ILjava/lang/CharSequence;)Z @ 44 c2
Event: 253.972 Thread 0x000002838931c9b0 DEOPT PACKING pc=0x000002833796bdc8 sp=0x000000c2e43f1230
Event: 253.972 Thread 0x000002838931c9b0 DEOPT UNPACKING pc=0x00000283371a69a3 sp=0x000000c2e43f1210 mode 2
Event: 255.675 Thread 0x000002838931c9b0 Uncommon trap: trap_request=0xffffffbe fr.pc=0x000002833795d4e8 relative=0x00000000000001c8
Event: 255.675 Thread 0x000002838931c9b0 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x000002833795d4e8 method=java.util.LinkedHashMap.keysToArray([Ljava/lang/Object;)[Ljava/lang/Object; @ 12 c2
Event: 255.675 Thread 0x000002838931c9b0 DEOPT PACKING pc=0x000002833795d4e8 sp=0x000000c2e43f1f40
Event: 255.675 Thread 0x000002838931c9b0 DEOPT UNPACKING pc=0x00000283371a69a3 sp=0x000000c2e43f1ef8 mode 2

Classes loaded (20 events):
Event: 253.484 Loading class javax/xml/parsers/FactoryFinder
Event: 253.484 Loading class javax/xml/parsers/FactoryFinder done
Event: 253.485 Loading class javax/xml/parsers/FactoryFinder$1
Event: 253.485 Loading class javax/xml/parsers/FactoryFinder$1 done
Event: 253.486 Loading class com/sun/org/apache/xerces/internal/jaxp/DocumentBuilderFactoryImpl
Event: 253.488 Loading class com/sun/org/apache/xerces/internal/jaxp/DocumentBuilderFactoryImpl done
Event: 253.488 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityManager
Event: 253.490 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityManager done
Event: 253.490 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$Limit
Event: 253.490 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$Limit done
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$NameMap
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityManager$NameMap done
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager done
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$State
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$State done
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property
Event: 253.491 Loading class com/sun/org/apache/xerces/internal/utils/XMLSecurityPropertyManager$Property done
Event: 253.964 Loading class java/util/regex/Pattern$SliceI
Event: 253.964 Loading class java/util/regex/Pattern$SliceI done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 151.337 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000068447c020}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000068447c020) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 174.347 Thread 0x000002838931c9b0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000684a2e6d8}: sun/misc/SharedSecrets> (0x0000000684a2e6d8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 177.583 Thread 0x000002838931c9b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684611168}> (0x0000000684611168) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 177.583 Thread 0x000002838931c9b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684611498}> (0x0000000684611498) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 177.583 Thread 0x000002838931c9b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684614680}> (0x0000000684614680) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 177.583 Thread 0x000002838931c9b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684614aa0}> (0x0000000684614aa0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 177.608 Thread 0x000002838931c9b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684625050}> (0x0000000684625050) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 177.608 Thread 0x000002838931c9b0 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684625470}> (0x0000000684625470) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 181.772 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683ec4d80}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000683ec4d80) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 188.428 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684d15088}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000684d15088) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 188.681 Thread 0x000002838931c9b0 Implicit null exception at 0x0000028337a02986 to 0x0000028337a02d3c
Event: 215.686 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x000000068435d660}: static Lorg/gradle/api/internal/catalog/DefaultVersionCatalog;.<clinit>()V> (0x000000068435d660) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.702 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006843c16e8}: static [Ljava/lang/Object;.<clinit>()V> (0x00000006843c16e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.714 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c045d8}: static Lorg/gradle/api/internal/catalog/DependencyModel;.<clinit>()V> (0x0000000683c045d8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.714 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c08520}: static Lorg/gradle/api/internal/catalog/AbstractContextAwareModel;.<clinit>()V> (0x0000000683c08520) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.717 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c25d50}: static Lorg/gradle/api/internal/artifacts/dependencies/AbstractVersionConstraint;.<clinit>()V> (0x0000000683c25d50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.976 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683cf5288}: static Lorg/gradle/api/internal/catalog/PluginModel;.<clinit>()V> (0x0000000683cf5288) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.979 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683d10560}: static Lorg/gradle/api/internal/catalog/VersionModel;.<clinit>()V> (0x0000000683d10560) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 215.988 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683d2aaf8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000683d2aaf8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 254.515 Thread 0x000002838931c9b0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684e108e0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000684e108e0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 247.658 Executing VM operation: Cleanup
Event: 247.658 Executing VM operation: Cleanup done
Event: 248.671 Executing VM operation: Cleanup
Event: 248.883 Executing VM operation: Cleanup done
Event: 249.885 Executing VM operation: Cleanup
Event: 249.886 Executing VM operation: Cleanup done
Event: 250.135 Executing VM operation: G1CollectForAllocation
Event: 250.177 Executing VM operation: G1CollectForAllocation done
Event: 251.184 Executing VM operation: Cleanup
Event: 251.499 Executing VM operation: Cleanup done
Event: 252.512 Executing VM operation: Cleanup
Event: 252.576 Executing VM operation: Cleanup done
Event: 253.561 Executing VM operation: HandshakeAllThreads
Event: 253.561 Executing VM operation: HandshakeAllThreads done
Event: 254.566 Executing VM operation: Cleanup
Event: 254.566 Executing VM operation: Cleanup done
Event: 255.578 Executing VM operation: Cleanup
Event: 255.578 Executing VM operation: Cleanup done
Event: 256.585 Executing VM operation: Cleanup
Event: 256.586 Executing VM operation: Cleanup done

Events (20 events):
Event: 143.746 Thread 0x000002834f568050 flushing nmethod 0x000002833041ca90
Event: 143.747 Thread 0x000002834f568050 flushing nmethod 0x00000283304e2d10
Event: 143.747 Thread 0x000002834f568050 flushing nmethod 0x0000028330503d90
Event: 143.747 Thread 0x000002834f568050 flushing nmethod 0x0000028330559790
Event: 148.268 Thread 0x000002838a6131a0 Thread added: 0x000002838a6131a0
Event: 148.268 Thread 0x000002838a60c240 Thread added: 0x000002838a60c240
Event: 150.092 Thread 0x000002838a613bc0 Thread added: 0x000002838a613bc0
Event: 150.094 Thread 0x000002838a60e5b0 Thread added: 0x000002838a60e5b0
Event: 151.339 Thread 0x000002838a60c750 Thread added: 0x000002838a60c750
Event: 151.341 Thread 0x000002838a60cc60 Thread added: 0x000002838a60cc60
Event: 226.456 Thread 0x000002838a60db90 Thread added: 0x000002838a60db90
Event: 226.457 Thread 0x000002838a60e0a0 Thread added: 0x000002838a60e0a0
Event: 226.457 Thread 0x000002838a60eac0 Thread added: 0x000002838a60eac0
Event: 233.698 Thread 0x000002838a60efd0 Thread added: 0x000002838a60efd0
Event: 233.702 Thread 0x000002838a60f4e0 Thread added: 0x000002838a60f4e0
Event: 233.704 Thread 0x000002838a60f9f0 Thread added: 0x000002838a60f9f0
Event: 236.881 Thread 0x000002838a610e30 Thread added: 0x000002838a610e30
Event: 236.882 Thread 0x000002838a611340 Thread added: 0x000002838a611340
Event: 236.885 Thread 0x000002838ae81cf0 Thread added: 0x000002838ae81cf0
Event: 254.231 Thread 0x000002838ae84570 Thread added: 0x000002838ae84570


Dynamic libraries:
0x00007ff6cc7f0000 - 0x00007ff6cc7fe000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffddafb0000 - 0x00007ffddb1c7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffdd9760000 - 0x00007ffdd9824000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffdd88a0000 - 0x00007ffdd8c71000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffdd86c0000 - 0x00007ffdd87d1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffdbddb0000 - 0x00007ffdbddc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffdbd740000 - 0x00007ffdbd75d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffdda8b0000 - 0x00007ffddaa61000 	C:\Windows\System32\USER32.dll
0x00007ffdd8520000 - 0x00007ffdd8546000 	C:\Windows\System32\win32u.dll
0x00007ffdd8c80000 - 0x00007ffdd8ca9000 	C:\Windows\System32\GDI32.dll
0x00007ffdd8400000 - 0x00007ffdd851b000 	C:\Windows\System32\gdi32full.dll
0x00007ffdd80f0000 - 0x00007ffdd818a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffdc0e00000 - 0x00007ffdc1092000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffdd9de0000 - 0x00007ffdd9e87000 	C:\Windows\System32\msvcrt.dll
0x00007ffdda7a0000 - 0x00007ffdda7d1000 	C:\Windows\System32\IMM32.DLL
0x00007ffdc1410000 - 0x00007ffdc141c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffd1cfd0000 - 0x00007ffd1d05d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffd16270000 - 0x00007ffd16ee0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffdd9900000 - 0x00007ffdd99b1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffdd9830000 - 0x00007ffdd98d7000 	C:\Windows\System32\sechost.dll
0x00007ffdd8290000 - 0x00007ffdd82b8000 	C:\Windows\System32\bcrypt.dll
0x00007ffddac20000 - 0x00007ffddad34000 	C:\Windows\System32\RPCRT4.dll
0x00007ffdda580000 - 0x00007ffdda5f1000 	C:\Windows\System32\WS2_32.dll
0x00007ffdd7fc0000 - 0x00007ffdd800d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffdcdc20000 - 0x00007ffdcdc54000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffdc96e0000 - 0x00007ffdc96ea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffdd7fa0000 - 0x00007ffdd7fb3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffdd7100000 - 0x00007ffdd7118000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffdc0230000 - 0x00007ffdc023a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffdd20e0000 - 0x00007ffdd2312000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffdd99c0000 - 0x00007ffdd9d50000 	C:\Windows\System32\combase.dll
0x00007ffdda600000 - 0x00007ffdda6d7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdb8520000 - 0x00007ffdb8552000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffdd8190000 - 0x00007ffdd820b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffdb4de0000 - 0x00007ffdb4dee000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffdb0030000 - 0x00007ffdb0055000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffd1cef0000 - 0x00007ffd1cfc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffdd8d10000 - 0x00007ffdd9598000 	C:\Windows\System32\SHELL32.dll
0x00007ffdd82c0000 - 0x00007ffdd83ff000 	C:\Windows\System32\wintypes.dll
0x00007ffdd6000000 - 0x00007ffdd690d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffddad80000 - 0x00007ffddae8a000 	C:\Windows\System32\SHCORE.dll
0x00007ffddaf10000 - 0x00007ffddaf6e000 	C:\Windows\System32\shlwapi.dll
0x00007ffdd8020000 - 0x00007ffdd804b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdab940000 - 0x00007ffdab958000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffdbce60000 - 0x00007ffdbce7a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffdcd8e0000 - 0x00007ffdcda0c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffdd7560000 - 0x00007ffdd75ca000 	C:\Windows\system32\mswsock.dll
0x00007ffdac240000 - 0x00007ffdac256000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffdbd730000 - 0x00007ffdbd740000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffda9480000 - 0x00007ffda94a7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000059e60000 - 0x0000000059ed3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdbc7c0000 - 0x00007ffdbc7ca000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffdb9b10000 - 0x00007ffdb9b1b000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffddad60000 - 0x00007ffddad68000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdd78b0000 - 0x00007ffdd78cb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffdd7060000 - 0x00007ffdd7097000 	C:\Windows\system32\rsaenh.dll
0x00007ffdd7600000 - 0x00007ffdd7628000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffdd7890000 - 0x00007ffdd789c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffdd6b70000 - 0x00007ffdd6b9d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffdda6e0000 - 0x00007ffdda6e9000 	C:\Windows\System32\NSI.dll
0x00007ffdcd7e0000 - 0x00007ffdcd7f9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdcd7a0000 - 0x00007ffdcd7bf000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffdd6be0000 - 0x00007ffdd6ce2000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffdb5dc0000 - 0x00007ffdb5dc9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffdb4630000 - 0x00007ffdb463e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffdd8550000 - 0x00007ffdd86b6000 	C:\Windows\System32\CRYPT32.dll
0x00007ffdd79c0000 - 0x00007ffdd79ed000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffdd7980000 - 0x00007ffdd79b7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffdbec10000 - 0x00007ffdbec18000 	C:\Windows\system32\wshunix.dll
0x0000000059d60000 - 0x0000000059dd3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform3671749702568290337dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform3671749702568290337dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 7:52 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (234M free)
TotalPageFile size 25066M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 174M, peak: 220M
current process commit charge ("private bytes"): 285M, peak: 292M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
