#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 610336 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:191), pid=3044, tid=24652
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Sat Jun 28 15:14:37 2025 W. Central Africa Standard Time elapsed time: 220.919076 seconds (0d 0h 3m 40s)

---------------  T H R E A D  ---------------

Current thread (0x000001d37fe401d0):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24652, stack(0x000000bae6500000,0x000000bae6600000)]


Current CompileTask:
C2: 220919 6991       4       java.util.regex.Pattern::compile (500 bytes)

Stack: [0x000000bae6500000,0x000000bae6600000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0xad0a4]
V  [jvm.dll+0xad73c]
V  [jvm.dll+0x2b09af]
V  [jvm.dll+0x58d829]
V  [jvm.dll+0x223592]
V  [jvm.dll+0x22398f]
V  [jvm.dll+0x21c740]
V  [jvm.dll+0x21986b]
V  [jvm.dll+0x1a53e6]
V  [jvm.dll+0x229faa]
V  [jvm.dll+0x2280fb]
V  [jvm.dll+0x7f3508]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001d34b5c1320, length=51, elements={
0x000001d369a45530, 0x000001d37fe291f0, 0x000001d37fe2a070, 0x000001d37fe3a060,
0x000001d37fe3dca0, 0x000001d37fe3e660, 0x000001d37fe3f020, 0x000001d37fe401d0,
0x000001d34a00a290, 0x000001d34a015460, 0x000001d34a0a33d0, 0x000001d34a1ed5a0,
0x000001d34a27b640, 0x000001d34a2e67e0, 0x000001d34abf34f0, 0x000001d34ad8d7b0,
0x000001d34b290430, 0x000001d34ad6a410, 0x000001d34ad6ae30, 0x000001d34ad6a920,
0x000001d34ad6b340, 0x000001d34ad6b850, 0x000001d34ad69f00, 0x000001d34ad685b0,
0x000001d34ad680a0, 0x000001d34ad68ac0, 0x000001d34ad694e0, 0x000001d34ad699f0,
0x000001d34bacf4c0, 0x000001d34bad1830, 0x000001d34bad0900, 0x000001d34bad1d40,
0x000001d34bad03f0, 0x000001d34bad4fe0, 0x000001d34bad0e10, 0x000001d34bad4ad0,
0x000001d34bad45c0, 0x000001d34bad2c70, 0x000001d34bad3690, 0x000001d34baceaa0,
0x000001d34bad3ba0, 0x000001d34bace080, 0x000001d34bad3180, 0x000001d34bacfee0,
0x000001d34bad40b0, 0x000001d34bad1320, 0x000001d34bad5a00, 0x000001d34c159e00,
0x000001d34c15a820, 0x000001d34c1593e0, 0x000001d34c157a90
}
_to_delete_list=0x000001d34cbb3880, length=48, elements={
0x000001d369a45530, 0x000001d37fe291f0, 0x000001d37fe2a070, 0x000001d37fe3a060,
0x000001d37fe3dca0, 0x000001d37fe3e660, 0x000001d37fe3f020, 0x000001d37fe401d0,
0x000001d34a00a290, 0x000001d34a015460, 0x000001d34a0a33d0, 0x000001d34a1ed5a0,
0x000001d34a27b640, 0x000001d34a2e67e0, 0x000001d34abf34f0, 0x000001d34ad8d7b0,
0x000001d34b290430, 0x000001d34ad6a410, 0x000001d34ad6ae30, 0x000001d34ad6a920,
0x000001d34ad6b340, 0x000001d34ad6b850, 0x000001d34ad69f00, 0x000001d34ad685b0,
0x000001d34ad680a0, 0x000001d34ad68ac0, 0x000001d34ad694e0, 0x000001d34ad699f0,
0x000001d34bacf4c0, 0x000001d34bad1830, 0x000001d34bad0900, 0x000001d34bad1d40,
0x000001d34bad03f0, 0x000001d34bad4fe0, 0x000001d34bad0e10, 0x000001d34bad4ad0,
0x000001d34bad45c0, 0x000001d34bad2c70, 0x000001d34bad3690, 0x000001d34baceaa0,
0x000001d34bad3ba0, 0x000001d34bace080, 0x000001d34bad3180, 0x000001d34bacfee0,
0x000001d34bad40b0, 0x000001d34bad1320, 0x000001d34bad5a00, 0x000001d34c159e00
}

Java Threads: ( => current thread )
  0x000001d369a45530 JavaThread "main" [_thread_blocked, id=24668, stack(0x000000bae5800000,0x000000bae5900000)]
  0x000001d37fe291f0 JavaThread "Reference Handler" daemon [_thread_blocked, id=15632, stack(0x000000bae5f00000,0x000000bae6000000)]
  0x000001d37fe2a070 JavaThread "Finalizer" daemon [_thread_blocked, id=4300, stack(0x000000bae6000000,0x000000bae6100000)]
  0x000001d37fe3a060 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=24628, stack(0x000000bae6100000,0x000000bae6200000)]
  0x000001d37fe3dca0 JavaThread "Attach Listener" daemon [_thread_blocked, id=24592, stack(0x000000bae6200000,0x000000bae6300000)]
  0x000001d37fe3e660 JavaThread "Service Thread" daemon [_thread_blocked, id=24584, stack(0x000000bae6300000,0x000000bae6400000)]
  0x000001d37fe3f020 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=24588, stack(0x000000bae6400000,0x000000bae6500000)]
=>0x000001d37fe401d0 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=24652, stack(0x000000bae6500000,0x000000bae6600000)]
  0x000001d34a00a290 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=24796, stack(0x000000bae6600000,0x000000bae6700000)]
  0x000001d34a015460 JavaThread "Sweeper thread" daemon [_thread_blocked, id=23440, stack(0x000000bae6700000,0x000000bae6800000)]
  0x000001d34a0a33d0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=12616, stack(0x000000bae6800000,0x000000bae6900000)]
  0x000001d34a1ed5a0 JavaThread "Notification Thread" daemon [_thread_blocked, id=19320, stack(0x000000bae6900000,0x000000bae6a00000)]
  0x000001d34a27b640 JavaThread "Daemon health stats" [_thread_blocked, id=11876, stack(0x000000bae6e00000,0x000000bae6f00000)]
  0x000001d34a2e67e0 JavaThread "Incoming local TCP Connector on port 56933" [_thread_in_native, id=5056, stack(0x000000bae6f00000,0x000000bae7000000)]
  0x000001d34abf34f0 JavaThread "Daemon periodic checks" [_thread_blocked, id=17516, stack(0x000000bae7000000,0x000000bae7100000)]
  0x000001d34ad8d7b0 JavaThread "Daemon" [_thread_blocked, id=25228, stack(0x000000bae7100000,0x000000bae7200000)]
  0x000001d34b290430 JavaThread "Handler for socket connection from /127.0.0.1:56933 to /127.0.0.1:56935" [_thread_in_native, id=25092, stack(0x000000bae7200000,0x000000bae7300000)]
  0x000001d34ad6a410 JavaThread "Cancel handler" [_thread_blocked, id=3708, stack(0x000000bae7300000,0x000000bae7400000)]
  0x000001d34ad6ae30 JavaThread "Daemon worker" [_thread_in_vm, id=7644, stack(0x000000bae7400000,0x000000bae7500000)]
  0x000001d34ad6a920 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56933 to /127.0.0.1:56935" [_thread_blocked, id=10444, stack(0x000000bae7500000,0x000000bae7600000)]
  0x000001d34ad6b340 JavaThread "Stdin handler" [_thread_blocked, id=7256, stack(0x000000bae7600000,0x000000bae7700000)]
  0x000001d34ad6b850 JavaThread "Daemon client event forwarder" [_thread_blocked, id=23392, stack(0x000000bae7700000,0x000000bae7800000)]
  0x000001d34ad69f00 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=24760, stack(0x000000bae7800000,0x000000bae7900000)]
  0x000001d34ad685b0 JavaThread "File lock request listener" [_thread_in_native, id=16992, stack(0x000000bae7900000,0x000000bae7a00000)]
  0x000001d34ad680a0 JavaThread "File lock release action executor" [_thread_blocked, id=19032, stack(0x000000bae7a00000,0x000000bae7b00000)]
  0x000001d34ad68ac0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=23656, stack(0x000000bae7b00000,0x000000bae7c00000)]
  0x000001d34ad694e0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=25324, stack(0x000000bae7c00000,0x000000bae7d00000)]
  0x000001d34ad699f0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=21548, stack(0x000000bae7d00000,0x000000bae7e00000)]
  0x000001d34bacf4c0 JavaThread "File watcher server" daemon [_thread_in_native, id=23932, stack(0x000000bae7e00000,0x000000bae7f00000)]
  0x000001d34bad1830 JavaThread "File watcher consumer" daemon [_thread_blocked, id=25032, stack(0x000000bae7f00000,0x000000bae8000000)]
  0x000001d34bad0900 JavaThread "jar transforms" [_thread_blocked, id=24648, stack(0x000000bae5600000,0x000000bae5700000)]
  0x000001d34bad1d40 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=24604, stack(0x000000bae5700000,0x000000bae5800000)]
  0x000001d34bad03f0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=20712, stack(0x000000bae8300000,0x000000bae8400000)]
  0x000001d34bad4fe0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=23824, stack(0x000000bae8400000,0x000000bae8500000)]
  0x000001d34bad0e10 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=16800, stack(0x000000bae8500000,0x000000bae8600000)]
  0x000001d34bad4ad0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=25376, stack(0x000000bae8600000,0x000000bae8700000)]
  0x000001d34bad45c0 JavaThread "Unconstrained build operations" [_thread_blocked, id=22212, stack(0x000000bae8700000,0x000000bae8800000)]
  0x000001d34bad2c70 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=24204, stack(0x000000bae8800000,0x000000bae8900000)]
  0x000001d34bad3690 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=15700, stack(0x000000bae8900000,0x000000bae8a00000)]
  0x000001d34baceaa0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=23640, stack(0x000000bae8a00000,0x000000bae8b00000)]
  0x000001d34bad3ba0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=4288, stack(0x000000bae8b00000,0x000000bae8c00000)]
  0x000001d34bace080 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=25508, stack(0x000000bae8c00000,0x000000bae8d00000)]
  0x000001d34bad3180 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=26108, stack(0x000000bae8000000,0x000000bae8100000)]
  0x000001d34bacfee0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=26112, stack(0x000000bae8100000,0x000000bae8200000)]
  0x000001d34bad40b0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=26116, stack(0x000000bae8d00000,0x000000bae8e00000)]
  0x000001d34bad1320 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=26408, stack(0x000000bae8e00000,0x000000bae8f00000)]
  0x000001d34bad5a00 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=26412, stack(0x000000bae8f00000,0x000000bae9000000)]
  0x000001d34c159e00 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=26416, stack(0x000000bae9000000,0x000000bae9100000)]
  0x000001d34c15a820 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=26464, stack(0x000000bae9100000,0x000000bae9200000)]
  0x000001d34c1593e0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=26468, stack(0x000000bae9200000,0x000000bae9300000)]
  0x000001d34c157a90 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=26472, stack(0x000000bae9300000,0x000000bae9400000)]

Other Threads:
  0x000001d37fe20e70 VMThread "VM Thread" [stack: 0x000000bae5e00000,0x000000bae5f00000] [id=16316]
  0x000001d34a1afde0 WatcherThread [stack: 0x000000bae6a00000,0x000000bae6b00000] [id=9652]
  0x000001d369acf0c0 GCTaskThread "GC Thread#0" [stack: 0x000000bae5900000,0x000000bae5a00000] [id=21728]
  0x000001d34a1f4e70 GCTaskThread "GC Thread#1" [stack: 0x000000bae6b00000,0x000000bae6c00000] [id=25140]
  0x000001d34a1f5130 GCTaskThread "GC Thread#2" [stack: 0x000000bae6c00000,0x000000bae6d00000] [id=25436]
  0x000001d34a11fb30 GCTaskThread "GC Thread#3" [stack: 0x000000bae6d00000,0x000000bae6e00000] [id=19840]
  0x000001d36790cfe0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000bae5a00000,0x000000bae5b00000] [id=22508]
  0x000001d36790da00 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000bae5b00000,0x000000bae5c00000] [id=16544]
  0x000001d369aee790 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000bae5c00000,0x000000bae5d00000] [id=21088]
  0x000001d34b624c00 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000bae8200000,0x000000bae8300000] [id=14764]
  0x000001d37fcdf3d0 ConcurrentGCThread "G1 Service" [stack: 0x000000bae5d00000,0x000000bae5e00000] [id=24988]

Threads with active compile tasks:
C2 CompilerThread0   220989 6991       4       java.util.regex.Pattern::compile (500 bytes)

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000001d369a410d0] Metaspace_lock - owner thread: 0x000001d34ad6ae30

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000001d311000000-0x000001d311bb0000-0x000001d311bb0000), size 12255232, SharedBaseAddress: 0x000001d311000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000001d312000000-0x000001d346000000, reserved size: 872415232
Narrow klass base: 0x000001d311000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 98304K, used 40201K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 2 survivors (8192K)
 Metaspace       used 53730K, committed 54272K, reserved 917504K
  class space    used 7529K, committed 7808K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HS|  |TAMS 0x0000000680400000, 0x0000000680000000| Complete 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%| O|  |TAMS 0x0000000680800000, 0x0000000680400000| Untracked 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000681e7f000, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x000000068227f000, 0x0000000682400000| 62%| O|  |TAMS 0x0000000682000000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682400000, 0x0000000682800000|  0%| F|  |TAMS 0x0000000682400000, 0x0000000682400000| Untracked 
|  10|0x0000000682800000, 0x0000000682800000, 0x0000000682c00000|  0%| F|  |TAMS 0x0000000682800000, 0x0000000682800000| Untracked 
|  11|0x0000000682c00000, 0x0000000682c00000, 0x0000000683000000|  0%| F|  |TAMS 0x0000000682c00000, 0x0000000682c00000| Untracked 
|  12|0x0000000683000000, 0x0000000683000000, 0x0000000683400000|  0%| F|  |TAMS 0x0000000683000000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x00000006834c37b0, 0x0000000683800000| 19%| S|CS|TAMS 0x0000000683400000, 0x0000000683400000| Complete 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| S|CS|TAMS 0x0000000683800000, 0x0000000683800000| Complete 
|  15|0x0000000683c00000, 0x0000000683c00000, 0x0000000684000000|  0%| F|  |TAMS 0x0000000683c00000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684000000, 0x0000000684400000|  0%| F|  |TAMS 0x0000000684000000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684400000, 0x0000000684800000|  0%| F|  |TAMS 0x0000000684400000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684800000, 0x0000000684c00000|  0%| F|  |TAMS 0x0000000684800000, 0x0000000684800000| Untracked 
|  19|0x0000000684c00000, 0x0000000684c00000, 0x0000000685000000|  0%| F|  |TAMS 0x0000000684c00000, 0x0000000684c00000| Untracked 
|  20|0x0000000685000000, 0x0000000685000000, 0x0000000685400000|  0%| F|  |TAMS 0x0000000685000000, 0x0000000685000000| Untracked 
|  21|0x0000000685400000, 0x0000000685400000, 0x0000000685800000|  0%| F|  |TAMS 0x0000000685400000, 0x0000000685400000| Untracked 
|  22|0x0000000685800000, 0x0000000685800000, 0x0000000685c00000|  0%| F|  |TAMS 0x0000000685800000, 0x0000000685800000| Untracked 
|  31|0x0000000687c00000, 0x0000000687d5f0d8, 0x0000000688000000| 34%| E|  |TAMS 0x0000000687c00000, 0x0000000687c00000| Complete 

Card table byte_map: [0x000001d372160000,0x000001d372d60000] _byte_map_base: 0x000001d36ed60000

Marking Bits (Prev, Next): (CMBitMap*) 0x000001d369acf6e0, (CMBitMap*) 0x000001d369acf720
 Prev Bits: [0x000001d373960000, 0x000001d379960000)
 Next Bits: [0x000001d379960000, 0x000001d37f960000)

Polling page: 0x000001d367ab0000

Metaspace:

Usage:
  Non-class:     45.12 MB used.
      Class:      7.35 MB used.
       Both:     52.47 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      45.38 MB ( 71%) committed,  1 nodes.
      Class space:      832.00 MB reserved,       7.62 MB ( <1%) committed,  1 nodes.
             Both:      896.00 MB reserved,      53.00 MB (  6%) committed. 

Chunk freelists:
   Non-Class:  1.86 MB
       Class:  8.24 MB
        Both:  10.10 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.88 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 716.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 848.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 2877.
num_chunk_merges: 6.
num_chunk_splits: 1925.
num_chunks_enlarged: 1324.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3380Kb max_used=3380Kb free=116619Kb
 bounds [0x000001d307ad0000, 0x000001d307e20000, 0x000001d30f000000]
CodeHeap 'profiled nmethods': size=120000Kb used=12099Kb max_used=12099Kb free=107900Kb
 bounds [0x000001d300000000, 0x000001d300be0000, 0x000001d307530000]
CodeHeap 'non-nmethods': size=5760Kb used=2347Kb max_used=2422Kb free=3412Kb
 bounds [0x000001d307530000, 0x000001d3077a0000, 0x000001d307ad0000]
 total_blobs=7075 nmethods=6206 adapters=781
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 220.611 Thread 0x000001d34a00a290 6988       3       java.security.ProtectionDomain$Key::<init> (5 bytes)
Event: 220.611 Thread 0x000001d34a00a290 nmethod 6988 0x000001d300bca310 code [0x000001d300bca4a0, 0x000001d300bca5f8]
Event: 220.616 Thread 0x000001d37fe401d0 nmethod 6979 0x000001d307e19990 code [0x000001d307e19c00, 0x000001d307e1b470]
Event: 220.617 Thread 0x000001d37fe401d0 6986       4       java.lang.ThreadLocal$ThreadLocalMap::set (133 bytes)
Event: 220.632 Thread 0x000001d37fe401d0 nmethod 6986 0x000001d307e1c490 code [0x000001d307e1c640, 0x000001d307e1ce18]
Event: 220.765 Thread 0x000001d34a00a290 6989       3       java.util.TreeMap::<init> (20 bytes)
Event: 220.765 Thread 0x000001d34a00a290 nmethod 6989 0x000001d300bca690 code [0x000001d300bca840, 0x000001d300bcaa58]
Event: 220.819 Thread 0x000001d34a00a290 6990       3       java.lang.String::replaceAll (13 bytes)
Event: 220.819 Thread 0x000001d37fe401d0 6991       4       java.util.regex.Pattern::compile (500 bytes)
Event: 220.819 Thread 0x000001d34a00a290 nmethod 6990 0x000001d300bcab10 code [0x000001d300bcace0, 0x000001d300bcaf68]
Event: 220.819 Thread 0x000001d34a00a290 6992       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$151/0x000001d3121c7788::emit (25 bytes)
Event: 220.819 Thread 0x000001d34a00a290 nmethod 6992 0x000001d300bcb090 code [0x000001d300bcb240, 0x000001d300bcb3a8]
Event: 220.819 Thread 0x000001d34a00a290 6993       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::lambda$addLazyGetter$15 (17 bytes)
Event: 220.820 Thread 0x000001d34a00a290 nmethod 6993 0x000001d300bcb490 code [0x000001d300bcb640, 0x000001d300bcb808]
Event: 220.820 Thread 0x000001d34a00a290 6994       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$16::<init> (144 bytes)
Event: 220.822 Thread 0x000001d34a00a290 nmethod 6994 0x000001d300bcb910 code [0x000001d300bcbce0, 0x000001d300bce298]
Event: 220.822 Thread 0x000001d34a00a290 6996       3       com.google.common.collect.ImmutableList::copyIntoArray (36 bytes)
Event: 220.823 Thread 0x000001d34a00a290 nmethod 6996 0x000001d300bced10 code [0x000001d300bceee0, 0x000001d300bcf418]
Event: 220.823 Thread 0x000001d34a00a290 6995       3       org.gradle.model.internal.asm.BytecodeFragment$1::emit (1 bytes)
Event: 220.823 Thread 0x000001d34a00a290 nmethod 6995 0x000001d300bcf590 code [0x000001d300bcf720, 0x000001d300bcf838]

GC Heap History (20 events):
Event: 86.688 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 98304K, used 72689K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 29406K, committed 29824K, reserved 917504K
  class space    used 4082K, committed 4288K, reserved 851968K
}
Event: 86.706 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 98304K, used 27817K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 29406K, committed 29824K, reserved 917504K
  class space    used 4082K, committed 4288K, reserved 851968K
}
Event: 86.899 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 98304K, used 31913K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 2 survivors (8192K)
 Metaspace       used 29448K, committed 29888K, reserved 917504K
  class space    used 4082K, committed 4288K, reserved 851968K
}
Event: 86.932 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 98304K, used 28103K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 29448K, committed 29888K, reserved 917504K
  class space    used 4082K, committed 4288K, reserved 851968K
}
Event: 101.255 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 98304K, used 64967K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 32863K, committed 33280K, reserved 917504K
  class space    used 4541K, committed 4736K, reserved 851968K
}
Event: 101.270 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 98304K, used 30888K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 32863K, committed 33280K, reserved 917504K
  class space    used 4541K, committed 4736K, reserved 851968K
}
Event: 109.670 GC heap before
{Heap before GC invocations=9 (full 0):
 garbage-first heap   total 98304K, used 55464K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 35599K, committed 35968K, reserved 917504K
  class space    used 4948K, committed 5120K, reserved 851968K
}
Event: 109.680 GC heap after
{Heap after GC invocations=10 (full 0):
 garbage-first heap   total 98304K, used 32352K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 35599K, committed 35968K, reserved 917504K
  class space    used 4948K, committed 5120K, reserved 851968K
}
Event: 120.119 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 98304K, used 69216K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 38114K, committed 38592K, reserved 917504K
  class space    used 5224K, committed 5440K, reserved 851968K
}
Event: 120.129 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 98304K, used 33218K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 38114K, committed 38592K, reserved 917504K
  class space    used 5224K, committed 5440K, reserved 851968K
}
Event: 135.575 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 98304K, used 74178K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 41402K, committed 41856K, reserved 917504K
  class space    used 5755K, committed 5952K, reserved 851968K
}
Event: 135.581 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 98304K, used 34142K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 41402K, committed 41856K, reserved 917504K
  class space    used 5755K, committed 5952K, reserved 851968K
}
Event: 156.970 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 98304K, used 75102K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 1 survivors (4096K)
 Metaspace       used 45196K, committed 45696K, reserved 917504K
  class space    used 6329K, committed 6528K, reserved 851968K
}
Event: 156.982 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 98304K, used 35554K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 45196K, committed 45696K, reserved 917504K
  class space    used 6329K, committed 6528K, reserved 851968K
}
Event: 177.023 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 98304K, used 72418K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 49633K, committed 50240K, reserved 917504K
  class space    used 6939K, committed 7232K, reserved 851968K
}
Event: 177.043 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 98304K, used 36843K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 49633K, committed 50240K, reserved 917504K
  class space    used 6939K, committed 7232K, reserved 851968K
}
Event: 205.551 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 98304K, used 73707K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 52803K, committed 53376K, reserved 917504K
  class space    used 7431K, committed 7680K, reserved 851968K
}
Event: 205.570 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 98304K, used 38731K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 52803K, committed 53376K, reserved 917504K
  class space    used 7431K, committed 7680K, reserved 851968K
}
Event: 220.773 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 98304K, used 75595K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 11 young (45056K), 2 survivors (8192K)
 Metaspace       used 53594K, committed 54144K, reserved 917504K
  class space    used 7502K, committed 7744K, reserved 851968K
}
Event: 220.804 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 98304K, used 40201K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 53594K, committed 54144K, reserved 917504K
  class space    used 7502K, committed 7744K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.017 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.093 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 1.768 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 2.170 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 2.186 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 2.188 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 2.438 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 9.217 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 13.416 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 14.945 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 15.380 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 42.239 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 42.621 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 45.858 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 48.847 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 57.289 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform15902693733299168289dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 213.936 Thread 0x000001d34ad68ac0 DEOPT PACKING pc=0x000001d30052ecd4 sp=0x000000bae7bfdfc0
Event: 213.936 Thread 0x000001d34ad68ac0 DEOPT UNPACKING pc=0x000001d307587143 sp=0x000000bae7bfd480 mode 0
Event: 214.087 Thread 0x000001d34bad5a00 DEOPT PACKING pc=0x000001d30043b574 sp=0x000000bae8ffc930
Event: 214.087 Thread 0x000001d34bad5a00 DEOPT UNPACKING pc=0x000001d307587143 sp=0x000000bae8ffbeb0 mode 0
Event: 215.452 Thread 0x000001d34ad68ac0 DEOPT PACKING pc=0x000001d30001e4f1 sp=0x000000bae7bfe380
Event: 215.452 Thread 0x000001d34ad68ac0 DEOPT UNPACKING pc=0x000001d307587143 sp=0x000000bae7bfd820 mode 0
Event: 215.713 Thread 0x000001d34c1593e0 DEOPT PACKING pc=0x000001d30043b574 sp=0x000000bae92fcb30
Event: 215.715 Thread 0x000001d34c1593e0 DEOPT UNPACKING pc=0x000001d307587143 sp=0x000000bae92fc0b0 mode 0
Event: 217.070 Thread 0x000001d34ad68ac0 DEOPT PACKING pc=0x000001d30046cbc3 sp=0x000000bae7bfe3a0
Event: 217.070 Thread 0x000001d34ad68ac0 DEOPT UNPACKING pc=0x000001d307587143 sp=0x000000bae7bfd828 mode 0
Event: 217.197 Thread 0x000001d34c157a90 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d307bd0270 relative=0x0000000000000150
Event: 217.197 Thread 0x000001d34c157a90 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d307bd0270 method=java.util.concurrent.ConcurrentHashMap.addCount(JI)V @ 34 c2
Event: 217.197 Thread 0x000001d34c157a90 DEOPT PACKING pc=0x000001d307bd0270 sp=0x000000bae93fd580
Event: 217.197 Thread 0x000001d34c157a90 DEOPT UNPACKING pc=0x000001d3075869a3 sp=0x000000bae93fd4c0 mode 2
Event: 217.200 Thread 0x000001d34c157a90 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001d307b8c5e8 relative=0x00000000000010c8
Event: 217.200 Thread 0x000001d34c157a90 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001d307b8c5e8 method=java.util.concurrent.ConcurrentHashMap.addCount(JI)V @ 7 c2
Event: 217.200 Thread 0x000001d34c157a90 DEOPT PACKING pc=0x000001d307b8c5e8 sp=0x000000bae93fd5b0
Event: 217.200 Thread 0x000001d34c157a90 DEOPT UNPACKING pc=0x000001d3075869a3 sp=0x000000bae93fd470 mode 2
Event: 220.612 Thread 0x000001d34ad6ae30 DEOPT PACKING pc=0x000001d30044fddb sp=0x000000bae74f3ee0
Event: 220.612 Thread 0x000001d34ad6ae30 DEOPT UNPACKING pc=0x000001d307587143 sp=0x000000bae74f3400 mode 0

Classes loaded (20 events):
Event: 203.726 Loading class jdk/internal/logger/LoggerFinderLoader$TemporaryLoggerFinder
Event: 203.726 Loading class jdk/internal/logger/LoggerFinderLoader$TemporaryLoggerFinder done
Event: 203.730 Loading class sun/util/logging/internal/LoggingProviderImpl$JULWrapper
Event: 203.730 Loading class sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration
Event: 203.730 Loading class sun/util/logging/PlatformLogger$ConfigurableBridge$LoggerConfiguration done
Event: 203.730 Loading class sun/util/logging/internal/LoggingProviderImpl$JULWrapper done
Event: 203.730 Loading class java/io/ObjectInputFilter
Event: 203.731 Loading class java/io/ObjectInputFilter done
Event: 203.731 Loading class java/io/ObjectInputFilter$Config$BuiltinFilterFactory
Event: 203.731 Loading class java/io/ObjectInputFilter$Config$BuiltinFilterFactory done
Event: 203.731 Loading class jdk/internal/access/JavaObjectInputFilterAccess
Event: 203.731 Loading class jdk/internal/access/JavaObjectInputFilterAccess done
Event: 203.732 Loading class java/lang/System$Logger$Level
Event: 203.732 Loading class java/lang/System$Logger$Level done
Event: 203.733 Loading class jdk/internal/event/DeserializationEvent
Event: 203.733 Loading class jdk/internal/event/DeserializationEvent done
Event: 203.777 Loading class java/io/ObjectInputStream$FieldValues
Event: 203.778 Loading class java/io/ObjectInputStream$GetField
Event: 203.778 Loading class java/io/ObjectInputStream$GetField done
Event: 203.778 Loading class java/io/ObjectInputStream$FieldValues done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 141.490 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684fb26f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000684fb26f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 147.414 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x000000068447cc30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000068447cc30) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 168.394 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/ClassNotFoundException'{0x0000000684ab4dd0}: sun/misc/SharedSecrets> (0x0000000684ab4dd0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 170.188 Thread 0x000001d34ad6ae30 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684674738}> (0x0000000684674738) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 170.188 Thread 0x000001d34ad6ae30 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684674a68}> (0x0000000684674a68) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 170.188 Thread 0x000001d34ad6ae30 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684677c50}> (0x0000000684677c50) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 170.188 Thread 0x000001d34ad6ae30 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684678070}> (0x0000000684678070) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 170.192 Thread 0x000001d34ad6ae30 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684688620}> (0x0000000684688620) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 170.192 Thread 0x000001d34ad6ae30 Exception <a 'sun/nio/fs/WindowsException'{0x0000000684688a40}> (0x0000000684688a40) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 516]
Event: 175.075 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683f1e9b0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000683f1e9b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 184.140 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684dac600}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000684dac600) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 186.119 Thread 0x000001d34ad6ae30 Implicit null exception at 0x000001d307af9606 to 0x000001d307af99bc
Event: 203.539 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x000000068438d798}: static Lorg/gradle/api/internal/catalog/DefaultVersionCatalog;.<clinit>()V> (0x000000068438d798) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.553 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x00000006843ea500}: static [Ljava/lang/Object;.<clinit>()V> (0x00000006843ea500) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.561 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c2d498}: static Lorg/gradle/api/internal/catalog/DependencyModel;.<clinit>()V> (0x0000000683c2d498) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.561 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c313e0}: static Lorg/gradle/api/internal/catalog/AbstractContextAwareModel;.<clinit>()V> (0x0000000683c313e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.564 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683c4ec10}: static Lorg/gradle/api/internal/artifacts/dependencies/AbstractVersionConstraint;.<clinit>()V> (0x0000000683c4ec10) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.698 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683d1e160}: static Lorg/gradle/api/internal/catalog/PluginModel;.<clinit>()V> (0x0000000683d1e160) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.704 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683d39438}: static Lorg/gradle/api/internal/catalog/VersionModel;.<clinit>()V> (0x0000000683d39438) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 203.711 Thread 0x000001d34ad6ae30 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683d53c20}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000683d53c20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]

VM Operations (20 events):
Event: 209.725 Executing VM operation: Cleanup
Event: 209.725 Executing VM operation: Cleanup done
Event: 210.736 Executing VM operation: Cleanup
Event: 210.736 Executing VM operation: Cleanup done
Event: 213.756 Executing VM operation: Cleanup
Event: 213.756 Executing VM operation: Cleanup done
Event: 214.761 Executing VM operation: Cleanup
Event: 214.969 Executing VM operation: Cleanup done
Event: 215.979 Executing VM operation: Cleanup
Event: 216.202 Executing VM operation: Cleanup done
Event: 217.209 Executing VM operation: Cleanup
Event: 217.368 Executing VM operation: Cleanup done
Event: 218.371 Executing VM operation: Cleanup
Event: 218.371 Executing VM operation: Cleanup done
Event: 219.385 Executing VM operation: Cleanup
Event: 219.385 Executing VM operation: Cleanup done
Event: 220.392 Executing VM operation: Cleanup
Event: 220.543 Executing VM operation: Cleanup done
Event: 220.773 Executing VM operation: G1CollectForAllocation
Event: 220.804 Executing VM operation: G1CollectForAllocation done

Events (20 events):
Event: 120.166 Thread 0x000001d34a015460 flushing nmethod 0x000001d3007fb390
Event: 120.166 Thread 0x000001d34a015460 flushing nmethod 0x000001d300849310
Event: 120.166 Thread 0x000001d34a015460 flushing nmethod 0x000001d300904810
Event: 140.521 Thread 0x000001d34bad45c0 Thread added: 0x000001d34bad45c0
Event: 140.521 Thread 0x000001d34bad2c70 Thread added: 0x000001d34bad2c70
Event: 142.198 Thread 0x000001d34bad3690 Thread added: 0x000001d34bad3690
Event: 142.346 Thread 0x000001d34baceaa0 Thread added: 0x000001d34baceaa0
Event: 147.417 Thread 0x000001d34bad3ba0 Thread added: 0x000001d34bad3ba0
Event: 147.420 Thread 0x000001d34bace080 Thread added: 0x000001d34bace080
Event: 207.823 Thread 0x000001d34ad29500 Thread added: 0x000001d34ad29500
Event: 208.018 Thread 0x000001d34ad29500 Thread exited: 0x000001d34ad29500
Event: 208.711 Thread 0x000001d34bad3180 Thread added: 0x000001d34bad3180
Event: 208.711 Thread 0x000001d34bacfee0 Thread added: 0x000001d34bacfee0
Event: 208.712 Thread 0x000001d34bad40b0 Thread added: 0x000001d34bad40b0
Event: 212.989 Thread 0x000001d34bad1320 Thread added: 0x000001d34bad1320
Event: 212.992 Thread 0x000001d34bad5a00 Thread added: 0x000001d34bad5a00
Event: 212.994 Thread 0x000001d34c159e00 Thread added: 0x000001d34c159e00
Event: 214.721 Thread 0x000001d34c15a820 Thread added: 0x000001d34c15a820
Event: 214.722 Thread 0x000001d34c1593e0 Thread added: 0x000001d34c1593e0
Event: 214.725 Thread 0x000001d34c157a90 Thread added: 0x000001d34c157a90


Dynamic libraries:
0x00007ff6cc7f0000 - 0x00007ff6cc7fe000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffddafb0000 - 0x00007ffddb1c7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffdd9760000 - 0x00007ffdd9824000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffdd88a0000 - 0x00007ffdd8c71000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffdd86c0000 - 0x00007ffdd87d1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffdbddb0000 - 0x00007ffdbddc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffdda8b0000 - 0x00007ffddaa61000 	C:\Windows\System32\USER32.dll
0x00007ffdd8520000 - 0x00007ffdd8546000 	C:\Windows\System32\win32u.dll
0x00007ffdd8c80000 - 0x00007ffdd8ca9000 	C:\Windows\System32\GDI32.dll
0x00007ffdd8400000 - 0x00007ffdd851b000 	C:\Windows\System32\gdi32full.dll
0x00007ffdd80f0000 - 0x00007ffdd818a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffdbd740000 - 0x00007ffdbd75d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffdc0e00000 - 0x00007ffdc1092000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffdd9de0000 - 0x00007ffdd9e87000 	C:\Windows\System32\msvcrt.dll
0x00007ffdda7a0000 - 0x00007ffdda7d1000 	C:\Windows\System32\IMM32.DLL
0x00007ffdc1410000 - 0x00007ffdc141c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffd1cfd0000 - 0x00007ffd1d05d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffd16270000 - 0x00007ffd16ee0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffdd9900000 - 0x00007ffdd99b1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffdd9830000 - 0x00007ffdd98d7000 	C:\Windows\System32\sechost.dll
0x00007ffdd8290000 - 0x00007ffdd82b8000 	C:\Windows\System32\bcrypt.dll
0x00007ffddac20000 - 0x00007ffddad34000 	C:\Windows\System32\RPCRT4.dll
0x00007ffdda580000 - 0x00007ffdda5f1000 	C:\Windows\System32\WS2_32.dll
0x00007ffdd7fc0000 - 0x00007ffdd800d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffdcdc20000 - 0x00007ffdcdc54000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffdc96e0000 - 0x00007ffdc96ea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffdd7fa0000 - 0x00007ffdd7fb3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffdd7100000 - 0x00007ffdd7118000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffdc0230000 - 0x00007ffdc023a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffdd20e0000 - 0x00007ffdd2312000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffdd99c0000 - 0x00007ffdd9d50000 	C:\Windows\System32\combase.dll
0x00007ffdda600000 - 0x00007ffdda6d7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdb8520000 - 0x00007ffdb8552000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffdd8190000 - 0x00007ffdd820b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffdb4de0000 - 0x00007ffdb4dee000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffdb0030000 - 0x00007ffdb0055000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffd1cef0000 - 0x00007ffd1cfc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffdd8d10000 - 0x00007ffdd9598000 	C:\Windows\System32\SHELL32.dll
0x00007ffdd82c0000 - 0x00007ffdd83ff000 	C:\Windows\System32\wintypes.dll
0x00007ffdd6000000 - 0x00007ffdd690d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffddad80000 - 0x00007ffddae8a000 	C:\Windows\System32\SHCORE.dll
0x00007ffddaf10000 - 0x00007ffddaf6e000 	C:\Windows\System32\shlwapi.dll
0x00007ffdd8020000 - 0x00007ffdd804b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdab940000 - 0x00007ffdab958000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffdbce60000 - 0x00007ffdbce7a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffdcd8e0000 - 0x00007ffdcda0c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffdd7560000 - 0x00007ffdd75ca000 	C:\Windows\system32\mswsock.dll
0x00007ffdac240000 - 0x00007ffdac256000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffdbd730000 - 0x00007ffdbd740000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffda9480000 - 0x00007ffda94a7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000059e60000 - 0x0000000059ed3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdbc7c0000 - 0x00007ffdbc7ca000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffdb9b10000 - 0x00007ffdb9b1b000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffddad60000 - 0x00007ffddad68000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdd78b0000 - 0x00007ffdd78cb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffdd7060000 - 0x00007ffdd7097000 	C:\Windows\system32\rsaenh.dll
0x00007ffdd7600000 - 0x00007ffdd7628000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffdd7890000 - 0x00007ffdd789c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffdd6b70000 - 0x00007ffdd6b9d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffdda6e0000 - 0x00007ffdda6e9000 	C:\Windows\System32\NSI.dll
0x00007ffdcd7e0000 - 0x00007ffdcd7f9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdcd7a0000 - 0x00007ffdcd7bf000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffdd6be0000 - 0x00007ffdd6ce2000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffdb5dc0000 - 0x00007ffdb5dc9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffdb4630000 - 0x00007ffdb463e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffdd8550000 - 0x00007ffdd86b6000 	C:\Windows\System32\CRYPT32.dll
0x00007ffdd79c0000 - 0x00007ffdd79ed000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffdd7980000 - 0x00007ffdd79b7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffdbec10000 - 0x00007ffdbec18000 	C:\Windows\system32\wshunix.dll
0x0000000059c60000 - 0x0000000059cd3000 	C:\Users\<USER>\AppData\Local\Temp\native-platform15902693733299168289dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform15902693733299168289dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 7:52 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (332M free)
TotalPageFile size 25066M (AvailPageFile size 0M)
current process WorkingSet (physical memory assigned to process): 185M, peak: 223M
current process commit charge ("private bytes"): 286M, peak: 293M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
