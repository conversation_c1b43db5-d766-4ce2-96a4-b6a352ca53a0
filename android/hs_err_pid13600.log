#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 65536 bytes. Error detail: Failed to commit metaspace.
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (virtualSpaceNode.cpp:110), pid=13600, tid=20512
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Sat Jun 28 15:15:05 2025 W. Central Africa Standard Time elapsed time: 255.470664 seconds (0d 0h 4m 15s)

---------------  T H R E A D  ---------------

Current thread (0x0000019d301d6300):  JavaThread "Daemon worker" [_thread_in_vm, id=20512, stack(0x0000000493b00000,0x0000000493c00000)]

Stack: [0x0000000493b00000,0x0000000493c00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0x8398ad]
V  [jvm.dll+0x62b25e]
V  [jvm.dll+0x1c16d0]
V  [jvm.dll+0x62db80]
V  [jvm.dll+0x62bc06]
V  [jvm.dll+0x246bdc]
V  [jvm.dll+0x6ddc3d]
V  [jvm.dll+0x6de61f]
V  [jvm.dll+0x377051]
V  [jvm.dll+0x417df2]
V  [jvm.dll+0x42bf6c]
C  [java.dll+0x1657]

Java frames: (J=compiled Java code, j=interpreted, Vv=VM code)
J 2927  java.lang.ClassLoader.defineClass0(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class; java.base@17.0.12 (0 bytes) @ 0x0000019d18551511 [0x0000019d18551420+0x00000000000000f1]
J 2926 c1 java.lang.System$2.defineClass(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class; java.base@17.0.12 (21 bytes) @ 0x0000019d10ea58ec [0x0000019d10ea5840+0x00000000000000ac]
J 2925 c1 java.lang.invoke.MethodHandles$Lookup$ClassDefiner.defineClass(ZLjava/lang/Object;)Ljava/lang/Class; java.base@17.0.12 (97 bytes) @ 0x0000019d10ea4d7c [0x0000019d10ea4220+0x0000000000000b5c]
j  java.lang.invoke.MethodHandles$Lookup$ClassDefiner.defineClass(Z)Ljava/lang/Class;+3 java.base@17.0.12
j  java.lang.invoke.MethodHandles$Lookup.defineClass([B)Ljava/lang/Class;+36 java.base@17.0.12
j  org.gradle.internal.classloader.ClassLoaderUtils$LookupClassDefiner.defineDecoratorClass(Ljava/lang/Class;Ljava/lang/ClassLoader;Ljava/lang/String;[B)Ljava/lang/Class;+22
j  org.gradle.internal.classloader.ClassLoaderUtils.defineDecorator(Ljava/lang/Class;Ljava/lang/ClassLoader;Ljava/lang/String;[B)Ljava/lang/Class;+7
j  org.gradle.model.internal.asm.AsmClassGenerator.define(Ljava/lang/ClassLoader;)Ljava/lang/Class;+16
j  org.gradle.model.internal.asm.AsmClassGenerator.define()Ljava/lang/Class;+8
j  org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl.generate()Ljava/lang/Class;+12
J 7039 c1 org.gradle.internal.instantiation.generator.AbstractClassGenerator.generateUnderLock(Ljava/lang/Class;)Lorg/gradle/internal/instantiation/generator/AbstractClassGenerator$GeneratedClassImpl; (704 bytes) @ 0x0000019d114a8d1c [0x0000019d114a3f20+0x0000000000004dfc]
j  org.gradle.internal.instantiation.generator.AbstractClassGenerator$$Lambda$127+0x0000019d32198450.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
J 4669 c1 org.gradle.cache.internal.DefaultCrossBuildInMemoryCacheFactory$AbstractCrossBuildInMemoryCache.get(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object; (65 bytes) @ 0x0000019d109d4104 [0x0000019d109d3f00+0x0000000000000204]
J 5404 c1 org.gradle.internal.instantiation.generator.AsmBackedClassGenerator.generate(Ljava/lang/Class;)Lorg/gradle/internal/instantiation/generator/ClassGenerator$GeneratedClass; (6 bytes) @ 0x0000019d10a1c47c [0x0000019d10a1c200+0x000000000000027c]
j  org.gradle.internal.instantiation.generator.Jsr330ConstructorSelector.lambda$forType$0(Ljava/lang/Class;)Lorg/gradle/internal/instantiation/generator/Jsr330ConstructorSelector$CachedConstructor;+9
j  org.gradle.internal.instantiation.generator.Jsr330ConstructorSelector$$Lambda$131+0x0000019d321af588.get()Ljava/lang/Object;+8
J 7112 c1 org.gradle.cache.Cache$$Lambda$84+0x0000019d32102470.apply(Ljava/lang/Object;)Ljava/lang/Object; (9 bytes) @ 0x0000019d114c6184 [0x0000019d114c6040+0x0000000000000144]
J 4669 c1 org.gradle.cache.internal.DefaultCrossBuildInMemoryCacheFactory$AbstractCrossBuildInMemoryCache.get(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object; (65 bytes) @ 0x0000019d109d4104 [0x0000019d109d3f00+0x0000000000000204]
J 6497 c1 org.gradle.cache.Cache.get(Ljava/lang/Object;Ljava/util/function/Supplier;)Ljava/lang/Object; (14 bytes) @ 0x0000019d113a1524 [0x0000019d113a0fa0+0x0000000000000584]
J 7180 c1 org.gradle.internal.instantiation.generator.Jsr330ConstructorSelector.forType(Ljava/lang/Class;)Lorg/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor; (32 bytes) @ 0x0000019d114db734 [0x0000019d114db240+0x00000000000004f4]
J 7212 c1 org.gradle.internal.instantiation.generator.Jsr330ConstructorSelector.forParams(Ljava/lang/Class;[Ljava/lang/Object;)Lorg/gradle/internal/instantiation/generator/ClassGenerator$GeneratedConstructor; (6 bytes) @ 0x0000019d114e45c4 [0x0000019d114e4540+0x0000000000000084]
J 4773 c1 org.gradle.internal.instantiation.generator.DependencyInjectingInstantiator.doCreate(Ljava/lang/Class;Lorg/gradle/api/Describable;[Ljava/lang/Object;)Ljava/lang/Object; (64 bytes) @ 0x0000019d10ae30ac [0x0000019d10ae2f80+0x000000000000012c]
J 4772 c1 org.gradle.internal.instantiation.generator.DependencyInjectingInstantiator.newInstance(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object; (8 bytes) @ 0x0000019d10db5544 [0x0000019d10db54c0+0x0000000000000084]
j  org.gradle.api.internal.model.DefaultObjectFactory.newInstance(Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;+6
j  org.jetbrains.kotlin.gradle.dsl.ToolchainSupport$Companion.createToolchain$kotlin_gradle_plugin_common(Lorg/gradle/api/Project;Lorg/jetbrains/kotlin/gradle/dsl/KotlinTopLevelExtensionConfig;)Lorg/jetbrains/kotlin/gradle/dsl/ToolchainSupport;+105
j  org.jetbrains.kotlin.gradle.dsl.KotlinTopLevelExtension.<init>(Lorg/gradle/api/Project;)V+27
j  org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtension.<init>(Lorg/gradle/api/Project;)V+8
j  org.jetbrains.kotlin.gradle.dsl.KotlinSingleTargetExtension.<init>(Lorg/gradle/api/Project;)V+8
j  org.jetbrains.kotlin.gradle.dsl.KotlinSingleJavaTargetExtension.<init>(Lorg/gradle/api/Project;)V+8
j  org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension.<init>(Lorg/gradle/api/Project;)V+8
j  org.jetbrains.kotlin.gradle.dsl.KotlinJvmProjectExtension_Decorated.<init>(Lorg/gradle/api/Project;)V+2
v  ~StubRoutines::call_stub
J 2206  jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Ljava/lang/reflect/Constructor;[Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.12 (0 bytes) @ 0x0000019d184be00e [0x0000019d184bdfa0+0x000000000000006e]
J 2205 c1 jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance([Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.12 (122 bytes) @ 0x0000019d10d17a54 [0x0000019d10d170e0+0x0000000000000974]
J 1430 c1 jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance([Ljava/lang/Object;)Ljava/lang/Object; java.base@17.0.12 (9 bytes) @ 0x0000019d10b8b1cc [0x0000019d10b8b0c0+0x000000000000010c]
J 1429 c1 java.lang.reflect.Constructor.newInstanceWithCaller([Ljava/lang/Object;ZLjava/lang/Class;)Ljava/lang/Object; java.base@17.0.12 (75 bytes) @ 0x0000019d10b8abe4 [0x0000019d10b8a980+0x0000000000000264]
J 5749 c1 org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$InvokeConstructorStrategy.newInstance(Lorg/gradle/internal/service/ServiceLookup;Lorg/gradle/internal/instantiation/InstanceGenerator;Lorg/gradle/api/Describable;[Ljava/lang/Object;)Ljava/lang/Object; (66 bytes) @ 0x0000019d11222a2c [0x0000019d112223e0+0x000000000000064c]
J 5768 c1 org.gradle.internal.instantiation.generator.AbstractClassGenerator$GeneratedClassImpl$GeneratedConstructorImpl.newInstance(Lorg/gradle/internal/service/ServiceLookup;Lorg/gradle/internal/instantiation/InstanceGenerator;Lorg/gradle/api/Describable;[Ljava/lang/Object;)Ljava/lang/Object; (15 bytes) @ 0x0000019d1122c0cc [0x0000019d1122bfc0+0x000000000000010c]
J 4773 c1 org.gradle.internal.instantiation.generator.DependencyInjectingInstantiator.doCreate(Ljava/lang/Class;Lorg/gradle/api/Describable;[Ljava/lang/Object;)Ljava/lang/Object; (64 bytes) @ 0x0000019d10ae3174 [0x0000019d10ae2f80+0x00000000000001f4]
j  org.gradle.internal.instantiation.generator.DependencyInjectingInstantiator.newInstanceWithDisplayName(Ljava/lang/Class;Lorg/gradle/api/Describable;[Ljava/lang/Object;)Ljava/lang/Object;+4
j  org.gradle.internal.extensibility.DefaultConvention.instantiate(Ljava/lang/Class;Ljava/lang/String;[Ljava/lang/Object;)Ljava/lang/Object;+12
j  org.gradle.internal.extensibility.DefaultConvention.create(Ljava/lang/String;Ljava/lang/Class;[Ljava/lang/Object;)Ljava/lang/Object;+4
j  org.jetbrains.kotlin.gradle.dsl.KotlinProjectExtensionKt.createKotlinExtension(Lorg/gradle/api/Project;Lkotlin/reflect/KClass;)Lorg/jetbrains/kotlin/gradle/dsl/KotlinTopLevelExtension;+34
j  org.jetbrains.kotlin.gradle.plugin.KotlinBasePluginWrapper.apply(Lorg/gradle/api/Project;)V+117
j  org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapper.apply(Lorg/gradle/api/Project;)V+12
j  org.jetbrains.kotlin.gradle.plugin.KotlinPluginWrapper.apply(Ljava/lang/Object;)V+5
j  org.gradle.api.internal.plugins.ImperativeOnlyPluginTarget.applyImperative(Ljava/lang/String;Lorg/gradle/api/Plugin;)V+13
j  org.gradle.api.internal.plugins.RuleBasedPluginTarget.applyImperative(Ljava/lang/String;Lorg/gradle/api/Plugin;)V+6
j  org.gradle.api.internal.plugins.DefaultPluginManager.addPlugin(Ljava/lang/Runnable;Lorg/gradle/api/internal/plugins/PluginImplementation;Ljava/lang/String;Ljava/lang/Class;)V+54
j  org.gradle.api.internal.plugins.DefaultPluginManager.access$100(Lorg/gradle/api/internal/plugins/DefaultPluginManager;Ljava/lang/Runnable;Lorg/gradle/api/internal/plugins/PluginImplementation;Ljava/lang/String;Ljava/lang/Class;)V+6
j  org.gradle.api.internal.plugins.DefaultPluginManager$AddPluginBuildOperation.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+20
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
J 6613 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000019d113d69bc [0x0000019d113d67c0+0x00000000000001fc]
J 6973 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000019d1148ae14 [0x0000019d1148ada0+0x0000000000000074]
J 6799 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000019d1142ca84 [0x0000019d1142bee0+0x0000000000000ba4]
J 6617 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000019d113da08c [0x0000019d113d9de0+0x00000000000002ac]
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.api.internal.plugins.DefaultPluginManager.lambda$doApply$0(Ljava/lang/Runnable;Lorg/gradle/api/internal/plugins/PluginImplementation;Ljava/lang/String;Ljava/lang/Class;Lorg/gradle/internal/code/UserCodeApplicationId;)V+20
j  org.gradle.api.internal.plugins.DefaultPluginManager$$Lambda$980+0x0000019d32667030.execute(Ljava/lang/Object;)V+24
j  org.gradle.internal.code.DefaultUserCodeApplicationContext.apply(Lorg/gradle/internal/code/UserCodeSource;Lorg/gradle/api/Action;)V+37
j  org.gradle.api.internal.plugins.DefaultPluginManager.doApply(Lorg/gradle/api/internal/plugins/PluginImplementation;)V+144
j  org.gradle.api.internal.plugins.DefaultPluginManager.apply(Lorg/gradle/api/internal/plugins/PluginImplementation;)V+2
j  org.gradle.plugin.use.resolve.internal.ClassPathPluginResolution.applyTo(Lorg/gradle/api/internal/plugins/PluginManagerInternal;)V+5
j  org.gradle.plugin.use.internal.DefaultPluginRequestApplicator$ApplyAction.apply(Lorg/gradle/api/internal/plugins/PluginManagerInternal;)V+21
j  org.gradle.plugin.use.internal.DefaultPluginRequestApplicator.lambda$applyPlugins$1(Lorg/gradle/api/internal/plugins/PluginManagerInternal;Lorg/gradle/plugin/use/internal/DefaultPluginRequestApplicator$ApplyAction;)V+2
j  org.gradle.plugin.use.internal.DefaultPluginRequestApplicator$$Lambda$979+0x0000019d32659ac0.accept(Ljava/lang/Object;)V+8
j  java.util.ArrayList.forEach(Ljava/util/function/Consumer;)V+46 java.base@17.0.12
j  org.gradle.plugin.use.internal.DefaultPluginRequestApplicator.applyPlugins(Lorg/gradle/plugin/management/internal/PluginRequests;Lorg/gradle/api/internal/initialization/ScriptHandlerInternal;Lorg/gradle/api/internal/plugins/PluginManagerInternal;Lorg/gradle/api/internal/initialization/ClassLoaderScope;)V+375
j  org.gradle.kotlin.dsl.provider.PluginRequestsHandler.handle(Lorg/gradle/plugin/management/internal/PluginRequests;Lorg/gradle/api/internal/initialization/ScriptHandlerInternal;Lorg/gradle/api/internal/plugins/PluginAwareInternal;Lorg/gradle/api/internal/initialization/ClassLoaderScope;)V+65
j  org.gradle.kotlin.dsl.provider.StandardKotlinScriptEvaluator$InterpreterHost.applyPluginsTo(Lorg/gradle/kotlin/dsl/support/KotlinScriptHost;Lorg/gradle/plugin/management/internal/PluginRequests;)V+52
j  org.gradle.kotlin.dsl.execution.Interpreter$ProgramHost.applyPluginsTo(Lorg/gradle/kotlin/dsl/support/KotlinScriptHost;Lorg/gradle/plugin/management/internal/PluginRequests;)V+21
j  Program.execute(Lorg/gradle/kotlin/dsl/execution/ExecutableProgram$Host;Lorg/gradle/kotlin/dsl/support/KotlinScriptHost;)V+38
j  org.gradle.kotlin.dsl.execution.Interpreter$ProgramHost.eval(Lorg/gradle/kotlin/dsl/execution/CompiledScript;Lorg/gradle/kotlin/dsl/support/KotlinScriptHost;)V+88
j  org.gradle.kotlin.dsl.execution.Interpreter.eval(Ljava/lang/Object;Lorg/gradle/groovy/scripts/ScriptSource;Lorg/gradle/internal/hash/HashCode;Lorg/gradle/api/initialization/dsl/ScriptHandler;Lorg/gradle/api/internal/initialization/ClassLoaderScope;Lorg/gradle/api/internal/initialization/ClassLoaderScope;ZLjava/util/EnumSet;)V+213
j  org.gradle.kotlin.dsl.provider.StandardKotlinScriptEvaluator.evaluate(Ljava/lang/Object;Lorg/gradle/groovy/scripts/ScriptSource;Lorg/gradle/api/initialization/dsl/ScriptHandler;Lorg/gradle/api/internal/initialization/ClassLoaderScope;Lorg/gradle/api/internal/initialization/ClassLoaderScope;ZLjava/util/EnumSet;)V+163
j  org.gradle.kotlin.dsl.provider.KotlinScriptPluginFactory$create$1.invoke(Ljava/lang/Object;)V+253
j  org.gradle.kotlin.dsl.provider.KotlinScriptPluginFactory$create$1.invoke(Ljava/lang/Object;)Ljava/lang/Object;+2
j  org.gradle.kotlin.dsl.provider.KotlinScriptPlugin.apply(Ljava/lang/Object;)V+22
j  org.gradle.configuration.BuildOperationScriptPlugin$1.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+11
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
J 6613 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000019d113d69bc [0x0000019d113d67c0+0x00000000000001fc]
J 6973 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000019d1148ae14 [0x0000019d1148ada0+0x0000000000000074]
J 6799 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000019d1142ca84 [0x0000019d1142bee0+0x0000000000000ba4]
J 6617 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000019d113da08c [0x0000019d113d9de0+0x00000000000002ac]
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.configuration.BuildOperationScriptPlugin.lambda$apply$0(Ljava/lang/Object;Lorg/gradle/internal/code/UserCodeApplicationId;)V+14
j  org.gradle.configuration.BuildOperationScriptPlugin$$Lambda$445+0x0000019d3241d658.execute(Ljava/lang/Object;)V+12
j  org.gradle.internal.code.DefaultUserCodeApplicationContext.apply(Lorg/gradle/internal/code/UserCodeSource;Lorg/gradle/api/Action;)V+37
j  org.gradle.configuration.BuildOperationScriptPlugin.apply(Ljava/lang/Object;)V+71
j  org.gradle.configuration.project.BuildScriptProcessor$$Lambda$1091+0x0000019d3277b7b8.accept(Ljava/lang/Object;)V+8
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$1(Ljava/util/function/Consumer;Lorg/gradle/api/internal/project/ProjectInternal;)Ljava/lang/Object;+2
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl$$Lambda$1065+0x0000019d327437e0.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(Ljava/util/function/Function;)Ljava/lang/Object;+97
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(Ljava/util/function/Consumer;)V+7
j  org.gradle.configuration.project.BuildScriptProcessor.execute(Lorg/gradle/api/internal/project/ProjectInternal;)V+88
j  org.gradle.configuration.project.BuildScriptProcessor.execute(Ljava/lang/Object;)V+5
j  org.gradle.configuration.project.ConfigureActionsProjectEvaluator.evaluate(Lorg/gradle/api/internal/project/ProjectInternal;Lorg/gradle/api/internal/project/ProjectStateInternal;)V+33
j  org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.lambda$run$0(Lorg/gradle/internal/operations/BuildOperationContext;Lorg/gradle/api/internal/project/ProjectInternal;)V+67
j  org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject$$Lambda$1064+0x0000019d327435b0.accept(Ljava/lang/Object;)V+12
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$applyToMutableState$1(Ljava/util/function/Consumer;Lorg/gradle/api/internal/project/ProjectInternal;)Ljava/lang/Object;+2
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl$$Lambda$1065+0x0000019d327437e0.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.lambda$fromMutableState$2(Ljava/util/function/Function;)Ljava/lang/Object;+5
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl$$Lambda$1066+0x0000019d32743a20.create()Ljava/lang/Object;+8
j  org.gradle.internal.work.DefaultWorkerLeaseService.withReplacedLocks(Ljava/util/Collection;Lorg/gradle/internal/resources/ResourceLock;Lorg/gradle/internal/Factory;)Ljava/lang/Object;+40
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.fromMutableState(Ljava/util/function/Function;)Ljava/lang/Object;+133
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.applyToMutableState(Ljava/util/function/Consumer;)V+7
j  org.gradle.configuration.project.LifecycleProjectEvaluator$EvaluateProject.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+16
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
J 6613 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation; (110 bytes) @ 0x0000019d113d69bc [0x0000019d113d67c0+0x00000000000001fc]
J 6973 c1 org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object; (12 bytes) @ 0x0000019d1148ae14 [0x0000019d1148ada0+0x0000000000000074]
J 6799 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object; (147 bytes) @ 0x0000019d1142ca84 [0x0000019d1142bee0+0x0000000000000ba4]
J 6617 c1 org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V (23 bytes) @ 0x0000019d113da08c [0x0000019d113d9de0+0x00000000000002ac]
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.configuration.project.LifecycleProjectEvaluator.evaluate(Lorg/gradle/api/internal/project/ProjectInternal;Lorg/gradle/api/internal/project/ProjectStateInternal;)V+42
j  org.gradle.api.internal.project.DefaultProject.evaluateUnchecked()Lorg/gradle/api/internal/project/ProjectInternal;+20
j  org.gradle.api.internal.project.ProjectLifecycleController.lambda$ensureSelfConfigured$2()V+4
j  org.gradle.api.internal.project.ProjectLifecycleController$$Lambda$1062+0x0000019d32742030.run()V+4
j  org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(Ljava/lang/Runnable;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$323+0x0000019d3234a830.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+9
j  org.gradle.internal.model.StateTransitionController.lambda$maybeTransitionIfNotCurrentlyTransitioning$10(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+16
j  org.gradle.internal.model.StateTransitionController$$Lambda$1063+0x0000019d32742250.run()V+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Ljava/lang/Runnable;)V+6
j  org.gradle.internal.model.StateTransitionController.maybeTransitionIfNotCurrentlyTransitioning(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+13
j  org.gradle.api.internal.project.ProjectLifecycleController.ensureSelfConfigured()V+16
j  org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl.ensureConfigured()V+19
j  org.gradle.execution.TaskPathProjectEvaluator.configure(Lorg/gradle/api/internal/project/ProjectInternal;)V+6
j  org.gradle.execution.TaskPathProjectEvaluator.configureHierarchy(Lorg/gradle/api/internal/project/ProjectInternal;)V+41
j  org.gradle.configuration.DefaultProjectsPreparer.prepareProjects(Lorg/gradle/api/internal/GradleInternal;)V+52
j  org.gradle.configuration.BuildTreePreparingProjectsPreparer.prepareProjects(Lorg/gradle/api/internal/GradleInternal;)V+114
j  org.gradle.configuration.BuildOperationFiringProjectsPreparer$ConfigureBuild.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+11
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.configuration.BuildOperationFiringProjectsPreparer.prepareProjects(Lorg/gradle/api/internal/GradleInternal;)V+13
j  org.gradle.initialization.VintageBuildModelController.lambda$prepareProjects$2()V+8
j  org.gradle.initialization.VintageBuildModelController$$Lambda$1017+0x0000019d326ecc98.run()V+4
j  org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(Ljava/lang/Runnable;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$323+0x0000019d3234a830.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+9
j  org.gradle.internal.model.StateTransitionController.lambda$transitionIfNotPreviously$11(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+16
j  org.gradle.internal.model.StateTransitionController$$Lambda$325+0x0000019d3234ac70.run()V+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Ljava/lang/Runnable;)V+6
j  org.gradle.internal.model.StateTransitionController.transitionIfNotPreviously(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+13
j  org.gradle.initialization.VintageBuildModelController.prepareProjects()V+16
j  org.gradle.initialization.VintageBuildModelController.getConfiguredModel()Lorg/gradle/api/internal/GradleInternal;+5
j  org.gradle.internal.build.DefaultBuildLifecycleController$$Lambda$1016+0x0000019d326eca78.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.lambda$notInState$3(Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+11
j  org.gradle.internal.model.StateTransitionController$$Lambda$674+0x0000019d325504d8.create()Ljava/lang/Object;+12
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+6
j  org.gradle.internal.model.StateTransitionController.notInState(Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+12
j  org.gradle.internal.build.DefaultBuildLifecycleController.configureProjects()V+21
j  org.gradle.internal.build.AbstractBuildState.ensureProjectsConfigured()V+4
j  org.gradle.internal.buildtree.BuildInclusionCoordinator$$Lambda$1013+0x0000019d326ec1e8.run()V+4
j  org.gradle.internal.buildtree.BuildInclusionCoordinator$BuildSynchronizer.lambda$withLock$0(Ljava/lang/Runnable;)V+14
j  org.gradle.internal.buildtree.BuildInclusionCoordinator$BuildSynchronizer$$Lambda$1015+0x0000019d326ec858.run()V+8
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Ljava/lang/Runnable;)V+6
j  org.gradle.internal.buildtree.BuildInclusionCoordinator$BuildSynchronizer.withLock(Ljava/lang/Runnable;)V+11
j  org.gradle.internal.buildtree.BuildInclusionCoordinator.withLockForBuild(Lorg/gradle/internal/build/BuildState;Ljava/lang/Runnable;)V+20
j  org.gradle.internal.buildtree.BuildInclusionCoordinator.prepareForPluginResolution(Lorg/gradle/internal/build/IncludedBuildState;)V+13
j  org.gradle.internal.composite.DefaultBuildIncluder.prepareForPluginResolution(Lorg/gradle/internal/build/IncludedBuildState;)V+5
j  org.gradle.composite.internal.plugins.CompositeBuildPluginResolverContributor$CompositeBuildPluginResolver.resolveFromIncludedPluginBuilds(Lorg/gradle/plugin/use/PluginId;)Lorg/gradle/plugin/use/resolve/internal/PluginResolution;+39
j  org.gradle.composite.internal.plugins.CompositeBuildPluginResolverContributor$CompositeBuildPluginResolver.doResolve(Lorg/gradle/plugin/use/PluginId;)Lorg/gradle/composite/internal/plugins/CompositeBuildPluginResolverContributor$PluginResult;+2
j  org.gradle.composite.internal.plugins.CompositeBuildPluginResolverContributor$CompositeBuildPluginResolver$$Lambda$663+0x0000019d3254c468.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
J 4046 c2 java.util.concurrent.ConcurrentHashMap.computeIfAbsent(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object; java.base@17.0.12 (576 bytes) @ 0x0000019d18606530 [0x0000019d18605ae0+0x0000000000000a50]
j  org.gradle.composite.internal.plugins.CompositeBuildPluginResolverContributor$CompositeBuildPluginResolver.resolve(Lorg/gradle/plugin/management/internal/PluginRequestInternal;)Lorg/gradle/plugin/use/resolve/internal/PluginResolutionResult;+16
j  org.gradle.plugin.use.resolve.internal.CompositePluginResolver.resolve(Lorg/gradle/plugin/management/internal/PluginRequestInternal;)Lorg/gradle/plugin/use/resolve/internal/PluginResolutionResult;+37
j  org.gradle.plugin.use.resolve.internal.AlreadyOnClasspathPluginResolver.resolve(Lorg/gradle/plugin/management/internal/PluginRequestInternal;)Lorg/gradle/plugin/use/resolve/internal/PluginResolutionResult;+28
j  org.gradle.plugin.use.internal.DefaultPluginRequestApplicator.resolvePluginRequest(Lorg/gradle/plugin/use/resolve/internal/PluginResolver;Lorg/gradle/plugin/management/internal/PluginRequestInternal;)Lorg/gradle/plugin/use/resolve/internal/PluginResolution;+2
j  org.gradle.plugin.use.internal.DefaultPluginRequestApplicator.applyPlugins(Lorg/gradle/plugin/management/internal/PluginRequests;Lorg/gradle/api/internal/initialization/ScriptHandlerInternal;Lorg/gradle/api/internal/plugins/PluginManagerInternal;Lorg/gradle/api/internal/initialization/ClassLoaderScope;)V+136
j  org.gradle.configuration.DefaultScriptPluginFactory$ScriptPluginImpl.apply(Ljava/lang/Object;)V+174
j  org.gradle.configuration.BuildOperationScriptPlugin$1.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+11
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.configuration.BuildOperationScriptPlugin.lambda$apply$0(Ljava/lang/Object;Lorg/gradle/internal/code/UserCodeApplicationId;)V+14
j  org.gradle.configuration.BuildOperationScriptPlugin$$Lambda$445+0x0000019d3241d658.execute(Ljava/lang/Object;)V+12
j  org.gradle.internal.code.DefaultUserCodeApplicationContext.apply(Lorg/gradle/internal/code/UserCodeSource;Lorg/gradle/api/Action;)V+37
j  org.gradle.configuration.BuildOperationScriptPlugin.apply(Ljava/lang/Object;)V+71
j  org.gradle.initialization.ScriptEvaluatingSettingsProcessor.applySettingsScript(Lorg/gradle/groovy/scripts/TextResourceScriptSource;Lorg/gradle/api/internal/SettingsInternal;)V+32
j  org.gradle.initialization.ScriptEvaluatingSettingsProcessor.process(Lorg/gradle/api/internal/GradleInternal;Lorg/gradle/initialization/SettingsLocation;Lorg/gradle/api/internal/initialization/ClassLoaderScope;Lorg/gradle/StartParameter;)Lorg/gradle/initialization/SettingsState;+90
j  org.gradle.initialization.SettingsEvaluatedCallbackFiringSettingsProcessor.process(Lorg/gradle/api/internal/GradleInternal;Lorg/gradle/initialization/SettingsLocation;Lorg/gradle/api/internal/initialization/ClassLoaderScope;Lorg/gradle/StartParameter;)Lorg/gradle/initialization/SettingsState;+9
j  org.gradle.initialization.RootBuildCacheControllerSettingsProcessor.process(Lorg/gradle/api/internal/GradleInternal;Lorg/gradle/initialization/SettingsLocation;Lorg/gradle/api/internal/initialization/ClassLoaderScope;Lorg/gradle/StartParameter;)Lorg/gradle/initialization/SettingsState;+9
j  org.gradle.initialization.BuildOperationSettingsProcessor$2.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/initialization/SettingsState;+23
j  org.gradle.initialization.BuildOperationSettingsProcessor$2.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/CallableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+3
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object;+16
j  org.gradle.initialization.BuildOperationSettingsProcessor.process(Lorg/gradle/api/internal/GradleInternal;Lorg/gradle/initialization/SettingsLocation;Lorg/gradle/api/internal/initialization/ClassLoaderScope;Lorg/gradle/StartParameter;)Lorg/gradle/initialization/SettingsState;+17
j  org.gradle.initialization.DefaultSettingsLoader.findSettingsAndLoadIfAppropriate(Lorg/gradle/api/internal/GradleInternal;Lorg/gradle/StartParameter;Lorg/gradle/initialization/SettingsLocation;Lorg/gradle/api/internal/initialization/ClassLoaderScope;)Lorg/gradle/initialization/SettingsState;+9
j  org.gradle.initialization.DefaultSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+103
j  org.gradle.initialization.SettingsAttachingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+5
j  org.gradle.internal.composite.CommandLineIncludedBuildSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+5
j  org.gradle.internal.composite.ChildBuildRegisteringSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+5
j  org.gradle.internal.composite.CompositeBuildSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+5
j  org.gradle.initialization.InitScriptHandlingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+13
j  org.gradle.api.internal.initialization.CacheConfigurationsHandlingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+15
j  org.gradle.initialization.GradlePropertiesHandlingSettingsLoader.findAndLoadSettings(Lorg/gradle/api/internal/GradleInternal;)Lorg/gradle/initialization/SettingsState;+40
j  org.gradle.initialization.DefaultSettingsPreparer.prepareSettings(Lorg/gradle/api/internal/GradleInternal;)V+33
j  org.gradle.initialization.BuildOperationFiringSettingsPreparer$LoadBuild.doLoadBuild()V+11
j  org.gradle.initialization.BuildOperationFiringSettingsPreparer$LoadBuild.run(Lorg/gradle/internal/operations/BuildOperationContext;)V+1
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/RunnableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.run(Lorg/gradle/internal/operations/RunnableBuildOperation;)V+9
j  org.gradle.initialization.BuildOperationFiringSettingsPreparer.prepareSettings(Lorg/gradle/api/internal/GradleInternal;)V+31
j  org.gradle.initialization.VintageBuildModelController.lambda$prepareSettings$1()V+8
j  org.gradle.initialization.VintageBuildModelController$$Lambda$324+0x0000019d3234aa50.run()V+4
j  org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(Ljava/lang/Runnable;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$323+0x0000019d3234a830.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+9
j  org.gradle.internal.model.StateTransitionController.lambda$transitionIfNotPreviously$11(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+16
j  org.gradle.internal.model.StateTransitionController$$Lambda$325+0x0000019d3234ac70.run()V+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Ljava/lang/Runnable;)V+6
j  org.gradle.internal.model.StateTransitionController.transitionIfNotPreviously(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+13
j  org.gradle.initialization.VintageBuildModelController.prepareSettings()V+16
j  org.gradle.initialization.VintageBuildModelController.prepareToScheduleTasks()V+1
j  org.gradle.internal.build.DefaultBuildLifecycleController.lambda$prepareToScheduleTasks$6()V+9
j  org.gradle.internal.build.DefaultBuildLifecycleController$$Lambda$321+0x0000019d3234a3f0.run()V+4
j  org.gradle.internal.model.StateTransitionController.lambda$doTransition$14(Ljava/lang/Runnable;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$323+0x0000019d3234a830.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+9
j  org.gradle.internal.model.StateTransitionController.lambda$maybeTransition$9(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+16
j  org.gradle.internal.model.StateTransitionController$$Lambda$322+0x0000019d3234a610.run()V+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Ljava/lang/Runnable;)V+6
j  org.gradle.internal.model.StateTransitionController.maybeTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/lang/Runnable;)V+13
j  org.gradle.internal.build.DefaultBuildLifecycleController.prepareToScheduleTasks()V+16
j  org.gradle.internal.buildtree.DefaultBuildTreeWorkPreparer.scheduleRequestedTasks(Lorg/gradle/internal/buildtree/BuildTreeWorkGraph;Lorg/gradle/execution/EntryTaskSelector;)Lorg/gradle/internal/buildtree/BuildTreeWorkGraph$FinalizedGraph;+4
j  org.gradle.internal.cc.impl.VintageBuildTreeWorkController$scheduleAndRunRequestedTasks$1.apply(Lorg/gradle/internal/buildtree/BuildTreeWorkGraph;)Lorg/gradle/internal/build/ExecutionResult;+18
j  org.gradle.internal.cc.impl.VintageBuildTreeWorkController$scheduleAndRunRequestedTasks$1.apply(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.gradle.composite.internal.DefaultIncludedBuildTaskGraph.withNewWorkGraph(Ljava/util/function/Function;)Ljava/lang/Object;+30
j  org.gradle.internal.cc.impl.VintageBuildTreeWorkController.scheduleAndRunRequestedTasks(Lorg/gradle/execution/EntryTaskSelector;)Lorg/gradle/internal/build/ExecutionResult;+16
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$scheduleAndRunTasks$1(Lorg/gradle/execution/EntryTaskSelector;)Lorg/gradle/internal/build/ExecutionResult;+5
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController$$Lambda$316+0x0000019d3234f4a0.get()Ljava/lang/Object;+8
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.lambda$runBuild$4(Ljava/util/function/Supplier;)Ljava/lang/Object;+1
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController$$Lambda$317+0x0000019d3234f6c0.get()Ljava/lang/Object;+8
j  org.gradle.internal.model.StateTransitionController.lambda$transition$6(Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+1
j  org.gradle.internal.model.StateTransitionController$$Lambda$319+0x0000019d3234fb00.get()Ljava/lang/Object;+4
j  org.gradle.internal.model.StateTransitionController.doTransition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Lorg/gradle/internal/build/ExecutionResult;+24
j  org.gradle.internal.model.StateTransitionController.lambda$transition$7(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+9
j  org.gradle.internal.model.StateTransitionController$$Lambda$318+0x0000019d3234f8e0.create()Ljava/lang/Object;+16
j  org.gradle.internal.work.DefaultSynchronizer.withLock(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+6
j  org.gradle.internal.model.StateTransitionController.transition(Lorg/gradle/internal/model/StateTransitionController$State;Lorg/gradle/internal/model/StateTransitionController$State;Ljava/util/function/Supplier;)Ljava/lang/Object;+13
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.runBuild(Ljava/util/function/Supplier;)Ljava/lang/Object;+17
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.scheduleAndRunTasks(Lorg/gradle/execution/EntryTaskSelector;)V+8
j  org.gradle.internal.buildtree.DefaultBuildTreeLifecycleController.scheduleAndRunTasks()V+2
j  org.gradle.tooling.internal.provider.ExecuteBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+12
j  org.gradle.launcher.exec.ChainingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+34
j  org.gradle.internal.buildtree.ProblemReportingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+12
j  org.gradle.launcher.exec.BuildOutcomeReportingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+69
j  org.gradle.tooling.internal.provider.FileSystemWatchingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+317
j  org.gradle.launcher.exec.BuildCompletionNotifyingBuildActionRunner.run(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+6
j  org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.lambda$execute$0(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeLifecycleController;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+6
j  org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor$$Lambda$285+0x0000019d32336408.apply(Ljava/lang/Object;)Ljava/lang/Object;+12
j  org.gradle.composite.internal.DefaultRootBuildState.run(Ljava/util/function/Function;)Ljava/lang/Object;+87
j  org.gradle.launcher.exec.RootBuildLifecycleBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+75
j  org.gradle.internal.buildtree.InitDeprecationLoggingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+83
j  org.gradle.internal.buildtree.InitProblems.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+13
j  org.gradle.internal.buildtree.DefaultBuildTreeContext.execute(Lorg/gradle/internal/invocation/BuildAction;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+64
j  org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.lambda$execute$0(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/buildtree/BuildTreeContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+2
j  org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor$$Lambda$220+0x0000019d3227bdd0.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.gradle.internal.buildtree.BuildTreeState.run(Ljava/util/function/Function;)Ljava/lang/Object;+47
j  org.gradle.launcher.exec.BuildTreeLifecycleBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+74
j  org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$2.call(Lorg/gradle/internal/operations/BuildOperationContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+25
j  org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor$2.call(Lorg/gradle/internal/operations/BuildOperationContext;)Ljava/lang/Object;+2
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/CallableBuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+3
j  org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationContext;)V+6
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Lorg/gradle/internal/operations/BuildOperation;+22
j  org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$ReadableBuildOperationContext;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecutionListener;)Ljava/lang/Object;+8
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperationDescriptor$Builder;Lorg/gradle/internal/operations/BuildOperationState;Lorg/gradle/internal/operations/DefaultBuildOperationRunner$BuildOperationExecution;)Ljava/lang/Object;+141
j  org.gradle.internal.operations.DefaultBuildOperationRunner.execute(Lorg/gradle/internal/operations/BuildOperation;Lorg/gradle/internal/operations/BuildOperationWorker;Lorg/gradle/internal/operations/BuildOperationState;)V+18
j  org.gradle.internal.operations.DefaultBuildOperationRunner.call(Lorg/gradle/internal/operations/CallableBuildOperation;)Ljava/lang/Object;+16
j  org.gradle.launcher.exec.RunAsBuildOperationBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+23
j  org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.lambda$execute$0(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+6
j  org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor$$Lambda$214+0x0000019d3224dc48.create()Ljava/lang/Object;+12
j  org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(Ljava/util/Collection;Lorg/gradle/internal/Factory;)Ljava/lang/Object;+28
j  org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(Lorg/gradle/internal/Factory;)Ljava/lang/Object;+33
j  org.gradle.launcher.exec.RunAsWorkerThreadBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+12
j  org.gradle.tooling.internal.provider.continuous.ContinuousBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+58
j  org.gradle.tooling.internal.provider.SubscribableBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+30
j  org.gradle.internal.session.DefaultBuildSessionContext.execute(Lorg/gradle/internal/invocation/BuildAction;)Lorg/gradle/internal/buildtree/BuildActionRunner$Result;+64
j  org.gradle.internal.buildprocess.execution.BuildSessionLifecycleBuildActionExecutor$ActionImpl.apply(Lorg/gradle/internal/session/BuildSessionContext;)Lorg/gradle/launcher/exec/BuildActionResult;+6
j  org.gradle.internal.buildprocess.execution.BuildSessionLifecycleBuildActionExecutor$ActionImpl.apply(Ljava/lang/Object;)Ljava/lang/Object;+5
j  org.gradle.internal.session.BuildSessionState.run(Ljava/util/function/Function;)Ljava/lang/Object;+5
j  org.gradle.internal.buildprocess.execution.BuildSessionLifecycleBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+95
j  org.gradle.internal.buildprocess.execution.BuildSessionLifecycleBuildActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.internal.buildprocess.execution.StartParamsValidatingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+203
j  org.gradle.internal.buildprocess.execution.StartParamsValidatingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.internal.buildprocess.execution.SessionFailureReportingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+7
j  org.gradle.internal.buildprocess.execution.SessionFailureReportingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.internal.buildprocess.execution.SetupLoggingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Lorg/gradle/launcher/exec/BuildActionParameters;Lorg/gradle/initialization/BuildRequestContext;)Lorg/gradle/launcher/exec/BuildActionResult;+50
j  org.gradle.internal.buildprocess.execution.SetupLoggingActionExecutor.execute(Lorg/gradle/internal/invocation/BuildAction;Ljava/lang/Object;Ljava/lang/Object;)Lorg/gradle/launcher/exec/BuildActionResult;+10
j  org.gradle.launcher.daemon.server.exec.ExecuteBuild.doBuild(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/launcher/daemon/protocol/Build;)V+147
j  org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+49
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.WatchForDisconnection.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+19
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.ResetDeprecationLogger.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+7
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.RequestStopIfSingleUsedDaemon.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+33
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.ForwardClientInput.lambda$execute$0(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/internal/daemon/clientinput/StdinHandler;)Ljava/lang/Object;+11
j  org.gradle.launcher.daemon.server.exec.ForwardClientInput$$Lambda$116+0x0000019d3213b170.apply(Ljava/lang/Object;)Ljava/lang/Object;+8
j  org.gradle.internal.daemon.clientinput.ClientInputForwarder.forwardInput(Ljava/util/function/Function;)Ljava/lang/Object;+46
j  org.gradle.launcher.daemon.server.exec.ForwardClientInput.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+10
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.LogAndCheckHealth.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+51
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.LogToClient.doBuild(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/launcher/daemon/protocol/Build;)V+87
j  org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+49
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.EstablishBuildEnvironment.doBuild(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;Lorg/gradle/launcher/daemon/protocol/Build;)V+356
j  org.gradle.launcher.daemon.server.exec.BuildCommandOnly.execute(Lorg/gradle/launcher/daemon/server/api/DaemonCommandExecution;)V+49
j  org.gradle.launcher.daemon.server.api.DaemonCommandExecution.proceed()Z+23
j  org.gradle.launcher.daemon.server.exec.StartBuildOrRespondWithBusy$1.run()V+44
j  org.gradle.launcher.daemon.server.DaemonStateCoordinator.lambda$runCommand$0(Ljava/lang/Runnable;)V+1
j  org.gradle.launcher.daemon.server.DaemonStateCoordinator$$Lambda$115+0x0000019d32139bd8.run()V+8
j  org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(Ljava/lang/Runnable;)V+1
j  org.gradle.internal.concurrent.AbstractManagedExecutor$1.run()V+25
j  java.util.concurrent.ThreadPoolExecutor.runWorker(Ljava/util/concurrent/ThreadPoolExecutor$Worker;)V+92 java.base@17.0.12
j  java.util.concurrent.ThreadPoolExecutor$Worker.run()V+5 java.base@17.0.12
j  java.lang.Thread.run()V+11 java.base@17.0.12
v  ~StubRoutines::call_stub

Compiled method (n/a)  255645 2927     n 0       java.lang.ClassLoader::defineClass0 (native)
 total in heap  [0x0000019d18551290,0x0000019d18551798] = 1288
 relocation     [0x0000019d185513e8,0x0000019d18551420] = 56
 main code      [0x0000019d18551420,0x0000019d18551798] = 888

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000019d310114a0} 'defineClass0' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/ClassLoader'
  # parm0:    rdx:rdx   = 'java/lang/ClassLoader'
  # parm1:    r8:r8     = 'java/lang/Class'
  # parm2:    r9:r9     = 'java/lang/String'
  # parm3:    rdi:rdi   = '[B'
  # parm4:    rsi       = int
  # parm5:    rcx       = int
  # parm6:    [sp+0xb0]   = 'java/security/ProtectionDomain'  (sp of caller)
  # parm7:    [sp+0xb8]   = boolean
  # parm8:    [sp+0xc0]   = int
  # parm9:    [sp+0xc8]   = 'java/lang/Object'
  0x0000019d18551420: 448b 5208 | 49bb 0000 | 0031 9d01 | 0000 4d03 | d349 3bc2 | 0f84 0600 

  0x0000019d18551438: ;   {runtime_call ic_miss_stub}
  0x0000019d18551438: 0000 e941 | fc8e ff90 
[Verified Entry Point]
  0x0000019d18551440: 8984 2400 | 90ff ff55 | 488b ec48 | 81ec a000 | 0000 4881 | 7d28 0000 | 0000 488d | 4528 480f 
  0x0000019d18551460: 4445 2848 | 8944 2458 | 4863 4520 | 4889 4424 | 5048 6345 | 1848 8944 | 2448 4881 | 7d10 0000 
  0x0000019d18551480: 0000 488d | 4510 480f | 4445 1048 | 8944 2440 | 4889 4c24 | 3848 8974 | 2430 4889 | 7c24 7848 
  0x0000019d185514a0: 83ff 0048 | 8d44 2478 | 480f 4444 | 2478 4889 | 4424 284c | 894c 2470 | 4983 f900 | 488d 4424 
  0x0000019d185514c0: 7048 0f44 | 4424 7048 | 8944 2420 | 4c89 4424 | 6849 83f8 | 004c 8d4c | 2468 4c0f | 444c 2468 
  0x0000019d185514e0: 4889 5424 | 6048 83fa | 004c 8d44 | 2460 4c0f | 4444 2460 

  0x0000019d185514f4: ;   {oop(a 'java/lang/Class'{0x0000000680cc25d0} = 'java/lang/ClassLoader')}
  0x0000019d185514f4: 49be d025 | cc80 0600 | 0000 4c89 | b424 9000 | 0000 4c8d | b424 9000 | 0000 498b | d6c5 f877 
  0x0000019d18551514: ;   {internal_word}
  0x0000019d18551514: 49ba 1115 | 5518 9d01 | 0000 4d89 | 97a0 0200 | 0049 89a7 | 9802 0000 

  0x0000019d1855152c: ;   {external_word}
  0x0000019d1855152c: 49ba 1717 | e016 fd7f | 0000 4180 | 3a00 0f84 | 5200 0000 | 5241 5041 

  0x0000019d18551544: ;   {metadata({method} {0x0000019d310114a0} 'defineClass0' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000019d18551544: 5148 ba98 | 1401 319d | 0100 0049 | 8bcf 4883 | ec20 f7c4 | 0f00 0000 | 0f84 1a00 | 0000 4883 
  0x0000019d18551564: ;   {runtime_call}
  0x0000019d18551564: ec08 49ba | 40d0 9516 | fd7f 0000 | 41ff d248 | 83c4 08e9 | 0d00 0000 

  0x0000019d1855157c: ;   {runtime_call}
  0x0000019d1855157c: 49ba 40d0 | 9516 fd7f | 0000 41ff | d248 83c4 | 2041 5941 | 585a 498d | 8fb8 0200 | 0041 c787 
  0x0000019d1855159c: 4803 0000 | 0400 0000 

  0x0000019d185515a4: ;   {runtime_call}
  0x0000019d185515a4: 49ba 1c15 | 03b0 fd7f | 0000 41ff | d2c5 f877 | 41c7 8748 | 0300 0005 | 0000 00f0 | 8344 24c0 
  0x0000019d185515c4: 0049 3baf | 5003 0000 | 0f87 1100 | 0000 4181 | bf38 0300 | 0000 0000 | 000f 842c | 0000 00c5 
  0x0000019d185515e4: f877 4889 | 45f8 498b | cf4c 8be4 | 4883 ec20 | 4883 e4f0 

  0x0000019d185515f8: ;   {runtime_call}
  0x0000019d185515f8: 49ba 30dd | a516 fd7f | 0000 41ff | d249 8be4 | 4d33 e448 | 8b45 f841 | c787 4803 | 0000 0800 
  0x0000019d18551618: 0000 4181 | bfb8 0300 | 0002 0000 | 000f 843f 

  0x0000019d18551628: ;   {external_word}
  0x0000019d18551628: 0100 0049 | ba17 17e0 | 16fd 7f00 | 0041 803a | 000f 8450 | 0000 0048 

  0x0000019d18551640: ;   {metadata({method} {0x0000019d310114a0} 'defineClass0' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BIILjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/ClassLoader')}
  0x0000019d18551640: 8945 f848 | ba98 1401 | 319d 0100 | 0049 8bcf | 4883 ec20 | f7c4 0f00 | 0000 0f84 | 1a00 0000 
  0x0000019d18551660: 4883 ec08 

  0x0000019d18551664: ;   {runtime_call}
  0x0000019d18551664: 49ba 40d0 | 9516 fd7f | 0000 41ff | d248 83c4 | 08e9 0d00 

  0x0000019d18551678: ;   {runtime_call}
  0x0000019d18551678: 0000 49ba | 40d0 9516 | fd7f 0000 | 41ff d248 | 83c4 2048 | 8b45 f849 | c787 9802 | 0000 0000 
  0x0000019d18551698: 0000 49c7 | 87a0 0200 | 0000 0000 | 00c5 f877 | 4885 c00f | 8493 0000 | 0048 f7c0 | 0100 0000 
  0x0000019d185516b8: 0f84 8300 | 0000 488b | 40ff 4180 | 7f38 000f | 846f 0000 | 0048 83f8 | 000f 8465 | 0000 0049 
  0x0000019d185516d8: 8b4f 2048 | 83f9 000f | 8414 0000 | 0048 83e9 | 0849 894f | 2049 034f | 3048 8901 | e943 0000 
  0x0000019d185516f8: 0050 498b | d748 8bc8 | 4883 ec20 | f7c4 0f00 | 0000 0f84 | 1a00 0000 | 4883 ec08 

  0x0000019d18551714: ;   {runtime_call}
  0x0000019d18551714: 49ba d082 | 5416 fd7f | 0000 41ff | d248 83c4 | 08e9 0d00 

  0x0000019d18551728: ;   {runtime_call}
  0x0000019d18551728: 0000 49ba | d082 5416 | fd7f 0000 | 41ff d248 | 83c4 2058 | e903 0000 | 0048 8b00 | 498b 8fd8 
  0x0000019d18551748: 0000 00c7 | 8100 0100 | 0000 0000 | 00c9 4981 | 7f08 0000 | 0000 0f85 | 0100 0000 

  0x0000019d18551764: ;   {runtime_call StubRoutines (1)}
  0x0000019d18551764: c3e9 96f7 | 89ff c5f8 | 7748 8945 | f84c 8be4 | 4883 ec20 | 4883 e4f0 

  0x0000019d1855177c: ;   {runtime_call}
  0x0000019d1855177c: 49ba 5001 | 9616 fd7f | 0000 41ff | d249 8be4 | 4d33 e448 | 8b45 f8e9 | 93fe ffff 
[/MachCode]


Compiled method (c1)  255770 2926       3       java.lang.System$2::defineClass (21 bytes)
 total in heap  [0x0000019d10ea5690,0x0000019d10ea5a80] = 1008
 relocation     [0x0000019d10ea57e8,0x0000019d10ea5828] = 64
 main code      [0x0000019d10ea5840,0x0000019d10ea5980] = 320
 stub code      [0x0000019d10ea5980,0x0000019d10ea59c8] = 72
 metadata       [0x0000019d10ea59c8,0x0000019d10ea59d0] = 8
 scopes data    [0x0000019d10ea59d0,0x0000019d10ea5a18] = 72
 scopes pcs     [0x0000019d10ea5a18,0x0000019d10ea5a68] = 80
 dependencies   [0x0000019d10ea5a68,0x0000019d10ea5a70] = 8
 nul chk table  [0x0000019d10ea5a70,0x0000019d10ea5a80] = 16

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000019d31407658} 'defineClass' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/System$2'
  # this:     rdx:rdx   = 'java/lang/System$2'
  # parm0:    r8:r8     = 'java/lang/ClassLoader'
  # parm1:    r9:r9     = 'java/lang/Class'
  # parm2:    rdi:rdi   = 'java/lang/String'
  # parm3:    rsi:rsi   = '[B'
  # parm4:    rcx:rcx   = 'java/security/ProtectionDomain'
  # parm5:    [sp+0x70]   = boolean  (sp of caller)
  # parm6:    [sp+0x78]   = int
  # parm7:    [sp+0x80]   = 'java/lang/Object'
  0x0000019d10ea5840: 448b 5208 | 49bb 0000 | 0031 9d01 | 0000 4d03 | d34c 3bd0 

  0x0000019d10ea5854: ;   {runtime_call ic_miss_stub}
  0x0000019d10ea5854: 0f85 26b8 | f906 660f | 1f44 0000 
[Verified Entry Point]
  0x0000019d10ea5860: 8984 2400 | 90ff ff55 | 4883 ec60 

  0x0000019d10ea586c: ;   {metadata(method data for {method} {0x0000019d31407658} 'defineClass' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/System$2')}
  0x0000019d10ea586c: 488b d948 | b970 838f | 669d 0100 | 008b 81cc | 0000 0083 | c002 8981 | cc00 0000 | 81e0 fe07 
  0x0000019d10ea588c: 0000 83f8 | 000f 8468 | 0000 004c | 8bac 2480 | 0000 0044 | 8b5c 2478 | 8b44 2470 

  0x0000019d10ea58a8: ; implicit exception: dispatches to 0x0000019d10ea5920
                      ;   {metadata(method data for {method} {0x0000019d31407658} 'defineClass' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/System$2')}
  0x0000019d10ea58a8: 8b4e 0c48 | ba70 838f | 669d 0100 | 0048 8382 | 1001 0000 | 0149 8bd0 | 4d8b c14c | 8bcf 488b 
  0x0000019d10ea58c8: febe 0000 | 0000 4889 | 1c24 8944 | 2408 4489 | 5c24 104c | 896c 2418 | 0f1f 8000 

  0x0000019d10ea58e4: ;   {static_call}
  0x0000019d10ea58e4: 0000 00e8 

  0x0000019d10ea58e8: ; ImmutableOopMap {}
                      ;*invokestatic defineClass0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.System$2::defineClass@17 (line 2311)
  0x0000019d10ea58e8: 54bb 6a07 | 4883 c460 

  0x0000019d10ea58f0: ;   {poll_return}
  0x0000019d10ea58f0: 5d49 3ba7 | 5003 0000 | 0f87 2700 

  0x0000019d10ea58fc: ;   {metadata({method} {0x0000019d31407658} 'defineClass' '(Ljava/lang/ClassLoader;Ljava/lang/Class;Ljava/lang/String;[BLjava/security/ProtectionDomain;ZILjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/System$2')}
  0x0000019d10ea58fc: 0000 c349 | ba50 7640 | 319d 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000019d10ea5914: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea5914: ffff e865 

  0x0000019d10ea5918: ; ImmutableOopMap {rdx=Oop r8=Oop r9=Oop rdi=Oop rsi=Oop rbx=Oop [128]=Oop }
                      ;*synchronization entry
                      ; - java.lang.System$2::defineClass@-1 (line 2311)
  0x0000019d10ea5918: 4705 07e9 | 77ff ffff 

  0x0000019d10ea5920: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea5920: e87b f604 

  0x0000019d10ea5924: ; ImmutableOopMap {r8=Oop r9=Oop rdi=Oop rsi=Oop rbx=Oop r13=Oop [128]=Oop }
                      ;*arraylength {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.System$2::defineClass@8 (line 2311)
                      ;   {internal_word}
  0x0000019d10ea5924: 0749 baf1 | 58ea 109d | 0100 004d | 8997 6803 

  0x0000019d10ea5934: ;   {runtime_call SafepointBlob}
  0x0000019d10ea5934: 0000 e9c5 | 20fa 0690 | 9049 8b87 | e003 0000 | 49c7 87e0 | 0300 0000 | 0000 0049 | c787 e803 
  0x0000019d10ea5954: 0000 0000 | 0000 4883 

  0x0000019d10ea595c: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000019d10ea595c: c460 5de9 | 1ce7 0407 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 
  0x0000019d10ea597c: f4f4 f4f4 
[Stub Code]
  0x0000019d10ea5980: ;   {no_reloc}
  0x0000019d10ea5980: 0f1f 4400 

  0x0000019d10ea5984: ;   {static_stub}
  0x0000019d10ea5984: 0048 bb00 | 0000 0000 

  0x0000019d10ea598c: ;   {runtime_call}
  0x0000019d10ea598c: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x0000019d10ea5994: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000019d10ea5994: e8e7 1305 

  0x0000019d10ea5998: ;   {external_word}
  0x0000019d10ea5998: 0748 b910 | 2aba 16fd | 7f00 0048 

  0x0000019d10ea59a4: ;   {runtime_call}
  0x0000019d10ea59a4: 83e4 f049 | ba20 c082 | 16fd 7f00 | 0041 ffd2 

  0x0000019d10ea59b4: ;   {section_word}
  0x0000019d10ea59b4: f449 bab5 | 59ea 109d | 0100 0041 

  0x0000019d10ea59c0: ;   {runtime_call DeoptimizationBlob}
  0x0000019d10ea59c0: 52e9 da12 | fa06 f4f4 
[/MachCode]


Compiled method (c1)  255790 2925       3       java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass (97 bytes)
 total in heap  [0x0000019d10ea3f90,0x0000019d10ea5630] = 5792
 relocation     [0x0000019d10ea40e8,0x0000019d10ea4220] = 312
 main code      [0x0000019d10ea4220,0x0000019d10ea5060] = 3648
 stub code      [0x0000019d10ea5060,0x0000019d10ea50d8] = 120
 metadata       [0x0000019d10ea50d8,0x0000019d10ea5160] = 136
 scopes data    [0x0000019d10ea5160,0x0000019d10ea53a0] = 576
 scopes pcs     [0x0000019d10ea53a0,0x0000019d10ea55c0] = 544
 dependencies   [0x0000019d10ea55c0,0x0000019d10ea55d8] = 24
 nul chk table  [0x0000019d10ea55d8,0x0000019d10ea5630] = 88

[Constant Pool (empty)]

[MachCode]
[Entry Point]
  # {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner'
  # this:     rdx:rdx   = 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner'
  # parm0:    r8        = boolean
  # parm1:    r9:r9     = 'java/lang/Object'
  #           [sp+0x100]  (sp of caller)
  0x0000019d10ea4220: 448b 5208 | 49bb 0000 | 0031 9d01 | 0000 4d03 | d34c 3bd0 

  0x0000019d10ea4234: ;   {runtime_call ic_miss_stub}
  0x0000019d10ea4234: 0f85 46ce | f906 660f | 1f44 0000 
[Verified Entry Point]
  0x0000019d10ea4240: 8984 2400 | 90ff ff55 | 4881 ecf0 | 0000 0048 | 8994 24b8 | 0000 0044 | 8984 24c8 | 0000 004c 
  0x0000019d10ea4260: 898c 24d0 

  0x0000019d10ea4264: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4264: 0000 0048 | be68 7a8f | 669d 0100 | 008b becc | 0000 0083 | c702 89be | cc00 0000 | 81e7 fe07 
  0x0000019d10ea4284: 0000 83ff | 000f 8413 | 0b00 008b | 7210 48c1 | e603 483b | 0648 8bfe 

  0x0000019d10ea429c: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea429c: 48bb 687a | 8f66 9d01 | 0000 4883 | 8310 0100 | 0001 8b76 | 1048 c1e6 | 0348 89b4 | 24c0 0000 
  0x0000019d10ea42bc: 0048 3b06 

  0x0000019d10ea42c0: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea42c0: 488b fe48 | bb68 7a8f | 669d 0100 | 0048 8383 | 4801 0000 

  0x0000019d10ea42d4: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea42d4: 0148 bfe8 | a302 669d | 0100 008b | 9fcc 0000 | 0083 c302 | 899f cc00 | 0000 81e3 | feff 1f00 
  0x0000019d10ea42f4: 83fb 000f | 84d0 0a00 | 0048 8bfe 

  0x0000019d10ea4300: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4300: 48bb e8a3 | 0266 9d01 | 0000 4883 | 8310 0100 | 0001 8b7e | 3448 c1e7 | 0348 89bc | 24b0 0000 
  0x0000019d10ea4320: 0048 83ff 

  0x0000019d10ea4324: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4324: 0048 bbe8 | a302 669d | 0100 0048 | b848 0100 | 0000 0000 | 000f 850a | 0000 0048 | b858 0100 
  0x0000019d10ea4344: 0000 0000 | 0048 8b0c | 0348 8d49 | 0148 890c | 030f 850f 

  0x0000019d10ea4358: ;   {oop(NULL)}
  0x0000019d10ea4358: 0000 0048 | bf00 0000 | 0000 0000 | 00e9 ba06 

  0x0000019d10ea4368: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4368: 0000 48bb | e8a3 0266 | 9d01 0000 | 4883 8368 | 0100 0001 

  0x0000019d10ea437c: ;   {metadata(method data for {method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea437c: 48bb e0a5 | 0266 9d01 | 0000 8b83 | cc00 0000 | 83c0 0289 | 83cc 0000 | 0081 e0fe | ff1f 0083 
  0x0000019d10ea439c: f800 0f84 | 4a0a 0000 

  0x0000019d10ea43a4: ;   {metadata(method data for {method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea43a4: 48bb e0a5 | 0266 9d01 | 0000 4883 | 8310 0100 

  0x0000019d10ea43b4: ;   {metadata(method data for {method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea43b4: 0001 48bb | 58a7 0266 | 9d01 0000 | 8b83 cc00 | 0000 83c0 | 0289 83cc | 0000 0081 | e0fe ff1f 
  0x0000019d10ea43d4: 0083 f800 | 0f84 310a 

  0x0000019d10ea43dc: ;   {metadata(method data for {method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea43dc: 0000 48bb | 58a7 0266 | 9d01 0000 | ff83 2001 

  0x0000019d10ea43ec: ;   {metadata(method data for {method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea43ec: 0000 48bb | 58a7 0266 | 9d01 0000 | ff83 3001 

  0x0000019d10ea43fc: ;   {metadata(method data for {method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea43fc: 0000 48bb | e0a5 0266 | 9d01 0000 | ff83 3001 

  0x0000019d10ea440c: ;   {oop(a 'java/lang/Class'{0x0000000680dd1080} = 'java/lang/System')}
  0x0000019d10ea440c: 0000 48bb | 8010 dd80 | 0600 0000 | 8b5b 7c48 | c1e3 0348 

  0x0000019d10ea4420: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4420: 83fb 0048 | bbe8 a302 | 669d 0100 | 0048 b878 | 0100 0000 | 0000 000f | 840a 0000 | 0048 b888 
  0x0000019d10ea4440: 0100 0000 | 0000 0048 | 8b0c 0348 | 8d49 0148 | 890c 030f | 84cb 0500 

  0x0000019d10ea4458: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4458: 0048 bbe8 | a302 669d | 0100 0048 | 8383 9801 | 0000 010f 

  0x0000019d10ea446c: ;   {static_call}
  0x0000019d10ea446c: 1f40 00e8 

  0x0000019d10ea4470: ; ImmutableOopMap {[192]=Oop [184]=Oop [176]=Oop [208]=Oop }
                      ;*invokestatic getCallerClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.Class::getClassLoader@20 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4470: 0cd5 f906 

  0x0000019d10ea4474: ;   {metadata(method data for {method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4474: 49b8 e8a3 | 0266 9d01 | 0000 4983 | 80a8 0100 

  0x0000019d10ea4484: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea4484: 0001 49b8 | b8a8 0266 | 9d01 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x0000019d10ea44a4: ff1f 0083 | fa00 0f84 | 8009 0000 

  0x0000019d10ea44b0: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea44b0: 49b8 b8a8 | 0266 9d01 | 0000 4983 | 8010 0100 

  0x0000019d10ea44c0: ;   {metadata(method data for {method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea44c0: 0001 49b8 | e0a5 0266 | 9d01 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x0000019d10ea44e0: ff1f 0083 | fa00 0f84 | 6509 0000 

  0x0000019d10ea44ec: ;   {metadata(method data for {method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea44ec: 49b8 e0a5 | 0266 9d01 | 0000 4983 | 8010 0100 

  0x0000019d10ea44fc: ;   {metadata(method data for {method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea44fc: 0001 49b8 | 58a7 0266 | 9d01 0000 | 418b 90cc | 0000 0083 | c202 4189 | 90cc 0000 | 0081 e2fe 
  0x0000019d10ea451c: ff1f 0083 | fa00 0f84 | 4a09 0000 

  0x0000019d10ea4528: ;   {metadata(method data for {method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea4528: 49b8 58a7 | 0266 9d01 | 0000 41ff | 8020 0100 

  0x0000019d10ea4538: ;   {metadata(method data for {method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea4538: 0049 b858 | a702 669d | 0100 0041 | ff80 3001 

  0x0000019d10ea4548: ;   {metadata(method data for {method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea4548: 0000 49b8 | e0a5 0266 | 9d01 0000 | 41ff 8030 

  0x0000019d10ea4558: ;   {oop(a 'java/lang/Class'{0x0000000680dd1080} = 'java/lang/System')}
  0x0000019d10ea4558: 0100 0049 | b880 10dd | 8006 0000 | 0041 8b50 | 7c48 c1e2 | 0348 83fa 

  0x0000019d10ea4570: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea4570: 0049 b8b8 | a802 669d | 0100 0048 | be20 0100 | 0000 0000 | 000f 840a | 0000 0048 | be30 0100 
  0x0000019d10ea4590: 0000 0000 | 0049 8b3c | 3048 8d7f | 0149 893c | 300f 8475 

  0x0000019d10ea45a4: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea45a4: 0400 0049 | b8b8 a802 | 669d 0100 | 0049 8380 | 4001 0000 

  0x0000019d10ea45b8: ;   {metadata(method data for {method} {0x0000019d31010788} 'getClassLoader' '(Ljava/lang/Class;)Ljava/lang/ClassLoader;' in 'java/lang/ClassLoader')}
  0x0000019d10ea45b8: 0149 b8b8 | aa02 669d | 0100 0041 | 8bb0 cc00 | 0000 83c6 | 0241 89b0 | cc00 0000 | 81e6 feff 
  0x0000019d10ea45d8: 1f00 83fe | 000f 84b0 | 0800 0048 

  0x0000019d10ea45e4: ;   {metadata(method data for {method} {0x0000019d31010788} 'getClassLoader' '(Ljava/lang/Class;)Ljava/lang/ClassLoader;' in 'java/lang/ClassLoader')}
  0x0000019d10ea45e4: 83f8 0049 | b8b8 aa02 | 669d 0100 | 0048 be10 | 0100 0000 | 0000 000f | 850a 0000 | 0048 be20 
  0x0000019d10ea4604: 0100 0000 | 0000 0049 | 8b3c 3048 | 8d7f 0149 | 893c 300f | 850f 0000 

  0x0000019d10ea461c: ;   {oop(NULL)}
  0x0000019d10ea461c: 0049 b800 | 0000 0000 | 0000 00e9 | 2000 0000 

  0x0000019d10ea462c: ; implicit exception: dispatches to 0x0000019d10ea4eb4
  0x0000019d10ea462c: 483b 004c 

  0x0000019d10ea4630: ;   {metadata(method data for {method} {0x0000019d31010788} 'getClassLoader' '(Ljava/lang/Class;)Ljava/lang/ClassLoader;' in 'java/lang/ClassLoader')}
  0x0000019d10ea4630: 8bc0 48be | b8aa 0266 | 9d01 0000 | 4883 8630 | 0100 0001 | 448b 4034 | 49c1 e003 | 488b bc24 
  0x0000019d10ea4650: b000 0000 

  0x0000019d10ea4654: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea4654: 48be b8a8 | 0266 9d01 | 0000 4883 | 8650 0100 

  0x0000019d10ea4664: ;   {metadata(method data for {method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea4664: 0001 48be | 60ac 0266 | 9d01 0000 | 8b9e cc00 | 0000 83c3 | 0289 9ecc | 0000 0081 | e3fe ff1f 
  0x0000019d10ea4684: 0083 fb00 | 0f84 2b08 | 0000 4c3b 

  0x0000019d10ea4690: ;   {metadata(method data for {method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea4690: c748 be60 | ac02 669d | 0100 0048 | bb10 0100 | 0000 0000 | 000f 850a | 0000 0048 | bb20 0100 
  0x0000019d10ea46b0: 0000 0000 | 0048 8b04 | 1e48 8d40 | 0148 8904 | 1e0f 850b | 0000 0041 | b800 0000 | 00e9 5902 
  0x0000019d10ea46d0: 0000 4983 

  0x0000019d10ea46d4: ;   {metadata(method data for {method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea46d4: f800 48be | 60ac 0266 | 9d01 0000 | 48bb 3001 | 0000 0000 | 0000 0f85 | 0a00 0000 | 48bb 4001 
  0x0000019d10ea46f4: 0000 0000 | 0000 488b | 041e 488d | 4001 4889 | 041e 0f85 | 0b00 0000 | 41b8 0000 | 0000 e914 
  0x0000019d10ea4714: 0200 0048 | 3b07 488b 

  0x0000019d10ea471c: ;   {metadata(method data for {method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea471c: f748 bb60 | ac02 669d | 0100 008b | 7608 49ba | 0000 0031 | 9d01 0000 | 4903 f248 | 3bb3 6001 
  0x0000019d10ea473c: 0000 750d | 4883 8368 | 0100 0001 | e966 0000 | 0048 3bb3 | 7001 0000 | 750d 4883 | 8378 0100 
  0x0000019d10ea475c: 0001 e950 | 0000 0048 | 81bb 6001 | 0000 0000 | 0000 7517 | 4889 b360 | 0100 0048 | c783 6801 
  0x0000019d10ea477c: 0000 0100 | 0000 e92c | 0000 0048 | 81bb 7001 | 0000 0000 | 0000 7517 | 4889 b370 | 0100 0048 
  0x0000019d10ea479c: c783 7801 | 0000 0100 | 0000 e908 | 0000 0048 | 8383 5001 

  0x0000019d10ea47b0: ;   {metadata(method data for {method} {0x0000019d3100a508} 'isAncestor' '(Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea47b0: 0000 0148 | bea0 ae02 | 669d 0100 | 008b 9ecc | 0000 0083 | c302 899e | cc00 0000 | 81e3 feff 
  0x0000019d10ea47d0: 1f00 83fb | 000f 8404 | 0700 0048 | 8bf7 e992 | 0000 000f | 1f44 0000 | 4883 fe00 | bb02 0000 
  0x0000019d10ea47f0: 000f 8505 | 0000 00bb | 0000 0000 

  0x0000019d10ea47fc: ;   {metadata(method data for {method} {0x0000019d3100a508} 'isAncestor' '(Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea47fc: 48b8 a0ae | 0266 9d01 | 0000 8b88 | d000 0000 | 03cb 8988 | d000 0000 | 83fb 000f | 8505 0000 
  0x0000019d10ea481c: 00b9 0200 | 0000 81e1 | fe3f 0000 | 83f9 000f | 84cf 0600 | 004d 8b97 

  0x0000019d10ea4834: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop rdx=Oop rdi=Oop [176]=Oop r8=Oop rsi=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::isAncestor@15 (line 2017)
                      ; - java.lang.ClassLoader::needsClassLoaderPermissionCheck@15 (line 2035)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@15 (line 2059)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4834: 5803 0000 

  0x0000019d10ea4838: ;   {poll}
  0x0000019d10ea4838: 4185 0248 

  0x0000019d10ea483c: ;   {metadata(method data for {method} {0x0000019d3100a508} 'isAncestor' '(Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea483c: 83fe 0048 | bba0 ae02 | 669d 0100 | 0048 b840 | 0100 0000 | 0000 000f | 840a 0000 | 0048 b830 
  0x0000019d10ea485c: 0100 0000 | 0000 0048 | 8b0c 0348 | 8d49 0148 | 890c 030f | 8445 0000 | 008b 7618 | 48c1 e603 
  0x0000019d10ea487c: ;   {metadata(method data for {method} {0x0000019d3100a508} 'isAncestor' '(Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea487c: 4c3b c648 | bba0 ae02 | 669d 0100 | 0048 b810 | 0100 0000 | 0000 000f | 850a 0000 | 0048 b820 
  0x0000019d10ea489c: 0100 0000 | 0000 0048 | 8b0c 0348 | 8d49 0148 | 890c 030f | 8533 ffff | ffe9 0b00 | 0000 41b8 
  0x0000019d10ea48bc: 0000 0000 | e906 0000 | 0041 b801 | 0000 0041 

  0x0000019d10ea48cc: ;   {metadata(method data for {method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea48cc: 83f8 0049 | b860 ac02 | 669d 0100 | 0048 be88 | 0100 0000 | 0000 000f | 850a 0000 | 0048 be98 
  0x0000019d10ea48ec: 0100 0000 | 0000 0049 | 8b1c 3048 | 8d5b 0149 | 891c 300f | 851c 0000 

  0x0000019d10ea4904: ;   {metadata(method data for {method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea4904: 0049 b860 | ac02 669d | 0100 0041 | ff80 a801 | 0000 41b8 | 0100 0000 | e906 0000 | 0041 b800 
  0x0000019d10ea4924: 0000 0041 | 83e0 0141 

  0x0000019d10ea492c: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea492c: 83f8 0049 | b8b8 a802 | 669d 0100 | 0048 be60 | 0100 0000 | 0000 000f | 840a 0000 | 0048 be70 
  0x0000019d10ea494c: 0100 0000 | 0000 0049 | 8b1c 3048 | 8d5b 0149 | 891c 300f | 84b7 0000 | 0048 3b02 

  0x0000019d10ea4968: ;   {metadata(method data for {method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea4968: 4c8b c248 | beb8 a802 | 669d 0100 | 0045 8b40 | 0849 ba00 | 0000 319d | 0100 004d | 03c2 4c3b 
  0x0000019d10ea4988: 8690 0100 | 0075 0d48 | 8386 9801 | 0000 01e9 | 6600 0000 | 4c3b 86a0 | 0100 0075 | 0d48 8386 
  0x0000019d10ea49a8: a801 0000 | 01e9 5000 | 0000 4881 | be90 0100 | 0000 0000 | 0075 174c | 8986 9001 | 0000 48c7 
  0x0000019d10ea49c8: 8698 0100 | 0001 0000 | 00e9 2c00 | 0000 4881 | bea0 0100 | 0000 0000 | 0075 174c | 8986 a001 
  0x0000019d10ea49e8: 0000 48c7 | 86a8 0100 | 0001 0000 | 00e9 0800 | 0000 4883 | 8680 0100 

  0x0000019d10ea4a00: ;   {oop(a 'java/lang/RuntimePermission'{0x00000006812b4fd0})}
  0x0000019d10ea4a00: 0001 49b8 | d04f 2b81 | 0600 0000 | 9048 b8ff | ffff ffff 

  0x0000019d10ea4a14: ;   {virtual_call}
  0x0000019d10ea4a14: ffff ffe8 

  0x0000019d10ea4a18: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop [176]=Oop }
                      ;*invokevirtual checkPermission {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@25 (line 2060)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4a18: 64cc f906 | 488b bc24 | b000 0000 | 4883 ff00 

  0x0000019d10ea4a28: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4a28: 48ba 687a | 8f66 9d01 | 0000 48be | 8001 0000 | 0000 0000 | 0f84 0a00 | 0000 48be | 9001 0000 
  0x0000019d10ea4a48: 0000 0000 | 488b 1c32 | 488d 5b01 | 4889 1c32 | 0f84 f301 | 0000 488b | 9424 b800 | 0000 8b72 
  0x0000019d10ea4a68: 1048 c1e6 | 0348 3b06 

  0x0000019d10ea4a70: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4a70: 488b de48 | b868 7a8f | 669d 0100 | 0048 8380 | a001 0000 

  0x0000019d10ea4a84: ;   {metadata(method data for {method} {0x0000019d3137ef70} 'lookupClassProtectionDomain' '()Ljava/security/ProtectionDomain;' in 'java/lang/invoke/MethodHandles$Lookup')}
  0x0000019d10ea4a84: 0148 bb08 | 7e8f 669d | 0100 008b | 83cc 0000 | 0083 c002 | 8983 cc00 | 0000 81e0 | feff 1f00 
  0x0000019d10ea4aa4: 83f8 000f | 8483 0400 | 008b 5e18 | 48c1 e303 | 4883 fb00 

  0x0000019d10ea4ab8: ;   {metadata(method data for {method} {0x0000019d3137ef70} 'lookupClassProtectionDomain' '()Ljava/security/ProtectionDomain;' in 'java/lang/invoke/MethodHandles$Lookup')}
  0x0000019d10ea4ab8: 48b8 087e | 8f66 9d01 | 0000 48b9 | 1001 0000 | 0000 0000 | 0f85 0a00 | 0000 48b9 | 2001 0000 
  0x0000019d10ea4ad8: 0000 0000 | 4c8b 0408 | 4d8d 4001 | 4c89 0408 | 4889 bc24 | e000 0000 | 0f85 4501 

  0x0000019d10ea4af4: ;   {metadata(method data for {method} {0x0000019d3137ef70} 'lookupClassProtectionDomain' '()Ljava/security/ProtectionDomain;' in 'java/lang/invoke/MethodHandles$Lookup')}
  0x0000019d10ea4af4: 0000 48bb | 087e 8f66 | 9d01 0000 | 4883 8330 | 0100 0001 

  0x0000019d10ea4b08: ;   {metadata(method data for {method} {0x0000019d3140d018} 'getJavaLangAccess' '()Ljdk/internal/access/JavaLangAccess;' in 'jdk/internal/access/SharedSecrets')}
  0x0000019d10ea4b08: 48bb 80f0 | 1966 9d01 | 0000 8b83 | cc00 0000 | 83c0 0289 | 83cc 0000 | 0081 e0fe | ff1f 0083 
  0x0000019d10ea4b28: f800 0f84 | 2104 0000 

  0x0000019d10ea4b30: ;   {oop(a 'java/lang/Class'{0x0000000680dd83c8} = 'jdk/internal/access/SharedSecrets')}
  0x0000019d10ea4b30: 48bb c883 | dd80 0600 | 0000 8b9b | 8000 0000 | 48c1 e303 | 8b46 1048 | c1e0 0348 | 83fb 000f 
  0x0000019d10ea4b50: 842a 0000 

  0x0000019d10ea4b54: ;   {metadata('java/lang/System$2')}
  0x0000019d10ea4b54: 0049 bb38 | 5e01 319d | 0100 0044 | 8b4b 0849 | ba00 0000 | 319d 0100 | 004d 03ca | 4d3b 5938 
  0x0000019d10ea4b74: 0f85 f803 | 0000 e900 | 0000 0048 | 8bcb 483b 

  0x0000019d10ea4b84: ;   {metadata(method data for {method} {0x0000019d3137ef70} 'lookupClassProtectionDomain' '()Ljava/security/ProtectionDomain;' in 'java/lang/invoke/MethodHandles$Lookup')}
  0x0000019d10ea4b84: 0348 b908 | 7e8f 669d 

  0x0000019d10ea4b8c: ;   {metadata('java/lang/System$2')}
  0x0000019d10ea4b8c: 0100 0049 | ba38 5e01 | 319d 0100 | 004c 8991 | 5001 0000 | 4883 8158 | 0100 0001 

  0x0000019d10ea4ba8: ;   {metadata(method data for {method} {0x0000019d31407970} 'protectionDomain' '(Ljava/lang/Class;)Ljava/security/ProtectionDomain;' in 'java/lang/System$2')}
  0x0000019d10ea4ba8: 48bb c07f | 8f66 9d01 | 0000 8b8b | cc00 0000 | 83c1 0289 | 8bcc 0000 | 0081 e1fe | ff1f 0083 
  0x0000019d10ea4bc8: f900 0f84 | ac03 0000 

  0x0000019d10ea4bd0: ; implicit exception: dispatches to 0x0000019d10ea4f9d
  0x0000019d10ea4bd0: 483b 0048 

  0x0000019d10ea4bd4: ;   {metadata(method data for {method} {0x0000019d31407970} 'protectionDomain' '(Ljava/lang/Class;)Ljava/security/ProtectionDomain;' in 'java/lang/System$2')}
  0x0000019d10ea4bd4: 8bd8 48b9 | c07f 8f66 | 9d01 0000 | 4883 8110 | 0100 0001 | 488b d048 | 89b4 24d8 | 0000 000f 
  0x0000019d10ea4bf4: ;   {optimized virtual_call}
  0x0000019d10ea4bf4: 1f40 00e8 

  0x0000019d10ea4bf8: ; ImmutableOopMap {[192]=Oop [208]=Oop [216]=Oop [224]=Oop [184]=Oop }
                      ;*invokevirtual protectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.System$2::protectionDomain@1 (line 2426)
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@17 (line 2457)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4bf8: e493 1200 | 450f be47 | 3841 83f8 | 0048 8bb4 | 24d8 0000 | 000f 858f | 0300 004c | 8bd0 49c1 
  0x0000019d10ea4c18: ea03 4489 | 5618 f083 | 4424 c000 | 4c8b c64c | 33c0 49c1 | e816 4983 | f800 0f85 | 8a03 0000 
  0x0000019d10ea4c38: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4c38: 488b d849 | b868 7a8f | 669d 0100 | 0041 ff80 | d801 0000 | e912 0000 | 0048 89bc | 24e0 0000 
  0x0000019d10ea4c58: ;   {oop(NULL)}
  0x0000019d10ea4c58: 0048 bb00 | 0000 0000 | 0000 0048 | 8bbc 24e0 | 0000 0048 | 8bb4 24c0 | 0000 0048 | 8b94 24b8 
  0x0000019d10ea4c78: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4c78: 0000 0049 | b868 7a8f | 669d 0100 | 0049 8380 | f001 0000 

  0x0000019d10ea4c8c: ;   {metadata(method data for {method} {0x0000019d3140d018} 'getJavaLangAccess' '()Ljdk/internal/access/JavaLangAccess;' in 'jdk/internal/access/SharedSecrets')}
  0x0000019d10ea4c8c: 0149 b880 | f019 669d | 0100 0045 | 8b88 cc00 | 0000 4183 | c102 4589 | 88cc 0000 | 0041 81e1 
  0x0000019d10ea4cac: feff 1f00 | 4183 f900 | 0f84 2003 

  0x0000019d10ea4cb8: ;   {oop(a 'java/lang/Class'{0x0000000680dd83c8} = 'jdk/internal/access/SharedSecrets')}
  0x0000019d10ea4cb8: 0000 49b8 | c883 dd80 | 0600 0000 | 458b a880 | 0000 0049 | c1e5 038b | 4a14 48c1 | e103 448b 
  0x0000019d10ea4cd8: 7218 49c1 | e603 448b | 4a0c 4983 | fd00 0f84 | 2a00 0000 

  0x0000019d10ea4cec: ;   {metadata('java/lang/System$2')}
  0x0000019d10ea4cec: 48b8 385e | 0131 9d01 | 0000 458b | 5d08 49ba | 0000 0031 | 9d01 0000 | 4d03 da49 | 3b43 380f 
  0x0000019d10ea4d0c: 85ea 0200 | 00e9 0000 | 0000 4d8b | c549 3b45 | 004d 8bc5 

  0x0000019d10ea4d20: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4d20: 48ba 687a | 8f66 9d01 

  0x0000019d10ea4d28: ;   {metadata('java/lang/System$2')}
  0x0000019d10ea4d28: 0000 49ba | 385e 0131 | 9d01 0000 | 4c89 9210 | 0200 0048 | 8382 1802 | 0000 014c | 8bc7 498b 
  0x0000019d10ea4d48: d14c 8bce | 488b f949 | 8bf6 488b | cb8b 9c24 | c800 0000 | 891c 2489 | 5424 0848 | 8b94 24d0 
  0x0000019d10ea4d68: 0000 0048 | 8954 2410 | 498b d50f 

  0x0000019d10ea4d74: ;   {optimized virtual_call}
  0x0000019d10ea4d74: 1f40 00e8 

  0x0000019d10ea4d78: ; ImmutableOopMap {[192]=Oop [184]=Oop }
                      ;*invokeinterface defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@54 (line 2439)
  0x0000019d10ea4d78: e40a 0000 

  0x0000019d10ea4d7c: ;   {metadata(method data for {method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4d7c: 48be 687a | 8f66 9d01 | 0000 ff86 | 3802 0000 | 4881 c4f0 | 0000 005d 

  0x0000019d10ea4d94: ;   {poll_return}
  0x0000019d10ea4d94: 493b a750 | 0300 000f | 8764 0200 

  0x0000019d10ea4da0: ;   {metadata({method} {0x0000019d311bef00} 'defineClass' '(ZLjava/lang/Object;)Ljava/lang/Class;' in 'java/lang/invoke/MethodHandles$Lookup$ClassDefiner')}
  0x0000019d10ea4da0: 00c3 49ba | f8ee 1b31 | 9d01 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x0000019d10ea4db8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4db8: ffe8 c252 

  0x0000019d10ea4dbc: ; ImmutableOopMap {rdx=Oop [184]=Oop r9=Oop [208]=Oop }
                      ;*synchronization entry
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@-1 (line 2435)
  0x0000019d10ea4dbc: 0507 e9cc 

  0x0000019d10ea4dc0: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4dc0: f4ff ffe8 

  0x0000019d10ea4dc4: ; ImmutableOopMap {rdx=Oop [184]=Oop r9=Oop [208]=Oop rsi=Oop }
                      ;*invokevirtual lookupClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@4 (line 2435)
  0x0000019d10ea4dc4: d801 0507 

  0x0000019d10ea4dc8: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4dc8: e8d3 0105 

  0x0000019d10ea4dcc: ; ImmutableOopMap {rdx=Oop [184]=Oop r9=Oop [208]=Oop rsi=Oop [192]=Oop }
                      ;*invokevirtual getClassLoader {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
                      ;   {metadata({method} {0x0000019d3143ce78} 'getClassLoader' '()Ljava/lang/ClassLoader;' in 'java/lang/Class')}
  0x0000019d10ea4dcc: 0749 ba70 | ce43 319d | 0100 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x0000019d10ea4de4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4de4: e897 5205 

  0x0000019d10ea4de8: ; ImmutableOopMap {rdx=Oop [184]=Oop r9=Oop [208]=Oop rsi=Oop [192]=Oop }
                      ;*synchronization entry
                      ; - java.lang.Class::getClassLoader@-1 (line 895)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4de8: 07e9 0ff5 

  0x0000019d10ea4dec: ;   {metadata({method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea4dec: ffff 49ba | b8e1 0131 | 9d01 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x0000019d10ea4e04: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4e04: ffe8 7652 

  0x0000019d10ea4e08: ; ImmutableOopMap {rdx=Oop [184]=Oop r9=Oop [208]=Oop rsi=Oop [192]=Oop rdi=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.System::getSecurityManager@-1 (line 484)
                      ; - java.lang.Class::getClassLoader@11 (line 899)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4e08: 0507 e995 

  0x0000019d10ea4e0c: ;   {metadata({method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea4e0c: f5ff ff49 | bad0 e201 | 319d 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000019d10ea4e24: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4e24: ffff e855 

  0x0000019d10ea4e28: ; ImmutableOopMap {rdx=Oop [184]=Oop r9=Oop [208]=Oop rsi=Oop [192]=Oop rdi=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.System::allowSecurityManager@-1 (line 194)
                      ; - java.lang.System::getSecurityManager@0 (line 484)
                      ; - java.lang.Class::getClassLoader@11 (line 899)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4e28: 5205 07e9 | aef5 ffff 

  0x0000019d10ea4e30: ;   {metadata({method} {0x0000019d310107e0} 'checkClassLoaderPermission' '(Ljava/lang/ClassLoader;Ljava/lang/Class;)V' in 'java/lang/ClassLoader')}
  0x0000019d10ea4e30: 49ba d807 | 0131 9d01 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x0000019d10ea4e44: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4e44: ffff ffe8 

  0x0000019d10ea4e48: ; ImmutableOopMap {[192]=Oop [184]=Oop [176]=Oop [208]=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@-1 (line 2055)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4e48: 3452 0507 | e95f f6ff 

  0x0000019d10ea4e50: ;   {metadata({method} {0x0000019d3101e1c0} 'getSecurityManager' '()Ljava/lang/SecurityManager;' in 'java/lang/System')}
  0x0000019d10ea4e50: ff49 bab8 | e101 319d | 0100 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x0000019d10ea4e68: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4e68: e813 5205 

  0x0000019d10ea4e6c: ; ImmutableOopMap {[192]=Oop [184]=Oop [176]=Oop [208]=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.System::getSecurityManager@-1 (line 484)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@0 (line 2055)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4e6c: 07e9 7af6 

  0x0000019d10ea4e70: ;   {metadata({method} {0x0000019d3101e2d8} 'allowSecurityManager' '()Z' in 'java/lang/System')}
  0x0000019d10ea4e70: ffff 49ba | d0e2 0131 | 9d01 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x0000019d10ea4e88: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4e88: ffe8 f251 

  0x0000019d10ea4e8c: ; ImmutableOopMap {[192]=Oop [184]=Oop [176]=Oop [208]=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.System::allowSecurityManager@-1 (line 194)
                      ; - java.lang.System::getSecurityManager@0 (line 484)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@0 (line 2055)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4e8c: 0507 e995 

  0x0000019d10ea4e90: ;   {metadata({method} {0x0000019d31010788} 'getClassLoader' '(Ljava/lang/Class;)Ljava/lang/ClassLoader;' in 'java/lang/ClassLoader')}
  0x0000019d10ea4e90: f6ff ff49 | ba80 0701 | 319d 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000019d10ea4ea8: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4ea8: ffff e8d1 

  0x0000019d10ea4eac: ; ImmutableOopMap {[192]=Oop [184]=Oop [176]=Oop [208]=Oop rdx=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::getClassLoader@-1 (line 2041)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@9 (line 2058)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4eac: 5105 07e9 | 2ff7 ffff 

  0x0000019d10ea4eb4: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4eb4: e8e7 0005 

  0x0000019d10ea4eb8: ; ImmutableOopMap {[192]=Oop [184]=Oop [176]=Oop [208]=Oop rdx=Oop rax=Oop }
                      ;*invokevirtual getClassLoader0 {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::getClassLoader@7 (line 2045)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@9 (line 2058)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
                      ;   {metadata({method} {0x0000019d310111d0} 'needsClassLoaderPermissionCheck' '(Ljava/lang/ClassLoader;Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea4eb8: 0749 bac8 | 1101 319d | 0100 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x0000019d10ea4ed0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4ed0: e8ab 5105 

  0x0000019d10ea4ed4: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop rdx=Oop r8=Oop rdi=Oop [176]=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::needsClassLoaderPermissionCheck@-1 (line 2029)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@15 (line 2059)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4ed4: 07e9 b4f7 

  0x0000019d10ea4ed8: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4ed8: ffff e8c1 

  0x0000019d10ea4edc: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop rdx=Oop rdi=Oop [176]=Oop r8=Oop }
                      ;*invokevirtual isAncestor {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::needsClassLoaderPermissionCheck@15 (line 2035)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@15 (line 2059)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
                      ;   {metadata({method} {0x0000019d3100a508} 'isAncestor' '(Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea4edc: 0005 0749 | ba00 a500 | 319d 0100 | 004c 8954 | 2408 48c7 | 0424 ffff 

  0x0000019d10ea4ef4: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4ef4: ffff e885 

  0x0000019d10ea4ef8: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop rdx=Oop rdi=Oop [176]=Oop r8=Oop }
                      ;*synchronization entry
                      ; - java.lang.ClassLoader::isAncestor@-1 (line 2011)
                      ; - java.lang.ClassLoader::needsClassLoaderPermissionCheck@15 (line 2035)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@15 (line 2059)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4ef8: 5105 07e9 | dbf8 ffff 

  0x0000019d10ea4f00: ;   {metadata({method} {0x0000019d3100a508} 'isAncestor' '(Ljava/lang/ClassLoader;)Z' in 'java/lang/ClassLoader')}
  0x0000019d10ea4f00: 49ba 00a5 | 0031 9d01 | 0000 4c89 | 5424 0848 | c704 240f 

  0x0000019d10ea4f14: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4f14: 0000 00e8 

  0x0000019d10ea4f18: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop rdx=Oop rdi=Oop [176]=Oop r8=Oop rsi=Oop }
                      ;*ifnonnull {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::isAncestor@15 (line 2017)
                      ; - java.lang.ClassLoader::needsClassLoaderPermissionCheck@15 (line 2035)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@15 (line 2059)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
  0x0000019d10ea4f18: 6451 0507 | e910 f9ff 

  0x0000019d10ea4f20: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4f20: ffe8 7a00 

  0x0000019d10ea4f24: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop rdx=Oop rdi=Oop [176]=Oop r8=Oop }
                      ;*getfield parent {reexecute=1 rethrow=0 return_oop=0}
                      ; - (reexecute) java.lang.ClassLoader::isAncestor@3 (line 2013)
                      ; - java.lang.ClassLoader::needsClassLoaderPermissionCheck@15 (line 2035)
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@15 (line 2059)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4f24: 0507 e875 

  0x0000019d10ea4f28: ; ImmutableOopMap {[192]=Oop [184]=Oop [208]=Oop [176]=Oop rdx=Oop }
                      ;*invokevirtual checkPermission {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.ClassLoader::checkClassLoaderPermission@25 (line 2060)
                      ; - java.lang.Class::getClassLoader@23 (line 901)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@9 (line 2436)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4f28: 0005 07e8 

  0x0000019d10ea4f2c: ; ImmutableOopMap {[192]=Oop [208]=Oop rdi=Oop rdx=Oop [184]=Oop rsi=Oop }
                      ;*invokevirtual lookupClassProtectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4f2c: 7000 0507 

  0x0000019d10ea4f30: ;   {metadata({method} {0x0000019d3137ef70} 'lookupClassProtectionDomain' '()Ljava/security/ProtectionDomain;' in 'java/lang/invoke/MethodHandles$Lookup')}
  0x0000019d10ea4f30: 49ba 68ef | 3731 9d01 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x0000019d10ea4f44: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4f44: ffff ffe8 

  0x0000019d10ea4f48: ; ImmutableOopMap {[192]=Oop [208]=Oop rdi=Oop rdx=Oop [184]=Oop rsi=Oop }
                      ;*synchronization entry
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@-1 (line 2455)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4f48: 3451 0507 | e95c fbff 

  0x0000019d10ea4f50: ;   {metadata({method} {0x0000019d3140d018} 'getJavaLangAccess' '()Ljdk/internal/access/JavaLangAccess;' in 'jdk/internal/access/SharedSecrets')}
  0x0000019d10ea4f50: ff49 ba10 | d040 319d | 0100 004c | 8954 2408 | 48c7 0424 | ffff ffff 

  0x0000019d10ea4f68: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4f68: e813 5105 

  0x0000019d10ea4f6c: ; ImmutableOopMap {[192]=Oop [208]=Oop [184]=Oop [224]=Oop rsi=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.access.SharedSecrets::getJavaLangAccess@-1 (line 122)
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@10 (line 2457)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4f6c: 07e9 befb 

  0x0000019d10ea4f70: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x0000019d10ea4f70: ffff e829 

  0x0000019d10ea4f74: ; ImmutableOopMap {[192]=Oop [208]=Oop [184]=Oop [224]=Oop rsi=Oop rbx=Oop rax=Oop }
                      ;*invokeinterface protectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@17 (line 2457)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
                      ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4f74: 2705 07e8 

  0x0000019d10ea4f78: ; ImmutableOopMap {[192]=Oop [208]=Oop [184]=Oop [224]=Oop rsi=Oop rbx=Oop rax=Oop }
                      ;*invokeinterface protectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@17 (line 2457)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4f78: 2400 0507 

  0x0000019d10ea4f7c: ;   {metadata({method} {0x0000019d31407970} 'protectionDomain' '(Ljava/lang/Class;)Ljava/security/ProtectionDomain;' in 'java/lang/System$2')}
  0x0000019d10ea4f7c: 49ba 6879 | 4031 9d01 | 0000 4c89 | 5424 0848 | c704 24ff 

  0x0000019d10ea4f90: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4f90: ffff ffe8 

  0x0000019d10ea4f94: ; ImmutableOopMap {[192]=Oop [208]=Oop [184]=Oop [224]=Oop rsi=Oop rax=Oop }
                      ;*synchronization entry
                      ; - java.lang.System$2::protectionDomain@-1 (line 2426)
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@17 (line 2457)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4f94: e850 0507 | e933 fcff 

  0x0000019d10ea4f9c: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea4f9c: ffe8 feff 

  0x0000019d10ea4fa0: ; ImmutableOopMap {[192]=Oop [208]=Oop [184]=Oop [224]=Oop rsi=Oop rax=Oop }
                      ;*invokevirtual protectionDomain {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.System$2::protectionDomain@1 (line 2426)
                      ; - java.lang.invoke.MethodHandles$Lookup::lookupClassProtectionDomain@17 (line 2457)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@23 (line 2437)
  0x0000019d10ea4fa0: 0407 448b | 4618 49c1 | e003 4983 | f800 0f84 | 5ffc ffff | 4c89 0424 

  0x0000019d10ea4fb8: ;   {runtime_call g1_pre_barrier_slow}
  0x0000019d10ea4fb8: e8c3 5605 | 07e9 51fc | ffff 4883 | f800 0f84 | 6cfc ffff | 4889 3424 

  0x0000019d10ea4fd0: ;   {runtime_call g1_post_barrier_slow}
  0x0000019d10ea4fd0: e8ab 5905 | 07e9 5efc 

  0x0000019d10ea4fd8: ;   {metadata({method} {0x0000019d3140d018} 'getJavaLangAccess' '()Ljdk/internal/access/JavaLangAccess;' in 'jdk/internal/access/SharedSecrets')}
  0x0000019d10ea4fd8: ffff 49ba | 10d0 4031 | 9d01 0000 | 4c89 5424 | 0848 c704 | 24ff ffff 

  0x0000019d10ea4ff0: ;   {runtime_call counter_overflow Runtime1 stub}
  0x0000019d10ea4ff0: ffe8 8a50 

  0x0000019d10ea4ff4: ; ImmutableOopMap {rbx=Oop rdi=Oop rdx=Oop [184]=Oop rsi=Oop [192]=Oop [208]=Oop }
                      ;*synchronization entry
                      ; - jdk.internal.access.SharedSecrets::getJavaLangAccess@-1 (line 122)
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@32 (line 2438)
  0x0000019d10ea4ff4: 0507 e9bf 

  0x0000019d10ea4ff8: ;   {runtime_call throw_incompatible_class_change_error Runtime1 stub}
  0x0000019d10ea4ff8: fcff ffe8 

  0x0000019d10ea4ffc: ; ImmutableOopMap {rbx=Oop rdi=Oop rsi=Oop [192]=Oop [208]=Oop r13=Oop rcx=Oop r14=Oop [184]=Oop }
                      ;*invokeinterface defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@54 (line 2439)
  0x0000019d10ea4ffc: a026 0507 

  0x0000019d10ea5000: ;   {runtime_call throw_null_pointer_exception Runtime1 stub}
  0x0000019d10ea5000: e89b ff04 

  0x0000019d10ea5004: ; ImmutableOopMap {rbx=Oop rdi=Oop rsi=Oop [192]=Oop [208]=Oop r13=Oop rcx=Oop r14=Oop [184]=Oop }
                      ;*invokeinterface defineClass {reexecute=0 rethrow=0 return_oop=0}
                      ; - java.lang.invoke.MethodHandles$Lookup$ClassDefiner::defineClass@54 (line 2439)
                      ;   {internal_word}
  0x0000019d10ea5004: 0749 ba94 | 4dea 109d | 0100 004d | 8997 6803 

  0x0000019d10ea5014: ;   {runtime_call SafepointBlob}
  0x0000019d10ea5014: 0000 e9e5 | 29fa 0690 | 9049 8b87 | e003 0000 | 49c7 87e0 | 0300 0000 | 0000 0049 | c787 e803 
  0x0000019d10ea5034: 0000 0000 | 0000 4881 | c4f0 0000 

  0x0000019d10ea5040: ;   {runtime_call unwind_exception Runtime1 stub}
  0x0000019d10ea5040: 005d e939 | f004 07f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 | f4f4 f4f4 
[Stub Code]
  0x0000019d10ea5060: ;   {no_reloc}
  0x0000019d10ea5060: 0f1f 4400 

  0x0000019d10ea5064: ;   {static_stub}
  0x0000019d10ea5064: 0048 bb00 | 0000 0000 

  0x0000019d10ea506c: ;   {runtime_call}
  0x0000019d10ea506c: 0000 00e9 | fbff ffff 

  0x0000019d10ea5074: ;   {static_stub}
  0x0000019d10ea5074: 9048 bb00 | 0000 0000 

  0x0000019d10ea507c: ;   {runtime_call}
  0x0000019d10ea507c: 0000 00e9 | fbff ffff 

  0x0000019d10ea5084: ;   {static_stub}
  0x0000019d10ea5084: 9048 bbd8 | bd43 319d 

  0x0000019d10ea508c: ;   {runtime_call I2C/C2I adapters}
  0x0000019d10ea508c: 0100 00e9 | c50c fa06 

  0x0000019d10ea5094: ;   {static_stub}
  0x0000019d10ea5094: 9048 bb00 | 0000 0000 

  0x0000019d10ea509c: ;   {runtime_call}
  0x0000019d10ea509c: 0000 00e9 | fbff ffff 
[Exception Handler]
  0x0000019d10ea50a4: ;   {runtime_call handle_exception_from_callee Runtime1 stub}
  0x0000019d10ea50a4: e8d7 1c05 

  0x0000019d10ea50a8: ;   {external_word}
  0x0000019d10ea50a8: 0748 b910 | 2aba 16fd | 7f00 0048 

  0x0000019d10ea50b4: ;   {runtime_call}
  0x0000019d10ea50b4: 83e4 f049 | ba20 c082 | 16fd 7f00 | 0041 ffd2 

  0x0000019d10ea50c4: ;   {section_word}
  0x0000019d10ea50c4: f449 bac5 | 50ea 109d | 0100 0041 

  0x0000019d10ea50d0: ;   {runtime_call DeoptimizationBlob}
  0x0000019d10ea50d0: 52e9 ca1b | fa06 f4f4 
[/MachCode]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000019d6c40edc0, length=53, elements={
0x0000019d08d9f380, 0x0000019d30176ed0, 0x0000019d30177c60, 0x0000019d30188ff0,
0x0000019d3018c8e0, 0x0000019d3018d1b0, 0x0000019d3018da80, 0x0000019d3018e730,
0x0000019d3018f0f0, 0x0000019d30208050, 0x0000019d3015d0e0, 0x0000019d30332950,
0x0000019d6a2dfb90, 0x0000019d303987b0, 0x0000019d3051d9e0, 0x0000019d6a93b6f0,
0x0000019d6aa9b5b0, 0x0000019d301d7230, 0x0000019d301d6300, 0x0000019d301d4ec0,
0x0000019d301d7740, 0x0000019d301d5df0, 0x0000019d301d58e0, 0x0000019d301d8160,
0x0000019d301d6810, 0x0000019d301d6d20, 0x0000019d305be510, 0x0000019d305bea20,
0x0000019d305bd5e0, 0x0000019d305b9920, 0x0000019d305bef30, 0x0000019d305bdaf0,
0x0000019d305bf440, 0x0000019d305bad60, 0x0000019d305b7fd0, 0x0000019d305bb270,
0x0000019d305bbc90, 0x0000019d305be000, 0x0000019d305bd0d0, 0x0000019d305b9e30,
0x0000019d305b8f00, 0x0000019d305bcbc0, 0x0000019d6b8f3120, 0x0000019d6b8f4050,
0x0000019d6b8f4f80, 0x0000019d6b8f17d0, 0x0000019d6b8f4560, 0x0000019d6b8f4a70,
0x0000019d6b8f3b40, 0x0000019d6b8f1ce0, 0x0000019d6b8f21f0, 0x0000019d6b8f2700,
0x0000019d6b8f2c10
}

Java Threads: ( => current thread )
  0x0000019d08d9f380 JavaThread "main" [_thread_blocked, id=16324, stack(0x0000000491e00000,0x0000000491f00000)]
  0x0000019d30176ed0 JavaThread "Reference Handler" daemon [_thread_blocked, id=16560, stack(0x0000000492600000,0x0000000492700000)]
  0x0000019d30177c60 JavaThread "Finalizer" daemon [_thread_blocked, id=14544, stack(0x0000000492700000,0x0000000492800000)]
  0x0000019d30188ff0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=20348, stack(0x0000000492800000,0x0000000492900000)]
  0x0000019d3018c8e0 JavaThread "Attach Listener" daemon [_thread_blocked, id=24072, stack(0x0000000492900000,0x0000000492a00000)]
  0x0000019d3018d1b0 JavaThread "Service Thread" daemon [_thread_blocked, id=10164, stack(0x0000000492a00000,0x0000000492b00000)]
  0x0000019d3018da80 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=7500, stack(0x0000000492b00000,0x0000000492c00000)]
  0x0000019d3018e730 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=19648, stack(0x0000000492c00000,0x0000000492d00000)]
  0x0000019d3018f0f0 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=22824, stack(0x0000000492d00000,0x0000000492e00000)]
  0x0000019d30208050 JavaThread "Sweeper thread" daemon [_thread_blocked, id=18236, stack(0x0000000492e00000,0x0000000492f00000)]
  0x0000019d3015d0e0 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=13004, stack(0x0000000492f00000,0x0000000493000000)]
  0x0000019d30332950 JavaThread "Notification Thread" daemon [_thread_blocked, id=6760, stack(0x0000000493000000,0x0000000493100000)]
  0x0000019d6a2dfb90 JavaThread "Daemon health stats" [_thread_blocked, id=16592, stack(0x0000000493500000,0x0000000493600000)]
  0x0000019d303987b0 JavaThread "Incoming local TCP Connector on port 56932" [_thread_in_native, id=25516, stack(0x0000000493600000,0x0000000493700000)]
  0x0000019d3051d9e0 JavaThread "Daemon periodic checks" [_thread_blocked, id=5508, stack(0x0000000493700000,0x0000000493800000)]
  0x0000019d6a93b6f0 JavaThread "Daemon" [_thread_blocked, id=14892, stack(0x0000000493800000,0x0000000493900000)]
  0x0000019d6aa9b5b0 JavaThread "Handler for socket connection from /127.0.0.1:56932 to /127.0.0.1:56936" [_thread_in_native, id=16268, stack(0x0000000493900000,0x0000000493a00000)]
  0x0000019d301d7230 JavaThread "Cancel handler" [_thread_blocked, id=12988, stack(0x0000000493a00000,0x0000000493b00000)]
=>0x0000019d301d6300 JavaThread "Daemon worker" [_thread_in_vm, id=20512, stack(0x0000000493b00000,0x0000000493c00000)]
  0x0000019d301d4ec0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:56932 to /127.0.0.1:56936" [_thread_blocked, id=17820, stack(0x0000000493c00000,0x0000000493d00000)]
  0x0000019d301d7740 JavaThread "Stdin handler" [_thread_blocked, id=20764, stack(0x0000000493d00000,0x0000000493e00000)]
  0x0000019d301d5df0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=24752, stack(0x0000000493e00000,0x0000000493f00000)]
  0x0000019d301d58e0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=17524, stack(0x0000000493f00000,0x0000000494000000)]
  0x0000019d301d8160 JavaThread "File lock request listener" [_thread_in_native, id=19952, stack(0x0000000494000000,0x0000000494100000)]
  0x0000019d301d6810 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=14192, stack(0x0000000494100000,0x0000000494200000)]
  0x0000019d301d6d20 JavaThread "File lock release action executor" [_thread_blocked, id=16836, stack(0x0000000494200000,0x0000000494300000)]
  0x0000019d305be510 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=21120, stack(0x0000000494300000,0x0000000494400000)]
  0x0000019d305bea20 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=2440, stack(0x0000000494400000,0x0000000494500000)]
  0x0000019d305bd5e0 JavaThread "File watcher server" daemon [_thread_in_native, id=24236, stack(0x0000000494500000,0x0000000494600000)]
  0x0000019d305b9920 JavaThread "File watcher consumer" daemon [_thread_blocked, id=12444, stack(0x0000000494600000,0x0000000494700000)]
  0x0000019d305bef30 JavaThread "jar transforms" [_thread_blocked, id=1440, stack(0x0000000491d00000,0x0000000491e00000)]
  0x0000019d305bdaf0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=24888, stack(0x0000000491f00000,0x0000000492000000)]
  0x0000019d305bf440 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=18452, stack(0x0000000494900000,0x0000000494a00000)]
  0x0000019d305bad60 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=8644, stack(0x0000000494a00000,0x0000000494b00000)]
  0x0000019d305b7fd0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=20444, stack(0x0000000494b00000,0x0000000494c00000)]
  0x0000019d305bb270 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=21040, stack(0x0000000494c00000,0x0000000494d00000)]
  0x0000019d305bbc90 JavaThread "Unconstrained build operations" [_thread_blocked, id=18460, stack(0x0000000494d00000,0x0000000494e00000)]
  0x0000019d305be000 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=25212, stack(0x0000000494e00000,0x0000000494f00000)]
  0x0000019d305bd0d0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=24488, stack(0x0000000494f00000,0x0000000495000000)]
  0x0000019d305b9e30 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=23616, stack(0x0000000495000000,0x0000000495100000)]
  0x0000019d305b8f00 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=19044, stack(0x0000000495100000,0x0000000495200000)]
  0x0000019d305bcbc0 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=25160, stack(0x0000000495200000,0x0000000495300000)]
  0x0000019d6b8f3120 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=26176, stack(0x0000000494700000,0x0000000494800000)]
  0x0000019d6b8f4050 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=26180, stack(0x0000000494800000,0x0000000494900000)]
  0x0000019d6b8f4f80 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=26184, stack(0x0000000495400000,0x0000000495500000)]
  0x0000019d6b8f17d0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=5680, stack(0x0000000495500000,0x0000000495600000)]
  0x0000019d6b8f4560 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=2428, stack(0x0000000495600000,0x0000000495700000)]
  0x0000019d6b8f4a70 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=2500, stack(0x0000000495700000,0x0000000495800000)]
  0x0000019d6b8f3b40 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=1356, stack(0x0000000495800000,0x0000000495900000)]
  0x0000019d6b8f1ce0 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=1472, stack(0x0000000495900000,0x0000000495a00000)]
  0x0000019d6b8f21f0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=16468, stack(0x0000000495a00000,0x0000000495b00000)]
  0x0000019d6b8f2700 JavaThread "Memory manager" [_thread_blocked, id=12016, stack(0x0000000495b00000,0x0000000495c00000)]
  0x0000019d6b8f2c10 JavaThread "build event listener" [_thread_blocked, id=24496, stack(0x0000000495c00000,0x0000000495d00000)]

Other Threads:
  0x0000019d3016fc50 VMThread "VM Thread" [stack: 0x0000000492500000,0x0000000492600000] [id=17568]
  0x0000019d08da0110 WatcherThread [stack: 0x0000000493100000,0x0000000493200000] [id=11392]
  0x0000019d08e28460 GCTaskThread "GC Thread#0" [stack: 0x0000000492000000,0x0000000492100000] [id=24028]
  0x0000019d30336ce0 GCTaskThread "GC Thread#1" [stack: 0x0000000493200000,0x0000000493300000] [id=24336]
  0x0000019d303ea5f0 GCTaskThread "GC Thread#2" [stack: 0x0000000493300000,0x0000000493400000] [id=20868]
  0x0000019d30bde4b0 GCTaskThread "GC Thread#3" [stack: 0x0000000493400000,0x0000000493500000] [id=18560]
  0x0000019d08e352c0 ConcurrentGCThread "G1 Main Marker" [stack: 0x0000000492100000,0x0000000492200000] [id=11316]
  0x0000019d08e35bf0 ConcurrentGCThread "G1 Conc#0" [stack: 0x0000000492200000,0x0000000492300000] [id=17960]
  0x0000019d08e4eaf0 ConcurrentGCThread "G1 Refine#0" [stack: 0x0000000492300000,0x0000000492400000] [id=16204]
  0x0000019d6ad114d0 ConcurrentGCThread "G1 Refine#1" [stack: 0x0000000495300000,0x0000000495400000] [id=21624]
  0x0000019d3002f6b0 ConcurrentGCThread "G1 Service" [stack: 0x0000000492400000,0x0000000492500000] [id=22788]

Threads with active compile tasks:

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000019d08d99c20] Metaspace_lock - owner thread: 0x0000019d301d6300

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000019d31000000-0x0000019d31bb0000-0x0000019d31bb0000), size 12255232, SharedBaseAddress: 0x0000019d31000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000019d32000000-0x0000019d66000000, reserved size: 872415232
Narrow klass base: 0x0000019d31000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 110592K, used 62260K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 5 young (20480K), 2 survivors (8192K)
 Metaspace       used 59188K, committed 59840K, reserved 917504K
  class space    used 8380K, committed 8704K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%|HS|  |TAMS 0x0000000680400000, 0x0000000680000000| Complete 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%| O|  |TAMS 0x0000000680800000, 0x0000000680400000| Untracked 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000680fcb800, 0x0000000681000000| 94%| O|  |TAMS 0x0000000680fcb800, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000681c2d200, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682400000, 0x0000000682400000|100%|HS|  |TAMS 0x0000000682000000, 0x0000000682000000| Complete 
|   9|0x0000000682400000, 0x0000000682800000, 0x0000000682800000|100%|HS|  |TAMS 0x0000000682400000, 0x0000000682400000| Complete 
|  10|0x0000000682800000, 0x0000000682800000, 0x0000000682c00000|  0%| F|  |TAMS 0x0000000682800000, 0x0000000682800000| Untracked 
|  11|0x0000000682c00000, 0x0000000682c00000, 0x0000000683000000|  0%| F|  |TAMS 0x0000000682c00000, 0x0000000682c00000| Untracked 
|  12|0x0000000683000000, 0x0000000683101800, 0x0000000683400000| 25%| O|  |TAMS 0x0000000683000000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x0000000683800000, 0x0000000683800000|100%| S|CS|TAMS 0x0000000683400000, 0x0000000683400000| Complete 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| S|CS|TAMS 0x0000000683800000, 0x0000000683800000| Complete 
|  15|0x0000000683c00000, 0x0000000683c00000, 0x0000000684000000|  0%| F|  |TAMS 0x0000000683c00000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684000000, 0x0000000684400000|  0%| F|  |TAMS 0x0000000684000000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684400000, 0x0000000684800000|  0%| F|  |TAMS 0x0000000684400000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684800000, 0x0000000684c00000|  0%| F|  |TAMS 0x0000000684800000, 0x0000000684800000| Untracked 
|  19|0x0000000684c00000, 0x0000000684c00000, 0x0000000685000000|  0%| F|  |TAMS 0x0000000684c00000, 0x0000000684c00000| Untracked 
|  20|0x0000000685000000, 0x0000000685000000, 0x0000000685400000|  0%| F|  |TAMS 0x0000000685000000, 0x0000000685000000| Untracked 
|  21|0x0000000685400000, 0x0000000685400000, 0x0000000685800000|  0%| F|  |TAMS 0x0000000685400000, 0x0000000685400000| Untracked 
|  22|0x0000000685800000, 0x0000000685800000, 0x0000000685c00000|  0%| F|  |TAMS 0x0000000685800000, 0x0000000685800000| Untracked 
|  23|0x0000000685c00000, 0x0000000685db9908, 0x0000000686000000| 43%| E|  |TAMS 0x0000000685c00000, 0x0000000685c00000| Complete 
|  24|0x0000000686000000, 0x0000000686400000, 0x0000000686400000|100%| E|CS|TAMS 0x0000000686000000, 0x0000000686000000| Complete 
|  31|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%| E|CS|TAMS 0x0000000687c00000, 0x0000000687c00000| Complete 
|1535|0x00000007ffc00000, 0x0000000800000000, 0x0000000800000000|100%| O|  |TAMS 0x0000000800000000, 0x00000007ffc00000| Untracked 

Card table byte_map: [0x0000019d204c0000,0x0000019d210c0000] _byte_map_base: 0x0000019d1d0c0000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000019d08e28990, (CMBitMap*) 0x0000019d08e289d0
 Prev Bits: [0x0000019d21cc0000, 0x0000019d27cc0000)
 Next Bits: [0x0000019d27cc0000, 0x0000019d2dcc0000)

Polling page: 0x0000019d06c00000

Metaspace:

Usage:
  Non-class:     49.62 MB used.
      Class:      8.18 MB used.
       Both:     57.80 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,      49.94 MB ( 78%) committed,  1 nodes.
      Class space:      832.00 MB reserved,       8.50 MB (  1%) committed,  1 nodes.
             Both:      896.00 MB reserved,      58.44 MB (  7%) committed. 

Chunk freelists:
   Non-Class:  13.48 MB
       Class:  7.45 MB
        Both:  20.94 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 59.06 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 822.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 935.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 3254.
num_chunk_merges: 6.
num_chunk_splits: 2127.
num_chunks_enlarged: 1414.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=3987Kb max_used=3987Kb free=116012Kb
 bounds [0x0000019d18390000, 0x0000019d18780000, 0x0000019d1f8c0000]
CodeHeap 'profiled nmethods': size=120000Kb used=12870Kb max_used=12870Kb free=107129Kb
 bounds [0x0000019d108c0000, 0x0000019d11560000, 0x0000019d17df0000]
CodeHeap 'non-nmethods': size=5760Kb used=2365Kb max_used=2429Kb free=3394Kb
 bounds [0x0000019d17df0000, 0x0000019d18070000, 0x0000019d18390000]
 total_blobs=7577 nmethods=6680 adapters=809
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 254.859 Thread 0x0000019d3018f0f0 7458  s!   3       org.gradle.internal.classloader.TransformReplacer$JarLoader::loadTransformedClass (55 bytes)
Event: 254.860 Thread 0x0000019d3018f0f0 nmethod 7458 0x0000019d11548e10 code [0x0000019d11549100, 0x0000019d1154a0e8]
Event: 254.860 Thread 0x0000019d3018f0f0 7459       3       org.gradle.internal.classloader.TransformReplacer$JarLoader::getJarFileLocked (104 bytes)
Event: 254.863 Thread 0x0000019d3018f0f0 nmethod 7459 0x0000019d1154a710 code [0x0000019d1154aaa0, 0x0000019d1154c398]
Event: 254.863 Thread 0x0000019d3018f0f0 7460       3       org.gradle.internal.classloader.TransformReplacer::access$200 (5 bytes)
Event: 254.863 Thread 0x0000019d3018f0f0 nmethod 7460 0x0000019d1154cc90 code [0x0000019d1154ce40, 0x0000019d1154d0a8]
Event: 255.100 Thread 0x0000019d3018f0f0 7462       3       sun.reflect.annotation.AnnotationParser::skipMemberValue (11 bytes)
Event: 255.100 Thread 0x0000019d3018f0f0 nmethod 7462 0x0000019d1154d210 code [0x0000019d1154d3c0, 0x0000019d1154d5b8]
Event: 255.100 Thread 0x0000019d3018f0f0 7463       3       sun.reflect.annotation.AnnotationParser::skipMemberValue (65 bytes)
Event: 255.101 Thread 0x0000019d3018f0f0 nmethod 7463 0x0000019d1154d690 code [0x0000019d1154d8c0, 0x0000019d1154dfd8]
Event: 255.101 Thread 0x0000019d3018f0f0 7464       3       java.lang.reflect.ProxyGenerator$ProxyMethod::codeUnwrapReturnValue (172 bytes)
Event: 255.102 Thread 0x0000019d3018f0f0 nmethod 7464 0x0000019d1154e210 code [0x0000019d1154e4c0, 0x0000019d1154f418]
Event: 255.112 Thread 0x0000019d3018e730 7465       4       jdk.internal.org.objectweb.asm.MethodVisitor::<init> (88 bytes)
Event: 255.114 Thread 0x0000019d3018e730 nmethod 7465 0x0000019d18773f10 code [0x0000019d18774080, 0x0000019d18774238]
Event: 255.117 Thread 0x0000019d3018f0f0 7466       3       java.util.concurrent.atomic.AtomicLong::<init> (10 bytes)
Event: 255.117 Thread 0x0000019d3018f0f0 nmethod 7466 0x0000019d1154f790 code [0x0000019d1154f940, 0x0000019d1154fb18]
Event: 255.266 Thread 0x0000019d3018f0f0 7467       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::access$6900 (4 bytes)
Event: 255.266 Thread 0x0000019d3018f0f0 nmethod 7467 0x0000019d1154fc10 code [0x0000019d1154fda0, 0x0000019d1154fe98]
Event: 255.268 Thread 0x0000019d3018f0f0 7468       3       org.objectweb.asm.AnnotationWriter::putAnnotations (110 bytes)
Event: 255.268 Thread 0x0000019d3018f0f0 nmethod 7468 0x0000019d1154ff10 code [0x0000019d11550100, 0x0000019d11550528]

GC Heap History (20 events):
Event: 76.824 GC heap before
{Heap before GC invocations=4 (full 0):
 garbage-first heap   total 131072K, used 75077K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 16 young (65536K), 2 survivors (8192K)
 Metaspace       used 21152K, committed 21504K, reserved 917504K
  class space    used 3067K, committed 3264K, reserved 851968K
}
Event: 76.837 GC heap after
{Heap after GC invocations=5 (full 0):
 garbage-first heap   total 131072K, used 24366K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 21152K, committed 21504K, reserved 917504K
  class space    used 3067K, committed 3264K, reserved 851968K
}
Event: 94.904 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 98304K, used 85806K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 18 young (73728K), 2 survivors (8192K)
 Metaspace       used 29720K, committed 30080K, reserved 917504K
  class space    used 4118K, committed 4288K, reserved 851968K
}
Event: 94.925 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 110592K, used 29926K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 29720K, committed 30080K, reserved 917504K
  class space    used 4118K, committed 4288K, reserved 851968K
}
Event: 116.130 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 110592K, used 74982K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 15 young (61440K), 3 survivors (12288K)
 Metaspace       used 35035K, committed 35456K, reserved 917504K
  class space    used 4848K, committed 5056K, reserved 851968K
}
Event: 116.144 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 110592K, used 31976K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 35035K, committed 35456K, reserved 917504K
  class space    used 4848K, committed 5056K, reserved 851968K
}
Event: 117.400 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 110592K, used 36072K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 1 survivors (4096K)
 Metaspace       used 35537K, committed 35968K, reserved 917504K
  class space    used 4914K, committed 5120K, reserved 851968K
}
Event: 117.406 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 110592K, used 31703K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 35537K, committed 35968K, reserved 917504K
  class space    used 4914K, committed 5120K, reserved 851968K
}
Event: 132.029 GC heap before
{Heap before GC invocations=10 (full 0):
 garbage-first heap   total 110592K, used 76759K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 1 survivors (4096K)
 Metaspace       used 38515K, committed 38976K, reserved 917504K
  class space    used 5278K, committed 5504K, reserved 851968K
}
Event: 132.036 GC heap after
{Heap after GC invocations=11 (full 0):
 garbage-first heap   total 110592K, used 33104K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 38515K, committed 38976K, reserved 917504K
  class space    used 5278K, committed 5504K, reserved 851968K
}
Event: 145.068 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 110592K, used 78160K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 2 survivors (8192K)
 Metaspace       used 43050K, committed 43584K, reserved 917504K
  class space    used 5999K, committed 6208K, reserved 851968K
}
Event: 145.081 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 110592K, used 33913K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 43050K, committed 43584K, reserved 917504K
  class space    used 5999K, committed 6208K, reserved 851968K
}
Event: 174.176 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 110592K, used 83065K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 47849K, committed 48448K, reserved 917504K
  class space    used 6664K, committed 6976K, reserved 851968K
}
Event: 174.234 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 110592K, used 35453K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 47849K, committed 48448K, reserved 917504K
  class space    used 6664K, committed 6976K, reserved 851968K
}
Event: 209.850 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 110592K, used 84605K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 52803K, committed 53312K, reserved 917504K
  class space    used 7430K, committed 7680K, reserved 851968K
}
Event: 209.864 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 110592K, used 37890K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 52803K, committed 53312K, reserved 917504K
  class space    used 7430K, committed 7680K, reserved 851968K
}
Event: 233.880 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 110592K, used 87042K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 14 young (57344K), 2 survivors (8192K)
 Metaspace       used 54868K, committed 55488K, reserved 917504K
  class space    used 7709K, committed 8000K, reserved 851968K
}
Event: 233.911 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 110592K, used 40196K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 54868K, committed 55488K, reserved 917504K
  class space    used 7709K, committed 8000K, reserved 851968K
}
Event: 253.046 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 110592K, used 89348K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 2 survivors (8192K)
 Metaspace       used 58568K, committed 59200K, reserved 917504K
  class space    used 8293K, committed 8576K, reserved 851968K
}
Event: 253.076 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 110592K, used 54068K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 58568K, committed 59200K, reserved 917504K
  class space    used 8293K, committed 8576K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.477 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.820 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 2.226 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 2.358 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 2.416 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 2.425 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 2.434 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 9.667 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 17.006 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 18.892 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 18.907 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 47.474 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 47.492 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 51.624 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 55.401 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 64.742 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform6132104835634938183dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 253.134 Thread 0x0000019d301d6300 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019d18423e0c relative=0x00000000000001ec
Event: 253.134 Thread 0x0000019d301d6300 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019d18423e0c method=java.security.SecureClassLoader$CodeSourceKey.hashCode()I @ 9 c2
Event: 253.134 Thread 0x0000019d301d6300 DEOPT PACKING pc=0x0000019d18423e0c sp=0x0000000493bf3ec0
Event: 253.134 Thread 0x0000019d301d6300 DEOPT UNPACKING pc=0x0000019d17e469a3 sp=0x0000000493bf3e50 mode 2
Event: 253.135 Thread 0x0000019d301d6300 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000019d1863f46c relative=0x0000000000000c6c
Event: 253.135 Thread 0x0000019d301d6300 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000019d1863f46c method=java.lang.ClassLoader.defineClassSourceLocation(Ljava/security/ProtectionDomain;)Ljava/lang/String; @ 15 c2
Event: 253.135 Thread 0x0000019d301d6300 DEOPT PACKING pc=0x0000019d1863f46c sp=0x0000000493bf3f80
Event: 253.135 Thread 0x0000019d301d6300 DEOPT UNPACKING pc=0x0000019d17e469a3 sp=0x0000000493bf3ef8 mode 2
Event: 253.377 Thread 0x0000019d301d6300 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000019d18686454 relative=0x0000000000000614
Event: 253.377 Thread 0x0000019d301d6300 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000019d18686454 method=java.lang.Throwable.<init>(Ljava/lang/String;Ljava/lang/Throwable;)V @ 24 c2
Event: 253.377 Thread 0x0000019d301d6300 DEOPT PACKING pc=0x0000019d18686454 sp=0x0000000493bf5bd0
Event: 253.377 Thread 0x0000019d301d6300 DEOPT UNPACKING pc=0x0000019d17e469a3 sp=0x0000000493bf5b98 mode 2
Event: 254.621 Thread 0x0000019d301d6300 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000019d1850cf2c relative=0x00000000000041ac
Event: 254.621 Thread 0x0000019d301d6300 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000019d1850cf2c method=org.gradle.internal.service.DefaultServiceRegistry.isSatisfiedBy(Ljava/lang/reflect/Type;Ljava/lang/reflect/Type;)Z @ 11 c2
Event: 254.621 Thread 0x0000019d301d6300 DEOPT PACKING pc=0x0000019d1850cf2c sp=0x0000000493bf5c10
Event: 254.621 Thread 0x0000019d301d6300 DEOPT UNPACKING pc=0x0000019d17e469a3 sp=0x0000000493bf5a70 mode 2
Event: 254.621 Thread 0x0000019d301d6300 Uncommon trap: trap_request=0xffffffde fr.pc=0x0000019d1850cf2c relative=0x00000000000041ac
Event: 254.621 Thread 0x0000019d301d6300 Uncommon trap: reason=class_check action=maybe_recompile pc=0x0000019d1850cf2c method=org.gradle.internal.service.DefaultServiceRegistry.isSatisfiedBy(Ljava/lang/reflect/Type;Ljava/lang/reflect/Type;)Z @ 11 c2
Event: 254.621 Thread 0x0000019d301d6300 DEOPT PACKING pc=0x0000019d1850cf2c sp=0x0000000493bf5c10
Event: 254.621 Thread 0x0000019d301d6300 DEOPT UNPACKING pc=0x0000019d17e469a3 sp=0x0000000493bf5a70 mode 2

Classes loaded (20 events):
Event: 248.899 Loading class jdk/jfr/FlightRecorderListener done
Event: 248.899 Loading class jdk/management/jfr/FlightRecorderMXBeanImpl$MXBeanListener done
Event: 250.305 Loading class java/time/format/DateTimeFormatterBuilder$WeekBasedFieldPrinterParser
Event: 250.305 Loading class java/time/format/DateTimeFormatterBuilder$WeekBasedFieldPrinterParser done
Event: 250.871 Loading class com/sun/jmx/mbeanserver/StandardMBeanSupport
Event: 250.872 Loading class com/sun/jmx/mbeanserver/StandardMBeanSupport done
Event: 250.872 Loading class com/sun/jmx/mbeanserver/StandardMBeanIntrospector
Event: 250.872 Loading class com/sun/jmx/mbeanserver/StandardMBeanIntrospector done
Event: 253.121 Loading class sun/reflect/misc/MethodUtil
Event: 253.127 Loading class sun/reflect/misc/MethodUtil done
Event: 253.127 Loading class sun/reflect/misc/MethodUtil$1
Event: 253.128 Loading class sun/reflect/misc/MethodUtil$1 done
Event: 253.386 Loading class java/time/temporal/WeekFields
Event: 253.387 Loading class java/time/temporal/WeekFields done
Event: 253.387 Loading class java/time/temporal/WeekFields$ComputedDayOfField
Event: 253.388 Loading class java/time/temporal/WeekFields$ComputedDayOfField done
Event: 253.412 Loading class java/time/Month$1
Event: 253.413 Loading class java/time/Month$1 done
Event: 253.424 Loading class java/nio/channels/Channels$1
Event: 253.425 Loading class java/nio/channels/Channels$1 done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 208.911 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x00000006839e0478}: static Lorg/gradle/api/internal/catalog/VersionModel;.<clinit>()V> (0x00000006839e0478) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 208.917 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x00000006839faa10}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x00000006839faa10) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 233.658 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000683aea988}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x0000000683aea988) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 759]
Event: 242.710 Thread 0x0000019d301d6300 Implicit null exception at 0x0000019d186ab592 to 0x0000019d186abca4
Event: 242.710 Thread 0x0000019d301d6300 Implicit null exception at 0x0000019d186aa594 to 0x0000019d186aacc8
Event: 247.253 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684f4bb78}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultConfigurationTimePropertiesAccessorVariantFactory;.<clinit>()V> (0x0000000684f4bb78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.256 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684f6f2e8}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultProjectIsolationStartParameterAccessorVariantFactory;.<clinit>()V> (0x0000000684f6f2e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.263 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684fa1db8}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultArtifactTypeAttributeAccessorVariantFactory;.<clinit>()V> (0x0000000684fa1db8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.270 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684fd4710}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultCompatibilityConventionRegistrar$Factory;.<clinit>()V> (0x0000000684fd4710) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.273 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684ff8700}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultJavaSourceSetsAccessorVariantFactory;.<clinit>()V> (0x0000000684ff8700) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.581 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x000000068481c478}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultIdeaSyncDetectorVariantFactory;.<clinit>()V> (0x000000068481c478) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.603 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684841110}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultKotlinTestReportCompatibilityHelperVariantFactory;.<clinit>()V> (0x0000000684841110) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.618 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x00000006848681a0}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultMppTestReportHelperVariantFactory;.<clinit>()V> (0x00000006848681a0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.629 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x000000068489b1a8}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultConfigurationCacheStartParameterAccessorVariantFactory;.<clinit>()V> (0x000000068489b1a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.636 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x00000006848c0640}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/MavenPluginConfigurator$DefaultMavenPluginConfiguratorVariantFactory;.<clinit>()V> (0x00000006848c0640) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.646 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x00000006848e3d00}: static Lorg/jetbrains/kotlin/gradle/targets/js/nodejs/DefaultUnameExecutorVariantFactory;.<clinit>()V> (0x00000006848e3d00) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 247.652 Thread 0x0000019d301d6300 Exception <a 'java/lang/NoSuchMethodError'{0x0000000684907e20}: static Lorg/jetbrains/kotlin/gradle/plugin/internal/DefaultBasePluginConfigurationVariantFactory;.<clinit>()V> (0x0000000684907e20) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1112]
Event: 248.128 Thread 0x0000019d301d6300 Exception <a 'java/lang/IncompatibleClassChangeError'{0x00000006849d4380}: Found class java.lang.Object, but interface was expected> (0x00000006849d4380) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 826]
Event: 253.134 Thread 0x0000019d301d6300 Implicit null exception at 0x0000019d18423c60 to 0x0000019d18423dfc
Event: 253.135 Thread 0x0000019d301d6300 Implicit null exception at 0x0000019d1863ed27 to 0x0000019d1863f450

VM Operations (20 events):
Event: 246.155 Executing VM operation: Cleanup
Event: 246.155 Executing VM operation: Cleanup done
Event: 246.234 Executing VM operation: HandshakeAllThreads
Event: 246.234 Executing VM operation: HandshakeAllThreads done
Event: 247.247 Executing VM operation: Cleanup
Event: 247.247 Executing VM operation: Cleanup done
Event: 248.257 Executing VM operation: Cleanup
Event: 248.522 Executing VM operation: Cleanup done
Event: 249.536 Executing VM operation: Cleanup
Event: 249.654 Executing VM operation: Cleanup done
Event: 250.660 Executing VM operation: Cleanup
Event: 250.841 Executing VM operation: Cleanup done
Event: 251.849 Executing VM operation: Cleanup
Event: 251.849 Executing VM operation: Cleanup done
Event: 252.858 Executing VM operation: Cleanup
Event: 252.858 Executing VM operation: Cleanup done
Event: 253.046 Executing VM operation: G1CollectForAllocation
Event: 253.076 Executing VM operation: G1CollectForAllocation done
Event: 255.084 Executing VM operation: Cleanup
Event: 255.084 Executing VM operation: Cleanup done

Events (20 events):
Event: 129.896 Thread 0x0000019d30208050 flushing nmethod 0x0000019d110a8910
Event: 129.896 Thread 0x0000019d30208050 flushing nmethod 0x0000019d110fe290
Event: 129.897 Thread 0x0000019d30208050 flushing nmethod 0x0000019d111a6890
Event: 146.444 Thread 0x0000019d305bbc90 Thread added: 0x0000019d305bbc90
Event: 146.445 Thread 0x0000019d305be000 Thread added: 0x0000019d305be000
Event: 149.475 Thread 0x0000019d305bd0d0 Thread added: 0x0000019d305bd0d0
Event: 149.479 Thread 0x0000019d305b9e30 Thread added: 0x0000019d305b9e30
Event: 154.977 Thread 0x0000019d305b8f00 Thread added: 0x0000019d305b8f00
Event: 154.981 Thread 0x0000019d305bcbc0 Thread added: 0x0000019d305bcbc0
Event: 215.048 Thread 0x0000019d6b8f3120 Thread added: 0x0000019d6b8f3120
Event: 215.048 Thread 0x0000019d6b8f4050 Thread added: 0x0000019d6b8f4050
Event: 215.048 Thread 0x0000019d6b8f4f80 Thread added: 0x0000019d6b8f4f80
Event: 228.012 Thread 0x0000019d6b8f17d0 Thread added: 0x0000019d6b8f17d0
Event: 228.015 Thread 0x0000019d6b8f4560 Thread added: 0x0000019d6b8f4560
Event: 228.017 Thread 0x0000019d6b8f4a70 Thread added: 0x0000019d6b8f4a70
Event: 228.846 Thread 0x0000019d6b8f3b40 Thread added: 0x0000019d6b8f3b40
Event: 228.847 Thread 0x0000019d6b8f1ce0 Thread added: 0x0000019d6b8f1ce0
Event: 228.849 Thread 0x0000019d6b8f21f0 Thread added: 0x0000019d6b8f21f0
Event: 233.381 Thread 0x0000019d6b8f2700 Thread added: 0x0000019d6b8f2700
Event: 251.328 Thread 0x0000019d6b8f2c10 Thread added: 0x0000019d6b8f2c10


Dynamic libraries:
0x00007ff6cc7f0000 - 0x00007ff6cc7fe000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffddafb0000 - 0x00007ffddb1c7000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffdd9760000 - 0x00007ffdd9824000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffdd88a0000 - 0x00007ffdd8c71000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffdd86c0000 - 0x00007ffdd87d1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffdbddb0000 - 0x00007ffdbddc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffdda8b0000 - 0x00007ffddaa61000 	C:\Windows\System32\USER32.dll
0x00007ffdd8520000 - 0x00007ffdd8546000 	C:\Windows\System32\win32u.dll
0x00007ffdd8c80000 - 0x00007ffdd8ca9000 	C:\Windows\System32\GDI32.dll
0x00007ffdd8400000 - 0x00007ffdd851b000 	C:\Windows\System32\gdi32full.dll
0x00007ffdd80f0000 - 0x00007ffdd818a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffdbd740000 - 0x00007ffdbd75d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffdc0e00000 - 0x00007ffdc1092000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffdd9de0000 - 0x00007ffdd9e87000 	C:\Windows\System32\msvcrt.dll
0x00007ffdda7a0000 - 0x00007ffdda7d1000 	C:\Windows\System32\IMM32.DLL
0x00007ffdc1410000 - 0x00007ffdc141c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffd1cfd0000 - 0x00007ffd1d05d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffd16270000 - 0x00007ffd16ee0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffdd9900000 - 0x00007ffdd99b1000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffdd9830000 - 0x00007ffdd98d7000 	C:\Windows\System32\sechost.dll
0x00007ffdd8290000 - 0x00007ffdd82b8000 	C:\Windows\System32\bcrypt.dll
0x00007ffddac20000 - 0x00007ffddad34000 	C:\Windows\System32\RPCRT4.dll
0x00007ffdda580000 - 0x00007ffdda5f1000 	C:\Windows\System32\WS2_32.dll
0x00007ffdd7fc0000 - 0x00007ffdd800d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffdcdc20000 - 0x00007ffdcdc54000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffdc96e0000 - 0x00007ffdc96ea000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffdd7fa0000 - 0x00007ffdd7fb3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffdd7100000 - 0x00007ffdd7118000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffdc0230000 - 0x00007ffdc023a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffdd20e0000 - 0x00007ffdd2312000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffdd99c0000 - 0x00007ffdd9d50000 	C:\Windows\System32\combase.dll
0x00007ffdda600000 - 0x00007ffdda6d7000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffdb8520000 - 0x00007ffdb8552000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffdd8190000 - 0x00007ffdd820b000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffdb4de0000 - 0x00007ffdb4dee000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffdb0030000 - 0x00007ffdb0055000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffd1cef0000 - 0x00007ffd1cfc7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffdd8d10000 - 0x00007ffdd9598000 	C:\Windows\System32\SHELL32.dll
0x00007ffdd82c0000 - 0x00007ffdd83ff000 	C:\Windows\System32\wintypes.dll
0x00007ffdd6000000 - 0x00007ffdd690d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffddad80000 - 0x00007ffddae8a000 	C:\Windows\System32\SHCORE.dll
0x00007ffddaf10000 - 0x00007ffddaf6e000 	C:\Windows\System32\shlwapi.dll
0x00007ffdd8020000 - 0x00007ffdd804b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffdab940000 - 0x00007ffdab958000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffdbce60000 - 0x00007ffdbce7a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffdcd8e0000 - 0x00007ffdcda0c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffdd7560000 - 0x00007ffdd75ca000 	C:\Windows\system32\mswsock.dll
0x00007ffdac240000 - 0x00007ffdac256000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffdbd730000 - 0x00007ffdbd740000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffda9480000 - 0x00007ffda94a7000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x0000000059e60000 - 0x0000000059ed3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffdbc7c0000 - 0x00007ffdbc7ca000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffdb9b10000 - 0x00007ffdb9b1b000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffddad60000 - 0x00007ffddad68000 	C:\Windows\System32\PSAPI.DLL
0x00007ffdd78b0000 - 0x00007ffdd78cb000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffdd7060000 - 0x00007ffdd7097000 	C:\Windows\system32\rsaenh.dll
0x00007ffdd7600000 - 0x00007ffdd7628000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffdd7890000 - 0x00007ffdd789c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffdd6b70000 - 0x00007ffdd6b9d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffdda6e0000 - 0x00007ffdda6e9000 	C:\Windows\System32\NSI.dll
0x00007ffdcd7e0000 - 0x00007ffdcd7f9000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffdcd7a0000 - 0x00007ffdcd7bf000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffdd6be0000 - 0x00007ffdd6ce2000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffdb5dc0000 - 0x00007ffdb5dc9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffdb4630000 - 0x00007ffdb463e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffdd8550000 - 0x00007ffdd86b6000 	C:\Windows\System32\CRYPT32.dll
0x00007ffdd79c0000 - 0x00007ffdd79ed000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffdd7980000 - 0x00007ffdd79b7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffdbec10000 - 0x00007ffdbec18000 	C:\Windows\system32\wshunix.dll
0x0000000059be0000 - 0x0000000059c53000 	C:\Users\<USER>\AppData\Local\Temp\native-platform6132104835634938183dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform6132104835634938183dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 7:52 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (486M free)
TotalPageFile size 25066M (AvailPageFile size 92M)
current process WorkingSet (physical memory assigned to process): 223M, peak: 232M
current process commit charge ("private bytes"): 312M, peak: 319M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
