#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 1140850688 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3678), pid=19520, tid=19684
#
# JRE version: OpenJDK Runtime Environment Microsoft-9889599 (17.0.12+7) (build 17.0.12+7-LTS)
# Java VM: OpenJDK 64-Bit Server VM Microsoft-9889599 (17.0.12+7-LTS, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: Intel(R) Core(TM) i5-7200U CPU @ 2.50GHz, 4 cores, 7G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Mon May  5 19:39:32 2025 W. Central Africa Standard Time elapsed time: 634.911804 seconds (0d 0h 10m 34s)

---------------  T H R E A D  ---------------

Current thread (0x0000027da9caf1f0):  VMThread "VM Thread" [stack: 0x000000ca38d00000,0x000000ca38e00000] [id=19684]

Stack: [0x000000ca38d00000,0x000000ca38e00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x686d99]
V  [jvm.dll+0x83daf8]
V  [jvm.dll+0x83f5a3]
V  [jvm.dll+0x83fc13]
V  [jvm.dll+0x2492ef]
V  [jvm.dll+0x683dd9]
V  [jvm.dll+0x6782fa]
V  [jvm.dll+0x30a797]
V  [jvm.dll+0x311d06]
V  [jvm.dll+0x36252e]
V  [jvm.dll+0x36276d]
V  [jvm.dll+0x2e1a9c]
V  [jvm.dll+0x2dfe49]
V  [jvm.dll+0x2df5ac]
V  [jvm.dll+0x3220ab]
V  [jvm.dll+0x84444d]
V  [jvm.dll+0x845183]
V  [jvm.dll+0x8456af]
V  [jvm.dll+0x845a94]
V  [jvm.dll+0x845b5e]
V  [jvm.dll+0x7ed87c]
V  [jvm.dll+0x685c77]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]

VM_Operation (0x000000ca3a3f7a90): G1CollectForAllocation, mode: safepoint, requested by thread 0x0000027de447a3d0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000027de67b9200, length=89, elements={
0x0000027d828dd3d0, 0x0000027da9cb6160, 0x0000027da9cb6fe0, 0x0000027da9cc77c0,
0x0000027da9cc94f0, 0x0000027da9ccbec0, 0x0000027da9ccc880, 0x0000027da9ccf630,
0x0000027da9cd1d00, 0x0000027de3010060, 0x0000027de30a8770, 0x0000027de3171830,
0x0000027de4478060, 0x0000027de4479ec0, 0x0000027de447b810, 0x0000027de447adf0,
0x0000027de4478a80, 0x0000027de447b300, 0x0000027de447a3d0, 0x0000027de4478570,
0x0000027de4478f90, 0x0000027de44794a0, 0x0000027de5368aa0, 0x0000027de5362f80,
0x0000027de53639a0, 0x0000027de5363490, 0x0000027de5368590, 0x0000027de5368080,
0x0000027de5363eb0, 0x0000027de5365d10, 0x0000027de53648d0, 0x0000027de5364de0,
0x0000027de53652f0, 0x0000027de5366220, 0x0000027de5366730, 0x0000027de5366c40,
0x0000027de5368fb0, 0x0000027de5367660, 0x0000027de53694c0, 0x0000027de53699d0,
0x0000027de5369ee0, 0x0000027de536a3f0, 0x0000027de536a900, 0x0000027de44799b0,
0x0000027de4b244a0, 0x0000027de4b27230, 0x0000027de4b253d0, 0x0000027de4b28160,
0x0000027de4b23a80, 0x0000027de4b23f90, 0x0000027de4b2a4d0, 0x0000027de4b23060,
0x0000027de4b258e0, 0x0000027de4b23570, 0x0000027de4b2a9e0, 0x0000027de4b25df0,
0x0000027de4b26810, 0x0000027de4b27740, 0x0000027de4b27c50, 0x0000027de4b295a0,
0x0000027de4b29ab0, 0x0000027de4b29fc0, 0x0000027de70f1a10, 0x0000027de70ebef0,
0x0000027de70ece20, 0x0000027de70ef6a0, 0x0000027de70f3360, 0x0000027de70f3870,
0x0000027de70f2e50, 0x0000027de70f0ff0, 0x0000027de70eec80, 0x0000027de70f1f20,
0x0000027de70f1500, 0x0000027de70ef190, 0x0000027de70efbb0, 0x0000027de70ee260,
0x0000027de70f00c0, 0x0000027de70f2430, 0x0000027de70f2940, 0x0000027de70ec400,
0x0000027de70ec910, 0x0000027de70ed840, 0x0000027de70edd50, 0x0000027de70ee770,
0x0000027de4b29090, 0x0000027de73dffd0, 0x0000027de73e09f0, 0x0000027de4b26300,
0x0000027de4b24ec0
}

Java Threads: ( => current thread )
  0x0000027d828dd3d0 JavaThread "main" [_thread_blocked, id=19644, stack(0x000000ca38700000,0x000000ca38800000)]
  0x0000027da9cb6160 JavaThread "Reference Handler" daemon [_thread_blocked, id=19688, stack(0x000000ca38e00000,0x000000ca38f00000)]
  0x0000027da9cb6fe0 JavaThread "Finalizer" daemon [_thread_blocked, id=19692, stack(0x000000ca38f00000,0x000000ca39000000)]
  0x0000027da9cc77c0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=19696, stack(0x000000ca39000000,0x000000ca39100000)]
  0x0000027da9cc94f0 JavaThread "Attach Listener" daemon [_thread_blocked, id=19700, stack(0x000000ca39100000,0x000000ca39200000)]
  0x0000027da9ccbec0 JavaThread "Service Thread" daemon [_thread_blocked, id=19704, stack(0x000000ca39200000,0x000000ca39300000)]
  0x0000027da9ccc880 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=19708, stack(0x000000ca39300000,0x000000ca39400000)]
  0x0000027da9ccf630 JavaThread "C2 CompilerThread0" daemon [_thread_blocked, id=19712, stack(0x000000ca39400000,0x000000ca39500000)]
  0x0000027da9cd1d00 JavaThread "C1 CompilerThread0" daemon [_thread_blocked, id=19716, stack(0x000000ca39500000,0x000000ca39600000)]
  0x0000027de3010060 JavaThread "Sweeper thread" daemon [_thread_blocked, id=19720, stack(0x000000ca39600000,0x000000ca39700000)]
  0x0000027de30a8770 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=19728, stack(0x000000ca39700000,0x000000ca39800000)]
  0x0000027de3171830 JavaThread "Notification Thread" daemon [_thread_blocked, id=19760, stack(0x000000ca39800000,0x000000ca39900000)]
  0x0000027de4478060 JavaThread "Daemon health stats" [_thread_blocked, id=18888, stack(0x000000ca39d00000,0x000000ca39e00000)]
  0x0000027de4479ec0 JavaThread "Incoming local TCP Connector on port 50761" [_thread_in_native, id=19264, stack(0x000000ca39e00000,0x000000ca39f00000)]
  0x0000027de447b810 JavaThread "Daemon periodic checks" [_thread_blocked, id=5512, stack(0x000000ca39f00000,0x000000ca3a000000)]
  0x0000027de447adf0 JavaThread "Daemon" [_thread_blocked, id=18660, stack(0x000000ca3a000000,0x000000ca3a100000)]
  0x0000027de4478a80 JavaThread "Handler for socket connection from /127.0.0.1:50761 to /127.0.0.1:50763" [_thread_in_native, id=3888, stack(0x000000ca3a100000,0x000000ca3a200000)]
  0x0000027de447b300 JavaThread "Cancel handler" [_thread_blocked, id=12452, stack(0x000000ca3a200000,0x000000ca3a300000)]
  0x0000027de447a3d0 JavaThread "Daemon worker" [_thread_blocked, id=17488, stack(0x000000ca3a300000,0x000000ca3a400000)]
  0x0000027de4478570 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:50761 to /127.0.0.1:50763" [_thread_blocked, id=13684, stack(0x000000ca3a400000,0x000000ca3a500000)]
  0x0000027de4478f90 JavaThread "Stdin handler" [_thread_blocked, id=12528, stack(0x000000ca3a500000,0x000000ca3a600000)]
  0x0000027de44794a0 JavaThread "Daemon client event forwarder" [_thread_blocked, id=10376, stack(0x000000ca3a600000,0x000000ca3a700000)]
  0x0000027de5368aa0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_blocked, id=16260, stack(0x000000ca3a700000,0x000000ca3a800000)]
  0x0000027de5362f80 JavaThread "File lock request listener" [_thread_in_native, id=17256, stack(0x000000ca3a800000,0x000000ca3a900000)]
  0x0000027de53639a0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=1512, stack(0x000000ca3a900000,0x000000ca3aa00000)]
  0x0000027de5363490 JavaThread "File lock release action executor" [_thread_blocked, id=13544, stack(0x000000ca3aa00000,0x000000ca3ab00000)]
  0x0000027de5368590 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=10536, stack(0x000000ca3ab00000,0x000000ca3ac00000)]
  0x0000027de5368080 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=3504, stack(0x000000ca3ac00000,0x000000ca3ad00000)]
  0x0000027de5363eb0 JavaThread "File watcher server" daemon [_thread_in_native, id=18064, stack(0x000000ca3ad00000,0x000000ca3ae00000)]
  0x0000027de5365d10 JavaThread "File watcher consumer" daemon [_thread_blocked, id=19624, stack(0x000000ca3ae00000,0x000000ca3af00000)]
  0x0000027de53648d0 JavaThread "jar transforms" [_thread_blocked, id=19404, stack(0x000000ca3b100000,0x000000ca3b200000)]
  0x0000027de5364de0 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\.gradle\8.12\checksums)" [_thread_blocked, id=19880, stack(0x000000ca3b200000,0x000000ca3b300000)]
  0x0000027de53652f0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=18180, stack(0x000000ca3b300000,0x000000ca3b400000)]
  0x0000027de5366220 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=1428, stack(0x000000ca3b400000,0x000000ca3b500000)]
  0x0000027de5366730 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=2120, stack(0x000000ca3b500000,0x000000ca3b600000)]
  0x0000027de5366c40 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\buildOutputCleanup)" [_thread_blocked, id=19460, stack(0x000000ca3b600000,0x000000ca3b700000)]
  0x0000027de5368fb0 JavaThread "Unconstrained build operations" [_thread_blocked, id=18128, stack(0x000000ca3b700000,0x000000ca3b800000)]
  0x0000027de5367660 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=2044, stack(0x000000ca3b800000,0x000000ca3b900000)]
  0x0000027de53694c0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=1192, stack(0x000000ca3b900000,0x000000ca3ba00000)]
  0x0000027de53699d0 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=19904, stack(0x000000ca3ba00000,0x000000ca3bb00000)]
  0x0000027de5369ee0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=20160, stack(0x000000ca3bb00000,0x000000ca3bc00000)]
  0x0000027de536a3f0 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=20360, stack(0x000000ca3bc00000,0x000000ca3bd00000)]
  0x0000027de536a900 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=20008, stack(0x000000ca38500000,0x000000ca38600000)]
  0x0000027de44799b0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=4348, stack(0x000000ca38600000,0x000000ca38700000)]
  0x0000027de4b244a0 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=20484, stack(0x000000ca3bd00000,0x000000ca3be00000)]
  0x0000027de4b27230 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=20492, stack(0x000000ca3be00000,0x000000ca3bf00000)]
  0x0000027de4b253d0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=20496, stack(0x000000ca3bf00000,0x000000ca3c000000)]
  0x0000027de4b28160 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=20500, stack(0x000000ca3c000000,0x000000ca3c100000)]
  0x0000027de4b23a80 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=20520, stack(0x000000ca3c100000,0x000000ca3c200000)]
  0x0000027de4b23f90 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=20524, stack(0x000000ca3c200000,0x000000ca3c300000)]
  0x0000027de4b2a4d0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=20528, stack(0x000000ca3c300000,0x000000ca3c400000)]
  0x0000027de4b23060 JavaThread "Memory manager" [_thread_blocked, id=20656, stack(0x000000ca3c400000,0x000000ca3c500000)]
  0x0000027de4b258e0 JavaThread "build event listener" [_thread_blocked, id=20980, stack(0x000000ca3af00000,0x000000ca3b000000)]
  0x0000027de4b23570 JavaThread "Execution worker" [_thread_blocked, id=20796, stack(0x000000ca3c900000,0x000000ca3ca00000)]
  0x0000027de4b2a9e0 JavaThread "Execution worker Thread 2" [_thread_blocked, id=20788, stack(0x000000ca3ca00000,0x000000ca3cb00000)]
  0x0000027de4b25df0 JavaThread "Execution worker Thread 3" [_thread_blocked, id=12684, stack(0x000000ca3cb00000,0x000000ca3cc00000)]
  0x0000027de4b26810 JavaThread "Cache worker for execution history cache (C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\@react-native\gradle-plugin\.gradle\8.12\executionHistory)" [_thread_blocked, id=18440, stack(0x000000ca3cc00000,0x000000ca3cd00000)]
  0x0000027de4b27740 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=20604, stack(0x000000ca3cd00000,0x000000ca3ce00000)]
  0x0000027de4b27c50 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=688, stack(0x000000ca3ce00000,0x000000ca3cf00000)]
  0x0000027de4b295a0 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=20724, stack(0x000000ca3cf00000,0x000000ca3d000000)]
  0x0000027de4b29ab0 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=20904, stack(0x000000ca3d000000,0x000000ca3d100000)]
  0x0000027de4b29fc0 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=20872, stack(0x000000ca3d100000,0x000000ca3d200000)]
  0x0000027de70f1a10 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=17808, stack(0x000000ca3d200000,0x000000ca3d300000)]
  0x0000027de70ebef0 JavaThread "Unconstrained build operations Thread 22" [_thread_blocked, id=20892, stack(0x000000ca3d300000,0x000000ca3d400000)]
  0x0000027de70ece20 JavaThread "Unconstrained build operations Thread 23" [_thread_blocked, id=20844, stack(0x000000ca3d400000,0x000000ca3d500000)]
  0x0000027de70ef6a0 JavaThread "Unconstrained build operations Thread 24" [_thread_blocked, id=20840, stack(0x000000ca3d500000,0x000000ca3d600000)]
  0x0000027de70f3360 JavaThread "Unconstrained build operations Thread 25" [_thread_blocked, id=7512, stack(0x000000ca3d600000,0x000000ca3d700000)]
  0x0000027de70f3870 JavaThread "Unconstrained build operations Thread 26" [_thread_blocked, id=1300, stack(0x000000ca3d700000,0x000000ca3d800000)]
  0x0000027de70f2e50 JavaThread "Unconstrained build operations Thread 27" [_thread_blocked, id=18668, stack(0x000000ca3d800000,0x000000ca3d900000)]
  0x0000027de70f0ff0 JavaThread "Unconstrained build operations Thread 28" [_thread_blocked, id=9744, stack(0x000000ca3d900000,0x000000ca3da00000)]
  0x0000027de70eec80 JavaThread "Unconstrained build operations Thread 29" [_thread_blocked, id=21060, stack(0x000000ca3da00000,0x000000ca3db00000)]
  0x0000027de70f1f20 JavaThread "Unconstrained build operations Thread 30" [_thread_blocked, id=21076, stack(0x000000ca3db00000,0x000000ca3dc00000)]
  0x0000027de70f1500 JavaThread "Unconstrained build operations Thread 31" [_thread_blocked, id=21092, stack(0x000000ca3dc00000,0x000000ca3dd00000)]
  0x0000027de70ef190 JavaThread "Unconstrained build operations Thread 32" [_thread_blocked, id=18004, stack(0x000000ca3dd00000,0x000000ca3de00000)]
  0x0000027de70efbb0 JavaThread "Unconstrained build operations Thread 33" [_thread_blocked, id=14816, stack(0x000000ca3de00000,0x000000ca3df00000)]
  0x0000027de70ee260 JavaThread "Unconstrained build operations Thread 34" [_thread_blocked, id=21160, stack(0x000000ca3df00000,0x000000ca3e000000)]
  0x0000027de70f00c0 JavaThread "Unconstrained build operations Thread 35" [_thread_blocked, id=21152, stack(0x000000ca3e000000,0x000000ca3e100000)]
  0x0000027de70f2430 JavaThread "Unconstrained build operations Thread 36" [_thread_blocked, id=21396, stack(0x000000ca3e100000,0x000000ca3e200000)]
  0x0000027de70f2940 JavaThread "jar transforms Thread 2" [_thread_blocked, id=16688, stack(0x000000ca3e200000,0x000000ca3e300000)]
  0x0000027de70ec400 JavaThread "jar transforms Thread 3" [_thread_blocked, id=18956, stack(0x000000ca3e300000,0x000000ca3e400000)]
  0x0000027de70ec910 JavaThread "Unconstrained build operations Thread 37" [_thread_blocked, id=13480, stack(0x000000ca3e400000,0x000000ca3e500000)]
  0x0000027de70ed840 JavaThread "Unconstrained build operations Thread 38" [_thread_blocked, id=20032, stack(0x000000ca3e500000,0x000000ca3e600000)]
  0x0000027de70edd50 JavaThread "Unconstrained build operations Thread 39" [_thread_blocked, id=20320, stack(0x000000ca3e600000,0x000000ca3e700000)]
  0x0000027de70ee770 JavaThread "Unconstrained build operations Thread 40" [_thread_blocked, id=3064, stack(0x000000ca3e700000,0x000000ca3e800000)]
  0x0000027de4b29090 JavaThread "jar transforms Thread 4" [_thread_blocked, id=20396, stack(0x000000ca3e800000,0x000000ca3e900000)]
  0x0000027de73dffd0 JavaThread "build event listener" [_thread_blocked, id=21912, stack(0x000000ca3ea00000,0x000000ca3eb00000)]
  0x0000027de73e09f0 JavaThread "Problems report writer" [_thread_blocked, id=22172, stack(0x000000ca3c700000,0x000000ca3c800000)]
  0x0000027de4b26300 JavaThread "ForkJoinPool.commonPool-worker-4" daemon [_thread_blocked, id=15868, stack(0x000000ca3b000000,0x000000ca3b100000)]
  0x0000027de4b24ec0 JavaThread "ForkJoinPool.commonPool-worker-6" daemon [_thread_blocked, id=11640, stack(0x000000ca3c600000,0x000000ca3c700000)]

Other Threads:
=>0x0000027da9caf1f0 VMThread "VM Thread" [stack: 0x000000ca38d00000,0x000000ca38e00000] [id=19684]
  0x0000027de319dba0 WatcherThread [stack: 0x000000ca39900000,0x000000ca39a00000] [id=19764]
  0x0000027d82966a50 GCTaskThread "GC Thread#0" [stack: 0x000000ca38800000,0x000000ca38900000] [id=19664]
  0x0000027de3256620 GCTaskThread "GC Thread#1" [stack: 0x000000ca39a00000,0x000000ca39b00000] [id=19824]
  0x0000027de32568e0 GCTaskThread "GC Thread#2" [stack: 0x000000ca39b00000,0x000000ca39c00000] [id=19828]
  0x0000027de3c99070 GCTaskThread "GC Thread#3" [stack: 0x000000ca39c00000,0x000000ca39d00000] [id=19960]
  0x0000027d829739a0 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000ca38900000,0x000000ca38a00000] [id=19668]
  0x0000027d829743c0 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000ca38a00000,0x000000ca38b00000] [id=19672]
  0x0000027d8298e6e0 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000ca38b00000,0x000000ca38c00000] [id=19676]
  0x0000027de6ddc640 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000ca3eb00000,0x000000ca3ec00000] [id=20332]
  0x0000027da9b6e2b0 ConcurrentGCThread "G1 Service" [stack: 0x000000ca38c00000,0x000000ca38d00000] [id=19680]

Threads with active compile tasks:
C1 CompilerThread0   635105 19818   !   3       jdk.proxy6.$Proxy132::annotationType (29 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x0000027d828d9f10] Threads_lock - owner thread: 0x0000027da9caf1f0
[0x0000027d828d98e0] Heap_lock - owner thread: 0x0000027de447a3d0

Heap address: 0x0000000680000000, size: 6144 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000027daa000000-0x0000027daabb0000-0x0000027daabb0000), size 12255232, SharedBaseAddress: 0x0000027daa000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x0000027dab000000-0x0000027ddf000000, reserved size: 872415232
Narrow klass base: 0x0000027daa000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 4 total, 4 available
 Memory: 8067M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 128M
 Heap Max Capacity: 6G
 Pre-touch: Disabled
 Parallel Workers: 4
 Concurrent Workers: 1
 Concurrent Refinement Workers: 4
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 663552K, used 318324K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 116450K, committed 118336K, reserved 983040K
  class space    used 16163K, committed 17024K, reserved 851968K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000680000000, 0x0000000680400000, 0x0000000680400000|100%| O|  |TAMS 0x0000000680400000, 0x0000000680000000| Untracked 
|   1|0x0000000680400000, 0x0000000680800000, 0x0000000680800000|100%|HS|  |TAMS 0x0000000680800000, 0x0000000680400000| Complete 
|   2|0x0000000680800000, 0x0000000680c00000, 0x0000000680c00000|100%| O|  |TAMS 0x0000000680c00000, 0x0000000680800000| Untracked 
|   3|0x0000000680c00000, 0x0000000681000000, 0x0000000681000000|100%| O|  |TAMS 0x0000000681000000, 0x0000000680c00000| Untracked 
|   4|0x0000000681000000, 0x0000000681400000, 0x0000000681400000|100%| O|  |TAMS 0x0000000681400000, 0x0000000681000000| Untracked 
|   5|0x0000000681400000, 0x0000000681800000, 0x0000000681800000|100%| O|  |TAMS 0x0000000681800000, 0x0000000681400000| Untracked 
|   6|0x0000000681800000, 0x0000000681c00000, 0x0000000681c00000|100%| O|  |TAMS 0x0000000681c00000, 0x0000000681800000| Untracked 
|   7|0x0000000681c00000, 0x0000000682000000, 0x0000000682000000|100%| O|  |TAMS 0x0000000682000000, 0x0000000681c00000| Untracked 
|   8|0x0000000682000000, 0x0000000682400000, 0x0000000682400000|100%| O|  |TAMS 0x0000000682400000, 0x0000000682000000| Untracked 
|   9|0x0000000682400000, 0x0000000682800000, 0x0000000682800000|100%|HS|  |TAMS 0x0000000682800000, 0x0000000682400000| Complete 
|  10|0x0000000682800000, 0x0000000682c00000, 0x0000000682c00000|100%|HS|  |TAMS 0x0000000682c00000, 0x0000000682800000| Complete 
|  11|0x0000000682c00000, 0x0000000683000000, 0x0000000683000000|100%| O|  |TAMS 0x0000000683000000, 0x0000000682c00000| Untracked 
|  12|0x0000000683000000, 0x0000000683400000, 0x0000000683400000|100%| O|  |TAMS 0x0000000683400000, 0x0000000683000000| Untracked 
|  13|0x0000000683400000, 0x0000000683800000, 0x0000000683800000|100%| O|  |TAMS 0x0000000683800000, 0x0000000683400000| Untracked 
|  14|0x0000000683800000, 0x0000000683c00000, 0x0000000683c00000|100%| O|  |TAMS 0x0000000683c00000, 0x0000000683800000| Untracked 
|  15|0x0000000683c00000, 0x0000000684000000, 0x0000000684000000|100%| O|  |TAMS 0x0000000684000000, 0x0000000683c00000| Untracked 
|  16|0x0000000684000000, 0x0000000684400000, 0x0000000684400000|100%| O|  |TAMS 0x0000000684400000, 0x0000000684000000| Untracked 
|  17|0x0000000684400000, 0x0000000684800000, 0x0000000684800000|100%| O|  |TAMS 0x0000000684800000, 0x0000000684400000| Untracked 
|  18|0x0000000684800000, 0x0000000684c00000, 0x0000000684c00000|100%| O|  |TAMS 0x0000000684c00000, 0x0000000684800000| Untracked 
|  19|0x0000000684c00000, 0x0000000685000000, 0x0000000685000000|100%| O|  |TAMS 0x0000000685000000, 0x0000000684c00000| Untracked 
|  20|0x0000000685000000, 0x0000000685400000, 0x0000000685400000|100%| O|  |TAMS 0x0000000685400000, 0x0000000685000000| Untracked 
|  21|0x0000000685400000, 0x0000000685800000, 0x0000000685800000|100%| O|  |TAMS 0x0000000685800000, 0x0000000685400000| Untracked 
|  22|0x0000000685800000, 0x0000000685c00000, 0x0000000685c00000|100%|HS|  |TAMS 0x0000000685c00000, 0x0000000685800000| Complete 
|  23|0x0000000685c00000, 0x0000000686000000, 0x0000000686000000|100%| O|  |TAMS 0x0000000686000000, 0x0000000685c00000| Untracked 
|  24|0x0000000686000000, 0x0000000686400000, 0x0000000686400000|100%| O|  |TAMS 0x0000000686400000, 0x0000000686000000| Untracked 
|  25|0x0000000686400000, 0x0000000686800000, 0x0000000686800000|100%| O|  |TAMS 0x0000000686800000, 0x0000000686400000| Untracked 
|  26|0x0000000686800000, 0x0000000686c00000, 0x0000000686c00000|100%|HS|  |TAMS 0x0000000686c00000, 0x0000000686800000| Complete 
|  27|0x0000000686c00000, 0x0000000687000000, 0x0000000687000000|100%| O|  |TAMS 0x0000000687000000, 0x0000000686c00000| Untracked 
|  28|0x0000000687000000, 0x0000000687400000, 0x0000000687400000|100%| O|  |TAMS 0x0000000687400000, 0x0000000687000000| Untracked 
|  29|0x0000000687400000, 0x0000000687800000, 0x0000000687800000|100%| O|  |TAMS 0x0000000687800000, 0x0000000687400000| Untracked 
|  30|0x0000000687800000, 0x0000000687c00000, 0x0000000687c00000|100%| O|  |TAMS 0x0000000687c00000, 0x0000000687800000| Untracked 
|  31|0x0000000687c00000, 0x0000000688000000, 0x0000000688000000|100%| O|  |TAMS 0x0000000688000000, 0x0000000687c00000| Untracked 
|  32|0x0000000688000000, 0x0000000688400000, 0x0000000688400000|100%| O|  |TAMS 0x0000000688400000, 0x0000000688000000| Untracked 
|  33|0x0000000688400000, 0x0000000688800000, 0x0000000688800000|100%| O|  |TAMS 0x0000000688800000, 0x0000000688400000| Untracked 
|  34|0x0000000688800000, 0x0000000688c00000, 0x0000000688c00000|100%| O|  |TAMS 0x0000000688c00000, 0x0000000688800000| Untracked 
|  35|0x0000000688c00000, 0x0000000689000000, 0x0000000689000000|100%| O|  |TAMS 0x0000000689000000, 0x0000000688c00000| Untracked 
|  36|0x0000000689000000, 0x0000000689400000, 0x0000000689400000|100%| O|  |TAMS 0x0000000689400000, 0x0000000689000000| Untracked 
|  37|0x0000000689400000, 0x0000000689800000, 0x0000000689800000|100%| O|  |TAMS 0x0000000689800000, 0x0000000689400000| Untracked 
|  38|0x0000000689800000, 0x0000000689c00000, 0x0000000689c00000|100%| O|  |TAMS 0x0000000689c00000, 0x0000000689800000| Untracked 
|  39|0x0000000689c00000, 0x000000068a000000, 0x000000068a000000|100%| O|  |TAMS 0x000000068a000000, 0x0000000689c00000| Untracked 
|  40|0x000000068a000000, 0x000000068a400000, 0x000000068a400000|100%| O|  |TAMS 0x000000068a400000, 0x000000068a000000| Untracked 
|  41|0x000000068a400000, 0x000000068a800000, 0x000000068a800000|100%| O|  |TAMS 0x000000068a800000, 0x000000068a400000| Untracked 
|  42|0x000000068a800000, 0x000000068ac00000, 0x000000068ac00000|100%| O|  |TAMS 0x000000068ac00000, 0x000000068a800000| Untracked 
|  43|0x000000068ac00000, 0x000000068b000000, 0x000000068b000000|100%| O|  |TAMS 0x000000068b000000, 0x000000068ac00000| Untracked 
|  44|0x000000068b000000, 0x000000068b400000, 0x000000068b400000|100%| O|  |TAMS 0x000000068b400000, 0x000000068b000000| Untracked 
|  45|0x000000068b400000, 0x000000068b800000, 0x000000068b800000|100%| O|  |TAMS 0x000000068b800000, 0x000000068b400000| Untracked 
|  46|0x000000068b800000, 0x000000068bc00000, 0x000000068bc00000|100%| O|  |TAMS 0x000000068bc00000, 0x000000068b800000| Untracked 
|  47|0x000000068bc00000, 0x000000068c000000, 0x000000068c000000|100%| O|  |TAMS 0x000000068c000000, 0x000000068bc00000| Untracked 
|  48|0x000000068c000000, 0x000000068c400000, 0x000000068c400000|100%| O|  |TAMS 0x000000068c400000, 0x000000068c000000| Untracked 
|  49|0x000000068c400000, 0x000000068c800000, 0x000000068c800000|100%| O|  |TAMS 0x000000068c800000, 0x000000068c400000| Untracked 
|  50|0x000000068c800000, 0x000000068cc00000, 0x000000068cc00000|100%| O|  |TAMS 0x000000068cc00000, 0x000000068c800000| Untracked 
|  51|0x000000068cc00000, 0x000000068d000000, 0x000000068d000000|100%| O|  |TAMS 0x000000068d000000, 0x000000068cc00000| Untracked 
|  52|0x000000068d000000, 0x000000068d400000, 0x000000068d400000|100%| O|  |TAMS 0x000000068d400000, 0x000000068d000000| Untracked 
|  53|0x000000068d400000, 0x000000068d800000, 0x000000068d800000|100%| O|  |TAMS 0x000000068d800000, 0x000000068d400000| Untracked 
|  54|0x000000068d800000, 0x000000068dc00000, 0x000000068dc00000|100%| O|  |TAMS 0x000000068d800000, 0x000000068d800000| Untracked 
|  55|0x000000068dc00000, 0x000000068e000000, 0x000000068e000000|100%| O|  |TAMS 0x000000068e000000, 0x000000068dc00000| Untracked 
|  56|0x000000068e000000, 0x000000068e400000, 0x000000068e400000|100%| O|  |TAMS 0x000000068e400000, 0x000000068e000000| Untracked 
|  57|0x000000068e400000, 0x000000068e800000, 0x000000068e800000|100%| O|  |TAMS 0x000000068e800000, 0x000000068e400000| Untracked 
|  58|0x000000068e800000, 0x000000068ec00000, 0x000000068ec00000|100%| O|  |TAMS 0x000000068ec00000, 0x000000068e800000| Untracked 
|  59|0x000000068ec00000, 0x000000068f000000, 0x000000068f000000|100%| O|  |TAMS 0x000000068ec00000, 0x000000068ec00000| Untracked 
|  60|0x000000068f000000, 0x000000068f400000, 0x000000068f400000|100%| O|  |TAMS 0x000000068f400000, 0x000000068f000000| Untracked 
|  61|0x000000068f400000, 0x000000068f800000, 0x000000068f800000|100%| O|  |TAMS 0x000000068f800000, 0x000000068f400000| Untracked 
|  62|0x000000068f800000, 0x000000068fc00000, 0x000000068fc00000|100%| O|  |TAMS 0x000000068fc00000, 0x000000068f800000| Untracked 
|  63|0x000000068fc00000, 0x0000000690000000, 0x0000000690000000|100%| O|  |TAMS 0x0000000690000000, 0x000000068fc00000| Untracked 
|  64|0x0000000690000000, 0x0000000690400000, 0x0000000690400000|100%| O|  |TAMS 0x0000000690400000, 0x0000000690000000| Untracked 
|  65|0x0000000690400000, 0x0000000690800000, 0x0000000690800000|100%| O|  |TAMS 0x0000000690800000, 0x0000000690400000| Untracked 
|  66|0x0000000690800000, 0x0000000690c00000, 0x0000000690c00000|100%| O|  |TAMS 0x0000000690c00000, 0x0000000690800000| Untracked 
|  67|0x0000000690c00000, 0x0000000690c00000, 0x0000000691000000|  0%| F|  |TAMS 0x0000000690c00000, 0x0000000690c00000| Untracked 
|  68|0x0000000691000000, 0x0000000691400000, 0x0000000691400000|100%| O|  |TAMS 0x0000000691400000, 0x0000000691000000| Untracked 
|  69|0x0000000691400000, 0x0000000691800000, 0x0000000691800000|100%| O|  |TAMS 0x0000000691800000, 0x0000000691400000| Untracked 
|  70|0x0000000691800000, 0x0000000691c00000, 0x0000000691c00000|100%| O|  |TAMS 0x0000000691c00000, 0x0000000691800000| Untracked 
|  71|0x0000000691c00000, 0x0000000692000000, 0x0000000692000000|100%| O|  |TAMS 0x0000000691d38c00, 0x0000000691c00000| Untracked 
|  72|0x0000000692000000, 0x0000000692400000, 0x0000000692400000|100%| O|  |TAMS 0x0000000692000000, 0x0000000692000000| Untracked 
|  73|0x0000000692400000, 0x0000000692800000, 0x0000000692800000|100%| O|  |TAMS 0x0000000692400000, 0x0000000692400000| Untracked 
|  74|0x0000000692800000, 0x0000000692c00000, 0x0000000692c00000|100%| O|  |TAMS 0x0000000692800000, 0x0000000692800000| Untracked 
|  75|0x0000000692c00000, 0x0000000693000000, 0x0000000693000000|100%| O|  |TAMS 0x0000000692c00000, 0x0000000692c00000| Untracked 
|  76|0x0000000693000000, 0x0000000693400000, 0x0000000693400000|100%| O|  |TAMS 0x0000000693000000, 0x0000000693000000| Untracked 
|  77|0x0000000693400000, 0x00000006936dd200, 0x0000000693800000| 71%| O|  |TAMS 0x0000000693400000, 0x0000000693400000| Untracked 
|  78|0x0000000693800000, 0x0000000693800000, 0x0000000693c00000|  0%| F|  |TAMS 0x0000000693800000, 0x0000000693800000| Untracked 
|  79|0x0000000693c00000, 0x0000000693c00000, 0x0000000694000000|  0%| F|  |TAMS 0x0000000693c00000, 0x0000000693c00000| Untracked 
|  80|0x0000000694000000, 0x0000000694000000, 0x0000000694400000|  0%| F|  |TAMS 0x0000000694000000, 0x0000000694000000| Untracked 
|  81|0x0000000694400000, 0x0000000694400000, 0x0000000694800000|  0%| F|  |TAMS 0x0000000694400000, 0x0000000694400000| Untracked 
|  82|0x0000000694800000, 0x0000000694800000, 0x0000000694c00000|  0%| F|  |TAMS 0x0000000694800000, 0x0000000694800000| Untracked 
|  83|0x0000000694c00000, 0x0000000694c00000, 0x0000000695000000|  0%| F|  |TAMS 0x0000000694c00000, 0x0000000694c00000| Untracked 
|  84|0x0000000695000000, 0x0000000695000000, 0x0000000695400000|  0%| F|  |TAMS 0x0000000695000000, 0x0000000695000000| Untracked 
|  85|0x0000000695400000, 0x0000000695400000, 0x0000000695800000|  0%| F|  |TAMS 0x0000000695400000, 0x0000000695400000| Untracked 
|  86|0x0000000695800000, 0x0000000695800000, 0x0000000695c00000|  0%| F|  |TAMS 0x0000000695800000, 0x0000000695800000| Untracked 
|  87|0x0000000695c00000, 0x0000000695c00000, 0x0000000696000000|  0%| F|  |TAMS 0x0000000695c00000, 0x0000000695c00000| Untracked 
|  88|0x0000000696000000, 0x0000000696000000, 0x0000000696400000|  0%| F|  |TAMS 0x0000000696000000, 0x0000000696000000| Untracked 
|  89|0x0000000696400000, 0x0000000696400000, 0x0000000696800000|  0%| F|  |TAMS 0x0000000696400000, 0x0000000696400000| Untracked 
|  90|0x0000000696800000, 0x0000000696800000, 0x0000000696c00000|  0%| F|  |TAMS 0x0000000696800000, 0x0000000696800000| Untracked 
|  91|0x0000000696c00000, 0x0000000696c00000, 0x0000000697000000|  0%| F|  |TAMS 0x0000000696c00000, 0x0000000696c00000| Untracked 
|  92|0x0000000697000000, 0x0000000697000000, 0x0000000697400000|  0%| F|  |TAMS 0x0000000697000000, 0x0000000697000000| Untracked 
|  93|0x0000000697400000, 0x0000000697400000, 0x0000000697800000|  0%| F|  |TAMS 0x0000000697400000, 0x0000000697400000| Untracked 
|  94|0x0000000697800000, 0x0000000697800000, 0x0000000697c00000|  0%| F|  |TAMS 0x0000000697800000, 0x0000000697800000| Untracked 
|  95|0x0000000697c00000, 0x0000000697c00000, 0x0000000698000000|  0%| F|  |TAMS 0x0000000697c00000, 0x0000000697c00000| Untracked 
|  96|0x0000000698000000, 0x0000000698000000, 0x0000000698400000|  0%| F|  |TAMS 0x0000000698000000, 0x0000000698000000| Untracked 
|  97|0x0000000698400000, 0x0000000698400000, 0x0000000698800000|  0%| F|  |TAMS 0x0000000698400000, 0x0000000698400000| Untracked 
|  98|0x0000000698800000, 0x0000000698800000, 0x0000000698c00000|  0%| F|  |TAMS 0x0000000698800000, 0x0000000698800000| Untracked 
|  99|0x0000000698c00000, 0x0000000698c00000, 0x0000000699000000|  0%| F|  |TAMS 0x0000000698c00000, 0x0000000698c00000| Untracked 
| 100|0x0000000699000000, 0x0000000699000000, 0x0000000699400000|  0%| F|  |TAMS 0x0000000699000000, 0x0000000699000000| Untracked 
| 101|0x0000000699400000, 0x0000000699400000, 0x0000000699800000|  0%| F|  |TAMS 0x0000000699400000, 0x0000000699400000| Untracked 
| 102|0x0000000699800000, 0x0000000699800000, 0x0000000699c00000|  0%| F|  |TAMS 0x0000000699800000, 0x0000000699800000| Untracked 
| 103|0x0000000699c00000, 0x0000000699c00000, 0x000000069a000000|  0%| F|  |TAMS 0x0000000699c00000, 0x0000000699c00000| Untracked 
| 104|0x000000069a000000, 0x000000069a000000, 0x000000069a400000|  0%| F|  |TAMS 0x000000069a000000, 0x000000069a000000| Untracked 
| 105|0x000000069a400000, 0x000000069a400000, 0x000000069a800000|  0%| F|  |TAMS 0x000000069a400000, 0x000000069a400000| Untracked 
| 106|0x000000069a800000, 0x000000069a800000, 0x000000069ac00000|  0%| F|  |TAMS 0x000000069a800000, 0x000000069a800000| Untracked 
| 107|0x000000069ac00000, 0x000000069ac00000, 0x000000069b000000|  0%| F|  |TAMS 0x000000069ac00000, 0x000000069ac00000| Untracked 
| 108|0x000000069b000000, 0x000000069b000000, 0x000000069b400000|  0%| F|  |TAMS 0x000000069b000000, 0x000000069b000000| Untracked 
| 109|0x000000069b400000, 0x000000069b400000, 0x000000069b800000|  0%| F|  |TAMS 0x000000069b400000, 0x000000069b400000| Untracked 
| 110|0x000000069b800000, 0x000000069b800000, 0x000000069bc00000|  0%| F|  |TAMS 0x000000069b800000, 0x000000069b800000| Untracked 
| 111|0x000000069bc00000, 0x000000069bc00000, 0x000000069c000000|  0%| F|  |TAMS 0x000000069bc00000, 0x000000069bc00000| Untracked 
| 112|0x000000069c000000, 0x000000069c000000, 0x000000069c400000|  0%| F|  |TAMS 0x000000069c000000, 0x000000069c000000| Untracked 
| 113|0x000000069c400000, 0x000000069c400000, 0x000000069c800000|  0%| F|  |TAMS 0x000000069c400000, 0x000000069c400000| Untracked 
| 114|0x000000069c800000, 0x000000069c800000, 0x000000069cc00000|  0%| F|  |TAMS 0x000000069c800000, 0x000000069c800000| Untracked 
| 115|0x000000069cc00000, 0x000000069cc00000, 0x000000069d000000|  0%| F|  |TAMS 0x000000069cc00000, 0x000000069cc00000| Untracked 
| 116|0x000000069d000000, 0x000000069d000000, 0x000000069d400000|  0%| F|  |TAMS 0x000000069d000000, 0x000000069d000000| Untracked 
| 117|0x000000069d400000, 0x000000069d400000, 0x000000069d800000|  0%| F|  |TAMS 0x000000069d400000, 0x000000069d400000| Untracked 
| 118|0x000000069d800000, 0x000000069d800000, 0x000000069dc00000|  0%| F|  |TAMS 0x000000069d800000, 0x000000069d800000| Untracked 
| 119|0x000000069dc00000, 0x000000069dc00000, 0x000000069e000000|  0%| F|  |TAMS 0x000000069dc00000, 0x000000069dc00000| Untracked 
| 120|0x000000069e000000, 0x000000069e000000, 0x000000069e400000|  0%| F|  |TAMS 0x000000069e000000, 0x000000069e000000| Untracked 
| 121|0x000000069e400000, 0x000000069e400000, 0x000000069e800000|  0%| F|  |TAMS 0x000000069e400000, 0x000000069e400000| Untracked 
| 122|0x000000069e800000, 0x000000069e800000, 0x000000069ec00000|  0%| F|  |TAMS 0x000000069e800000, 0x000000069e800000| Untracked 
| 123|0x000000069ec00000, 0x000000069ec00000, 0x000000069f000000|  0%| F|  |TAMS 0x000000069ec00000, 0x000000069ec00000| Untracked 
| 124|0x000000069f000000, 0x000000069f000000, 0x000000069f400000|  0%| F|  |TAMS 0x000000069f000000, 0x000000069f000000| Untracked 
| 125|0x000000069f400000, 0x000000069f400000, 0x000000069f800000|  0%| F|  |TAMS 0x000000069f400000, 0x000000069f400000| Untracked 
| 126|0x000000069f800000, 0x000000069f800000, 0x000000069fc00000|  0%| F|  |TAMS 0x000000069f800000, 0x000000069f800000| Untracked 
| 127|0x000000069fc00000, 0x000000069fc00000, 0x00000006a0000000|  0%| F|  |TAMS 0x000000069fc00000, 0x000000069fc00000| Untracked 
| 128|0x00000006a0000000, 0x00000006a0000000, 0x00000006a0400000|  0%| F|  |TAMS 0x00000006a0000000, 0x00000006a0000000| Untracked 
| 129|0x00000006a0400000, 0x00000006a0400000, 0x00000006a0800000|  0%| F|  |TAMS 0x00000006a0400000, 0x00000006a0400000| Untracked 
| 130|0x00000006a0800000, 0x00000006a0800000, 0x00000006a0c00000|  0%| F|  |TAMS 0x00000006a0800000, 0x00000006a0800000| Untracked 
| 131|0x00000006a0c00000, 0x00000006a0c00000, 0x00000006a1000000|  0%| F|  |TAMS 0x00000006a0c00000, 0x00000006a0c00000| Untracked 
| 132|0x00000006a1000000, 0x00000006a1000000, 0x00000006a1400000|  0%| F|  |TAMS 0x00000006a1000000, 0x00000006a1000000| Untracked 
| 133|0x00000006a1400000, 0x00000006a1400000, 0x00000006a1800000|  0%| F|  |TAMS 0x00000006a1400000, 0x00000006a1400000| Untracked 
| 134|0x00000006a1800000, 0x00000006a1800000, 0x00000006a1c00000|  0%| F|  |TAMS 0x00000006a1800000, 0x00000006a1800000| Untracked 
| 135|0x00000006a1c00000, 0x00000006a1c00000, 0x00000006a2000000|  0%| F|  |TAMS 0x00000006a1c00000, 0x00000006a1c00000| Untracked 
| 136|0x00000006a2000000, 0x00000006a2000000, 0x00000006a2400000|  0%| F|  |TAMS 0x00000006a2000000, 0x00000006a2000000| Untracked 
| 137|0x00000006a2400000, 0x00000006a2400000, 0x00000006a2800000|  0%| F|  |TAMS 0x00000006a2400000, 0x00000006a2400000| Untracked 
| 138|0x00000006a2800000, 0x00000006a2800000, 0x00000006a2c00000|  0%| F|  |TAMS 0x00000006a2800000, 0x00000006a2800000| Untracked 
| 139|0x00000006a2c00000, 0x00000006a2c00000, 0x00000006a3000000|  0%| F|  |TAMS 0x00000006a2c00000, 0x00000006a2c00000| Untracked 
| 140|0x00000006a3000000, 0x00000006a3000000, 0x00000006a3400000|  0%| F|  |TAMS 0x00000006a3000000, 0x00000006a3000000| Untracked 
| 141|0x00000006a3400000, 0x00000006a3400000, 0x00000006a3800000|  0%| F|  |TAMS 0x00000006a3400000, 0x00000006a3400000| Untracked 
| 142|0x00000006a3800000, 0x00000006a3800000, 0x00000006a3c00000|  0%| F|  |TAMS 0x00000006a3800000, 0x00000006a3800000| Untracked 
| 143|0x00000006a3c00000, 0x00000006a3c00000, 0x00000006a4000000|  0%| F|  |TAMS 0x00000006a3c00000, 0x00000006a3c00000| Untracked 
| 144|0x00000006a4000000, 0x00000006a4000000, 0x00000006a4400000|  0%| F|  |TAMS 0x00000006a4000000, 0x00000006a4000000| Untracked 
| 145|0x00000006a4400000, 0x00000006a4400000, 0x00000006a4800000|  0%| F|  |TAMS 0x00000006a4400000, 0x00000006a4400000| Untracked 
| 146|0x00000006a4800000, 0x00000006a4800000, 0x00000006a4c00000|  0%| F|  |TAMS 0x00000006a4800000, 0x00000006a4800000| Untracked 
| 147|0x00000006a4c00000, 0x00000006a4c00000, 0x00000006a5000000|  0%| F|  |TAMS 0x00000006a4c00000, 0x00000006a4c00000| Untracked 
| 148|0x00000006a5000000, 0x00000006a5000000, 0x00000006a5400000|  0%| F|  |TAMS 0x00000006a5000000, 0x00000006a5000000| Untracked 
| 149|0x00000006a5400000, 0x00000006a5400000, 0x00000006a5800000|  0%| F|  |TAMS 0x00000006a5400000, 0x00000006a5400000| Untracked 
| 150|0x00000006a5800000, 0x00000006a5800000, 0x00000006a5c00000|  0%| F|  |TAMS 0x00000006a5800000, 0x00000006a5800000| Untracked 
| 151|0x00000006a5c00000, 0x00000006a5c00000, 0x00000006a6000000|  0%| F|  |TAMS 0x00000006a5c00000, 0x00000006a5c00000| Untracked 
| 152|0x00000006a6000000, 0x00000006a6000000, 0x00000006a6400000|  0%| F|  |TAMS 0x00000006a6000000, 0x00000006a6000000| Untracked 
| 153|0x00000006a6400000, 0x00000006a6800000, 0x00000006a6800000|100%| S|CS|TAMS 0x00000006a6400000, 0x00000006a6400000| Complete 
| 154|0x00000006a6800000, 0x00000006a6800000, 0x00000006a6c00000|  0%| F|  |TAMS 0x00000006a6800000, 0x00000006a6800000| Untracked 
| 155|0x00000006a6c00000, 0x00000006a6c00000, 0x00000006a7000000|  0%| F|  |TAMS 0x00000006a6c00000, 0x00000006a6c00000| Untracked 
| 156|0x00000006a7000000, 0x00000006a7000000, 0x00000006a7400000|  0%| F|  |TAMS 0x00000006a7000000, 0x00000006a7000000| Untracked 
| 157|0x00000006a7400000, 0x00000006a7400000, 0x00000006a7800000|  0%| F|  |TAMS 0x00000006a7400000, 0x00000006a7400000| Untracked 
| 158|0x00000006a7800000, 0x00000006a7800000, 0x00000006a7c00000|  0%| F|  |TAMS 0x00000006a7800000, 0x00000006a7800000| Untracked 
| 159|0x00000006a7c00000, 0x00000006a7c00000, 0x00000006a8000000|  0%| F|  |TAMS 0x00000006a7c00000, 0x00000006a7c00000| Untracked 
| 160|0x00000006a8000000, 0x00000006a8000000, 0x00000006a8400000|  0%| F|  |TAMS 0x00000006a8000000, 0x00000006a8000000| Untracked 
| 161|0x00000006a8400000, 0x00000006a8400000, 0x00000006a8800000|  0%| F|  |TAMS 0x00000006a8400000, 0x00000006a8400000| Untracked 

Card table byte_map: [0x0000027d9a000000,0x0000027d9ac00000] _byte_map_base: 0x0000027d96c00000

Marking Bits (Prev, Next): (CMBitMap*) 0x0000027d829670b0, (CMBitMap*) 0x0000027d82967070
 Prev Bits: [0x0000027da1800000, 0x0000027da7800000)
 Next Bits: [0x0000027d9b800000, 0x0000027da1800000)

Polling page: 0x0000027d807a0000

Metaspace:

Usage:
  Non-class:     97.94 MB used.
      Class:     15.78 MB used.
       Both:    113.72 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      98.94 MB ( 77%) committed,  2 nodes.
      Class space:      832.00 MB reserved,      16.62 MB (  2%) committed,  1 nodes.
             Both:      960.00 MB reserved,     115.56 MB ( 12%) committed. 

Chunk freelists:
   Non-Class:  13.06 MB
       Class:  15.41 MB
        Both:  28.47 MB

MaxMetaspaceSize: 1.00 GB
CompressedClassSpaceSize: 832.00 MB
Initial GC threshold: 21.00 MB
Current GC threshold: 191.19 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 2134.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1849.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 7839.
num_chunk_merges: 9.
num_chunk_splits: 4896.
num_chunks_enlarged: 2862.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=14710Kb max_used=14710Kb free=105290Kb
 bounds [0x0000027d91ed0000, 0x0000027d92d30000, 0x0000027d99400000]
CodeHeap 'profiled nmethods': size=120000Kb used=30658Kb max_used=31078Kb free=89341Kb
 bounds [0x0000027d8a400000, 0x0000027d8c260000, 0x0000027d91930000]
CodeHeap 'non-nmethods': size=5760Kb used=2455Kb max_used=2545Kb free=3304Kb
 bounds [0x0000027d91930000, 0x0000027d91bc0000, 0x0000027d91ed0000]
 total_blobs=16975 nmethods=15975 adapters=911
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 632.843 Thread 0x0000027da9cd1d00 19810       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl$$Lambda$142/0x0000027dab1c43f8::emit (9 bytes)
Event: 632.844 Thread 0x0000027da9cd1d00 nmethod 19810 0x0000027d8c0bf990 code [0x0000027d8c0bfb40, 0x0000027d8c0bfd48]
Event: 632.844 Thread 0x0000027da9cd1d00 19811       3       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::lambda$generateModelObjectMethods$28 (11 bytes)
Event: 632.844 Thread 0x0000027da9cd1d00 nmethod 19811 0x0000027d8ba78310 code [0x0000027d8ba784c0, 0x0000027d8ba78668]
Event: 632.850 Thread 0x0000027da9cd1d00 19812   !   3       jdk.proxy1.$Proxy98::annotationType (29 bytes)
Event: 632.851 Thread 0x0000027da9cd1d00 nmethod 19812 0x0000027d8c0bef90 code [0x0000027d8c0bf160, 0x0000027d8c0bf558]
Event: 632.877 Thread 0x0000027da9ccf630 nmethod 19795 0x0000027d92d28490 code [0x0000027d92d287e0, 0x0000027d92d2b188]
Event: 632.878 Thread 0x0000027da9ccf630 19796       4       org.gradle.internal.resolve.resolver.DefaultVariantArtifactResolver::resolveVariant (19 bytes)
Event: 632.879 Thread 0x0000027da9ccf630 nmethod 19796 0x0000027d92d2cb90 code [0x0000027d92d2cd20, 0x0000027d92d2ce08]
Event: 632.918 Thread 0x0000027da9cd1d00 19813       3       org.gradle.internal.properties.annotations.DefaultTypeMetadataStore$DefaultFunctionMetadata::getMethod (10 bytes)
Event: 632.918 Thread 0x0000027da9cd1d00 nmethod 19813 0x0000027d8a676890 code [0x0000027d8a676a20, 0x0000027d8a676c18]
Event: 632.926 Thread 0x0000027da9ccf630 19814       4       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator$ClassBuilderImpl::access$4300 (4 bytes)
Event: 632.927 Thread 0x0000027da9ccf630 nmethod 19814 0x0000027d92d2cf10 code [0x0000027d92d2d080, 0x0000027d92d2d0d8]
Event: 632.976 Thread 0x0000027da9cd1d00 19815       3       org.gradle.internal.reflect.annotations.impl.DefaultTypeAnnotationMetadataStore::lambda$mergePropertiesAndFieldMetadata$9 (50 bytes)
Event: 632.977 Thread 0x0000027da9cd1d00 nmethod 19815 0x0000027d8a675b90 code [0x0000027d8a675da0, 0x0000027d8a676688]
Event: 634.298 Thread 0x0000027da9cd1d00 19816       3       java.lang.ThreadLocal::remove (18 bytes)
Event: 634.299 Thread 0x0000027da9cd1d00 nmethod 19816 0x0000027d8a675610 code [0x0000027d8a6757c0, 0x0000027d8a675a48]
Event: 634.475 Thread 0x0000027da9ccf630 19817       4       java.util.HashMap$EntrySet::iterator (12 bytes)
Event: 634.480 Thread 0x0000027da9ccf630 nmethod 19817 0x0000027d92d2d210 code [0x0000027d92d2d3a0, 0x0000027d92d2d658]
Event: 634.493 Thread 0x0000027da9cd1d00 19818   !   3       jdk.proxy6.$Proxy132::annotationType (29 bytes)

GC Heap History (20 events):
Event: 473.357 GC heap after
{Heap after GC invocations=82 (full 0):
 garbage-first heap   total 663552K, used 259821K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 108011K, committed 109696K, reserved 983040K
  class space    used 15022K, committed 15744K, reserved 851968K
}
Event: 521.230 GC heap before
{Heap before GC invocations=82 (full 0):
 garbage-first heap   total 663552K, used 493293K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 62 young (253952K), 4 survivors (16384K)
 Metaspace       used 108523K, committed 110336K, reserved 983040K
  class space    used 15094K, committed 15872K, reserved 851968K
}
Event: 522.685 GC heap after
{Heap after GC invocations=83 (full 0):
 garbage-first heap   total 663552K, used 277048K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 8 survivors (32768K)
 Metaspace       used 108523K, committed 110336K, reserved 983040K
  class space    used 15094K, committed 15872K, reserved 851968K
}
Event: 522.768 GC heap before
{Heap before GC invocations=83 (full 0):
 garbage-first heap   total 663552K, used 281144K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 9 young (36864K), 8 survivors (32768K)
 Metaspace       used 108523K, committed 110336K, reserved 983040K
  class space    used 15094K, committed 15872K, reserved 851968K
}
Event: 522.889 GC heap after
{Heap after GC invocations=84 (full 0):
 garbage-first heap   total 663552K, used 280237K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 108523K, committed 110336K, reserved 983040K
  class space    used 15094K, committed 15872K, reserved 851968K
}
Event: 523.745 GC heap before
{Heap before GC invocations=84 (full 0):
 garbage-first heap   total 663552K, used 321197K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 108523K, committed 110336K, reserved 983040K
  class space    used 15094K, committed 15872K, reserved 851968K
}
Event: 523.761 GC heap after
{Heap after GC invocations=85 (full 0):
 garbage-first heap   total 663552K, used 279593K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 108523K, committed 110336K, reserved 983040K
  class space    used 15094K, committed 15872K, reserved 851968K
}
Event: 554.261 GC heap before
{Heap before GC invocations=85 (full 0):
 garbage-first heap   total 663552K, used 349225K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 19 young (77824K), 1 survivors (4096K)
 Metaspace       used 109053K, committed 110912K, reserved 983040K
  class space    used 15178K, committed 16000K, reserved 851968K
}
Event: 554.300 GC heap after
{Heap after GC invocations=86 (full 0):
 garbage-first heap   total 663552K, used 288429K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 3 young (12288K), 3 survivors (12288K)
 Metaspace       used 109053K, committed 110912K, reserved 983040K
  class space    used 15178K, committed 16000K, reserved 851968K
}
Event: 557.230 GC heap before
{Heap before GC invocations=86 (full 0):
 garbage-first heap   total 663552K, used 313005K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 10 young (40960K), 3 survivors (12288K)
 Metaspace       used 109104K, committed 111040K, reserved 983040K
  class space    used 15184K, committed 16000K, reserved 851968K
}
Event: 557.322 GC heap after
{Heap after GC invocations=87 (full 0):
 garbage-first heap   total 663552K, used 292293K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 109104K, committed 111040K, reserved 983040K
  class space    used 15184K, committed 16000K, reserved 851968K
}
Event: 562.213 GC heap before
{Heap before GC invocations=88 (full 0):
 garbage-first heap   total 663552K, used 357829K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 19 young (77824K), 2 survivors (8192K)
 Metaspace       used 109110K, committed 111040K, reserved 983040K
  class space    used 15184K, committed 16000K, reserved 851968K
}
Event: 562.228 GC heap after
{Heap after GC invocations=89 (full 0):
 garbage-first heap   total 663552K, used 292507K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 109110K, committed 111040K, reserved 983040K
  class space    used 15184K, committed 16000K, reserved 851968K
}
Event: 564.867 GC heap before
{Heap before GC invocations=89 (full 0):
 garbage-first heap   total 663552K, used 312987K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 2 survivors (8192K)
 Metaspace       used 109335K, committed 111232K, reserved 983040K
  class space    used 15213K, committed 16000K, reserved 851968K
}
Event: 564.899 GC heap after
{Heap after GC invocations=90 (full 0):
 garbage-first heap   total 663552K, used 287789K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 109335K, committed 111232K, reserved 983040K
  class space    used 15213K, committed 16000K, reserved 851968K
}
Event: 622.718 GC heap before
{Heap before GC invocations=90 (full 0):
 garbage-first heap   total 663552K, used 472109K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 46 young (188416K), 1 survivors (4096K)
 Metaspace       used 115462K, committed 117440K, reserved 983040K
  class space    used 16028K, committed 16896K, reserved 851968K
}
Event: 622.881 GC heap after
{Heap after GC invocations=91 (full 0):
 garbage-first heap   total 663552K, used 314511K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 6 young (24576K), 6 survivors (24576K)
 Metaspace       used 115462K, committed 117440K, reserved 983040K
  class space    used 16028K, committed 16896K, reserved 851968K
}
Event: 625.458 GC heap before
{Heap before GC invocations=92 (full 0):
 garbage-first heap   total 663552K, used 322703K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 6 survivors (24576K)
 Metaspace       used 115499K, committed 117440K, reserved 983040K
  class space    used 16036K, committed 16896K, reserved 851968K
}
Event: 625.559 GC heap after
{Heap after GC invocations=93 (full 0):
 garbage-first heap   total 663552K, used 318700K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 115499K, committed 117440K, reserved 983040K
  class space    used 16036K, committed 16896K, reserved 851968K
}
Event: 634.520 GC heap before
{Heap before GC invocations=93 (full 0):
 garbage-first heap   total 663552K, used 347372K [0x0000000680000000, 0x0000000800000000)
  region size 4096K, 8 young (32768K), 1 survivors (4096K)
 Metaspace       used 116450K, committed 118336K, reserved 983040K
  class space    used 16163K, committed 17024K, reserved 851968K
}

Dll operation events (16 events):
Event: 0.182 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
Event: 0.226 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
Event: 0.551 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 0.630 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
Event: 0.642 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
Event: 0.649 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
Event: 0.658 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
Event: 5.740 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
Event: 11.953 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
Event: 12.316 Loaded shared library C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
Event: 12.406 Loaded shared library C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
Event: 29.997 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
Event: 30.002 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
Event: 33.035 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
Event: 38.920 Loaded shared library C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
Event: 47.018 Loaded shared library C:\Users\<USER>\AppData\Local\Temp\native-platform9563108177092876416dir\gradle-fileevents.dll

Deoptimization events (20 events):
Event: 631.132 Thread 0x0000027de447a3d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000027d927024e4 relative=0x0000000000000c24
Event: 631.132 Thread 0x0000027de447a3d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000027d927024e4 method=org.gradle.api.internal.artifacts.configurations.DefaultConfiguration.maybePreventMutation(Lorg/gradle/api/internal/artifacts/configurations/MutationValidator$MutationType;L
Event: 631.132 Thread 0x0000027de447a3d0 DEOPT PACKING pc=0x0000027d927024e4 sp=0x000000ca3a3f96d0
Event: 631.132 Thread 0x0000027de447a3d0 DEOPT UNPACKING pc=0x0000027d919869a3 sp=0x000000ca3a3f94f8 mode 2
Event: 631.132 Thread 0x0000027de447a3d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000027d925c95a4 relative=0x0000000000000084
Event: 631.132 Thread 0x0000027de447a3d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000027d925c95a4 method=org.gradle.api.internal.artifacts.configurations.DefaultConfiguration.maybePreventMutation(Lorg/gradle/api/internal/artifacts/configurations/MutationValidator$MutationType;L
Event: 631.132 Thread 0x0000027de447a3d0 DEOPT PACKING pc=0x0000027d925c95a4 sp=0x000000ca3a3f9610
Event: 631.132 Thread 0x0000027de447a3d0 DEOPT UNPACKING pc=0x0000027d919869a3 sp=0x000000ca3a3f9548 mode 2
Event: 631.132 Thread 0x0000027de447a3d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000027d926ecfe8 relative=0x0000000000000068
Event: 631.132 Thread 0x0000027de447a3d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000027d926ecfe8 method=org.gradle.api.internal.artifacts.configurations.DefaultConfiguration.maybePreventMutation(Lorg/gradle/api/internal/artifacts/configurations/MutationValidator$MutationType;L
Event: 631.132 Thread 0x0000027de447a3d0 DEOPT PACKING pc=0x0000027d926ecfe8 sp=0x000000ca3a3f9580
Event: 631.132 Thread 0x0000027de447a3d0 DEOPT UNPACKING pc=0x0000027d919869a3 sp=0x000000ca3a3f9530 mode 2
Event: 631.182 Thread 0x0000027de447a3d0 Uncommon trap: trap_request=0xffffffc6 fr.pc=0x0000027d92c49778 relative=0x0000000000000b98
Event: 631.182 Thread 0x0000027de447a3d0 Uncommon trap: reason=bimorphic_or_optimized_type_check action=maybe_recompile pc=0x0000027d92c49778 method=java.util.HashMap.removeNode(ILjava/lang/Object;Ljava/lang/Object;ZZ)Ljava/util/HashMap$Node; @ 143 c2
Event: 631.182 Thread 0x0000027de447a3d0 DEOPT PACKING pc=0x0000027d92c49778 sp=0x000000ca3a3fa6b0
Event: 631.182 Thread 0x0000027de447a3d0 DEOPT UNPACKING pc=0x0000027d919869a3 sp=0x000000ca3a3fa648 mode 2
Event: 632.748 Thread 0x0000027de447a3d0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x0000027d92cf1ebc relative=0x00000000000074dc
Event: 632.750 Thread 0x0000027de447a3d0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x0000027d92cf1ebc method=org.gradle.api.internal.artifacts.transform.TransformationChainSelector.selectTransformationChain(Lorg/gradle/api/internal/artifacts/ivyservice/resolveengine/artifact/Resolv
Event: 632.750 Thread 0x0000027de447a3d0 DEOPT PACKING pc=0x0000027d92cf1ebc sp=0x000000ca3a3fa170
Event: 632.750 Thread 0x0000027de447a3d0 DEOPT UNPACKING pc=0x0000027d919869a3 sp=0x000000ca3a3fa010 mode 2

Classes loaded (20 events):
Event: 581.754 Loading class org/xml/sax/ext/Locator2 done
Event: 581.754 Loading class com/sun/org/apache/xerces/internal/parsers/AbstractSAXParser$LocatorProxy done
Event: 581.780 Loading class org/xml/sax/helpers/AttributesImpl
Event: 581.781 Loading class org/xml/sax/helpers/AttributesImpl done
Event: 581.792 Loading class java/util/regex/Pattern$Behind
Event: 581.792 Loading class java/util/regex/Pattern$Behind done
Event: 581.793 Loading class java/util/regex/Pattern$Neg
Event: 581.793 Loading class java/util/regex/Pattern$Neg done
Event: 582.033 Loading class javax/xml/datatype/DatatypeConfigurationException
Event: 582.033 Loading class javax/xml/datatype/DatatypeConfigurationException done
Event: 582.035 Loading class jdk/internal/reflect/UnsafeBooleanFieldAccessorImpl
Event: 582.037 Loading class jdk/internal/reflect/UnsafeBooleanFieldAccessorImpl done
Event: 582.038 Loading class jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl
Event: 582.038 Loading class jdk/internal/reflect/UnsafeIntegerFieldAccessorImpl done
Event: 594.380 Loading class java/io/CharArrayWriter
Event: 594.381 Loading class java/io/CharArrayWriter done
Event: 594.408 Loading class java/io/SequenceInputStream
Event: 594.408 Loading class java/io/SequenceInputStream done
Event: 594.689 Loading class java/util/function/IntUnaryOperator
Event: 594.689 Loading class java/util/function/IntUnaryOperator done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 625.237 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a8148d28}: com/android/build/gradle/tasks/PrefabPackageConfigurationTaskCustomizer> (0x00000006a8148d28) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.239 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a816a7e0}: com/android/build/gradle/tasks/PrefabPackageConfigurationTask_DecoratedCustomizer> (0x00000006a816a7e0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.249 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a825a8d0}: com/android/build/gradle/internal/tasks/PackageRenderscriptTask_DecoratedBeanInfo> (0x00000006a825a8d0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.250 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a826afb0}: com/android/build/gradle/internal/tasks/PackageRenderscriptTaskBeanInfo> (0x00000006a826afb0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.251 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a8278428}: org/gradle/api/tasks/SyncBeanInfo> (0x00000006a8278428) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.251 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a8285630}: org/gradle/api/tasks/SyncCustomizer> (0x00000006a8285630) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.253 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a82af280}: com/android/build/gradle/internal/tasks/PackageRenderscriptTaskCustomizer> (0x00000006a82af280) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.255 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a82d89d0}: com/android/build/gradle/internal/tasks/PackageRenderscriptTask_DecoratedCustomizer> (0x00000006a82d89d0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.452 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a83e91d8}: com/android/build/gradle/internal/res/GenerateLibraryRFileTask_DecoratedBeanInfo> (0x00000006a83e91d8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.457 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a83f9a58}: com/android/build/gradle/internal/res/GenerateLibraryRFileTaskBeanInfo> (0x00000006a83f9a58) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.567 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a840ada0}: com/android/build/gradle/tasks/ProcessAndroidResourcesBeanInfo> (0x00000006a840ada0) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.568 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a841b358}: com/android/build/gradle/internal/tasks/NewIncrementalTaskBeanInfo> (0x00000006a841b358) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.569 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a842b910}: com/android/build/gradle/internal/tasks/NewIncrementalTaskCustomizer> (0x00000006a842b910) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.577 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a844ab78}: com/android/build/gradle/tasks/ProcessAndroidResourcesCustomizer> (0x00000006a844ab78) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.603 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a846c1b8}: com/android/build/gradle/internal/res/GenerateLibraryRFileTaskCustomizer> (0x00000006a846c1b8) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.610 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a84a2b98}: com/android/build/gradle/internal/res/GenerateLibraryRFileTask_DecoratedCustomizer> (0x00000006a84a2b98) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.717 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a858fd30}: com/android/build/gradle/tasks/ZipMergingTask_DecoratedBeanInfo> (0x00000006a858fd30) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.740 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a859ff08}: com/android/build/gradle/tasks/ZipMergingTaskBeanInfo> (0x00000006a859ff08) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.744 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a85b0198}: com/android/build/gradle/tasks/ZipMergingTaskCustomizer> (0x00000006a85b0198) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 625.801 Thread 0x0000027de447a3d0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006a85d3618}: com/android/build/gradle/tasks/ZipMergingTask_DecoratedCustomizer> (0x00000006a85d3618) 
thrown [s\src\hotspot\share\classfile\systemDictionary.cpp, line 256]

VM Operations (20 events):
Event: 624.761 Executing VM operation: G1PauseRemark done
Event: 625.205 Executing VM operation: G1PauseCleanup
Event: 625.224 Executing VM operation: G1PauseCleanup done
Event: 625.458 Executing VM operation: G1CollectForAllocation
Event: 625.559 Executing VM operation: G1CollectForAllocation done
Event: 626.570 Executing VM operation: Cleanup
Event: 626.659 Executing VM operation: Cleanup done
Event: 627.661 Executing VM operation: Cleanup
Event: 627.661 Executing VM operation: Cleanup done
Event: 628.669 Executing VM operation: Cleanup
Event: 628.719 Executing VM operation: Cleanup done
Event: 629.735 Executing VM operation: Cleanup
Event: 629.877 Executing VM operation: Cleanup done
Event: 630.885 Executing VM operation: Cleanup
Event: 630.885 Executing VM operation: Cleanup done
Event: 631.898 Executing VM operation: Cleanup
Event: 632.056 Executing VM operation: Cleanup done
Event: 633.069 Executing VM operation: Cleanup
Event: 633.085 Executing VM operation: Cleanup done
Event: 634.493 Executing VM operation: G1CollectForAllocation

Events (20 events):
Event: 613.018 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8bdcbb10
Event: 613.018 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8be56c10
Event: 613.018 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8be75710
Event: 613.018 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8be8e810
Event: 613.019 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8bfd9e10
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b2610
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b4110
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b5090
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b6510
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b7590
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b8010
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0b8a10
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0bef90
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0c0690
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c0c0b90
Event: 613.020 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c138690
Event: 613.021 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c194110
Event: 613.021 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c194d90
Event: 613.021 Thread 0x0000027de3010060 flushing nmethod 0x0000027d8c1f3110
Event: 632.801 Thread 0x0000027de4b26d20 Thread exited: 0x0000027de4b26d20


Dynamic libraries:
0x00007ff603630000 - 0x00007ff60363e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.exe
0x00007ffacb2f0000 - 0x00007ffacb507000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffac9a70000 - 0x00007ffac9b34000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffac8be0000 - 0x00007ffac8fb1000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffac84d0000 - 0x00007ffac85e1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffaaeea0000 - 0x00007ffaaeeb7000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jli.dll
0x00007ffac9b60000 - 0x00007ffac9d11000 	C:\Windows\System32\USER32.dll
0x00007ffac8ac0000 - 0x00007ffac8ae6000 	C:\Windows\System32\win32u.dll
0x00007ffacb280000 - 0x00007ffacb2a9000 	C:\Windows\System32\GDI32.dll
0x00007ffac85f0000 - 0x00007ffac870b000 	C:\Windows\System32\gdi32full.dll
0x00007ffac8430000 - 0x00007ffac84ca000 	C:\Windows\System32\msvcp_win.dll
0x00007ffaaed30000 - 0x00007ffaaed4d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\VCRUNTIME140.dll
0x00007ffab0c00000 - 0x00007ffab0e92000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffacb170000 - 0x00007ffacb217000 	C:\Windows\System32\msvcrt.dll
0x00007ffacb220000 - 0x00007ffacb251000 	C:\Windows\System32\IMM32.DLL
0x00007ffabf040000 - 0x00007ffabf04c000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\vcruntime140_1.dll
0x00007ffabdda0000 - 0x00007ffabde2d000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\msvcp140.dll
0x00007ffa17d70000 - 0x00007ffa189e0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server\jvm.dll
0x00007ffac96e0000 - 0x00007ffac9791000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffac9d20000 - 0x00007ffac9dc7000 	C:\Windows\System32\sechost.dll
0x00007ffac8af0000 - 0x00007ffac8b18000 	C:\Windows\System32\bcrypt.dll
0x00007ffacb050000 - 0x00007ffacb164000 	C:\Windows\System32\RPCRT4.dll
0x00007ffac9130000 - 0x00007ffac91a1000 	C:\Windows\System32\WS2_32.dll
0x00007ffac8300000 - 0x00007ffac834d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffabdc00000 - 0x00007ffabdc34000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffabc100000 - 0x00007ffabc10a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffac82e0000 - 0x00007ffac82f3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffac7440000 - 0x00007ffac7458000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffabeaa0000 - 0x00007ffabeaaa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jimage.dll
0x00007ffac2e00000 - 0x00007ffac3032000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffac92c0000 - 0x00007ffac9650000 	C:\Windows\System32\combase.dll
0x00007ffac9990000 - 0x00007ffac9a67000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffaa95d0000 - 0x00007ffaa9602000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffac8a40000 - 0x00007ffac8abb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffac1190000 - 0x00007ffac119e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\instrument.dll
0x00007ffaaded0000 - 0x00007ffaadef5000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\java.dll
0x00007ffaad6b0000 - 0x00007ffaad787000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\jsvml.dll
0x00007ffaca760000 - 0x00007ffacafe8000 	C:\Windows\System32\SHELL32.dll
0x00007ffac8880000 - 0x00007ffac89bf000 	C:\Windows\System32\wintypes.dll
0x00007ffac6340000 - 0x00007ffac6c4d000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffac9020000 - 0x00007ffac912a000 	C:\Windows\System32\SHCORE.dll
0x00007ffacaff0000 - 0x00007ffacb04e000 	C:\Windows\System32\shlwapi.dll
0x00007ffac8360000 - 0x00007ffac838b000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffaad690000 - 0x00007ffaad6a8000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\zip.dll
0x00007ffaaeb80000 - 0x00007ffaaeb9a000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\net.dll
0x00007ffabe3f0000 - 0x00007ffabe51c000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffac78a0000 - 0x00007ffac790a000 	C:\Windows\system32\mswsock.dll
0x00007ffaae4d0000 - 0x00007ffaae4e6000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\nio.dll
0x00007ffabdd90000 - 0x00007ffabdda0000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\verify.dll
0x00007ffaad1e0000 - 0x00007ffaad207000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000005ace0000 - 0x000000005ad53000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffab9cf0000 - 0x00007ffab9cfa000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management.dll
0x00007ffab0ed0000 - 0x00007ffab0edb000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\management_ext.dll
0x00007ffac97a0000 - 0x00007ffac97a8000 	C:\Windows\System32\PSAPI.DLL
0x00007ffac7bf0000 - 0x00007ffac7c0b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffac73a0000 - 0x00007ffac73d7000 	C:\Windows\system32\rsaenh.dll
0x00007ffac7940000 - 0x00007ffac7968000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffac7bd0000 - 0x00007ffac7bdc000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffac6eb0000 - 0x00007ffac6edd000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffac9dd0000 - 0x00007ffac9dd9000 	C:\Windows\System32\NSI.dll
0x00007ffabe730000 - 0x00007ffabe749000 	C:\Windows\SYSTEM32\dhcpcsvc6.DLL
0x00007ffabe010000 - 0x00007ffabe02f000 	C:\Windows\SYSTEM32\dhcpcsvc.DLL
0x00007ffac6f20000 - 0x00007ffac7022000 	C:\Windows\SYSTEM32\DNSAPI.dll
0x00007ffac11a0000 - 0x00007ffac11a9000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\extnet.dll
0x00007ffac1180000 - 0x00007ffac118e000 	C:\Program Files\Microsoft\jdk-*********-hotspot\bin\sunmscapi.dll
0x00007ffac8710000 - 0x00007ffac8876000 	C:\Windows\System32\CRYPT32.dll
0x00007ffac7d00000 - 0x00007ffac7d2d000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffac7cc0000 - 0x00007ffac7cf7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffabf6d0000 - 0x00007ffabf6d8000 	C:\Windows\system32\wshunix.dll
0x000000005a8e0000 - 0x000000005a953000 	C:\Users\<USER>\AppData\Local\Temp\native-platform9563108177092876416dir\gradle-fileevents.dll
0x00007ffaaa3d0000 - 0x00007ffaaa3e7000 	C:\Windows\system32\napinsp.dll
0x00007ffaaa000000 - 0x00007ffaaa01b000 	C:\Windows\system32\pnrpnsp.dll
0x00007ffaa7ef0000 - 0x00007ffaa7f01000 	C:\Windows\System32\winrnr.dll
0x00007ffabd040000 - 0x00007ffabd055000 	C:\Windows\system32\wshbth.dll
0x00007ffaa1bc0000 - 0x00007ffaa1be7000 	C:\Windows\system32\nlansp_c.dll
0x00007ffab2af0000 - 0x00007ffab2afa000 	C:\Windows\System32\rasadhlp.dll
0x00007ffaba010000 - 0x00007ffaba093000 	C:\Windows\System32\fwpuclnt.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Program Files\Microsoft\jdk-*********-hotspot\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform9563108177092876416dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Xmx6g -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 3                                         {product} {ergonomic}
   size_t CompressedClassSpaceSize                 = 872415232                                 {product} {ergonomic}
     uint ConcGCThreads                            = 1                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 4                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 134217728                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 6442450944                                {product} {command line}
   size_t MaxMetaspaceSize                         = 1073741824                                {product} {command line}
   size_t MaxNewSize                               = 3862953984                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5832780                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122912730                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122912730                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 6442450944                             {manageable} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=C:\Program Files\Microsoft\jdk-*********-hotspot
CLASSPATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\android\\gradle\wrapper\gradle-wrapper.jar
PATH=C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\RideFuze_application\node_modules\.bin;C:\Users\<USER>\OneDrive\Desktop\node_modules\.bin;C:\Users\<USER>\OneDrive\node_modules\.bin;C:\Users\<USER>\node_modules\.bin;C:\Users\<USER>\.bin;C:\node_modules\.bin;C:\Users\<USER>\AppData\Roaming\npm\node_modules\npm\node_modules\@npmcli\run-script\lib\node-gyp-bin;C:\Program Files\Microsoft\jdk-*********-hotspot\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;;C:\ProgramData\chocolatey\bin;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\msys64\ucrt64\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=henry
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 9, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp



Periodic native trim disabled


---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 0 days 0:34 hours
Hyper-V role detected

CPU: total 4 (initial active 4) (2 cores per cpu, 2 threads per core) family 6 model 142 stepping 9 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, fma, vzeroupper, clflush, clflushopt, hv
Processor Information for the first 4 processors :
  Max Mhz: 2712, Current Mhz: 2511, Mhz Limit: 2495

Memory: 4k page, system-wide physical 8067M (364M free)
TotalPageFile size 26992M (AvailPageFile size 467M)
current process WorkingSet (physical memory assigned to process): 513M, peak: 647M
current process commit charge ("private bytes"): 1056M, peak: 2144M

vm_info: OpenJDK 64-Bit Server VM (17.0.12+7-LTS) for windows-amd64 JRE (17.0.12+7-LTS), built on Jul 16 2024 18:11:44 by "MicrosoftCorporation" with unknown MS VC++:1939

END.
