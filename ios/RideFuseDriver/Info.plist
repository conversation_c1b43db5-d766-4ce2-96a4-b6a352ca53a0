<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>RideFuze Driver</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.798004684989-v108vcaiiqam0j9sk1afkq8cjn0snu5g</string>
			</array>
		</dict>
		<dict/>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>This app requires access to your media library to play and manage music files.</string>
	<key>NSCameraUsageDescription</key>
	<string>We need access to your camera to take photos.</string>
	<key>NSContactsUsageDescription</key>
	<string>This app requires access to your contacts to enhance user experience by connecting with friends.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>We need your location to provide accurate services.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>We need your location to provide accurate services.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>Location Access</key>
		<string>We use your location for better user experience</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need your location to provide accurate services.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app requires access to your microphone for voice input and audio recording.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>We need access to save photos to your library.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to upload images.</string>
	<key>UIAppFonts</key>
	<array>
		<string>MaterialCommunityIcons.ttf</string>
		<string>AntDesign.ttf</string>
		<string>Entypo.ttf</string>
		<string>Evillcons.ttf</string>
		<string>Feather.ttf</string>
		<string>FontAwesome5_Brands.ttf</string>
		<string>FontAwesome5_Regular.ttf</string>
		<string>FontAwesome5_Solid.ttf</string>
		<string>FontAwesome6_Brands.ttf</string>
		<string>FontAwesome6_Regular.ttf</string>
		<string>FontAwesome6_Solid.ttf</string>
		<string>Foundation.ttf</string>
		<string>Ionicons.ttf</string>
		<string>MaterialIcons.ttf</string>
		<string>Octicons.ttf</string>
		<string>SimpleLineIcons.ttf</string>
		<string>Fontisto.ttf</string>
		<string>Zocial.ttf</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>arm64</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
</dict>
</plist>
