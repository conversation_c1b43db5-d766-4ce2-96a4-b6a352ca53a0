-------------------------------------
Translated Report (Full Report Below)
-------------------------------------

Incident Identifier: 5A42C38D-3794-4AA1-ABB4-00C33B3C8EBA
CrashReporter Key:   CE864D80-1530-CD12-8781-F1F1ED7AAD6C
Hardware Model:      MacBookPro16,2
Process:             RideFuseDriver [14145]
Path:                /Users/<USER>/Library/Developer/CoreSimulator/Devices/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E/data/Containers/Bundle/Application/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1/RideFuseDriver.app/RideFuseDriver
Identifier:          com.ridefuzeDriver
Version:             1.0 (1)
Code Type:           X86-64 (Native)
Role:                Foreground
Parent Process:      launchd_sim [939]
Coalition:           com.apple.CoreSimulator.SimDevice.4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E [1179]
Responsible Process: SimulatorTrampoline [931]

Date/Time:           2025-07-04 12:22:45.7629 +0100
Launch Time:         2025-07-04 12:22:43.4360 +0100
OS Version:          macOS 15.5 (24F74)
Release Type:        User
Report Version:      104

Exception Type:  EXC_BAD_ACCESS (SIGSEGV)
Exception Subtype: UNKNOWN_0xD at 0x0000000000000000
Exception Codes: 0x000000000000000d, 0x0000000000000000
VM Region Info: 0 is not in any region.  Bytes before following region: 4520706048
      REGION TYPE                    START - END         [ VSIZE] PRT/MAX SHRMOD  REGION DETAIL
      UNUSED SPACE AT START
--->  
      __TEXT                      10d748000-10d74b000    [   12K] r-x/r-x SM=COW  /Users/<USER>/Library/Developer/CoreSimulator/Devices/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E/data/Containers/Bundle/Application/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1/RideFuseDriver.app/RideFuseDriver
Termination Reason: SIGNAL 11 Segmentation fault: 11
Terminating Process: exc handler [14145]

Triggered by Thread:  0

Thread 0 Crashed::  Dispatch queue: com.apple.main-thread
0   libunwind.dylib               	    0x7ffa169d0fbf _Unwind_RaiseException + 399
1   libc++abi.dylib               	    0x7ff801c10764 __cxa_throw + 61
2   libobjc.A.dylib               	    0x7ff8019d8871 objc_exception_throw + 321
3   CoreFoundation                	    0x7ff801e28d82 +[NSException raise:format:] + 226
4   RideFuseDriver.debug.dylib    	       0x1121efd83 +[FIRApp configure] + 115 (FIRApp.m:110)
5   RideFuseDriver.debug.dylib    	       0x1121dff0c -[AppDelegate application:didFinishLaunchingWithOptions:] + 92 (AppDelegate.mm:32)
6   UIKitCore                     	    0x7ff807f90520 -[UIApplication _handleDelegateCallbacksWithOptions:isSuspended:restoreState:] + 298
7   UIKitCore                     	    0x7ff807f92479 -[UIApplication _callInitializationDelegatesWithActions:forCanvas:payload:fromOriginatingProcess:] + 4595
8   UIKitCore                     	    0x7ff807f9883c -[UIApplication _runWithMainScene:transitionContext:completion:] + 1343
9   UIKitCore                     	    0x7ff8071e1c0c -[_UISceneLifecycleMultiplexer completeApplicationLaunchWithFBSScene:transitionContext:] + 122
10  UIKitCore                     	    0x7ff80798ac05 _UIScenePerformActionsWithLifecycleActionMask + 93
11  UIKitCore                     	    0x7ff8071e27a0 __101-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]_block_invoke + 259
12  UIKitCore                     	    0x7ff8071e21d6 -[_UISceneLifecycleMultiplexer _performBlock:withApplicationOfDeactivationReasons:fromReasons:] + 260
13  UIKitCore                     	    0x7ff8071e25ac -[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:] + 813
14  UIKitCore                     	    0x7ff8071e1ddf -[_UISceneLifecycleMultiplexer uiScene:transitionedFromState:withTransitionContext:] + 341
15  UIKitCore                     	    0x7ff8071f02d0 __186-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]_block_invoke + 174
16  UIKitCore                     	    0x7ff807825462 +[BSAnimationSettings(UIKit) tryAnimatingWithSettings:fromCurrentState:actions:completion:] + 856
17  UIKitCore                     	    0x7ff8079acbd5 _UISceneSettingsDiffActionPerformChangesWithTransitionContextAndCompletion + 261
18  UIKitCore                     	    0x7ff8071eff5f -[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:] + 347
19  UIKitCore                     	    0x7ff806f83c2f __64-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]_block_invoke.615 + 879
20  UIKitCore                     	    0x7ff806f825d9 -[UIScene _emitSceneSettingsUpdateResponseForCompletion:afterSceneUpdateWork:] + 246
21  UIKitCore                     	    0x7ff806f83795 -[UIScene scene:didUpdateWithDiff:transitionContext:completion:] + 252
22  UIKitCore                     	    0x7ff807f96f80 -[UIApplication workspace:didCreateScene:withTransitionContext:completion:] + 968
23  UIKitCore                     	    0x7ff80785d94b -[UIApplicationSceneClientAgent scene:didInitializeWithEvent:completion:] + 353
24  FrontBoardServices            	    0x7ff80a221ea3 __95-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]_block_invoke + 301
25  FrontBoardServices            	    0x7ff80a22243d -[FBSScene _callOutQueue_coalesceClientSettingsUpdates:] + 44
26  FrontBoardServices            	    0x7ff80a221d09 -[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:] + 556
27  FrontBoardServices            	    0x7ff80a2532ae __93-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]_block_invoke.344 + 269
28  FrontBoardServices            	    0x7ff80a230feb -[FBSWorkspace _calloutQueue_executeCalloutFromSource:withBlock:] + 213
29  FrontBoardServices            	    0x7ff80a2513db -[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:] + 540
30  libdispatch.dylib             	    0x7ff801ae05b6 _dispatch_client_callout + 6
31  libdispatch.dylib             	    0x7ff801acafb7 _dispatch_block_invoke_direct + 473
32  FrontBoardServices            	    0x7ff80a27478d __FBSSERIALQUEUE_IS_CALLING_OUT_TO_A_BLOCK__ + 30
33  FrontBoardServices            	    0x7ff80a274667 -[FBSMainRunLoopSerialQueue _targetQueue_performNextIfPossible] + 188
34  FrontBoardServices            	    0x7ff80a2acad1 -[FBSMainRunLoopSerialQueue _performNextFromRunLoopSource] + 19
35  CoreFoundation                	    0x7ff801d83124 __CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__ + 17
36  CoreFoundation                	    0x7ff801d83066 __CFRunLoopDoSource0 + 157
37  CoreFoundation                	    0x7ff801d828bb __CFRunLoopDoSources0 + 311
38  CoreFoundation                	    0x7ff801d7d0c6 __CFRunLoopRun + 973
39  CoreFoundation                	    0x7ff801d7c8f1 CFRunLoopRunSpecific + 536
40  GraphicsServices              	    0x7ff814242c09 GSEventRunModal + 137
41  UIKitCore                     	    0x7ff807f94c50 -[UIApplication _run] + 875
42  UIKitCore                     	    0x7ff807f99dd7 UIApplicationMain + 123
43  RideFuseDriver.debug.dylib    	       0x1121e02b0 __debug_main_executable_dylib_entry_point + 96 (main.m:8)
44  ???                           	       0x10d8923de ???
45  dyld                          	       0x10e7eb530 start + 3056

Thread 1:
0   libsystem_pthread.dylib       	       0x10d757834 start_wqthread + 0

Thread 2:
0   libsystem_pthread.dylib       	       0x10d757834 start_wqthread + 0

Thread 3:
0   libsystem_pthread.dylib       	       0x10d757834 start_wqthread + 0

Thread 4::  Dispatch queue: com.apple.UIKit.KeyboardManagement
0   libsystem_kernel.dylib        	       0x10d7cf316 __ulock_wait + 10
1   libdispatch.dylib             	    0x7ff801ac7e47 _dlock_wait + 46
2   libdispatch.dylib             	    0x7ff801ac7cbf _dispatch_thread_event_wait_slow + 40
3   libdispatch.dylib             	    0x7ff801ad6a2a __DISPATCH_WAIT_FOR_QUEUE__ + 356
4   libdispatch.dylib             	    0x7ff801ad6582 _dispatch_sync_f_slow + 196
5   UIKitCore                     	    0x7ff807d44290 __37-[_UIRemoteKeyboards startConnection]_block_invoke.676 + 108
6   CoreFoundation                	    0x7ff801e3011c __invoking___ + 140
7   CoreFoundation                	    0x7ff801e2d229 -[NSInvocation invoke] + 302
8   Foundation                    	    0x7ff8029d3848 <deduplicated_symbol> + 17
9   Foundation                    	    0x7ff8029d344c -[NSXPCConnection _decodeAndInvokeReplyBlockWithEvent:sequence:replyInfo:] + 608
10  Foundation                    	    0x7ff8029d7f50 __88-[NSXPCConnection _sendInvocation:orArguments:count:methodSignature:selector:withProxy:]_block_invoke_3 + 205
11  libxpc.dylib                  	    0x7ff801a1befe _xpc_connection_reply_callout + 36
12  libxpc.dylib                  	    0x7ff801a109a4 _xpc_connection_call_reply_async + 83
13  libdispatch.dylib             	    0x7ff801ae05c6 <deduplicated_symbol> + 6
14  libdispatch.dylib             	    0x7ff801ae4bf1 _dispatch_mach_msg_async_reply_invoke + 599
15  libdispatch.dylib             	    0x7ff801aceb78 _dispatch_lane_serial_drain + 355
16  libdispatch.dylib             	    0x7ff801acf996 _dispatch_lane_invoke + 413
17  libdispatch.dylib             	    0x7ff801adad32 _dispatch_root_queue_drain_deferred_wlh + 290
18  libdispatch.dylib             	    0x7ff801ada375 _dispatch_workloop_worker_thread + 518
19  libsystem_pthread.dylib       	       0x10d7588ad _pthread_wqthread + 298
20  libsystem_pthread.dylib       	       0x10d757843 start_wqthread + 15

Thread 5:: com.apple.uikit.eventfetch-thread
0   libsystem_kernel.dylib        	       0x10d7cdb4a mach_msg2_trap + 10
1   libsystem_kernel.dylib        	       0x10d7dc704 mach_msg2_internal + 83
2   libsystem_kernel.dylib        	       0x10d7d4bc3 mach_msg_overwrite + 574
3   libsystem_kernel.dylib        	       0x10d7cde3b mach_msg + 19
4   CoreFoundation                	    0x7ff801d829d4 __CFRunLoopServiceMachPort + 145
5   CoreFoundation                	    0x7ff801d7d28f __CFRunLoopRun + 1430
6   CoreFoundation                	    0x7ff801d7c8f1 CFRunLoopRunSpecific + 536
7   Foundation                    	    0x7ff8028d575f -[NSRunLoop(NSRunLoop) runMode:beforeDate:] + 213
8   Foundation                    	    0x7ff8028d59d2 -[NSRunLoop(NSRunLoop) runUntilDate:] + 72
9   UIKitCore                     	    0x7ff80807be91 -[UIEventFetcher threadMain] + 518
10  Foundation                    	    0x7ff8029005d9 __NSThread__start__ + 1014
11  libsystem_pthread.dylib       	       0x10d75bdf1 _pthread_start + 99
12  libsystem_pthread.dylib       	       0x10d757857 thread_start + 15

Thread 6:
0   libsystem_pthread.dylib       	       0x10d757834 start_wqthread + 0

Thread 7:
0   libsystem_pthread.dylib       	       0x10d757834 start_wqthread + 0

Thread 8::  Dispatch queue: GULLoggingClientQueue
0   RideFuseDriver.debug.dylib    	       0x11221d340 __destroy_helper_block_e8_32s40s48s + 0
1   libsystem_blocks.dylib        	    0x7ff801a3bc54 _call_dispose_helpers_excp + 45
2   libsystem_blocks.dylib        	    0x7ff801a3c8b0 _Block_release + 211
3   libdispatch.dylib             	    0x7ff801ae05b6 _dispatch_client_callout + 6
4   libdispatch.dylib             	    0x7ff801aceea7 _dispatch_lane_serial_drain + 1170
5   libdispatch.dylib             	    0x7ff801acf996 _dispatch_lane_invoke + 413
6   libdispatch.dylib             	    0x7ff801adad32 _dispatch_root_queue_drain_deferred_wlh + 290
7   libdispatch.dylib             	    0x7ff801ada375 _dispatch_workloop_worker_thread + 518
8   libsystem_pthread.dylib       	       0x10d7588ad _pthread_wqthread + 298
9   libsystem_pthread.dylib       	       0x10d757843 start_wqthread + 15

Thread 9:
0   libsystem_pthread.dylib       	       0x10d757834 start_wqthread + 0


Thread 0 crashed with X86 Thread State (64-bit):
  rax: 0x0020019008070c10  rbx: 0x000060000330c1a0  rcx: 0x000060000330c1a0  rdx: 0x434c4e47432b2b00
  rdi: 0x0000000000000001  rsi: 0x0000000000000001  rbp: 0x00007ff7b27b2730  rsp: 0x00007ff7b27b1b20
   r8: 0x00007ff7b27b2060   r9: 0x00000000ffffff80  r10: 0x00007ff801b071a0  r11: 0x0000000000ffffff
  r12: 0x00007ff7b27b26c0  r13: 0x0000000000000000  r14: 0x00007ff7b27b2060  r15: 0x0000000000000003
  rip: 0x00007ffa169d0fbf  rfl: 0x0000000000010202  cr2: 0x0000000000000000
  
Logical CPU:     4
Error Code:      0x00000000 
Trap Number:     13


Binary Images:
       0x10e7e5000 -        0x10e87ffff dyld (*) <3771ea6a-0fe5-3b63-961d-c09e01d5e680> /usr/lib/dyld
       0x10d748000 -        0x10d74afff com.ridefuzeDriver (1.0) <e35d7752-55b6-30b5-9f55-8d58f6d4bf25> /Users/<USER>/Library/Developer/CoreSimulator/Devices/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E/data/Containers/Bundle/Application/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1/RideFuseDriver.app/RideFuseDriver
       0x1121dd000 -        0x1132d2fff RideFuseDriver.debug.dylib (*) <9ea616d3-2d5d-347e-95c8-0b3c87be3f96> /Users/<USER>/Library/Developer/CoreSimulator/Devices/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E/data/Containers/Bundle/Application/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1/RideFuseDriver.app/RideFuseDriver.debug.dylib
       0x10e901000 -        0x10f655fff org.webrtc.WebRTC (1.0) <4c4c440c-5555-3144-a1e2-aea9b0483609> /Users/<USER>/Library/Developer/CoreSimulator/Devices/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E/data/Containers/Bundle/Application/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1/RideFuseDriver.app/Frameworks/WebRTC.framework/WebRTC
       0x10e016000 -        0x10e3edfff dev.hermesengine.iphonesimulator (0.12.0) <9acd9425-820e-3de7-9d55-0734a58dbe44> /Users/<USER>/Library/Developer/CoreSimulator/Devices/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E/data/Containers/Bundle/Application/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1/RideFuseDriver.app/Frameworks/hermes.framework/hermes
       0x10d7b4000 -        0x10d7bdfff libsystem_platform.dylib (*) <1fb5adb0-059e-3369-9c63-abde93e91499> /usr/lib/system/libsystem_platform.dylib
       0x10d7cd000 -        0x10d809fff libsystem_kernel.dylib (*) <dab10aa4-8afa-3d02-9cde-6023554ac858> /usr/lib/system/libsystem_kernel.dylib
       0x10d756000 -        0x10d761fff libsystem_pthread.dylib (*) <a6d1f05a-0743-31b7-9fe2-268f06ccd51a> /usr/lib/system/libsystem_pthread.dylib
       0x10db69000 -        0x10db75fff libobjc-trampolines.dylib (*) <27de8f2a-a376-3a92-958d-7d7534b2a8c2> /Volumes/VOLUME/*/libobjc-trampolines.dylib
    0x7ffa169ca000 -     0x7ffa169d288b libunwind.dylib (*) <a62cfa81-9ec8-3f5d-bc73-75edf0eb8f01> /Volumes/VOLUME/*/libunwind.dylib
    0x7ff801bfe000 -     0x7ff801c14ffb libc++abi.dylib (*) <38ee0e6c-f1c2-3ba1-9e87-************> /Volumes/VOLUME/*/libc++abi.dylib
    0x7ff8019af000 -     0x7ff8019e98b8 libobjc.A.dylib (*) <08be3089-1995-3b6b-8d0d-b5905a292e56> /Volumes/VOLUME/*/libobjc.A.dylib
    0x7ff801cf0000 -     0x7ff80207effc com.apple.CoreFoundation (6.9) <a14dc6b1-6e11-370c-91bc-eef65e80b420> /Volumes/VOLUME/*/CoreFoundation.framework/CoreFoundation
    0x7ff806c7c000 -     0x7ff808efa045 com.apple.UIKitCore (1.0) <31e56e71-c837-3acb-b211-7a93c926f6ff> /Volumes/VOLUME/*/UIKitCore.framework/UIKitCore
    0x7ff80a20f000 -     0x7ff80a2e8016 com.apple.FrontBoardServices (943.6.1) <17263dfc-48b9-34b3-a6e4-25b657f8de5b> /Volumes/VOLUME/*/FrontBoardServices.framework/FrontBoardServices
    0x7ff801ac5000 -     0x7ff801b08ce1 libdispatch.dylib (*) <28e4fcbc-fc35-3d9f-bf59-36d297024274> /Volumes/VOLUME/*/libdispatch.dylib
    0x7ff814240000 -     0x7ff814247b8a com.apple.GraphicsServices (1.0) <e3a6180c-5eb8-35ab-8e80-3aa2dd0f1ca6> /Volumes/VOLUME/*/GraphicsServices.framework/GraphicsServices
               0x0 - 0xffffffffffffffff ??? (*) <00000000-0000-0000-0000-000000000000> ???
    0x7ff8020f9000 -     0x7ff802d7cf7d com.apple.Foundation (6.9) <b1613ef4-d54b-3d54-aedc-4bea18e33c23> /Volumes/VOLUME/*/Foundation.framework/Foundation
    0x7ff801a03000 -     0x7ff801a3a90d libxpc.dylib (*) <e086bb89-6564-32d7-bc95-9defc1a6777d> /Volumes/VOLUME/*/libxpc.dylib
    0x7ff801a3b000 -     0x7ff801a3e0d0 libsystem_blocks.dylib (*) <a2244f95-3e6d-3c71-af95-eeb6eff24622> /Volumes/VOLUME/*/libsystem_blocks.dylib

EOF

-----------
Full Report
-----------

{"app_name":"RideFuseDriver","timestamp":"2025-07-04 12:22:48.00 +0100","app_version":"1.0","slice_uuid":"e35d7752-55b6-30b5-9f55-8d58f6d4bf25","build_version":"1","platform":7,"bundleID":"com.ridefuzeDriver","share_with_app_devs":0,"is_first_party":0,"bug_type":"309","os_version":"macOS 15.5 (24F74)","roots_installed":0,"name":"RideFuseDriver","incident_id":"5A42C38D-3794-4AA1-ABB4-00C33B3C8EBA"}
{
  "uptime" : 13000,
  "procRole" : "Foreground",
  "version" : 2,
  "userID" : 501,
  "deployVersion" : 210,
  "modelCode" : "MacBookPro16,2",
  "coalitionID" : 1179,
  "osVersion" : {
    "train" : "macOS 15.5",
    "build" : "24F74",
    "releaseType" : "User"
  },
  "captureTime" : "2025-07-04 12:22:45.7629 +0100",
  "codeSigningMonitor" : 0,
  "incident" : "5A42C38D-3794-4AA1-ABB4-00C33B3C8EBA",
  "pid" : 14145,
  "cpuType" : "X86-64",
  "roots_installed" : 0,
  "bug_type" : "309",
  "procLaunch" : "2025-07-04 12:22:43.4360 +0100",
  "procStartAbsTime" : 13375842801402,
  "procExitAbsTime" : 13378168106836,
  "procName" : "RideFuseDriver",
  "procPath" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/RideFuseDriver",
  "bundleInfo" : {"CFBundleShortVersionString":"1.0","CFBundleVersion":"1","CFBundleIdentifier":"com.ridefuzeDriver"},
  "storeInfo" : {"deviceIdentifierForVendor":"A50B647D-71CE-51B2-B1AF-31F66F60C937","thirdParty":true},
  "parentProc" : "launchd_sim",
  "parentPid" : 939,
  "coalitionName" : "com.apple.CoreSimulator.SimDevice.4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E",
  "crashReporterKey" : "CE864D80-1530-CD12-8781-F1F1ED7AAD6C",
  "lowPowerMode" : 1,
  "appleIntelligenceStatus" : {"reasons":["selectedLanguageDoesNotMatchSelectedSiriLanguage","deviceNotCapable"],"state":"unavailable"},
  "responsiblePid" : 931,
  "responsibleProc" : "SimulatorTrampoline",
  "codeSigningID" : "com.ridefuzeDriver",
  "codeSigningTeamID" : "",
  "codeSigningFlags" : 570425857,
  "codeSigningValidationCategory" : 10,
  "codeSigningTrustLevel" : 4294967295,
  "codeSigningAuxiliaryInfo" : 0,
  "bootSessionUUID" : "15B82D75-A742-4E18-B207-CC5510D081BE",
  "wakeTime" : 401,
  "bridgeVersion" : {"build":"22P5072","train":"9.5"},
  "sleepWakeUUID" : "F00DD25B-6256-4597-92D2-D07F6E4257EF",
  "sip" : "enabled",
  "vmRegionInfo" : "0 is not in any region.  Bytes before following region: 4520706048\n      REGION TYPE                    START - END         [ VSIZE] PRT\/MAX SHRMOD  REGION DETAIL\n      UNUSED SPACE AT START\n--->  \n      __TEXT                      10d748000-10d74b000    [   12K] r-x\/r-x SM=COW  \/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/RideFuseDriver",
  "exception" : {"codes":"0x000000000000000d, 0x0000000000000000","rawCodes":[13,0],"type":"EXC_BAD_ACCESS","signal":"SIGSEGV","subtype":"UNKNOWN_0xD at 0x0000000000000000"},
  "termination" : {"flags":0,"code":11,"namespace":"SIGNAL","indicator":"Segmentation fault: 11","byProc":"exc handler","byPid":14145},
  "vmregioninfo" : "0 is not in any region.  Bytes before following region: 4520706048\n      REGION TYPE                    START - END         [ VSIZE] PRT\/MAX SHRMOD  REGION DETAIL\n      UNUSED SPACE AT START\n--->  \n      __TEXT                      10d748000-10d74b000    [   12K] r-x\/r-x SM=COW  \/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/RideFuseDriver",
  "extMods" : {"caller":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"system":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"targeted":{"thread_create":0,"thread_set_state":0,"task_for_pid":0},"warnings":0},
  "faultingThread" : 0,
  "threads" : [{"triggered":true,"id":240616,"instructionState":{"instructionStream":{"bytes":[204,180,255,255,76,141,101,144,65,191,3,0,0,0,102,144,72,139,133,48,249,255,255,76,137,247,49,246,255,80,64,133,192,15,132,150,0,0,0,15,136,183,0,0,0,15,87,192,15,41,69,192,15,41,69,176,15,41,69,160,15,41,69,144,72,199,69,208,0,0,0,0,72,139,133,48,249,255,255,76,137,247,76,137,230,255,80,72,72,131,125,152,0,15,132,129,0,0,0,72,139,69,168,72,133,192,116,164,72,139,19,191,1,0,0,0,190,1,0,0,0,72,137,217,77,137,240,255,208,131,248,8,116,138,131,248,6,117,89,72,139,133,48,249,255,255,72,141,189,48,249,255,255,190,254,255,255,255,255,80,16,132,192,116,32,72,139,133,48,249,255,255,72,141,189,48,249,255,255,190,254,255,255,255,255,80,24,235,10,65,191,5],"offset":127}},"threadState":{"r13":{"value":0},"rax":{"value":9008917376338960},"rflags":{"value":66050},"cpu":{"value":4},"r14":{"value":140701828063328},"rsi":{"value":1},"r8":{"value":140701828063328},"cr2":{"value":0},"rdx":{"value":4849336966747728640},"r10":{"value":140703156957600},"r9":{"value":4294967168},"r15":{"value":3},"rbx":{"value":105553169793440},"trap":{"value":13},"err":{"value":0},"r11":{"value":16777215},"rip":{"value":140712097943487,"matchesCrashFrame":1},"rbp":{"value":140701828065072},"rsp":{"value":140701828061984},"r12":{"value":140701828064960},"rcx":{"value":105553169793440},"flavor":"x86_THREAD_STATE","rdi":{"value":1}},"queue":"com.apple.main-thread","frames":[{"imageOffset":28607,"symbol":"_Unwind_RaiseException","symbolLocation":399,"imageIndex":9},{"imageOffset":75620,"symbol":"__cxa_throw","symbolLocation":61,"imageIndex":10},{"imageOffset":170097,"symbol":"objc_exception_throw","symbolLocation":321,"imageIndex":11},{"imageOffset":1281410,"symbol":"+[NSException raise:format:]","symbolLocation":226,"imageIndex":12},{"imageOffset":77187,"sourceLine":110,"sourceFile":"FIRApp.m","symbol":"+[FIRApp configure]","imageIndex":2,"symbolLocation":115},{"imageOffset":12044,"sourceLine":32,"sourceFile":"AppDelegate.mm","symbol":"-[AppDelegate application:didFinishLaunchingWithOptions:]","imageIndex":2,"symbolLocation":92},{"imageOffset":20006176,"symbol":"-[UIApplication _handleDelegateCallbacksWithOptions:isSuspended:restoreState:]","symbolLocation":298,"imageIndex":13},{"imageOffset":20014201,"symbol":"-[UIApplication _callInitializationDelegatesWithActions:forCanvas:payload:fromOriginatingProcess:]","symbolLocation":4595,"imageIndex":13},{"imageOffset":20039740,"symbol":"-[UIApplication _runWithMainScene:transitionContext:completion:]","symbolLocation":1343,"imageIndex":13},{"imageOffset":5659660,"symbol":"-[_UISceneLifecycleMultiplexer completeApplicationLaunchWithFBSScene:transitionContext:]","symbolLocation":122,"imageIndex":13},{"imageOffset":13691909,"symbol":"_UIScenePerformActionsWithLifecycleActionMask","symbolLocation":93,"imageIndex":13},{"imageOffset":5662624,"symbol":"__101-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]_block_invoke","symbolLocation":259,"imageIndex":13},{"imageOffset":5661142,"symbol":"-[_UISceneLifecycleMultiplexer _performBlock:withApplicationOfDeactivationReasons:fromReasons:]","symbolLocation":260,"imageIndex":13},{"imageOffset":5662124,"symbol":"-[_UISceneLifecycleMultiplexer _evalTransitionToSettings:fromSettings:forceExit:withTransitionStore:]","symbolLocation":813,"imageIndex":13},{"imageOffset":5660127,"symbol":"-[_UISceneLifecycleMultiplexer uiScene:transitionedFromState:withTransitionContext:]","symbolLocation":341,"imageIndex":13},{"imageOffset":5718736,"symbol":"__186-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]_block_invoke","symbolLocation":174,"imageIndex":13},{"imageOffset":12227682,"symbol":"+[BSAnimationSettings(UIKit) tryAnimatingWithSettings:fromCurrentState:actions:completion:]","symbolLocation":856,"imageIndex":13},{"imageOffset":13831125,"symbol":"_UISceneSettingsDiffActionPerformChangesWithTransitionContextAndCompletion","symbolLocation":261,"imageIndex":13},{"imageOffset":5717855,"symbol":"-[_UIWindowSceneFBSSceneTransitionContextDrivenLifecycleSettingsDiffAction _performActionsForUIScene:withUpdatedFBSScene:settingsDiff:fromSettings:transitionContext:lifecycleActionType:]","symbolLocation":347,"imageIndex":13},{"imageOffset":3177519,"symbol":"__64-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]_block_invoke.615","symbolLocation":879,"imageIndex":13},{"imageOffset":3171801,"symbol":"-[UIScene _emitSceneSettingsUpdateResponseForCompletion:afterSceneUpdateWork:]","symbolLocation":246,"imageIndex":13},{"imageOffset":3176341,"symbol":"-[UIScene scene:didUpdateWithDiff:transitionContext:completion:]","symbolLocation":252,"imageIndex":13},{"imageOffset":20033408,"symbol":"-[UIApplication workspace:didCreateScene:withTransitionContext:completion:]","symbolLocation":968,"imageIndex":13},{"imageOffset":12458315,"symbol":"-[UIApplicationSceneClientAgent scene:didInitializeWithEvent:completion:]","symbolLocation":353,"imageIndex":13},{"imageOffset":77475,"symbol":"__95-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]_block_invoke","symbolLocation":301,"imageIndex":14},{"imageOffset":78909,"symbol":"-[FBSScene _callOutQueue_coalesceClientSettingsUpdates:]","symbolLocation":44,"imageIndex":14},{"imageOffset":77065,"symbol":"-[FBSScene _callOutQueue_didCreateWithTransitionContext:alternativeCreationCallout:completion:]","symbolLocation":556,"imageIndex":14},{"imageOffset":279214,"symbol":"__93-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]_block_invoke.344","symbolLocation":269,"imageIndex":14},{"imageOffset":139243,"symbol":"-[FBSWorkspace _calloutQueue_executeCalloutFromSource:withBlock:]","symbolLocation":213,"imageIndex":14},{"imageOffset":271323,"symbol":"-[FBSWorkspaceScenesClient _callOutQueue_sendDidCreateForScene:transitionContext:completion:]","symbolLocation":540,"imageIndex":14},{"imageOffset":112054,"symbol":"_dispatch_client_callout","symbolLocation":6,"imageIndex":15},{"imageOffset":24503,"symbol":"_dispatch_block_invoke_direct","symbolLocation":473,"imageIndex":15},{"imageOffset":415629,"symbol":"__FBSSERIALQUEUE_IS_CALLING_OUT_TO_A_BLOCK__","symbolLocation":30,"imageIndex":14},{"imageOffset":415335,"symbol":"-[FBSMainRunLoopSerialQueue _targetQueue_performNextIfPossible]","symbolLocation":188,"imageIndex":14},{"imageOffset":645841,"symbol":"-[FBSMainRunLoopSerialQueue _performNextFromRunLoopSource]","symbolLocation":19,"imageIndex":14},{"imageOffset":602404,"symbol":"__CFRUNLOOP_IS_CALLING_OUT_TO_A_SOURCE0_PERFORM_FUNCTION__","symbolLocation":17,"imageIndex":12},{"imageOffset":602214,"symbol":"__CFRunLoopDoSource0","symbolLocation":157,"imageIndex":12},{"imageOffset":600251,"symbol":"__CFRunLoopDoSources0","symbolLocation":311,"imageIndex":12},{"imageOffset":577734,"symbol":"__CFRunLoopRun","symbolLocation":973,"imageIndex":12},{"imageOffset":575729,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":12},{"imageOffset":11273,"symbol":"GSEventRunModal","symbolLocation":137,"imageIndex":16},{"imageOffset":20024400,"symbol":"-[UIApplication _run]","symbolLocation":875,"imageIndex":13},{"imageOffset":20045271,"symbol":"UIApplicationMain","symbolLocation":123,"imageIndex":13},{"imageOffset":12976,"sourceLine":8,"sourceFile":"main.m","symbol":"__debug_main_executable_dylib_entry_point","imageIndex":2,"symbolLocation":96},{"imageOffset":4522058718,"imageIndex":17},{"imageOffset":25904,"symbol":"start","symbolLocation":3056,"imageIndex":0}]},{"id":240705,"frames":[{"imageOffset":6196,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":7}],"threadState":{"r13":{"value":0},"rax":{"value":33554800},"rflags":{"value":512},"cpu":{"value":0},"r14":{"value":0},"rsi":{"value":7171},"r8":{"value":409604},"cr2":{"value":0},"rdx":{"value":123145442123776},"r10":{"value":0},"r9":{"value":18446744073709551615},"r15":{"value":0},"rbx":{"value":123145442648064},"trap":{"value":133},"err":{"value":33554800},"r11":{"value":582},"rip":{"value":4520769588},"rbp":{"value":0},"rsp":{"value":123145442648064},"r12":{"value":0},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":123145442648064}}},{"id":240706,"frames":[{"imageOffset":6196,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":7}],"threadState":{"r13":{"value":0},"rax":{"value":33554800},"rflags":{"value":512},"cpu":{"value":0},"r14":{"value":1},"rsi":{"value":6915},"r8":{"value":409604},"cr2":{"value":0},"rdx":{"value":123145442660352},"r10":{"value":0},"r9":{"value":18446744073709551615},"r15":{"value":123145443183488},"rbx":{"value":123145443184640},"trap":{"value":133},"err":{"value":33554800},"r11":{"value":582},"rip":{"value":4520769588},"rbp":{"value":0},"rsp":{"value":123145443184640},"r12":{"value":1982472},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":123145443184640}}},{"id":240707,"frames":[{"imageOffset":6196,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":7}],"threadState":{"r13":{"value":0},"rax":{"value":33554800},"rflags":{"value":512},"cpu":{"value":0},"r14":{"value":1},"rsi":{"value":6147},"r8":{"value":409604},"cr2":{"value":0},"rdx":{"value":123145443196928},"r10":{"value":0},"r9":{"value":18446744073709551615},"r15":{"value":123145443720064},"rbx":{"value":123145443721216},"trap":{"value":133},"err":{"value":33554800},"r11":{"value":582},"rip":{"value":4520769588},"rbp":{"value":0},"rsp":{"value":123145443721216},"r12":{"value":1982472},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":123145443721216}}},{"id":240728,"threadState":{"r13":{"value":140703156755988,"symbolLocation":0,"symbol":"_dispatch_main_queue_push"},"rax":{"value":18446744073709551612},"rflags":{"value":514},"cpu":{"value":0},"r14":{"value":16777217},"rsi":{"value":123145444253432},"r8":{"value":4520802160,"symbolLocation":0,"symbol":"_pthread_qos_class_from_thread_qos.thread_qos_to_qos_class"},"cr2":{"value":0},"rdx":{"value":0},"r10":{"value":0},"r9":{"value":18},"r15":{"value":0},"rbx":{"value":4294967295},"trap":{"value":133},"err":{"value":33554947},"r11":{"value":514},"rip":{"value":4521259798},"rbp":{"value":123145444253216},"rsp":{"value":123145444253176},"r12":{"value":123145444253432},"rcx":{"value":123145444253176},"flavor":"x86_THREAD_STATE","rdi":{"value":16777217}},"queue":"com.apple.UIKit.KeyboardManagement","frames":[{"imageOffset":8982,"symbol":"__ulock_wait","symbolLocation":10,"imageIndex":6},{"imageOffset":11847,"symbol":"_dlock_wait","symbolLocation":46,"imageIndex":15},{"imageOffset":11455,"symbol":"_dispatch_thread_event_wait_slow","symbolLocation":40,"imageIndex":15},{"imageOffset":72234,"symbol":"__DISPATCH_WAIT_FOR_QUEUE__","symbolLocation":356,"imageIndex":15},{"imageOffset":71042,"symbol":"_dispatch_sync_f_slow","symbolLocation":196,"imageIndex":15},{"imageOffset":17597072,"symbol":"__37-[_UIRemoteKeyboards startConnection]_block_invoke.676","symbolLocation":108,"imageIndex":13},{"imageOffset":1311004,"symbol":"__invoking___","symbolLocation":140,"imageIndex":12},{"imageOffset":1298985,"symbol":"-[NSInvocation invoke]","symbolLocation":302,"imageIndex":12},{"imageOffset":9283656,"symbol":"<deduplicated_symbol>","symbolLocation":17,"imageIndex":18},{"imageOffset":9282636,"symbol":"-[NSXPCConnection _decodeAndInvokeReplyBlockWithEvent:sequence:replyInfo:]","symbolLocation":608,"imageIndex":18},{"imageOffset":9301840,"symbol":"__88-[NSXPCConnection _sendInvocation:orArguments:count:methodSignature:selector:withProxy:]_block_invoke_3","symbolLocation":205,"imageIndex":18},{"imageOffset":102142,"symbol":"_xpc_connection_reply_callout","symbolLocation":36,"imageIndex":19},{"imageOffset":55716,"symbol":"_xpc_connection_call_reply_async","symbolLocation":83,"imageIndex":19},{"imageOffset":112070,"symbol":"<deduplicated_symbol>","symbolLocation":6,"imageIndex":15},{"imageOffset":130033,"symbol":"_dispatch_mach_msg_async_reply_invoke","symbolLocation":599,"imageIndex":15},{"imageOffset":39800,"symbol":"_dispatch_lane_serial_drain","symbolLocation":355,"imageIndex":15},{"imageOffset":43414,"symbol":"_dispatch_lane_invoke","symbolLocation":413,"imageIndex":15},{"imageOffset":89394,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":290,"imageIndex":15},{"imageOffset":86901,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":518,"imageIndex":15},{"imageOffset":10413,"symbol":"_pthread_wqthread","symbolLocation":298,"imageIndex":7},{"imageOffset":6211,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":7}]},{"id":240729,"name":"com.apple.uikit.eventfetch-thread","threadState":{"r13":{"value":21592279046},"rax":{"value":268451845},"rflags":{"value":518},"cpu":{"value":0},"r14":{"value":36296768618496},"rsi":{"value":21592279046},"r8":{"value":1122631272},"cr2":{"value":0},"rdx":{"value":8589934592},"r10":{"value":36296768618496},"r9":{"value":36296768618496},"r15":{"value":2},"rbx":{"value":123145444789632},"trap":{"value":133},"err":{"value":16777263},"r11":{"value":518},"rip":{"value":4521253706},"rbp":{"value":123145444789472},"rsp":{"value":123145444789368},"r12":{"value":36296768618496},"rcx":{"value":123145444789368},"flavor":"x86_THREAD_STATE","rdi":{"value":123145444789632}},"frames":[{"imageOffset":2890,"symbol":"mach_msg2_trap","symbolLocation":10,"imageIndex":6},{"imageOffset":63236,"symbol":"mach_msg2_internal","symbolLocation":83,"imageIndex":6},{"imageOffset":31683,"symbol":"mach_msg_overwrite","symbolLocation":574,"imageIndex":6},{"imageOffset":3643,"symbol":"mach_msg","symbolLocation":19,"imageIndex":6},{"imageOffset":600532,"symbol":"__CFRunLoopServiceMachPort","symbolLocation":145,"imageIndex":12},{"imageOffset":578191,"symbol":"__CFRunLoopRun","symbolLocation":1430,"imageIndex":12},{"imageOffset":575729,"symbol":"CFRunLoopRunSpecific","symbolLocation":536,"imageIndex":12},{"imageOffset":8243039,"symbol":"-[NSRunLoop(NSRunLoop) runMode:beforeDate:]","symbolLocation":213,"imageIndex":18},{"imageOffset":8243666,"symbol":"-[NSRunLoop(NSRunLoop) runUntilDate:]","symbolLocation":72,"imageIndex":18},{"imageOffset":20971153,"symbol":"-[UIEventFetcher threadMain]","symbolLocation":518,"imageIndex":13},{"imageOffset":8418777,"symbol":"__NSThread__start__","symbolLocation":1014,"imageIndex":18},{"imageOffset":24049,"symbol":"_pthread_start","symbolLocation":99,"imageIndex":7},{"imageOffset":6231,"symbol":"thread_start","symbolLocation":15,"imageIndex":7}]},{"id":240730,"frames":[{"imageOffset":6196,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":7}],"threadState":{"r13":{"value":0},"rax":{"value":33554800},"rflags":{"value":512},"cpu":{"value":0},"r14":{"value":1},"rsi":{"value":14339},"r8":{"value":409604},"cr2":{"value":0},"rdx":{"value":123145444806656},"r10":{"value":0},"r9":{"value":18446744073709551615},"r15":{"value":123145445329784},"rbx":{"value":123145445330944},"trap":{"value":133},"err":{"value":33554800},"r11":{"value":582},"rip":{"value":4520769588},"rbp":{"value":0},"rsp":{"value":123145445330944},"r12":{"value":5193734},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":123145445330944}}},{"id":240731,"frames":[{"imageOffset":6196,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":7}],"threadState":{"r13":{"value":0},"rax":{"value":33554800},"rflags":{"value":512},"cpu":{"value":0},"r14":{"value":1},"rsi":{"value":21507},"r8":{"value":409604},"cr2":{"value":0},"rdx":{"value":123145445343232},"r10":{"value":0},"r9":{"value":18446744073709551615},"r15":{"value":123145445866360},"rbx":{"value":123145445867520},"trap":{"value":133},"err":{"value":33554800},"r11":{"value":582},"rip":{"value":4520769588},"rbp":{"value":0},"rsp":{"value":123145445867520},"r12":{"value":5193734},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":123145445867520}}},{"id":240732,"threadState":{"r13":{"value":196610},"rax":{"value":4599173952,"sourceFile":"FIRInstallationsAPIService.m","symbol":"__destroy_helper_block_e8_32s40s48s","symbolLocation":0},"rflags":{"value":66050},"cpu":{"value":7},"r14":{"value":105553140485184},"rsi":{"value":0},"r8":{"value":16},"cr2":{"value":4599173952},"rdx":{"value":3271557121},"r10":{"value":5},"r9":{"value":105553181295184},"r15":{"value":140704251063616,"symbolLocation":384,"symbol":"_dispatch_root_queues"},"rbx":{"value":0},"trap":{"value":14,"description":"(no mapping for user instruction read)"},"err":{"value":20},"r11":{"value":15},"rip":{"value":4599173952},"rbp":{"value":123145446402112},"rsp":{"value":123145446402056},"r12":{"value":105553162413568},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":105553140485184}},"queue":"GULLoggingClientQueue","frames":[{"imageOffset":262976,"sourceFile":"FIRInstallationsAPIService.m","symbol":"__destroy_helper_block_e8_32s40s48s","symbolLocation":0,"imageIndex":2},{"imageOffset":3156,"symbol":"_call_dispose_helpers_excp","symbolLocation":45,"imageIndex":20},{"imageOffset":6320,"symbol":"_Block_release","symbolLocation":211,"imageIndex":20},{"imageOffset":112054,"symbol":"_dispatch_client_callout","symbolLocation":6,"imageIndex":15},{"imageOffset":40615,"symbol":"_dispatch_lane_serial_drain","symbolLocation":1170,"imageIndex":15},{"imageOffset":43414,"symbol":"_dispatch_lane_invoke","symbolLocation":413,"imageIndex":15},{"imageOffset":89394,"symbol":"_dispatch_root_queue_drain_deferred_wlh","symbolLocation":290,"imageIndex":15},{"imageOffset":86901,"symbol":"_dispatch_workloop_worker_thread","symbolLocation":518,"imageIndex":15},{"imageOffset":10413,"symbol":"_pthread_wqthread","symbolLocation":298,"imageIndex":7},{"imageOffset":6211,"symbol":"start_wqthread","symbolLocation":15,"imageIndex":7}]},{"id":240733,"frames":[{"imageOffset":6196,"symbol":"start_wqthread","symbolLocation":0,"imageIndex":7}],"threadState":{"r13":{"value":0},"rax":{"value":0},"rflags":{"value":512},"cpu":{"value":0},"r14":{"value":0},"rsi":{"value":0},"r8":{"value":278532},"cr2":{"value":0},"rdx":{"value":123145446416384},"r10":{"value":0},"r9":{"value":18446744073709551615},"r15":{"value":0},"rbx":{"value":0},"trap":{"value":0},"err":{"value":0},"r11":{"value":0},"rip":{"value":4520769588},"rbp":{"value":0},"rsp":{"value":123145446940672},"r12":{"value":0},"rcx":{"value":0},"flavor":"x86_THREAD_STATE","rdi":{"value":123145446940672}}}],
  "usedImages" : [
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4538126336,
    "size" : 634880,
    "uuid" : "3771ea6a-0fe5-3b63-961d-c09e01d5e680",
    "path" : "\/usr\/lib\/dyld",
    "name" : "dyld"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4520706048,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.ridefuzeDriver",
    "size" : 12288,
    "uuid" : "e35d7752-55b6-30b5-9f55-8d58f6d4bf25",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/RideFuseDriver",
    "name" : "RideFuseDriver",
    "CFBundleVersion" : "1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4598910976,
    "size" : 17784832,
    "uuid" : "9ea616d3-2d5d-347e-95c8-0b3c87be3f96",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/RideFuseDriver.debug.dylib",
    "name" : "RideFuseDriver.debug.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4539289600,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "org.webrtc.WebRTC",
    "size" : 13979648,
    "uuid" : "4c4c440c-5555-3144-a1e2-aea9b0483609",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/Frameworks\/WebRTC.framework\/WebRTC",
    "name" : "WebRTC",
    "CFBundleVersion" : "1.0"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4529938432,
    "CFBundleShortVersionString" : "0.12.0",
    "CFBundleIdentifier" : "dev.hermesengine.iphonesimulator",
    "size" : 4030464,
    "uuid" : "9acd9425-820e-3de7-9d55-0734a58dbe44",
    "path" : "\/Users\/<USER>\/Library\/Developer\/CoreSimulator\/Devices\/4F4F11EE-B8D2-4D38-B6D1-5CC2528A055E\/data\/Containers\/Bundle\/Application\/C03A2523-0883-41AA-BCD1-D5A1C7DFBEA1\/RideFuseDriver.app\/Frameworks\/hermes.framework\/hermes",
    "name" : "hermes",
    "CFBundleVersion" : "0.12.0"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4521148416,
    "size" : 40960,
    "uuid" : "1fb5adb0-059e-3369-9c63-abde93e91499",
    "path" : "\/usr\/lib\/system\/libsystem_platform.dylib",
    "name" : "libsystem_platform.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4521250816,
    "size" : 249856,
    "uuid" : "dab10aa4-8afa-3d02-9cde-6023554ac858",
    "path" : "\/usr\/lib\/system\/libsystem_kernel.dylib",
    "name" : "libsystem_kernel.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4520763392,
    "size" : 49152,
    "uuid" : "a6d1f05a-0743-31b7-9fe2-268f06ccd51a",
    "path" : "\/usr\/lib\/system\/libsystem_pthread.dylib",
    "name" : "libsystem_pthread.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 4525035520,
    "size" : 53248,
    "uuid" : "27de8f2a-a376-3a92-958d-7d7534b2a8c2",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc-trampolines.dylib",
    "name" : "libobjc-trampolines.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140712097914880,
    "size" : 34956,
    "uuid" : "a62cfa81-9ec8-3f5d-bc73-75edf0eb8f01",
    "path" : "\/Volumes\/VOLUME\/*\/libunwind.dylib",
    "name" : "libunwind.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703157968896,
    "size" : 94204,
    "uuid" : "38ee0e6c-f1c2-3ba1-9e87-************",
    "path" : "\/Volumes\/VOLUME\/*\/libc++abi.dylib",
    "name" : "libc++abi.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703155548160,
    "size" : 239801,
    "uuid" : "08be3089-1995-3b6b-8d0d-b5905a292e56",
    "path" : "\/Volumes\/VOLUME\/*\/libobjc.A.dylib",
    "name" : "libobjc.A.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703158960128,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.CoreFoundation",
    "size" : 3731453,
    "uuid" : "a14dc6b1-6e11-370c-91bc-eef65e80b420",
    "path" : "\/Volumes\/VOLUME\/*\/CoreFoundation.framework\/CoreFoundation",
    "name" : "CoreFoundation",
    "CFBundleVersion" : "3502"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703242371072,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.UIKitCore",
    "size" : 36167750,
    "uuid" : "31e56e71-c837-3acb-b211-7a93c926f6ff",
    "path" : "\/Volumes\/VOLUME\/*\/UIKitCore.framework\/UIKitCore",
    "name" : "UIKitCore",
    "CFBundleVersion" : "8506.1.101"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703298547712,
    "CFBundleShortVersionString" : "943.6.1",
    "CFBundleIdentifier" : "com.apple.FrontBoardServices",
    "size" : 888855,
    "uuid" : "17263dfc-48b9-34b3-a6e4-25b657f8de5b",
    "path" : "\/Volumes\/VOLUME\/*\/FrontBoardServices.framework\/FrontBoardServices",
    "name" : "FrontBoardServices",
    "CFBundleVersion" : "943.6.1"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703156686848,
    "size" : 277730,
    "uuid" : "28e4fcbc-fc35-3d9f-bf59-36d297024274",
    "path" : "\/Volumes\/VOLUME\/*\/libdispatch.dylib",
    "name" : "libdispatch.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703466520576,
    "CFBundleShortVersionString" : "1.0",
    "CFBundleIdentifier" : "com.apple.GraphicsServices",
    "size" : 31627,
    "uuid" : "e3a6180c-5eb8-35ab-8e80-3aa2dd0f1ca6",
    "path" : "\/Volumes\/VOLUME\/*\/GraphicsServices.framework\/GraphicsServices",
    "name" : "GraphicsServices",
    "CFBundleVersion" : "1.0"
  },
  {
    "size" : 0,
    "source" : "A",
    "base" : 0,
    "uuid" : "00000000-0000-0000-0000-000000000000"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703163191296,
    "CFBundleShortVersionString" : "6.9",
    "CFBundleIdentifier" : "com.apple.Foundation",
    "size" : 13123454,
    "uuid" : "b1613ef4-d54b-3d54-aedc-4bea18e33c23",
    "path" : "\/Volumes\/VOLUME\/*\/Foundation.framework\/Foundation",
    "name" : "Foundation",
    "CFBundleVersion" : "3502"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703155892224,
    "size" : 227598,
    "uuid" : "e086bb89-6564-32d7-bc95-9defc1a6777d",
    "path" : "\/Volumes\/VOLUME\/*\/libxpc.dylib",
    "name" : "libxpc.dylib"
  },
  {
    "source" : "P",
    "arch" : "x86_64",
    "base" : 140703156121600,
    "size" : 12497,
    "uuid" : "a2244f95-3e6d-3c71-af95-eeb6eff24622",
    "path" : "\/Volumes\/VOLUME\/*\/libsystem_blocks.dylib",
    "name" : "libsystem_blocks.dylib"
  }
],
  "sharedCache" : {
  "base" : 140703128616960,
  "size" : 21474836480,
  "uuid" : "a9ad3602-92d7-3ad6-a17e-cd1bb414308e"
},
  "vmSummary" : "ReadOnly portion of Libraries: Total=1.0G resident=0K(0%) swapped_out_or_unallocated=1.0G(100%)\nWritable regions: Total=610.4M written=0K(0%) resident=0K(0%) swapped_out=0K(0%) unallocated=610.4M(100%)\n\n                                VIRTUAL   REGION \nREGION TYPE                        SIZE    COUNT (non-coalesced) \n===========                     =======  ======= \nActivity Tracing                   256K        1 \nColorSync                            8K        2 \nFoundation                          16K        1 \nKernel Alloc Once                    8K        1 \nMALLOC                           596.3M       36 \nMALLOC guard page                   32K        8 \nSTACK GUARD                       56.0M       10 \nStack                             12.6M       10 \nVM_ALLOCATE                        148K        2 \nVM_ALLOCATE (reserved)               4K        1         reserved VM address space (unallocated)\n__DATA                            21.6M      560 \n__DATA_CONST                      70.8M      579 \n__DATA_DIRTY                        39K       15 \n__FONT_DATA                        2352        1 \n__LINKEDIT                       234.3M       11 \n__OBJC_RO                         61.3M        1 \n__OBJC_RW                         2727K        1 \n__TEXT                           810.5M      593 \n__TPRO_CONST                         8K        2 \ndyld private memory               28.3G       23 \nmapped file                       33.1M        9 \nshared memory                       24K        3 \n===========                     =======  ======= \nTOTAL                             30.1G     1870 \nTOTAL, minus reserved VM space    30.1G     1870 \n",
  "legacyInfo" : {
  "threadTriggered" : {
    "queue" : "com.apple.main-thread"
  }
},
  "logWritingSignature" : "6931672e590f7c7f9e887ca5eb23638bca8dae53",
  "trialInfo" : {
  "rollouts" : [
    {
      "rolloutId" : "670ea6eb7a111748a97092a4",
      "factorPackIds" : {
        "SIRI_TEXT_TO_SPEECH" : "6847796ab4d8c1072b580d17"
      },
      "deploymentId" : *********
    },
    {
      "rolloutId" : "6410af69ed1e1e7ab93ed169",
      "factorPackIds" : {

      },
      "deploymentId" : *********
    }
  ],
  "experiments" : [

  ]
}
}

Model: MacBookPro16,2, BootROM 2075.120.2.0.0 (iBridge: 22.16.15072.0.0,0), 4 processors, Quad-Core Intel Core i5, 2 GHz, 16 GB, SMC 
Graphics: Intel Iris Plus Graphics, Intel Iris Plus Graphics, Built-In
Display: Color LCD, 2560 x 1600 Retina, Main, MirrorOff, Online
Memory Module: BANK 0/ChannelA-DIMM0, 8 GB, LPDDR4X, 3733 MHz, Samsung, K4UBE3D4AA-MGCL
Memory Module: BANK 2/ChannelB-DIMM0, 8 GB, LPDDR4X, 3733 MHz, Samsung, K4UBE3D4AA-MGCL
AirPort: spairport_wireless_card_type_wifi (0x14E4, 0x7BF), wl0: Jul 26 2024 22:36:01 version 9.30.514.0.32.5.94 FWID 01-68d7ff80
AirPort: 
Bluetooth: Version (null), 0 services, 0 devices, 0 incoming serial ports
Network Service: Wi-Fi, AirPort, en0
USB Device: USB31Bus
USB Device: USB31Bus
USB Device: T2Bus
USB Device: Touch Bar Backlight
USB Device: Touch Bar Display
USB Device: Apple Internal Keyboard / Trackpad
USB Device: Headset
USB Device: Ambient Light Sensor
USB Device: FaceTime HD Camera (Built-in)
USB Device: Apple T2 Controller
Thunderbolt Bus: MacBook Pro, Apple Inc., 4.0
Thunderbolt Bus: MacBook Pro, Apple Inc., 86.0
